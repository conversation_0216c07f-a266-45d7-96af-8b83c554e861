<?php

namespace App\Library\Enums;

use App\Modules\Organization\Models\HrJobDepartmentRelationModel;

/**
 * 报销模块常用枚举数据
 * Class enums
 */
final class ReimbursementEnums
{

    /**
     * @Message ('补充附件类型')
     */
    const OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE = 30;

    // 支付凭证附件类型枚举
    const OSS_BUCKET_TYPE_REIMBURSEMENT_PAYMENT_VOUCHER = 66;

    // 超时报销标准金额的审批邮件附件
    const OSS_BUCKET_TYPE_EXCEEDS_STANDARD_AMOUNT_EMAIL_FILE = 73;

    // 油费里程照片(开始里程/结束里程)
    const OSS_BUCKET_TYPE_FUEL_START_MILEAGE_FILE = 74;
    const OSS_BUCKET_TYPE_FUEL_END_MILEAGE_FILE = 75;


    /**
     * @Message ('先款后票且需要补充发票')
     */
    const IS_SUPPLEMENT_INVOICE_DEFAULT = 0;
    const IS_SUPPLEMENT_INVOICE_YES = 1;
    const IS_SUPPLEMENT_INVOICE_NO = 2;
    public static $supplement_invoice_status = [
        self::IS_SUPPLEMENT_INVOICE_DEFAULT => 'supplement_invoice_status_default',
        self::IS_SUPPLEMENT_INVOICE_YES     => 'supplement_invoice_status_yes',
        self::IS_SUPPLEMENT_INVOICE_NO      => 'supplement_invoice_status_no',
    ];

    //审核状态
    const STATUS_WAIT = 1;                 //待审核
    const STATUS_REJECT = 2;               //驳回
    const STATUS_PASS = 3;                 //通过
    const STATUS_CANCEL = 4;               //取消

    // 报销新增申请状态
    const STATUS_WAITING_CONFIRMED = 7;    //待共同住宿人确认
    const STATUS_WAITING_SIGNED = 8;       //待签字
    const STATUS_WAITING_SUBMITTED = 9;    //待提交
    const STATUS_DISCARDED = 10;           //已作废
    public static $apply_status_item = [
        self::STATUS_WAITING_CONFIRMED,
        self::STATUS_WAITING_SIGNED,
        self::STATUS_WAITING_SUBMITTED,
        self::STATUS_WAIT,
        self::STATUS_REJECT,
        self::STATUS_PASS,
        self::STATUS_CANCEL,
        self::STATUS_DISCARDED,
    ];

    public static $audit_status_item = [
        self::STATUS_WAIT,
        self::STATUS_REJECT,
        self::STATUS_PASS,
        self::STATUS_CANCEL,
    ];

    //支付状态
    const PAY_STATUS_WAIT = 1;             //待支付
    const PAY_STATUS_PAY = 2;              //已支付
    const PAY_STATUS_UN_PAY = 3;           //未支付

    // 报销来源
    const SOURCE_TYPE_OA = 1;
    const SOURCE_TYPE_BY = 2;

    // 是否使用备用金
    const PETTY_USED_NO = 0;               // 否
    const PETTY_USED_YES = 1;              // 是

    //报销审批流申请人所属一级部门（Network Management）
    const SYS_DEPARTMENT_NETWORK_MANAGEMENT_ID = 4;

    const JOB_TITLE_AM = 79;   //Area Manager
    const JOB_TITLE_DM = 269;  //District Manager

    const IS_FINE = 1;                                                    //有罚金
    const IS_NO_FINE = 0;                                                 //无罚金
    const REIMBURSEMENT_FLASH_HOME = 'FlashHome';                         //报销审批流配置文件类型 对应按照公司（Flash Home Operation 、Flash Home Holding）查找
    const REIMBURSEMENT_FLASH_EXPRESS = 'FlashExpress';                   //报销审批流配置文件类型 按照flash express公司下的网点类型查找
    public static $is_fine_items = [
        self::IS_FINE    => 'reimbursement_is_fine',
        self::IS_NO_FINE => 'reimbursement_is_no_fine',
    ];

    //是否包含外包外协 0 无 1.外包 2.外协
    const OUT_WORKER_NOT = 0;                                             //无
    const OUT_WORKER_EPIBOLY = 1;                                         //外包
    const OUT_WORKER_OUTSOURCE = 2;                                       //外协
    public static $out_worker_items = [
        self::OUT_WORKER_NOT       => 'out_worker_not',
        self::OUT_WORKER_EPIBOLY   => 'out_worker_outsourcing',
        self::OUT_WORKER_OUTSOURCE => 'out_worker_outsource',
    ];

    //出差类型，3境内出差，4境外出差
    const TRAVEL_BUSINESS_TRIP_TYPE_IN = 3;
    const TRAVEL_BUSINESS_TRIP_TYPE_OUT = 4;
    public static $travel_business_trip_type = [
        self::TRAVEL_BUSINESS_TRIP_TYPE_IN  => 'travel_business_trip_type.' . self::TRAVEL_BUSINESS_TRIP_TYPE_IN,
        self::TRAVEL_BUSINESS_TRIP_TYPE_OUT => 'travel_business_trip_type.' . self::TRAVEL_BUSINESS_TRIP_TYPE_OUT,
    ];

    // 出差表 - 是否是报销住宿费
    const BUSINESS_TRIP_IS_STAY_YES = 1;
    const BUSINESS_TRIP_IS_STAY_NO = 0;

    // 明细行的支付方式
    const PAYMENT_METHOD_CASH = 1;                                        // 现金
    const PAYMENT_METHOD_QRCODE = 2;                                      // 二维码
    const PAYMENT_METHOD_BANK_CARD = 3;                                   // 银行卡
    const PAYMENT_METHOD_OTHER = 4;                                       // 其他
    public static $payment_method_item = [
        self::PAYMENT_METHOD_CASH      => 'reimbursement_payment_method_' . self::PAYMENT_METHOD_CASH,
        self::PAYMENT_METHOD_QRCODE    => 'reimbursement_payment_method_' . self::PAYMENT_METHOD_QRCODE,
        self::PAYMENT_METHOD_BANK_CARD => 'reimbursement_payment_method_' . self::PAYMENT_METHOD_BANK_CARD,
        self::PAYMENT_METHOD_OTHER     => 'reimbursement_payment_method_' . self::PAYMENT_METHOD_OTHER,
    ];

    /*** 报销 - 系统配置 start ***/
    // 费用类型
    const EXPENSES_TYPE_1 = 1;                                            // 差旅油费
    const EXPENSES_TYPE_2 = 2;                                            // 交通油费
    const EXPENSES_TYPE_3 = 3;                                            // 支援油费
    public static $expenses_type_item = [
        self::EXPENSES_TYPE_1 => 'reimbursement_expenses_type_' . self::EXPENSES_TYPE_1,
        self::EXPENSES_TYPE_2 => 'reimbursement_expenses_type_' . self::EXPENSES_TYPE_2,
        self::EXPENSES_TYPE_3 => 'reimbursement_expenses_type_' . self::EXPENSES_TYPE_3,
    ];

    // 油类型
    const OIL_TYPE_1 = 1;                                                 // 汽油
    const OIL_TYPE_2 = 2;                                                 // 柴油
    const OIL_TYPE_3 = 3;                                                 // 天然气
    public static $oil_type_item = [
        self::OIL_TYPE_2 => 'reimbursement_oil_type_' . self::OIL_TYPE_2,
        self::OIL_TYPE_1 => 'reimbursement_oil_type_' . self::OIL_TYPE_1,
        self::OIL_TYPE_3 => 'reimbursement_oil_type_' . self::OIL_TYPE_3,
    ];

    // 总部/一线类型
    const POSITION_TYPE_1 = HrJobDepartmentRelationModel::POSITION_TYPE_1;//一线操作
    const POSITION_TYPE_2 = HrJobDepartmentRelationModel::POSITION_TYPE_2;//一线职能
    const POSITION_TYPE_3 = HrJobDepartmentRelationModel::POSITION_TYPE_3;//总部职能
    public static $position_type_item = [
        self::POSITION_TYPE_3 => 'position_type_' . self::POSITION_TYPE_3,
        self::POSITION_TYPE_2 => 'position_type_' . self::POSITION_TYPE_2,
        self::POSITION_TYPE_1 => 'position_type_' . self::POSITION_TYPE_1,
    ];

    // 住宿区域类型
    const ACCOMMODATION_AREA_TYPE_1 = 1;                                  // 曼谷周边
    const ACCOMMODATION_AREA_TYPE_2 = 2;                                  // 非曼谷旅游区
    const ACCOMMODATION_AREA_TYPE_3 = 3;                                  // 非曼谷非旅游区

    // 境内住宿区域类型枚举
    public static $domestic_accommodation_area_type_item = [
        self::ACCOMMODATION_AREA_TYPE_1 => 'accommodation_area_type_' . self::ACCOMMODATION_AREA_TYPE_1,
        self::ACCOMMODATION_AREA_TYPE_2 => 'accommodation_area_type_' . self::ACCOMMODATION_AREA_TYPE_2,
    ];

    // 境内住宿额度类型枚举
    public static $domestic_accommodation_quota_type_item = [
        self::ACCOMMODATION_AREA_TYPE_1 => 'accommodation_area_type_' . self::ACCOMMODATION_AREA_TYPE_1,
        self::ACCOMMODATION_AREA_TYPE_2 => 'accommodation_area_type_' . self::ACCOMMODATION_AREA_TYPE_2,
        self::ACCOMMODATION_AREA_TYPE_3 => 'accommodation_area_type_' . self::ACCOMMODATION_AREA_TYPE_3,
    ];

    // 住宿类型
    const ACCOMMODATION_TYPE_A = 1;                                       // A type
    const ACCOMMODATION_TYPE_B = 2;                                       // B type
    const ACCOMMODATION_TYPE_C = 3;                                       // C type

    // 境外住宿类型
    public static $overseas_accommodation_type_item = [
        self::ACCOMMODATION_TYPE_A => 'A Type',
        self::ACCOMMODATION_TYPE_B => 'B Type',
        self::ACCOMMODATION_TYPE_C => 'C Type',
    ];

    // 是否需邮件审批
    const IS_REQUIRED_EMAIL_APPROVAL_NO = 0;
    const IS_REQUIRED_EMAIL_APPROVAL_YES = 1;
    public static $is_required_email_approval_no_item = [
        self::IS_REQUIRED_EMAIL_APPROVAL_NO  => 'is_required_email_approval_' . self::IS_REQUIRED_EMAIL_APPROVAL_NO,
        self::IS_REQUIRED_EMAIL_APPROVAL_YES => 'is_required_email_approval_' . self::IS_REQUIRED_EMAIL_APPROVAL_YES,
    ];

    // 特殊费用项配置
    // 特殊费用明细类型
    const SPECIAL_EXPENSE_DETAILS_TYPE_DOMESTIC_AIR_TICKETS = 1;          // 境内机票
    const SPECIAL_EXPENSE_DETAILS_TYPE_FUEL_COST = 2;                     // 油费
    const SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES = 3;            // 住宿费
    const SPECIAL_EXPENSE_DETAILS_TYPE_CAR_RENTAL_FEE = 4;                // 租车费
    const SPECIAL_EXPENSE_DETAILS_TYPE_OVERSEAS_AIR_TICKETS = 5;          // 海外机票
    const SPECIAL_EXPENSE_DETAILS_TYPE_TRAIN_TICKETS = 6;                 // 火车票
    const SPECIAL_EXPENSE_DETAILS_TYPE_BUS_TICKETS = 7;                   // 汽车票
    const SPECIAL_EXPENSE_DETAILS_TYPE_FERRY_TICKETS = 8;                 // 船票
    const SPECIAL_EXPENSE_DETAILS_TYPE_DRINKING_WATER_FEE = 9;            // 饮用水费
    const SPECIAL_EXPENSE_DETAILS_TYPE_WATER_FEE = 10;                    // 水费
    const SPECIAL_EXPENSE_DETAILS_TYPE_ELECTRICITY_FEE = 11;              // 电费
    const SPECIAL_EXPENSE_DETAILS_TYPE_TRANSPORTATION = 12;               // 交通费

    // 特殊费用项类型
    const SPECIAL_EXPENSE_ITEM_TYPE_TRAVEL_FEE = 1;                       //差旅费
    const SPECIAL_EXPENSE_ITEM_TYPE_TRANSPORTATION_FEE = 2;               //交通费
    const SPECIAL_EXPENSE_ITEM_TYPE_SUPPORT_FEE = 3;                      //支援费
    const SPECIAL_EXPENSE_ITEM_TYPE_WATER_AND_ELECTRICITY_FEE = 4;        //水电费
    const SPECIAL_EXPENSE_ITEM_TYPE_OFFICE_FEE = 5;                       //办公费
    const SPECIAL_EXPENSE_ITEM_TYPE_BANK_HANDLING_FEE = 6;                //银行手续费

    // 汇率系统api配置
    const EXCHANGE_RATE_BOT_API = 'https://apigw1.bot.or.th/bot/public/Stat-ExchangeRate/v2/DAILY_AVG_EXG_RATE/';
    const EXCHANGE_RATE_BOT_API_CLIENT_ID = 'd13735c9-ffa1-43c5-b3a5-401298fbbaf0';

    // 支援网点支援状态
    const STORE_SUPPORT_STATUS_2 = 2;                                     // 已生效
    const STORE_SUPPORT_STATUS_3 = 3;                                     // 已失效

    // 是否有共同住宿人
    const TRAVEL_IS_HAVE_ROOMMATE_YES = 1;
    const TRAVEL_IS_HAVE_ROOMMATE_NO = 2;

    // 共同住宿人确认状态
    const TRAVEL_ROOMMATE_CONFIRM_STATUS_1 = 1;                           // 待确认
    const TRAVEL_ROOMMATE_CONFIRM_STATUS_2 = 2;                           // 已确认
    const TRAVEL_ROOMMATE_CONFIRM_STATUS_3 = 3;                           // 离职自动确认
    const TRAVEL_ROOMMATE_CONFIRM_STATUS_4 = 4;                           // 已拒绝
    const TRAVEL_ROOMMATE_CONFIRM_STATUS_5 = 5;                           // 超时自动拒绝
    public static $travel_roommate_confirm_status_item = [
        self::TRAVEL_ROOMMATE_CONFIRM_STATUS_1 => 'reimbursement_roommate_confirm_status_' . self::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
        self::TRAVEL_ROOMMATE_CONFIRM_STATUS_2 => 'reimbursement_roommate_confirm_status_' . self::TRAVEL_ROOMMATE_CONFIRM_STATUS_2,
        self::TRAVEL_ROOMMATE_CONFIRM_STATUS_3 => 'reimbursement_roommate_confirm_status_' . self::TRAVEL_ROOMMATE_CONFIRM_STATUS_3,
        self::TRAVEL_ROOMMATE_CONFIRM_STATUS_4 => 'reimbursement_roommate_confirm_status_' . self::TRAVEL_ROOMMATE_CONFIRM_STATUS_4,
        self::TRAVEL_ROOMMATE_CONFIRM_STATUS_5 => 'reimbursement_roommate_confirm_status_' . self::TRAVEL_ROOMMATE_CONFIRM_STATUS_5,
    ];

    // 报销单创建环节的动作
    const CREATE_ACTION_ADD_SUBMIT = 1;                                   // 新增提交
    const CREATE_ACTION_RESUBMIT = 2;                                     // 重新提交
    const CREATE_ACTION_APPLY_SIGNED = 3;                                 // 申请人签字完成(自动提交)
    const CREATE_ACTION_MANUAL_SUBMIT = 4;                                // 手动补充提交(自动提交失败后)
    const CREATE_ACTION_REJECT_CONFIRM = 5;                               // 共同住宿人拒绝确认
    const CREATE_ACTION_TIMEOUT_AUTO_REJECT = 6;                          // 超时自动拒绝确认
    const CREATE_ACTION_ALL_CONFIRMED = 7;                                // 共同住宿人全部确认
    const CREATE_ACTION_CREATED_INVALID = 8;                              // 发起人作废
    const CREATE_ACTION_APPLY_REJECT_SIGN = 9;                            // 申请人拒绝签字
    const CREATE_ACTION_APPLY_SIGNED_AUTO_SUBMIT_FAIL = 10;               // 申请人签字完成(自动提交失败)


    // 是否限制超过次月指定日期补充说明
    const TRIP_TIME_NOT_LIMIT = 0;
    const TRIP_TIME_IS_LIMIT = 1;

    // 是否可进一步提交申请
    const CAN_APPLY_NO = 0;
    const CAN_APPLY_YES = 1;

    // 明细行金额字段清单
    public static $expense_amount_fields = [
        'tax_not',              //发票金额(不含vat含wht)
        'tax',                  //vat金额
        'wht_tax_amount',       //wht税额
        'amount',               //发票金额(含vat含wht)
        'payable_amount',       //应付金额(含vat不含wht)
        'deductible_tax_amount',//可抵扣税额
    ];

    // 操作渠道(签字/作废/确认)
    const OPERATE_CHANNEL_BY = 1;
    const OPERATE_CHANNEL_KIT = 2;
    const OPERATE_CHANNEL_OA = 3;

    // 消息场景
    const MSG_SCENCE_WAITING_CONFIRMED = 1;                               // 待共同住宿人确认
    const MSG_SCENCE_WAITING_SIGNED = 2;                                  // 待申请人签字
    const MSG_SCENCE_REJECT_CONFIRM = 3;                                  // 共同住宿人拒绝确认
    const MSG_SCENCE_REJECT_SIGN = 4;                                     // 申请人拒绝签字
    const MSG_SCENCE_AUTO_SUBMIT_FAIL = 5;                                // 自动提交失败(签字提交后)

    // 消息队列
    const MSG_REDIS_QUEUE_NAME = 'reimbursement_msg_notice_queue';
    const MSG_SEND_SUCCESS = 1;
    const MSG_SEND_FAIL = 2;

    // 作废类型
    const INVALID_TYPE_1 = 1;                                             // 发起人主动作废
    const INVALID_TYPE_2 = 2;                                             // 共同住宿人拒绝确认
    const INVALID_TYPE_3 = 3;                                             // 申请人拒绝签字
    const INVALID_TYPE_4 = 4;                                             // 共同住宿人超时未确认
    const INVALID_TYPE_5 = 5;                                             // 申请人超时未签字


    // 申请列表tab类型
    const LIST_TAB_TYPE_ALL = 0;                                          // 全部
    const LIST_TAB_TYPE_PENDING = 1;                                      // 待处理

    // 获取移动端详情页类型
    const MOBILE_DETAIL_PAGE_TYPE_VIEW = 1;                               // 查看/查看详情
    const MOBILE_DETAIL_PAGE_TYPE_GO_SIGN = 2;                            // 去签字

    // 申请人签字状态
    const SIGN_STATUS_AGREED = 1;                                         // 同意
    const SIGN_STATUS_REJECTED = 2;                                       // 拒绝

    // 手机端详情页展示字段配置
    // 去签字详情页[一线操作]员工展示的字段
    public static $mobile_sign_page_frontline_operator_expense_fields = [
        'budget_text',                                   // 报销实质
        'cost_period',                                   // 费用期间
        'product_name',                                  // 费用明细
        'info',                                          // 报销说明
        'payable_amount',                                // 应付金额（含VAT不含WHT)
    ];

    // 详情页报销实质返回需要过滤的字段(反向)
    public static $mobile_detail_page_filter_fields = [
        'start_at',                            // 费用期间-开始日期
        'end_at',                              // 费用期间-结束日期
        'id',                                  // 行ID
        're_id',                               // 单据ID
        'category_a',                          // 老字段: 报销分类 1差旅，2本地
        'category_b',                          // 老字段: 费用明细
        'category_a_text',                     // 老字段: 报销分类 1差旅，2本地
        'category_b_text',                     // 老字段: 费用明细
        'budget_id',                           // 预算科目ID
        'level_code',                          // 预算科目code
        'budget_text_en',                      // 预算科目英文名
        'budget_text_th',                      // 预算科目泰文名
        'ledger_account_id',                   // 核算科目ID
        'product_id',                          // 明细ID
        'product_name_en',                     // 明细英文名称
        'product_name_th',                     // 明细泰文名
        'travel_id',                           // BY出差单ID
        'travel_days_num',                     // 出差天数
        'days_num',                            // 出差天数
        'cost_store_n_id',                     // 费用所属网点id
        'cost_store_id',                       // 饮水费&水电费-费用网点id
        'cost_store_name',                     // 饮水费&水电费-费用网点name
        'is_deduct',                           // 银行流水-可抵扣确认
        'ticket_type',                         // 发票种类
        'invoice_type',                        // 发票类型
        'invoice_type_label',                  // 发票类型翻译
        'is_ticket_no_exist',                  // 发票编号是否存在
        'is_with_vat_invoice',                 // 是否有增值税票
        'support_job_title_id',                // 支援费用-申请职位id
        'support_store_id',                    // 支援费用-申请网点id
        'support_is_followed_policy',          // 支援住宿是否已按报销政策标准执行
        'support_not_followed_policy_reasons', // 未按照标准执行说明
        'is_fine',                             // 是否有罚金
        'payment_method',                      // 支付方式
        'payment_voucher',                     // 支付凭证
        'fuel_oil_type',                       // 油费-燃油类型
        'travel_business_trip_type',           // 出差类型枚举值
        'travel_business_trip_type_text',      // 出差类型翻译
        'wht_type',                            // WHT类别枚举值
        'travel_is_have_roommate',             // 是否有共同住宿人枚举
        'travel_start_at',                     // 出差开始日期
        'travel_end_at',                       // 出差结束日期
        'budget_template_type',                // 预算科目的template_type
        'template_type',                       // 预算科目明细的template_type
        'meal_allowance_amount',               // 餐补额度
        'meal_allowance_currency',             // 餐补币种
        'cost_store_n_pc_code',                // 费用所属中心异形字段
    ];

    /**
     * 详情页报销实质需要展示的字段(正向)
     * @doc https://flashexpress.feishu.cn/wiki/TmwPwiGWCiPPg6kcc1CcPvZynDG
     */
    public static $mobile_detail_page_show_fields = [
        'budget_text',
        'product_name',
        'ledger_account_name',
        'cost_store_n_name',
        'cost_period',
        'cost_center_code',
        'info',
        'voucher_description',
        'support_serial_no',
        'support_store_name',
        'support_employment_begin_date',
        'support_employment_days',
        'support_employment_end_date',
        'support_job_title_name',
        'travel_serial_no',
        'travel_reimbursement_overdue_remark',
        'travel_is_have_roommate_label',
        'travel_roommate_item',
        'travel_start',
        'fuel_start',
        'travel_end',
        'fuel_end',
        'fuel_start_mileage_file',
        'fuel_end_mileage_file',
        'fuel_start_mileage',
        'fuel_end_mileage',
        'fuel_mileage',
        'fuel_vehicle_type',
        'fuel_vehicle_number',
        'fuel_oil_type_label',
        'fuel_use_date',
        'serial_no',
        'applicant_staff_name',
        'sys_store_name',
        'final_audit_num',
        'employment_days',
        'ticket_type_label',
        'is_ticket_no_exist_label',
        'invoices_ids',
        'invoice_no',
        'attachments',
        'required_supplement_file',
        'exceeds_standard_amount_email_file',
        'invoice_tax_no',
        'enterprise_name',
        'company_addr',
        'tax_not',
        'wht_type_name',
        'rate',
        'wht_tax',
        'tax',
        'wht_tax_amount',
        'amount',
        'payable_amount',
        'deductible_vat_tax',
        'deductible_tax_amount',
    ];

    // 如下字段为0的, 重置为空
    public static $mobile_datail_page_zero_value_reset_fields = [
        'support_employment_days', // 支援天数
        'fuel_start_mileage',      // 油费-开始里程
        'fuel_end_mileage',        // 油费-结束里程
        'fuel_mileage',            // 总里程
        'final_audit_num',         // 外协雇佣人数
        'employment_days',         // 外协雇佣天数
    ];

    // 移动端详情页的附件清单
    public static $mobile_detail_page_attachment_fields = [
        'attachments',                        // 附件
        'required_supplement_file',           // 补充附件
        'exceeds_standard_amount_email_file', // 超出报销标准金额审批邮件附件
        'fuel_start_mileage_file',            // 油费开始里程照片
        'fuel_end_mileage_file',              // 油费结束里程照片
    ];


    // 提醒消息页面路径
    const MSG_PAGE_PATH_VIEW_DETAIL = '/reimbursement-apply-detail';      // 查看详情
    const MSG_PAGE_PATH_TO_SIGN = '/reimbursement-apply-go-sign';         // 去签字
    const MSG_PAGE_PATH_TO_CONFIRM = '/reimbursement-apply-go-confirm';   // 去确认

    // 消息内容中的按钮样式
    const MSG_CONTENT_TEMPLATE = "<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' />
<div style='font-size: 14px; word-break: break-all; padding: 10px; box-sizing: border-box'>MESSAGE_CONTENT_TEXT</div>";
    const MSG_CONTENT_HREF_STYLE = 'color: #262626;display: block;width: 90%;margin: 60px auto;border-radius: 50px;background-color: #ffea33;height: 44px;line-height: 44px;text-align: center;text-decoration: none;';

    // 鉴权状态枚举
    const AUTH_STATUS_SUCCESS = 1;                                        // 正常
    const AUTH_STATUS_APPLY_ERROR = 2;                                    // 申请人不一致
    const AUTH_STATUS_ORDER_ERROR = 3;                                    // 报销单状态异常

    // 申请人户口地址相关项
    const STAFF_ITEM_REGISTER_PROVINCE = 'REGISTER_PROVINCE';             // 户口所在省
    const STAFF_ITEM_REGISTER_CITY = 'REGISTER_CITY';                     // 户口所在市
    const STAFF_ITEM_REGISTER_DISTRICT = 'REGISTER_DISTRICT';             // 户口所在乡
    const STAFF_ITEM_REGISTER_POSTCODES = 'REGISTER_POSTCODES';           // 邮编
    const STAFF_ITEM_REGISTER_HOUSE_NUM = 'REGISTER_HOUSE_NUM';           // 门牌号
    const STAFF_ITEM_REGISTER_VILLAGE_NUM = 'REGISTER_VILLAGE_NUM';       // 村号
    const STAFF_ITEM_REGISTER_VILLAGE = 'REGISTER_VILLAGE';               // 村庄
    const STAFF_ITEM_REGISTER_ALLEY = 'REGISTER_ALLEY';                   // 巷
    const STAFF_ITEM_REGISTER_STREET = 'REGISTER_STREET';                 // 街道
    public static $staff_husehold_register_items = [
        self::STAFF_ITEM_REGISTER_PROVINCE,
        self::STAFF_ITEM_REGISTER_CITY,
        self::STAFF_ITEM_REGISTER_DISTRICT,
        self::STAFF_ITEM_REGISTER_POSTCODES,
        self::STAFF_ITEM_REGISTER_HOUSE_NUM,
        self::STAFF_ITEM_REGISTER_VILLAGE_NUM,
        self::STAFF_ITEM_REGISTER_VILLAGE,
        self::STAFF_ITEM_REGISTER_ALLEY,
        self::STAFF_ITEM_REGISTER_STREET,
    ];

    // 曼谷省code
    const BANGKOK_PROVINCE_CODE = 'TH01';

    // PDF文件名
    const PDF_FILE_NAME_RECEIPT_VOUCHER = 'Cash Receipt ใบสำคัญรับเงิน.pdf';          // Cash Receipt 收据凭证
    const PDF_FILE_NAME_PRIVATE_CAR_EXPENSE = 'Private car expense ค่าใช้จ่ายรถยนต์ส่วนตัว.pdf';  // CLAIM Private car expense
    const PDF_FILE_NAME_PUBLIC_TRANSPORT_EXPENSE = 'Public transportation fee ค่ารถสาธารณะ.pdf'; // CLAIM Public transport expense
    const PDF_FILE_NAME_ACCOMMODAYION_DOMESTIC = 'Domestic accommodation expense ค่าที่พัก-ในประเทศ.pdf'; // CLAIM Accommodation-境内
    const PDF_FILE_NAME_ACCOMMODAYION_OVERSEAS = 'Overseas accommodation expense ค่าที่พัก-ต่างประเทศ.pdf'; // CLAIM Accommodation-境外

    /*** 报销 - 系统配置 end ***/

    // ph支援导入最多条数
    const UPLOAD_SUPPORT_ORDER_MAX_COUNT = 2000;

}
