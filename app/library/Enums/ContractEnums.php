<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/22
 * Time: 10:58
 */
namespace App\Library\Enums;

/**
 * 合同相关的枚举
 * Class enums
 */
final class ContractEnums
{
    // 合同归档状态
    const CONTRACT_ARCHIVE_STATUS_DEFAULF = 0;
    const CONTRACT_ARCHIVE_STATUS_PENDING = 1;
    const CONTRACT_ARCHIVE_STATUS_APPROVAL = 2;
    const CONTRACT_ARCHIVE_STATUS_INVALID = 3; // 已作废
    const CONTRACT_ARCHIVE_STATUS_NO_UPLOAD = 4; // 待上传盖章合同
    const CONTRACT_ARCHIVE_STATUS_TERMINAL = 5; // 已终止
    const CONTRACT_ARCHIVE_STATUS_INVALID_ING = 6; // 待作废
    const CONTRACT_ARCHIVE_STATUS_TERMINAL_ING = 7; // 待终止
    public static $contract_archive_status = [
        self::CONTRACT_ARCHIVE_STATUS_PENDING => 'contract_archive_status.1',
        self::CONTRACT_ARCHIVE_STATUS_APPROVAL => 'contract_archive_status.2',
        self::CONTRACT_ARCHIVE_STATUS_INVALID => 'contract_archive_status.3',
        self::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD => 'contract_archive_status.4',
        self::CONTRACT_ARCHIVE_STATUS_TERMINAL => 'contract_archive_status.5',
        self::CONTRACT_ARCHIVE_STATUS_INVALID_ING => 'contract_archive_status.6',
        self::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING => 'contract_archive_status.7'
    ];

    //押金合同状态显示
    public static $contract_status_arr = [
        ['value' => self::CONTRACT_ARCHIVE_STATUS_PENDING, 'label' => 'contract_archive_status.1'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_APPROVAL, 'label' => 'contract_archive_status.2'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_INVALID, 'label' => 'contract_archive_status.3'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD, 'label' => 'contract_archive_status.4'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_TERMINAL, 'label' => 'contract_archive_status.5'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_INVALID_ING, 'label' => 'contract_archive_status.6'],
        ['value' => self::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING, 'label' => 'contract_archive_status.7'],
    ];

    // 房东类型
    public const HOUSE_OWNER_TYPE_COMPANY   = 1;    // 企业(公司)
    public const HOUSE_OWNER_TYPE_PERSON    = 2;    // 个人
    public const HOUSE_OWNER_TYPE_AGENT     = 3;    // 中介

    // 所有的房东类型
    public const HOUSE_OWNER_TYPES =  self::HOUSE_OWNER_TYPE_COMPANY . ',' . self::HOUSE_OWNER_TYPE_PERSON . ',' . self::HOUSE_OWNER_TYPE_AGENT;

    //租房合同-付款方式 - 1、月付、2年付、3季度、4半年付、5一次性付款
    const CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_MONTH = 1;
    const CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_YEAR = 2;
    const CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_SEASON = 3;
    const CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_HALF_YEAR = 4;
    const CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_ONE = 5;

    //租房合同-付款方式 - 对应月份
    public static $contract_lease_type_month = [
        self::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_MONTH     => 1,
        self::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_YEAR      => 12,
        self::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_SEASON    => 3,
        self::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_HALF_YEAR => 6,
    ];

    // WHT分类名称
    public const WHT_CATEGORY_PND3          = 'PND3';    // PND3
    public const WHT_CATEGORY_PND53         = 'PND53';    // PND53

    // 是否终止
    public const CONTRACT_STATUS_NOT_TERMINAL = 0;  //未终止
    public const CONTRACT_STATUS_IS_TERMINAL = 1;  //已终止
    public const CONTRACT_STATUS_IS_TERMINAL_AUDIT = 2; //提交终止审批
    // 是否废弃
    const CONTRACT_STATUS_NOT_INVALID = 0;  //未作废
    const CONTRACT_STATUS_IS_INVALID = 1;  //作废状态
    const CONTRACT_STATUS_IS_INVALID_AUDIT = 2;  //提交作废审核

    //上传签字合同附件类型
    public const OSS_BUCKET_TYPE_CONTRACT_SIGNATURE_FILE = 28;
    //上传终止附件类型
    public const OSS_BUCKET_TYPE_TERMINAL_FILE = 29;

    // 租房合同附件主类型
    public const RENT_CONTRACT_FILE_OSS_BUCKET_TYPE = 61;

    // 租房合同附件子类型
    public const RENT_CONTRACT_SUB_TYPE_FILE_TEXT = 1;//合同正文
    public const RENT_CONTRACT_SUB_TYPE_FILE_OTHER = 2;//其他附件
    public const RENT_CONTRACT_SUB_TYPE_FILE_DTI = 3;//DTl注册文件
    public const RENT_CONTRACT_SUB_TYPE_FILE_OWNERSHIP_PROOF = 4;//所有权证明文件
    public const RENT_CONTRACT_SUB_TYPE_FILE_VALID_ID = 5;//身份证件
    public const RENT_CONTRACT_SUB_TYPE_FILE_RPT = 6;//不动产税缴纳证明
    public const RENT_CONTRACT_SUB_TYPE_FILE_BIR = 7;//税务文件
    public const RENT_CONTRACT_SUB_TYPE_FILE_SAMPLE_RECEIPT = 8;//收据样例
    public const RENT_CONTRACT_SUB_TYPE_FILE_LEASED_LAYOUT = 9;//房屋布局图
    public const RENT_CONTRACT_SUB_TYPE_FILE_LEASED_PICTURE = 10;//房屋图片
    public const RENT_CONTRACT_SUB_TYPE_FILE_CERTIFICATE_LOCATIONAL = 11;//位置许可
    public const RENT_CONTRACT_SUB_TYPE_FILE_BUILDING_PERMIT = 12;//建筑许可证
    public const RENT_CONTRACT_SUB_TYPE_FILE_OCCUPANCY_PERMIT = 13;//占用许可证
    public const RENT_CONTRACT_SUB_TYPE_FILE_FSI = 14;//消防许可证
    public const RENT_CONTRACT_SUB_TYPE_FILE_INSURANCE_POLICY = 15;//保险政策
    public const RENT_CONTRACT_SUB_TYPE_FILE_BY_LAWS_ARTICLES = 16;//公司证书
    public const RENT_CONTRACT_SUB_TYPE_FILE_DEED_COPY = 17;//地契副本
    public const RENT_CONTRACT_SUB_TYPE_FILE_BUILDING_REGISTRATION = 18;//建筑物登记
    public const RENT_CONTRACT_SUB_TYPE_FILE_BANK_HEADER = 19;//银行账户
    public const RENT_CONTRACT_SUB_TYPE_FILE_AUTHORIZED_ID_AND_ACCT = 20;//授权董事身份证和户口
    public const RENT_CONTRACT_SUB_TYPE_FILE_ID_AND_ACCT = 21;//身份证和户口
    public const RENT_CONTRACT_SUB_TYPE_FILE_AUTHORITH_LETTER = 22;//委托书


    // 地契类型
    public const LAND_TYPE_1          = 1;    // น.ส. 4
    public const LAND_TYPE_2          = 2;    // น.ส. 2
    public const LAND_TYPE_3          = 3;    // น.ส. 3
    public const LAND_TYPE_4          = 4;    // น.ส. 3n
    public const LAND_TYPE_5          = 5;    // น.ส. 5
    public const LAND_TYPE_6          = 6;    // 其他
    public static $land_type_item = [
        self::LAND_TYPE_1 => 'land_type_1',
        self::LAND_TYPE_2 => 'land_type_2',
        self::LAND_TYPE_3 => 'land_type_3',
        self::LAND_TYPE_4 => 'land_type_4',
        self::LAND_TYPE_5 => 'land_type_5',
        self::LAND_TYPE_6 => 'land_type_6'
    ];

    // 出租人类型
    public const LEASER_TYPE_OWNER          = 1;    // 房主
    public const LEASER_TYPE_AGENT          = 2;    // 二房东
    public static $leaser_type_item = [
        self::LEASER_TYPE_OWNER => 'leaser_type_owner',
        self::LEASER_TYPE_AGENT => 'leaser_type_agent',
    ];

    // flash express
    public const FLASH_EXPRESS_TH_ID          = 1;     // 泰国快递公司ID
    public const FLASH_EXPRESS_PH_ID          = 80001; // 菲律宾快递公司ID

    // 拆分月份类别
    public const MONTH_SPLIT_FULL           = 0;     // 满月
    public const MONTH_SPLIT_START          = 1;     // 起始日期
    public const MONTH_SPLIT_END            = 2;     // 结束日期

    // 数据查询每批数量
    public const SQL_QUERY_PAGE_SIZE        = 50000;

    // 付款情况
    public const PAYMENT_TYPE_NOT_PAY               = 1; // 暂未支付
    public const PAYMENT_TYPE_PART_PENDING_PAY      = 2; // 部分待支付
    public const PAYMENT_TYPE_PART_PAY_DONE         = 3; // 部分已支付
    public const PAYMENT_TYPE_PAY_DONE              = 4; // 已支付
    public const PAYMENT_TYPE_PENDING_PAY           = 5; // 待支付
    public static $payment_status_item = [
        self::PAYMENT_TYPE_NOT_PAY          => 'contract_enums_payment_status_not_pay',
        self::PAYMENT_TYPE_PART_PENDING_PAY => 'contract_enums_payment_status_part_pending_pay',
        self::PAYMENT_TYPE_PART_PAY_DONE    => 'contract_enums_payment_status_part_pay_done',
        self::PAYMENT_TYPE_PAY_DONE         => 'contract_enums_payment_status_pay_done',
        self::PAYMENT_TYPE_PENDING_PAY      => 'contract_enums_payment_status_pending_pay',
    ];

    // 费用类型
    public const COST_TYPE_RENTING                  = 1; // 房租
    public const COST_TYPE_DEPOSIT_AMOUNT           = 2; // 押金
    public const COST_TYPE_AREA                     = 3; // 区域服务费
    public const COST_TYPE_AMOUNT_MONTHLY           = 4; // 房产税
    public const COST_TYPE_CONTRACT_DEPOSIT         = 5; // 定金
    public const COST_TYPE_SIGNBOARD_TAX            = 6; // 广告牌税
    public const COST_TYPE_LAND_TAX                 = 7; // 土地税
    public const COST_TYPE_INSURANCE_AMOUNT         = 8; // 火灾保险费
    public const COST_TYPE_ANTIMOTH_AMOUNT          = 9; // 房屋杀虫防蛀费
    public const COST_TYPE_TOTAL_AMOUNT             = 10; // 印花税
    // 房租和区域服务费类型
    public static $cost_type_items = [
        self::COST_TYPE_RENTING => 'contract_enums_cost_type_renting',
        self::COST_TYPE_AREA => 'contract_enums_cost_type_area',
        self::COST_TYPE_DEPOSIT_AMOUNT => 'payment_cost_type.2',
        self::COST_TYPE_CONTRACT_DEPOSIT => 'payment_cost_type.5',
        self::COST_TYPE_AMOUNT_MONTHLY => 'payment_cost_type.4',
        self::COST_TYPE_TOTAL_AMOUNT => 'payment_cost_type_my_10',
        self::COST_TYPE_SIGNBOARD_TAX => 'contract_enums_cost_type_signbord_tax',
        self::COST_TYPE_LAND_TAX => 'payment_cost_type.7',
        self::COST_TYPE_INSURANCE_AMOUNT => 'payment_cost_type.8',
        self::COST_TYPE_ANTIMOTH_AMOUNT => 'payment_cost_type.9',
    ];

    // 导出类型
    public const EXPORT_TYPE_ALL                   = 0; // 所有
    public const EXPORT_TYPE_RENTING_AREA          = 1; // 房租和区域费
    public const EXPORT_TYPE_OTHER_SERVICE         = 2; // 其它费

    public const FLASH_EXPRESS_MY_ID          = 315;   // 马来快递公司ID
    public const FLASH_EXPRESS_ID_ID          = 1;     // 印尼快递公司ID
    public const FLASH_EXPRESS_LA_ID          = 40001; // 老挝快递公司ID

    // 导出状态
    public const EXPORT_STATE_NOT_DONE      = 0; // 未处理
    public const EXPORT_STATE_DONE          = 1; // 已处理

    // Flash Supply Chain Management

    // 其他合同-合同所属公司: 对应 contract_company 表的code
    const CONTRACT_COMPANY_DEFAULT = 0;
    const CONTRACT_COMPANY_FLASH_EXPRESS = 1;
    const CONTRACT_COMPANY_FLASH_FULLFILMENT = 2;
    const CONTRACT_COMPANY_FLASH_MONEY = 3;
    const CONTRACT_COMPANY_F_COMMERCE = 4;
    const CONTRACT_COMPANY_FLASH_LAOS = 5;
    const CONTRACT_COMPANY_FLASH_PAY = 6;
    const CONTRACT_COMPANY_FLASH_HR = 7;
    const CONTRACT_COMPANY_FLASH_EXPRESS_PH = 8;
    const CONTRACT_COMPANY_FLASH_EXPRESS_MALAYSIA = 9;
    const CONTRACT_COMPANY_FLASH_SUPPLY_CHAIN_MANAGEMENT = 10;
    const CONTRACT_COMPANY_FLASH_HOME_OPERATION = 15;

    // 有效期
    public const CONTRACT_DEADLINE_HALF_YRAE        = 1; // 半年
    public const CONTRACT_DEADLINE_YRAE             = 2; // 年
    public const CONTRACT_DEADLINE_OTHER            = 3; // 其它
    public static $contract_deadline_type_items = [
        self::CONTRACT_DEADLINE_HALF_YRAE => 'contract_deadline_type.1',
        self::CONTRACT_DEADLINE_YRAE => 'contract_deadline_type.2'
    ];

    // 结算方式
    public const PAY_METHOD_LEFT_AMOUNT             = 1; // 余额结算
    public const PAY_METHOD_LIVE_WAY                = 2; // 现场结算
    public const PAY_METHOD_ONE_WEEK                = 3; // 7天结算
    public const PAY_METHOD_HALF_MONTH              = 4; // 半月结算
    public const PAY_METHOD_HOULE_MONTH             = 5; // 月结算
    public static $pay_method_type_items = [
        self::PAY_METHOD_LEFT_AMOUNT => 'pay_method_type.1',
        self::PAY_METHOD_LIVE_WAY => 'pay_method_type.2',
        self::PAY_METHOD_ONE_WEEK => 'pay_method_type.3',
        self::PAY_METHOD_HALF_MONTH => 'pay_method_type.4',
        self::PAY_METHOD_HOULE_MONTH => 'pay_method_type.5'
    ];

    // 合同子模块标识
    const MODULE_TYPE_APPLY = 1;// 我的申请
    const MODULE_TYPE_AUDIT = 2;// 合同审批
    const MODULE_TYPE_ARCHIVE = 3;// 合同归档
    const MODULE_TYPE_DATA = 4;// 数据查询

    // 合同是否需关联报价单
    const CONTRACT_RELATION_QUOTATION_YES = 1; //是
    const CONTRACT_RELATION_QUOTATION_NO = 2; //否
    const VALIDATE_CONTRACT_RELATION_QUOTATION = self::CONTRACT_RELATION_QUOTATION_YES . ',' . self::CONTRACT_RELATION_QUOTATION_NO;

    // 合同消息提醒子模块
    // 合同消息提醒状态
    const TASK_TYPE_CONTRACT_EXPIRE = 1; //合同到期提醒
    const TASK_TYPE_LOI_SIGN = 2; //意向书签署提醒
    const TASK_TYPE_DEPOSIT_PAY = 3; //定金支付提醒
    const TASK_TYPE_CONTRACT_SIGN = 4; //合同签署提醒
    public static $task_type = [
        self::TASK_TYPE_CONTRACT_EXPIRE => 'contract_remind_task_type.contract_expire',
        self::TASK_TYPE_LOI_SIGN => 'contract_remind_task_type.loi_sign',
        self::TASK_TYPE_DEPOSIT_PAY => 'contract_remind_task_type.deposit_pay',
        self::TASK_TYPE_CONTRACT_SIGN => 'contract_remind_task_type.contract_sign',
    ];
    const VALIDATE_TASK_TYPE = self::TASK_TYPE_CONTRACT_EXPIRE . ',' . self::TASK_TYPE_LOI_SIGN . ',' . self::TASK_TYPE_DEPOSIT_PAY . ',' . self::TASK_TYPE_CONTRACT_SIGN;

    // 任务状态
    const TASK_STATUS_TODO = 1;
    const TASK_STATUS_DONE = 2;
    public static $task_status = [
        self::TASK_STATUS_TODO => 'contract_remind_task_status.todo',
        self::TASK_STATUS_DONE => 'contract_remind_task_status.done',
    ];
    const VALIDATE_TASK_STATUS = self::TASK_STATUS_TODO . ',' . self::TASK_STATUS_DONE;

    /*** 标准合同 start ***/
    // 合同分类是否在标准合同中启用 0-未启用; 1-已启用
    const IS_NO_ENABLED = 0;
    const IS_ENABLED = 1;

    /*** 标准合同 end ***/


    //合同归档 合同类型 1 其它合同2 租房合同 3 gpmd 平台合同
    const CONTRACT_TYPE_OTHER = 1;
    const CONTRACT_TYPE_STORING = 2;
    const CONTRACT_TYPE_PLATFORM = 3;
    public static $contract_type_items = [
        self::CONTRACT_TYPE_OTHER => 'contract_type_other',
        self::CONTRACT_TYPE_STORING => 'contract_type_storing',
        self::CONTRACT_TYPE_PLATFORM => 'contract_type_platform',
    ];

    /*** 租房合同归档可编辑字段 start ***/
    public static $rent_archive_contract_edit_fields = [
        'base_info' => [
            'id',
            'contract_id',
            'contract_name',
            'store_cate',
            'store_name',
            'store_id',
            'provinces',
            'lon_lat',
            'store_addr',
            'warehouse_id',
            'land_type',
            'land_type_content',
            'leaser_type',
            'leaser_type_content',
            'rent_due_date',
            'notice_renewal_days',
            'renovation_days',
            'hourse_owner_addr',
            'house_owner_name',
            'contract_signer_name',
            'contract_remarks',
            'house_contract_area',
            'house_actual_area',
            'contract_begin',
            'contract_end',
            'contract_effect_date',
            'is_rent_free',
            'rent_free_time',
            'contract_total_amount',
            'contract_total_amount_contain_wht',
            'deposit_amount',
            'modify_desc',
        ],

        'bank_collection' => '*',

        'amount_detail' => [
            'id',
            'contract_store_renting_id',
            'cost_start_date',
            'cost_end_date',
            'amount_no_tax',
            'vat_rate',
            'amount_vat',
            'amount_has_tax',
            'wht_category',
            'wht_rate',
            'amount_wht',
        ],

        'area_info' => [
            'id',
            'contract_store_renting_id',
            'start_time',
            'end_time',
            'area_service_amount_no_tax',
            'area_vat_rate',
            'area_service_amount_vat',
            'area_wht_category',
            'area_wht_rate',
            'area_amount_wht',
        ],

        'attachment_list' => [
            'oss_bucket_key',
            'oss_bucket_type',
            'sub_type',
            'bucket_name',
            'object_key',
            'file_name',
        ]
    ];

    /*** 租房合同归档可编辑字段 end ***/

    //租房合同是否请款
    const IS_PAYMENT_NO = 2;
    const IS_PAYMENT_YES = 1;
    //作废操作-款项退回方式
    const INVALID_REFUND_METHOD_BANK = 1;
    const INVALID_REFUND_METHOD_CHEQUE = 2;
    public static $invalid_refund_method_list = [
        self::INVALID_REFUND_METHOD_BANK => 'invalid_refund_method_bank',
        self::INVALID_REFUND_METHOD_CHEQUE => 'invalid_refund_method_cheque',
    ];
    const VALIDATE_INVALID_REFUND_METHOD = self::INVALID_REFUND_METHOD_BANK . ',' . self::INVALID_REFUND_METHOD_CHEQUE;

    //当前合同正在进行或完成哪种审批
    const CONTRACT_AUDIT_TYPE_APPLY = 1; //合同申请审批
    const CONTRACT_AUDIT_TYPE_INVALID = 2; //合同作废审批
    const CONTRACT_AUDIT_TYPE_TERMINAL = 3; //合同终止审批

    //作废操作选项
    const INVALID_REASON_TYPE_RELIEVE = 1;
    const INVALID_REASON_TYPE_RE_SIGN = 2;
    const INVALID_REASON_TYPE_OTHER = 3;
    public static $invalid_reason_type_list = [
        self::INVALID_REASON_TYPE_RELIEVE => 'invalid_reason_type_relieve',
        self::INVALID_REASON_TYPE_RE_SIGN => 'invalid_reason_type_re_sign',
        self::INVALID_REASON_TYPE_OTHER => 'invalid_reason_type_other',
    ];
    const VALIDATE_INVALID_REASON_TYPE = self::INVALID_REASON_TYPE_RELIEVE . ',' . self::INVALID_REASON_TYPE_RE_SIGN . ',' . self::INVALID_REASON_TYPE_OTHER;

    //合同类型 contract表contract_type字段
    const CONTRACT_TYPE_RENTING = 'store_renting_contract';

    //签约类型 1新合同2 续签合同
    const   RENEWAL_CONTRACT_TYPE_1 = '1';
    const   RENEWAL_CONTRACT_TYPE_2 = '2';

    //是否被续签 1 未续签 2 已续签
    const  IS_RENEWAL_1 = 1;
    const  IS_RENEWAL_2 = 2;

    //是否允许续签 1 允许2 不允许
    const  IS_SHOW_RENEWAL = 1;
    const IS_NO_SHOW_RENEWAL = 2;

    public static $contract_renewal_type = [
        self::RENEWAL_CONTRACT_TYPE_1 => 'contract_renewal_type.1',
        self::RENEWAL_CONTRACT_TYPE_2 => 'contract_renewal_type.2',
    ];

    //gpmd 合同 平台客户
    const PLAT_FORM_CUSTOMER_1 = 1;
    const PLAT_FORM_CUSTOMER_2 = 2;
    const PLAT_FORM_CUSTOMER_3 = 3;
    const PLAT_FORM_CUSTOMER_99 = 99;
    public static $plat_form_customer = [
        self::PLAT_FORM_CUSTOMER_1  => 'Tik Tok',
        self::PLAT_FORM_CUSTOMER_2  => 'Shopee',
        self::PLAT_FORM_CUSTOMER_3  => 'Lazada',
        self::PLAT_FORM_CUSTOMER_99 => 'Other'

    ];

    const PLATFORM_CONTRACT_TYPE_1 = 1; //1主合同
    const PLATFORM_CONTRACT_TYPE_2 = 2;// 补充协议

    public static $platform_contract_type = [
        self::PLAT_FORM_CUSTOMER_1 => 'plat_form_contract_type_1',
        self::PLAT_FORM_CUSTOMER_2 => 'plat_form_contract_type_2'
    ];


    //pmd 合同线上化

    // 合同存储类型 1纸质合同2电子合同
    const  CONTRACT_STORAGE_TYPE_1 = 1;
    const  CONTRACT_STORAGE_TYPE_2 = 2;
    public static $contract_storage_type = [
        self::CONTRACT_STORAGE_TYPE_1 => 'contract_storage_type_1',
        self::CONTRACT_STORAGE_TYPE_2 => 'contract_storage_type_2'
    ];

    // 子文件状态 1 使用中2结束使用3未开始使用
    const SUB_FILE_STATUS_1 = 1;
    const SUB_FILE_STATUS_2 = 2;
    const SUB_FILE_STATUS_3 = 3;
    public static $contract_sub_file_status = [
        self::SUB_FILE_STATUS_1 => 'contract_sub_file_status_1',
        self::SUB_FILE_STATUS_2 => 'contract_sub_file_status_2',
        self::SUB_FILE_STATUS_3 => 'contract_sub_file_status_3',
    ];

    //合同签约状态 1未发起2待签约3已签约-线上4已签约线下 5签字待复核 6待重新签约 7 作废-不签约;8-待BD复核签字;9-超时未签约
    const CONTRACT_SIGN_STATUS_1 = 1;
    const CONTRACT_SIGN_STATUS_2 = 2;
    const CONTRACT_SIGN_STATUS_3 = 3;
    const CONTRACT_SIGN_STATUS_4 = 4;
    const CONTRACT_SIGN_STATUS_5 = 5;
    const CONTRACT_SIGN_STATUS_6 = 6;
    const CONTRACT_SIGN_STATUS_7 = 7;
    const CONTRACT_SIGN_STATUS_8 = 8;
    const CONTRACT_SIGN_STATUS_9 = 9;
    public static $contract_sign_status = [
        self::CONTRACT_SIGN_STATUS_1 => 'contract_sign_status_1',
        self::CONTRACT_SIGN_STATUS_2 => 'contract_sign_status_2',
        self::CONTRACT_SIGN_STATUS_3 => 'contract_sign_status_3',
        self::CONTRACT_SIGN_STATUS_4 => 'contract_sign_status_4',
        self::CONTRACT_SIGN_STATUS_5 => 'contract_sign_status_5',
        self::CONTRACT_SIGN_STATUS_6 => 'contract_sign_status_6',
        self::CONTRACT_SIGN_STATUS_7 => 'contract_sign_status_7',
        self::CONTRACT_SIGN_STATUS_8 => 'contract_sign_status_8',
        self::CONTRACT_SIGN_STATUS_9 => 'contract_sign_status_9',
    ];

    //电子合同状态1未发起 2 重新提交3已发起审批
    const CONTRACT_ELECTRONIC_STATUS_1 = 1;
    const CONTRACT_ELECTRONIC_STATUS_2 = 2;
    const CONTRACT_ELECTRONIC_STATUS_3 = 3;
    public static $contract_electronic_status = [
        self::CONTRACT_ELECTRONIC_STATUS_1 => 'contract_electronic_status_1',
        self::CONTRACT_ELECTRONIC_STATUS_3 => 'contract_electronic_status_3'
    ];

    //合同状态 0-默认; 1草稿2完成3商务审核中4待修改
    const CONTRACT_STATUS_0 = 0;
    const CONTRACT_STATUS_1 = 1;
    const CONTRACT_STATUS_2 = 2;
    const CONTRACT_STATUS_3 = 3;
    const CONTRACT_STATUS_4 = 4;
    public static $contract_status = [
        self::CONTRACT_STATUS_1 => 'contract_status_1',
        self::CONTRACT_STATUS_2 => 'contract_status_2',
        self::CONTRACT_STATUS_3 => 'contract_status_3',
        self::CONTRACT_STATUS_4 => 'contract_status_4',
    ];


    //模版状态 1未开始使用2使用中3结束使用
    const CONTRACT_TEMPLATE_STATE_1 = 1;
    const CONTRACT_TEMPLATE_STATE_2 = 2;
    const CONTRACT_TEMPLATE_STATE_3 = 3;
    public static $contract_template_state = [
        self::CONTRACT_TEMPLATE_STATE_1 => 'contract_template_state_1',
        self::CONTRACT_TEMPLATE_STATE_2 => 'contract_template_state_2',
        self::CONTRACT_TEMPLATE_STATE_3 => 'contract_template_state_3'
    ];

    //是否开启商务审核 0否 1 是
    const  BUSINESS_REVIEW_NO = 0;
    const  BUSINESS_REVIEW_YES = 1;
    public static $contract_business_review = [
        self::BUSINESS_REVIEW_NO  => 'contract_business_review_no',
        self::BUSINESS_REVIEW_YES => 'contract_business_review_yes'
    ];

    //合同发起方 1 集团发起 2客服发起
    const  CONTRACT_INITIATOR_TYPE_1 = 1;
    const  CONTRACT_INITIATOR_TYPE_2 = 2;
    public static $contract_initiator_type = [
        self::CONTRACT_INITIATOR_TYPE_1 => 'contract_initiator_type_1',
        self::CONTRACT_INITIATOR_TYPE_2 => 'contract_initiator_type_2'
    ];

    //电子合同内容填写完成 1 未完成 2 已完成
    const CONTRACT_IS_NO_COMPLETED = 1;
    const CONTRACT_IS_COMPLETED = 2;


    //是否集团间合同
    const IS_GROUP_CONTRACT_YES = 1;
    const IS_GROUP_CONTRACT_NO = 0;
    public static $is_group_contract_items = [
        self::IS_GROUP_CONTRACT_YES => 'is_group_contract_yes',
        self::IS_GROUP_CONTRACT_NO => 'is_group_contract_no'
    ];

    //是否供应商
    const IS_VENDOR_YES = 1;
    const IS_VENDOR_NO = 0;
    public static $is_vendor_items = [
        self::IS_VENDOR_YES => 'is_vendor_yes',
        self::IS_VENDOR_NO => 'is_vendor_no'
    ];

    //月份格式
    const JANUARY = 1;
    const FEBRUARY = 2;
    const MARCH = 3;
    const APRIL = 4;
    const MAY = 5;
    const JUNE = 6;
    const JULY = 7;
    const AUGUST = 8;
    const SEPTEMBER = 9;
    const OCTOBER = 10;
    const NOVEMBER = 11;
    const DECEMBER = 12;
    public static $month_en_arr = [
        self::JANUARY   => 'January',
        self::FEBRUARY  => 'February',
        self::MARCH     => 'March',
        self::APRIL     => 'April',
        self::MAY       => 'May',
        self::JUNE      => 'June',
        self::JULY      => 'July',
        self::AUGUST    => 'August',
        self::SEPTEMBER => 'September',
        self::OCTOBER   => 'October',
        self::NOVEMBER  => 'November',
        self::DECEMBER  => 'December',
    ];
    public static $month_th_arr = [
        self::JANUARY   => 'มกราคม',
        self::FEBRUARY  => 'กุมภาพันธ์',
        self::MARCH     => 'มีนาคม',
        self::APRIL     => 'เมษายน',
        self::MAY       => 'พฤษภาคม',
        self::JUNE      => 'มิถุนายน',
        self::JULY      => 'กรกฎาคม',
        self::AUGUST    => 'สิงหาคม',
        self::SEPTEMBER => 'กันยายน',
        self::OCTOBER   => 'ตุลาคม',
        self::NOVEMBER  => 'พฤศจิกายน',
        self::DECEMBER  => 'ธันวาคม',
    ];

    /*** 仓库管理相关枚举 Start ***/
    // 仓库状态 -1-变更审批中; 0-空置; 1-使用中; 2-已停用
    const WAREHOUSE_STATUS_APPROVALING = -1; // 变更审批中
    const WAREHOUSE_STATUS_VACANT = 0; // 空置
    const WAREHOUSE_STATUS_USING = 1; // 使用中
    const WAREHOUSE_STATUS_DEACTIVATED = 2;// 已停用
    public static $warehouse_status_enums = [
        self::WAREHOUSE_STATUS_APPROVALING => 'warehouse_status_' . self::WAREHOUSE_STATUS_APPROVALING,
        self::WAREHOUSE_STATUS_VACANT => 'warehouse_status_' . self::WAREHOUSE_STATUS_VACANT,
        self::WAREHOUSE_STATUS_USING => 'warehouse_status_' . self::WAREHOUSE_STATUS_USING,
        self::WAREHOUSE_STATUS_DEACTIVATED => 'warehouse_status_' . self::WAREHOUSE_STATUS_DEACTIVATED,
    ];

    public static $warehouse_status_chars = self::WAREHOUSE_STATUS_VACANT . ',' . self::WAREHOUSE_STATUS_USING . ',' . self::WAREHOUSE_STATUS_DEACTIVATED;

    // 网点标识
    const WAREHOUSE_STORE_FLAG_MAIN = 1; // 主网点
    const WAREHOUSE_STORE_FLAG_SHARE = 2; // 共用网点

    // 网点使用状态
    const WAREHOUSE_STORE_USE_STATUS_ING = 1; // 使用中
    const WAREHOUSE_STORE_USE_STATUS_END = 2; // 使用结束

    // 仓库信息变更维度和相关属性
    const WAREHOUSE_BASE_INFO_KEY = 'warehouse_base_info';
    const WAREHOUSE_MAIN_STORE_INFO_KEY = 'warehouse_main_store_info';
    const WAREHOUSE_SHARE_STORE_INFO_KEY = 'warehouse_share_store_info';

    // 仓库变更OA审批单号前缀
    const WAREHOUSE_STATUS_CHANGE_AUDIT_NO_PREFIX = 'WHStatus';
    const WAREHOUSE_STORE_CHANGE_AUDIT_NO_PREFIX = 'WHBranch';

    // 仓库数据变更类型
    const WAREHOUSE_STATUS_CHANGE_DATA_TYPE = 1;
    const WAREHOUSE_STORE_CHANGE_DATA_TYPE = 2;
    const WAREHOUSE_EDIT_INFO_DATA_TYPE = 3;

    // 仓库数据变更来源
    const WAREHOUSE_DATA_CHANGE_SOURCE_APPLY = 1;
    const WAREHOUSE_DATA_CHANGE_SOURCE_CONTRACT = 2;
    const WAREHOUSE_DATA_CHANGE_SOURCE_EDIT_INFO = 3;

    // 仓库数据变更生效状态
    const WAREHOUSE_DATA_CHANGE_TO_BE_EFFECTIVE = 0;// 待生效
    const WAREHOUSE_DATA_CHANGE_EFFECTIVED = 1;// 已生效
    const WAREHOUSE_DATA_CHANGE_NO_EFFECTIVE = 2;// 未生效


    // 仓库信息编辑权限等级: 不同等级 可编辑字段范围不同
    const WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_1 = 1; // 特殊管理员
    const WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_2 = 2;
    const WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_3 = 3;
    public static $warehouse_info_can_edit_permission_level_fields_map = [
        self::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_1 => ['warehouse_name', 'province_code', 'city_code', 'district_code', 'warehouse_latitude', 'warehouse_longitude', 'real_area'],
        self::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_2 => ['warehouse_name', 'province_code', 'city_code', 'district_code', 'warehouse_latitude', 'warehouse_longitude', 'real_area'],
        self::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_3 => ['warehouse_name', 'real_area'],
    ];

    /*** 仓库管理相关枚举 End ***/

    /**
     * 加盟商类型
     * 1 个人 2企业
     */
    const FRANCHISEE_TYPE_1 = 1;
    const FRANCHISEE_TYPE_2 = 2;
    public static $franchisee_type_enums = [
        self::FRANCHISEE_TYPE_1 => 'franchisee_type_1',
        self::FRANCHISEE_TYPE_2 => 'franchisee_type_2'
    ];

    /**
     * 合同签约类型 1已签约-线下2已签约-线上
     */
    const CONTRACT_SIGN_TYPE_1 = 1;
    const CONTRACT_SIGN_TYPE_2 = 2;
    public static $contract_sign_type = [
        self::CONTRACT_SIGN_TYPE_1 => 'contract_sign_type_1',
        self::CONTRACT_SIGN_TYPE_2 => 'contract_sign_type_2'
    ];

    /**
     * 电子合同部门枚举
     */
    const FLASH_HOME_OPERATION = 1;

    // 电子合同: 是否是主合同组合补充协议
    const IS_COMBINATION_SUPPLEMENTAL_AGREEMENT_DEFAULT = 0;
    const IS_COMBINATION_SUPPLEMENTAL_AGREEMENT_YES = 1;
    const IS_COMBINATION_SUPPLEMENTAL_AGREEMENT_NO = 2;

    //电子合同导出-最大限制数
    const CONTRACT_ELECTRONIC_EXPORT_MAX = 100;

    /**
     * 签约客户类型枚举
     * 1 KAr客户 2 flash printer客户 3 FH客户 4 API客户 100-其他
     */
    const SIGN_CUSTOMER_TYPE_1 = 1;
    const SIGN_CUSTOMER_TYPE_2 = 2;
    const SIGN_CUSTOMER_TYPE_3 = 3;
    const SIGN_CUSTOMER_TYPE_4 = 4;
    const SIGN_CUSTOMER_TYPE_100 = 100;
    public static $sign_customer_type = [
        self::SIGN_CUSTOMER_TYPE_3 => 'sign_customer_type_3',
        self::SIGN_CUSTOMER_TYPE_100 => 'sign_customer_type_100',
    ];

    // 签约顺序
    const SIGN_ORDER_TYPE_PARTY_A = 1;// 甲方先签
    const SIGN_ORDER_TYPE_PARTY_B = 2; // 乙方先签
    public static $sign_order_type = [
        self::SIGN_ORDER_TYPE_PARTY_A => 'sign_order_type_' . self::SIGN_ORDER_TYPE_PARTY_A,
        self::SIGN_ORDER_TYPE_PARTY_B => 'sign_order_type_' . self::SIGN_ORDER_TYPE_PARTY_B,
    ];


    /**
     * 电子合同合同类型
     */
    const ELECTRONIC_CONTRACT_TYPE_11 = 11;// 补充协议

    /**
     * 电子合同双语分隔符 Bilingual separator
     */
    const ELECTRONIC_CONTRACT_BILINGUAL_SEPARATOR = '/';

    /**
     * 电子合同邮件发送场景
     */
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN = 1; // 待签约 - 乙方BD发起签约/重新发起签约时
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_RESIGN = 2; // 待重新签约 - 乙方审核签字驳回时
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_POA_SIGN = 3; // 待POA签约 - 甲方转POA签字时
    const ELECTRONIC_CONTRACT_EMIAL_SCENC_POA_SIGNED = 4; // 待甲方商务继续签约 - 甲方POA全部完成签字时
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_APPROVALED = 5; // 待甲方商务下载 - 乙方审核签字完成时
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_REVIEW = 6; // 待乙方BD复核 - 甲方商务提交签约时
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_TIMEOUT = 7; // 待乙方重新发起签约 - 甲方超时未签约
    const ELECTRONIC_CONTRACT_EMIAL_SCENCE_OTP_CODE = 100; // otp验证码

    /**
     * BD复核签字状态
     */
    const ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_PASS = 1; // 复核通过
    const ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_RETURN = 2; // 复核退回
    const ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_COMPLETED = 3; // 复核完成

    /**
     * 甲方签约角色
     */
    const ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS = 1; // 商务
    const ELECTRONIC_CONTRACT_SIGN_ROLE_POA = 2; // POA

    /**
     * 甲方签约otp场景
     */
    const ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN = 1;// 甲方商务签字 -> otp 身份认证
    const ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN = 2;// 甲方商务签字 -> otp 上传图片验证
    const ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN = 10;// 甲方POA签字 -> otp 身份认证
    const ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN = 11;// 甲方POA签字 -> otp 上传签字验证
    const ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN = 20;// 甲方商务下载签字版合同 -> otp 身份认证

    // otp code 长度
    const ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_LENGTH = 6;

    // otp code 有效时长: 单位 分钟
    const ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_VALIDITY_PERIOD = 5;

    // 发送频率: X秒内不可重复发送
    const ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_SEND_FREQUENCY = 60;

    // token类型
    const ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_TYPE = 2;

    // 甲方签名页, 允许编辑的字段
    const ELECTRONIC_CONTRACT_FIELD_RELATED_FILE = 'related_file_list';
    const ELECTRONIC_CONTRACT_FIELD_COMPANY_SIGN_IMG = 'custom_company_sign_img';
    const ELECTRONIC_CONTRACT_FIELD_POA_SIGN_IMG = 'sign_img';
    const ELECTRONIC_CONTRACT_FIELD_POA_JOB_TITLE = 'sign_job_title';
    const ELECTRONIC_CONTRACT_FIELD_RETURN_PERSON_SIGNATURE_IMG = 'return_person_signature_img';
    const ELECTRONIC_CONTRACT_CLIENT_CAN_EDIT_FIELDS = [
        self::ELECTRONIC_CONTRACT_FIELD_RELATED_FILE,
        self::ELECTRONIC_CONTRACT_FIELD_COMPANY_SIGN_IMG,
        self::ELECTRONIC_CONTRACT_FIELD_POA_SIGN_IMG,
        self::ELECTRONIC_CONTRACT_FIELD_POA_JOB_TITLE,
        self::ELECTRONIC_CONTRACT_FIELD_RETURN_PERSON_SIGNATURE_IMG,
    ];

    // 零售合同表单数据, 删除的字段列表
    public static $retail_delete_fields = [
        'contract_no',
        'original_no',
        'authorized_name',
        'authorized_job_title',
        'authorized_sign_img',
        'current_date',
        'flash_bd_name',
        'flash_bd_job_title',
        'customer_segment_list',
        'service_list',
        'cod_free_type_list',
        'billing_cycle_list',
        'credit_terms_list',
        'company_business_entity_type_list',
        'service_price'
    ];

    // 电子合同表单数据, 空值需重置为NA的字段列表
    const ELECTRONIC_CONTRACT_FORM_DATA_EMPTY_VALUE_DEFAULT_CHAR = 'NA';
    public static $electronic_contract_form_data_reset_na_fields = [
        'string' => [
            // 客户基础信息-公司名称
            'custom_company_name_en',
            'custom_company_name_zh',
            'custom_company_name_th',

            // 客户基础信息-公司法人
            'custom_legal_name_en',
            'custom_legal_name_zh',
            'custom_legal_name_th',

            // 客户基础信息-联系人
            'custom_contacts_name_en',
            'custom_contacts_name_zh',
            'custom_contacts_name_th',

            // 客户基础信息-地址
            'custom_address_en',
            'custom_address_zh',
            'custom_address_th',

            // Flash基础信息-授权人
            'flash_authorized_person_1',
            'flash_authorized_person_2',

            // Flash基础信息-联系人
            'flash_contact_name_en',
            'flash_contact_name_zh',
            'flash_contact_name_th',

            // Flash基础信息-销售代表
            'flash_sale_name_en',
            'flash_sale_name_zh',
            'flash_sale_name_th',

            // 补充协议-甲方公司名称
            'agreement_company_name_en',
            'agreement_company_name_zh',
            'agreement_company_name_th',

            // 补充协议-甲方公司地址
            'agreement_company_address_en',
            'agreement_company_address_zh',
            'agreement_company_address_th',
        ],

        'array' => [
            // 客户基础信息-授权人
            'custom_authorized_person_info' => [
                'custom_authorized_person_en',
                'custom_authorized_person_zh',
                'custom_authorized_person_th',
            ]
        ]
    ];


}
