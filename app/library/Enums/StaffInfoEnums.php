<?php
namespace App\Library\Enums;


final class StaffInfoEnums
{
    //员工状态
    const STAFF_STATE_IN = 1; //在职
    const STAFF_STATE_LEAVE = 2;  //离职
    const STAFF_STATE_STOP = 3; //停职
    const STAFF_STAFF_WAIT_LEAVE = 999;//待离职
    public static $staff_state = [
        self::STAFF_STATE_IN => 'staff_state.1',
        self::STAFF_STATE_LEAVE => 'staff_state.2',
        self::STAFF_STATE_STOP => 'staff_state.3',
        self::STAFF_STAFF_WAIT_LEAVE => 'staff_state.999',
    ];
    const STAFF_STATE_VALIDATE = self::STAFF_STATE_IN . ',' . self::STAFF_STATE_LEAVE . ',' . self::STAFF_STATE_STOP . ',' . self::STAFF_STAFF_WAIT_LEAVE;

    //员工待离职状态
    const STAFF_WAIT_LEAVE_STATE_NO = 0;//非待离职
    const STAFF_WAIT_LEAVE_STATE_YES = 1;//待离职


    const FORMAL_NOT_IN = 0; // 非编制
    const FORMAL_IN = 1;//编制
    const FORMAL_TRAINEE = 4;//实习生
    const IS_SUB_STAFF_NO = 0;//不是子账号

    const WORKING_COUNTRY_TH = 1;   //泰国
    const WORKING_COUNTRY_ZH = 2;   //中国
    const WORKING_COUNTRY_MY = 3;   //马来西亚
    const WORKING_COUNTRY_PH = 4;   //菲律宾
    const WORKING_COUNTRY_VN = 5;   //越南
    const WORKING_COUNTRY_LA = 6;   //老挝
    const WORKING_COUNTRY_ID = 7;   //印尼
    const WORKING_COUNTRY_SG = 8;   //新加坡
    const WORKING_COUNTRY_OTHER = 99;  //其他
    public static $working_country = [
        self::WORKING_COUNTRY_TH => 'working_country.th',
        self::WORKING_COUNTRY_ZH => 'working_country.zh',
        self::WORKING_COUNTRY_MY => 'working_country.my',
        self::WORKING_COUNTRY_PH => 'working_country.ph',
        self::WORKING_COUNTRY_VN => 'working_country.vn',
        self::WORKING_COUNTRY_LA => 'working_country.la',
        self::WORKING_COUNTRY_ID => 'working_country.id',
        self::WORKING_COUNTRY_SG => 'working_country.sg',
        self::WORKING_COUNTRY_OTHER => 'working_country.other',
    ];
    const WORKING_COUNTRY_VALIDATE = self::WORKING_COUNTRY_TH . ',' . self::WORKING_COUNTRY_ZH . ',' . self::WORKING_COUNTRY_MY . ',' . self::WORKING_COUNTRY_PH . ',' .
    self::WORKING_COUNTRY_VN . ',' . self::WORKING_COUNTRY_LA . ',' . self::WORKING_COUNTRY_ID . ',' . self::WORKING_COUNTRY_SG . ',' . self::WORKING_COUNTRY_OTHER;

    // 员工职级
    const JOB_TITLE_GRADE_V2_F15 = 15;
    public static $config_job_title_grade_v2 = [
        0 => 'F0',
        12 => 'F12',
        13 => 'F13',
        14 => 'F14',
        15 => 'F15',
        16 => 'F16',
        17 => 'F17',
        18 => 'F18',
        19 => 'F19',
        20 => 'F20',
        21 => 'F21',
        22 => 'F22',
        23 => 'F23',
        24 => 'F24'
    ];

    //学历
    const EDUCATION_JUNIOR_HIGH_BELOW = 1; //初中及以下
    const EDUCATION_HIGH_SCHOOL = 2; //高中
    const EDUCATION_VOCATIONAL = 3; //专科
    const EDUCATION_BACHELOR = 4; //本科
    const EDUCATION_MASTER_DEGREE = 5; //硕士
    const EDUCATION_DOCTOR = 6; //博士
    public static $education = [
        self::EDUCATION_JUNIOR_HIGH_BELOW => 'staff_education_junior_high_below',
        self::EDUCATION_HIGH_SCHOOL => 'staff_education_high_school',
        self::EDUCATION_VOCATIONAL => 'staff_education_vocational',
        self::EDUCATION_BACHELOR => 'staff_education_bachelor',
        self::EDUCATION_MASTER_DEGREE => 'staff_education_master_degree',
        self::EDUCATION_DOCTOR => 'staff_education_doctor',
    ];

    //员工雇佣类型
    const HIRE_TYPE_PERMANENT_EMPLOYEE = 1; //正式员工
    const HIRE_TYPE_MONTHLY = 2; //月薪制特殊合同工
    const HIRE_TYPE_DAILY = 3; //日薪制特殊合同工
    const HIRE_TYPE_HOURLY = 4; //时薪制特殊合同工
    const HIRE_TYPE_INTERN = 5;
    const HIRE_TYPE_PERSONAL_AGENCY = 13; //个人代理
    const HIRE_TYPE_PART_TIME_AGENT = 14;//兼职个人代理
    public static $hire_type = [
        self::HIRE_TYPE_PERMANENT_EMPLOYEE => 'staff_hire_type_permanent_employee',
        self::HIRE_TYPE_MONTHLY => 'staff_hire_type_monthly',
        self::HIRE_TYPE_DAILY => 'staff_hire_type_daily',
        self::HIRE_TYPE_HOURLY => 'staff_hire_type_hourly',
        self::HIRE_TYPE_INTERN => 'staff_hire_type_intern',
    ];

    //试用期
    const PROBATION_ON = 1; //试用期中
    const PROBATION_PASSED = 2; //已通过
    const PROBATION_FAILED = 3; //未通过
    const PROBATION_POSITIVE = 4; //已转正
    public static $probation_status = [
        self::PROBATION_ON => 'staff_probation_on',
        self::PROBATION_PASSED => 'staff_probation_passed',
        self::PROBATION_FAILED => 'staff_probation_failed',
        self::PROBATION_POSITIVE => 'staff_probation_positive',
    ];

    //性别
    const STAFF_SEX_UNKNOWN = 0;
    const STAFF_SEX_MALE = 1;
    const STAFF_SEX_FEMALE = 2;
    public static $staff_sex = [
        self::STAFF_SEX_UNKNOWN => 'staff_sex.0',
        self::STAFF_SEX_MALE => 'staff_sex.1',
        self::STAFF_SEX_FEMALE => 'staff_sex.2'
    ];

    // 犯罪记录类型
    const RECORD_TYPE_1 = 1;
    const RECORD_TYPE_2 = 2;
    const RECORD_TYPE_3 = 3;
    const RECORD_TYPE_4 = 4;
    const RECORD_TYPE_5 = 5;
    const RECORD_TYPE_6 = 6;
    const RECORD_TYPE_7 = 7;
    const RECORD_TYPE_DESC = [
        self::RECORD_TYPE_1 => 'criminal_record_1',
        self::RECORD_TYPE_2 => 'criminal_record_2',
        self::RECORD_TYPE_3 => 'criminal_record_3',
        self::RECORD_TYPE_4 => 'criminal_record_4',
        self::RECORD_TYPE_5 => 'criminal_record_5',
        self::RECORD_TYPE_6 => 'criminal_record_6',
        self::RECORD_TYPE_7 => 'criminal_record_7',
    ];

    //司龄
    public static $average_tenure = [
        1 => '<0.5',
        2 => '[0.5,1)',
        3 => '[1,2)',
        4 => '[2,3)',
        5 => '≧3',
    ];

    //搜索项目
    public static $search_probation_status =[
        self::PROBATION_ON => 'staff_probation_on'
    ];

    //员工属性表 类型为持卡人信息
    const HR_STAFF_ITEMS_BANK_NO_NAME = 'BANK_NO_NAME';

    // 员工工作所在国家ITEM KEY
    const HR_STAFF_ITEMS_WORKING_COUNTRY = 'WORKING_COUNTRY';

    //员工离职类型
    const LEAVE_SOURCE_OTHER = -1; //其他
    const LEAVE_SOURCE_DEFAULT = 0; //默认值
    const LEAVE_SOURCE_1 = 1;  //新增 (应该不用了)
    const LEAVE_SOURCE_2 = 2;   //hris (应该不用了)
    const LEAVE_SOURCE_NO_ATTENDANCE = 3; //三天未出勤停职员工  老挝:四天未出勤停职员工
    const LEAVE_SOURCE_NO_PUBLIC_FUNDS = 4; //未缴纳公款
    const LEAVE_SOURCE_BY_APPLY = 5; //BY申请离职
    const LEAVE_SOURCE_BATCH_IMPORT = 6; //批量导入离职
    const LEAVE_SOURCE_CRIME = 7; //犯罪记录
    const LEAVE_SOURCE_PROBATION_NO_PASS = 8; //试用期未通过
    const LEAVE_SOURCE_MANAGE_UPDATE = 9; //员工管理修改
    const LEAVE_SOURCE_GLOBAL = 10; //全局离职
    const LEAVE_SOURCE_CONTRACT_EXPIRE = 11;//合同到期
    const LEAVE_SOURCE_TRANSFER_PERSONAL_AGENCY = 12;//转个人代理
    const LEAVE_SOURCE_TERMINATION_PERSONAL_AGENCY = 13;//解约个人代理
    public static $leave_source = [
        self::LEAVE_SOURCE_OTHER => 'leave_source_-1',
        self::LEAVE_SOURCE_DEFAULT => 'leave_source_0',
        self::LEAVE_SOURCE_1 => 'leave_source_1',
        self::LEAVE_SOURCE_2 => 'leave_source_2',
        self::LEAVE_SOURCE_NO_ATTENDANCE => 'leave_source_3',
        self::LEAVE_SOURCE_NO_PUBLIC_FUNDS => 'leave_source_4',
        self::LEAVE_SOURCE_BY_APPLY => 'leave_source_5',
        self::LEAVE_SOURCE_BATCH_IMPORT => 'leave_source_6',
        self::LEAVE_SOURCE_CRIME => 'leave_source_7',
        self::LEAVE_SOURCE_PROBATION_NO_PASS => 'leave_source_8',
        self::LEAVE_SOURCE_MANAGE_UPDATE => 'leave_source_9',
        self::LEAVE_SOURCE_GLOBAL => 'leave_source_10',
        self::LEAVE_SOURCE_CONTRACT_EXPIRE => 'leave_source_11',
        self::LEAVE_SOURCE_TRANSFER_PERSONAL_AGENCY => 'leave_source_12',
        self::LEAVE_SOURCE_TERMINATION_PERSONAL_AGENCY => 'leave_source_13',

    ];
    //老挝特殊翻译key
    const TRANSLATE_LEAVE_SOURCE_NO_ATTENDANCE_LA = 'la_leave_source_3'; //老挝:四天未出勤停职员工

    //超级管理员账号
    const SUPER_ADMIN_STAFF_ID = 10000;
    const SUPER_ADMIN_STAFF_NAME = 'SuperAdmin';

    //马来总部所在州
    const HEAD_OFFICE_PROVINCE_CODE = 'MY06';
    //法定节假日对应类型=通用
    const TYPE_DEFAULT = 0;
    //法定节假日对应类型=5天班的
    const TYPE_WEEK_WORKING_DAY_5 = 2;

    //职位状态
    const HR_JOB_STATUS_OPEN = 1;//开启

    // 员工类型
    const STAFF_TYPE_INNER = 1; // 内部员工
    const STAFF_TYPE_OUTSOURCING = 2; // 外协
    const STAFF_TYPE_PERSONAL_AGENT = 3; // 个人代理


}
