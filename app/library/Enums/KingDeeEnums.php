<?php
/**
 * Created by PhpStorm.
 * Date: 2023/09/01
 * Time: 10:58
 */

namespace App\Library\Enums;

/**
 * 金碟枚举
 * Class enums
 */
final class KingDeeEnums
{
    //同步金碟状态 1待同步 2 成功 3失败

    const IS_SEND_KING_DEE_1 = 1;
    const IS_SEND_KING_DEE_2 = 2;
    const IS_SEND_KING_DEE_3 = 3;

    //金蝶BU公司ID配置key
    const KINGDEE_BU_SETTING_CODE = 'kingdee_company_ids';
    //金蝶个人供应商编码配置key
    const KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE = 'kingdee_personal_supplier_code';

    //应付（付款）是否成功同步至金蝶
    const PAY_IS_SEND_KINGDEE_NO = 0;//否
    const PAY_IS_SEND_KINGDEE_YES = 1;//是

    //应付（付款）付款类型
    const PAY_TYPE_PAYABLE = 1;//应付
    const PAY_TYPE_PAYBILL = 2;//付款

    //应付（付款）是否取消支付：0否，1是
    const IS_CANCEL_PAY_NO = 0;//否
    const IS_CANCEL_PAY_YES = 1;//是

    //结算方式
    public static $settle_type = [
        GlobalEnums::PAYMENT_METHOD_CASH => 'JSFS01_SYS',//现金
        GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER => 'JSFS04_SYS',//银行转帐
        GlobalEnums::PAYMENT_METHOD_CHECK => 'JSFS03_SYS',//支票
    ];

    //推送状态：0默认值、1成功、2失败
    const PAY_LOG_PUSH_STATUS_SUCCESS = 1;//成功
    const PAY_LOG_PUSH_STATUS_FAIL = 2;//失败

    //科目类型：1快递公司科目，2子公司科目
    const ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY = 1;
    const ACCOUNT_TYPE_SUB_COMPANY = 2;
    const TASK_SEND_DAY_NUM = 'kingdee_task_send_day_num';
    const COMPANY_PROJECT_ENUM = 'kingdee_company_project_enum';
}