<?php

namespace App\Library\Enums;

/**
 * by审批流 审批条件类型相关枚举
 * 对应数据表 workflow_branch_condition
 */
class WorkflowBranchEnums
{
    const BRANCH_CATEGORY_BELONG_DEPARTMENT = 1; //申请人所属部门
    const BRANCH_CATEGORY_JOB_TITLE = 2; //申请人职位
    const BRANCH_CATEGORY_JOB_GRADE = 3; //申请人职级
    const BRANCH_CATEGORY_SEX = 4; //申请人性别
    const BRANCH_CATEGORY_ON_JOB_STATE = 5; //申请人在职状态
    const BRANCH_CATEGORY_PROBATION = 6; //申请人是否处于试用期
    const BRANCH_CATEGORY_ROLE = 7; //申请人角色
    const BRANCH_CATEGORY_NATIONALITY = 8; //申请人国籍
    const BRANCH_CATEGORY_STORE_CATEGORY = 9; //申请人所属网点类型
    const BRANCH_CATEGORY_BELONG_STORE = 10; //申请人所属网点
    const BRANCH_CATEGORY_IS_STORE_MGR = 11; //申请人是否为网点负责人
    const BRANCH_CATEGORY_IS_PIECE_MGR = 12; //申请人是否为片区负责人
    const BRANCH_CATEGORY_IS_REGION_MGR = 13; //申请人是否为大区负责人
    const BRANCH_CATEGORY_IS_ORG_MGR = 14; //申请人是否为组织负责人
    const BRANCH_CATEGORY_HIRE_TYPE = 15;//申请人雇佣类型
    const BRANCH_CATEGORY_BELONG_REGION = 17;//申请人所属大区
    const BRANCH_CATEGORY_BELONG_PIECE = 18;//申请人所属片区
    const BRANCH_CATEGORY_POSITION_TYPE= 19;//申请人职位性质
    const BRANCH_CATEGORY_AMOUNT = 103; //薪资审批-金额
    const BRANCH_CATEGORY_CURRENCY = 104; //薪资审批-币种
    const BRANCH_CATEGORY_SALARY_IS_FIRST_LINE_JOB_TITLE = 105; //薪资审批-是否为一线职位
    const BRANCH_CATEGORY_BUSINESS_LINE_TYPE = 106; //薪资审批-业务线类型
    const BRANCH_CATEGORY_SALARY_RANGE = 107; //薪资审批-薪酬范围
    const BRANCH_CATEGORY_FORM_BELONG_DEPARTMENT = 108; //表单项所属部门
    const BRANCH_CATEGORY_LEAVE_TYPE = 109; //请假-请假类型
    const BRANCH_CATEGORY_LEAVE_START_DATE = 110; //请假-开始日期
    const BRANCH_CATEGORY_LEAVE_DAYS = 111; //请假-天数
    const BRANCH_CATEGORY_BUSINESS_TRIP_DAYS = 112; //出差-外出天数
    const BRANCH_CATEGORY_RESIGN_VEHICLE_SOURCE = 113; //离职申请-获取车辆来源
    const BRANCH_CATEGORY_OS_TYPE = 114; //外协-外协类型
    const BRANCH_CATEGORY_OS_JOB_TITLE = 115; //外协-外协职位
    const BRANCH_CATEGORY_TRIP_DAYS = 116; //黄牌出差-出差天数
    const BRANCH_CATEGORY_ASSET_BELONG_COMPANY = 121; //资产审批-所属公司
    const BRANCH_CATEGORY_REPORT_REASON = 122; //举报-违规原因
    const BRANCH_CATEGORY_REPORT_TYPE = 123; //举报-举报类型
    const BRANCH_CATEGORY_FAKE_MILEAGE_IS_STORE_MGR_ATTEND = 124; //虚假里程-网点主管否打卡
    const BRANCH_CATEGORY_FAKE_MILEAGE_IS_PIECE_MGR_ATTEND = 125; //举报-片区经理是否打卡
    const BRANCH_CATEGORY_FORM_BELONG_STORE = 126; //表单项所属网点
    const BRANCH_CATEGORY_SUBMITTER_MAX_LEVEL = 127; //申请人负责部门的最高级别
    const BRANCH_CATEGORY_QUIT_CLAIM_PAYMENT = 128; //Quitclaim支付方式
    const BRANCH_CATEGORY_REPORT_STORE_CATEGORY = 129; //举报-被举报人所属网点类型
    const BRANCH_CATEGORY_REPORT_JOB_TITLE_OF_REPORTED = 130; //举报-被举报人职位
    const BRANCH_CATEGORY_REPORT_JOB_GRADE_OF_REPORTED = 131; //举报表单-被举报人-职级
    const BRANCH_CATEGORY_REPORT_ROLES_OF_REPORTED = 132; //举报表单-被举报人-角色
    const BRANCH_CATEGORY_REPORT_IS_ORG_MGR_OF_REPORTED = 133; //举报表单-被举报人-是否为组织负责人
    const BRANCH_CATEGORY_SUSPENSION_REASON = 136; //恢复在职申请表单-停职原因
    const BRANCH_CATEGORY_HC_REASON_TYPE = 137;//HC用人原因
    const BRANCH_CATEGORY_HC_SUBMITTER_POSITION = 138; //HC申请-职位（表单填写的职位）
    const BRANCH_CATEGORY_TRANSFER_BEFORE_DEPARTMENT = 139; //表单-被转岗人转岗前所属部门
    const BRANCH_CATEGORY_TRANSFER_BEFORE_JOB_TITLE = 140; //表单-被转岗人转岗前职位
    const BRANCH_CATEGORY_TRANSFER_BEFORE_DEPARTMENT_LEVEL = 141; //表单-被转岗人转岗前所属部门级别
    const BRANCH_CATEGORY_TRANSFER_AFTER_DEPARTMENT = 142; //表单-被转岗人转岗后所属部门
    const BRANCH_CATEGORY_TRANSFER_AFTER_JOB_TITLE = 143; //表单-被转岗人转岗后职位
    const BRANCH_CATEGORY_TRANSFER_AFTER_DEPARTMENT_LEVEL = 144; //表单-被转岗人转岗后所属部门级别
    const BRANCH_CATEGORY_TRANSFER_WEATHER_REGION_CONSISTENCY = 145; //表单-转岗后所属大区与转岗前所属大区是否一致
    const BRANCH_CATEGORY_TRANSFER_WEATHER_PIECE_CONSISTENCY = 146; //表单-转岗后所属片区与转岗前所属片区是否一致
    const BRANCH_CATEGORY_TRANSFER_WEATHER_DEPARTMENT_CONSISTENCY = 147; //表单-转岗后所属部门与转岗前所属部门是否一致
    const BRANCH_CATEGORY_OT_TYPE = 148;//OT 类型
    const BRANCH_CATEGORY_OVERTIME_BUS_TYPE = 155;// 加班车类型
    const BRANCH_CATEGORY_SPECIAL_MARK = 156;// 是否有特殊标记
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_DEPARTMENT = 149; //恢复在职-申请恢复工号所属部门
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_STORE_CATEGORY = 150; //恢复在职-申请恢复工号所属网点类型
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_STORE = 151; //恢复在职-申请恢复工号所属网点
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_JOB_TITLE = 152; //恢复在职-申请恢复工号职位
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_JOB_TITLE_GRADE = 153; //恢复在职-申请恢复工号职级
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_ROLE = 154; //恢复在职-申请恢复工号角色
    const BRANCH_CATEGORY_TRANSFER_BEFORE_JOB_TITLE_GRADE = 157; //表单-被转岗人转岗前职级
    const BRANCH_CATEGORY_TRANSFER_AFTER_JOB_TITLE_GRADE = 158; //表单-被转岗人转后职级
    const BRANCH_CATEGORY_COST_COMPANY = 170; //表单-费用所属公司
    const BRANCH_CATEGORY_INTEGRATED = 168; //表单-是否接口集成的数据
    const BRANCH_CATEGORY_COST_DEPARTMENT = 169; //表单-费用所属部门
    const BRANCH_CATEGORY_COST_TYPE = 171; //表单-费用类型
    const BRANCH_CATEGORY_PAYABLE_AMOUNT = 185; //表单-应付总金额（含VAT含WHT）
    const BRANCH_CATEGORY_AMOUNT_TOTAL_ACTUALLY = 186; //表单-实付总金额（含VAT不含WHT）
    const BRANCH_CATEGORY_PENALTY = 162; //kit 处罚申诉
    const BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_HIRE_TYPE = 163; //恢复在职-申请恢复在职工号雇佣类型
    const BRANCH_CATEGORY_3DAY_STORE_TERMINATION_NUM = 164; //公司解约个人代理-3天内同一网点解约个人代理人数
    const BRANCH_CATEGORY_REEMPLOYMENT_FORM_DEPARTMENT = 159;//重新雇佣-部门
    const BRANCH_CATEGORY_REEMPLOYMENT_FORM_JOB_TITLE = 160;//重新雇佣-职位
    const BRANCH_CATEGORY_REEMPLOYMENT_FORM_JOB_TITLE_GRADE = 161;//重新雇佣-职级
    const BRANCH_CATEGORY_OT_MONTH_DURATION_PH = 165;//泰国加班 当月累计1OT - 当月ph * 8h
    const BRANCH_CATEGORY_OT_WEEK_DURATION_PERCENT = 166;//当周dco 对应类型 累计小时数 / 可申请ot 小时数（配置表）
    const BRANCH_CATEGORY_TERMINATION_PERSONAL_AGENCY_STAFF_TYPE = 167; //公司解约个人代理-员工问题解约类型
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_DEPARTMENT = 172;//暂停接单-部门
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_JOB_TITLE = 173;//暂停接单-职位
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_CATEGORY = 174;//暂停接单-网点类型
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE = 175;//暂停接单-网点
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_REASON_TYPE = 176;//暂停接单原因
    const BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_SUSPEND_NUM = 177;// 同网点停职人数
    const BRANCH_CATEGORY_TRANSFER_WEATHER_JOB_TITLE_CONSISTENCY = 178; //表单-被转岗人转岗前与转岗后职位是否一致
    const BRANCH_CATEGORY_TRANSFER_AFTER_POSITION_ID = 179; //表单-被转岗人转后职位
    const BRANCH_CATEGORY_OT_STAFF_EFFECT_RATE = 180; //加班-所在网点当天人效
    const BRANCH_CATEGORY_TRANSFER_BEFORE_POSITION_ID = 181; //表单-被转岗人转前职位
    const BRANCH_CATEGORY_OS_AT_STORE_CATEGORY = 182; //表单-工作网点对应的网点类型
    const BRANCH_CATEGORY_TRANSFER_AFTER_HIRE_TYPE = 183; //表单-被转岗人转岗前雇佣类型
    const BRANCH_CATEGORY_REPORT_IS_AUTO = 184; //举报-表单-是否为自动举报
    const BRANCH_CATEGORY_BUDGET_ACCOUNT = 187; //表单-预算科目
    const BRANCH_CATEGORY_PRE_WITHDRAWAL_AMOUNT = 188; //表单-预提金额


    const OPTION_YES = 1; //是
    const OPTION_NO = 2; //否

    const OPERATION_SINGLE = 1; //单选
    const OPERATION_MULTI = 2; //多选

    const ORDINARY_CAR = 1;     // 普通加班车
    const FD_COURIER_CAR = 2;   // FD 加班车

    const PUNISH_CATEGORY = 57; //kit处罚类型 矿工罚款
}