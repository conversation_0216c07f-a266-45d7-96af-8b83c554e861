<?php
namespace App\Library\Enums;

/**
 * 支付管理版块相关枚举定义
 * Class enums
 */
final class PayEnums
{
    // 业务侧数据是否进入了支付管理模块
    const BIZ_DATA_IS_PAY_MODULE_NO = 0; // 否, 业务侧单据在原模块的支付菜单处理
    const BIZ_DATA_IS_PAY_MODULE_YES = 1; // 是, 业务侧单据流转到了支付管理模块处理

    // 进入支付管理模块的业务单据来源
    const IS_FROM_SELF = 1; // 来自业务自身模块
    const IS_FROM_PAY = 2; // 来自付款模块, 含银行流水

    const BATCH_PAYMENT_NOT_MAX_ONE = 1; // 批量支付支票 收款人必须一致

    const PAYMENT_ONE_LEVEL_AUDIT = 1; //一级审核
    const PAYMENT_TWO_LEVEL_AUDIT = 2;//二级审核
    const PAYMENT_THREE_LEVEL_AUDIT = 3;//三级审核

    const PAY_WHERE_EMPTY = 0; //空
    const PAY_WHERE_IN = 1;    //境内
    const PAY_WHERE_OUT = 2;    //境外

    public static $pay_where_id_to_lang_key = [
        self::PAY_WHERE_EMPTY => 'pay_where.0',
        self::PAY_WHERE_IN      => 'pay_where.1',
        self::PAY_WHERE_OUT => 'pay_where.2'
    ];

    const PAYMENT_MODULE_PAY_STATUS_PENDING = 1; // 待支付
    const PAYMENT_MODULE_PAY_STATUS_PAY = 2;  // 已支付
    const PAYMENT_MODULE_PAY_STATUS_NOTPAY = 3; // 未支付
    const PAYMENT_MODULE_PAY_STATUS_ING = 4; // 支付中
    const PAYMENT_MODULE_PAY_STATUS_FAILED = 5; // 支付失败
    const PAYMENT_MODULE_PAY_STATUS_BANKING = 6; // 银行支付中
    const PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING = 7; // pay支付中
    const PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED = 8; // pay支付失败
    public static $payment_module_pay_status = [
        self::PAYMENT_MODULE_PAY_STATUS_PENDING => 'payment_pay_status.1',
        self::PAYMENT_MODULE_PAY_STATUS_PAY => 'payment_pay_status.2',
        self::PAYMENT_MODULE_PAY_STATUS_NOTPAY => 'payment_pay_status.3',
        self::PAYMENT_MODULE_PAY_STATUS_ING => 'payment_pay_status.4',
        self::PAYMENT_MODULE_PAY_STATUS_FAILED => 'payment_pay_status.5',
        self::PAYMENT_MODULE_PAY_STATUS_BANKING => 'payment_pay_status.6',
        self::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING => 'payment_pay_status.7',
        self::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED => 'payment_pay_status.8'
    ];

    //是否支付
    const IS_PAY_YES = 1; //是
    const IS_PAY_NO = 2; //否
    public static $payment_is_pay_key = [
        self::IS_PAY_YES => 'payment_is_pay_yes',
        self::IS_PAY_NO => 'payment_is_pay_no'
    ];

    //是否在线支付,0否，1是
    const IS_ONLINE_PAY_NO = 0;
    const IS_ONLINE_PAY_YES = 1;

    //支持在线支付的银行
    const ONLINE_PAY = [
        'Flash Pay'
    ];

    //外部交易发送状态：0未传输、1待反馈传输结果、2成功、3失败
    const PAYMENT_MODULE_PAY_SEND_STATUS_NO = 0; // 未传输
    const PAYMENT_MODULE_PAY_SEND_STATUS_WAIT = 1;  // 待反馈
    const PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS = 2; // 成功
    const PAYMENT_MODULE_PAY_SEND_STATUS_FAILED = 3; // 失败
    public static $out_send_status = [
        self::PAYMENT_MODULE_PAY_SEND_STATUS_NO => 'payment_pay_out_send_status.0',
        self::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT => 'payment_pay_out_send_status.1',
        self::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS => 'payment_pay_out_send_status.2',
        self::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED => 'payment_pay_out_send_status.3'
    ];

    //外部交易code码翻译key前缀
    const OUT_TRADE_CODE_PREFIX = 'payment_pay_online_trade_code.';

    //外部交易状态：0交易待支付、2交易处理中、3交易成功、4交易失败、5交易关闭
    const PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_WAIT = 0;
    const PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_ING = 2;
    const PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_SUCCESS = 3;
    const PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_FAILED = 4;
    const PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_CLOSE = 5;

    //是否开启支付模块 1.开启, 否则未开启
    const PAY_MODULE_STATUS_OPEN = 1; //开启

    /**
     * 代理支付-未支付原因分类
     */
    const AGENCY_NOT_PAY_REASON_CATEGORY_COLLECTION_ACCOUNT_ERROR = 1;//收款账户错误/关闭
    const AGENCY_NOT_PAY_REASON_CATEGORY_BANK_SYSTEM_ERROR = 2;//银行/电子钱包系统错误
    const AGENCY_NOT_PAY_REASON_CATEGORY_EXCEED_PAYMENT_LIMIT = 3;//超出支付限额
    const AGENCY_NOT_PAY_REASON_CATEGORY_OTHER = 99;//其他



    // FlashPay支付方式
    const PAYMENT_MODULE_FLASH_PAY_METHOD_API = 1; // API方式
    const PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP = 2; // SFTP方式

}
