<?php
namespace App\Library;

use App\Library\Enums\GlobalEnums;

/**
 * 常用枚举数据
 * Class enums
 */
final class Enums
{
    // TODO 审批流审批状态, 是通用的, 命名有局限性和误导, 后续需统一规划
    const CONTRACT_STATUS_PENDING = 1;
    const CONTRACT_STATUS_REJECTED = 2;
    const CONTRACT_STATUS_APPROVAL = 3;
    const CONTRACT_STATUS_CANCEL = 4;

    public static $contract_status = [
        self::CONTRACT_STATUS_PENDING => 'contract_status.1',
        self::CONTRACT_STATUS_REJECTED => 'contract_status.2',
        self::CONTRACT_STATUS_APPROVAL => 'contract_status.3',
        self::CONTRACT_STATUS_CANCEL => 'contract_status.4',
    ];

    // 租房合同类型
    const CONTRACT_IS_MASTER_YES = 1;
    const CONTRACT_IS_MASTER_NO = 2;
    const CONTRACT_IS_LOI_YES = 3;

    // 所有的租房合同类型
    const CONTRACT_TYPES = self::CONTRACT_IS_MASTER_YES . ',' . self::CONTRACT_IS_MASTER_NO . ',' . self::CONTRACT_IS_LOI_YES;

    const IN_SCOPE_YES = 1;
    const IN_SCOPE_NO = 0;

    //其他合同-结算方式
    const PAY_METHOD_FIXED = 1;//定结
    const PAY_METHOD_NOT_FIXED = 0;//现结
    const PAY_METHOD_PRELOAD = 3;//预充值
    const PAY_METHOD_SPECIAL = 4;//特殊条款
    public static $pay_method = [
        self::PAY_METHOD_FIXED => 'contract_pay_method.1',
        self::PAY_METHOD_NOT_FIXED => 'contract_pay_method.0',
        self::PAY_METHOD_PRELOAD => 'contract_pay_method.3',
        self::PAY_METHOD_SPECIAL => 'contract_pay_method.4',
    ];

    //其他合同-结算天数
    const CONTRACT_BALANCE_DAYS_30 = 1;
    const CONTRACT_BALANCE_DAYS_30_ABOVE = 2;
    public static $contract_balance_days = [
        self::CONTRACT_BALANCE_DAYS_30 => 'contract_balance_days.30',
        self::CONTRACT_BALANCE_DAYS_30_ABOVE => 'contract_balance_days.30above',
    ];

    //其他合同-计价方式
    const CONTRACT_VALUATION_TYPE_COST = 1;
    const CONTRACT_VALUATION_TYPE_DISCOUNT = 2;
    public static $contract_valuation_type = [
        self::CONTRACT_VALUATION_TYPE_COST => 'contract_valuation_type.cost',
        self::CONTRACT_VALUATION_TYPE_DISCOUNT => 'contract_valuation_type.discount',
    ];

    //其他合同-语种
    const CONTRACT_LANG_TH = 'th';
    const CONTRACT_LANG_EN = 'en';
    const CONTRACT_LANG_ZH = 'zh-CN';
    const CONTRACT_LANG_LA = 'la';
    const CONTRACT_LANG_ID = 'id';
    const CONTRACT_LANG_VN = 'vn';
    const CONTRACT_LANG_TH_ZH = 'th-zh';
    const CONTRACT_LANG_EN_ZH = 'en-zh';
    const CONTRACT_LANG_EN_TH = 'en-th';
    public static $contract_lang = [
        self::CONTRACT_LANG_TH => 'contract_lang.th',
        self::CONTRACT_LANG_EN => 'contract_lang.en',
        self::CONTRACT_LANG_ZH => 'contract_lang.zh',
        self::CONTRACT_LANG_LA => 'contract_lang.la',
        self::CONTRACT_LANG_ID => 'contract_lang.id',
        self::CONTRACT_LANG_VN => 'contract_lang.vn',
        self::CONTRACT_LANG_TH_ZH => 'contract_lang.th.zh',
        self::CONTRACT_LANG_EN_ZH => 'contract_lang.en.zh',
        self::CONTRACT_LANG_EN_TH => 'contract_lang.en.th',
    ];
    const STORE_CONTRACT_LANG_TH = 1;
    const STORE_CONTRACT_LANG_EN = 2;
    const STORE_CONTRACT_LANG_ZH = 3;
    const STORE_CONTRACT_LANG_LA = 4;
    public static $store_contract_lang = [
        self::STORE_CONTRACT_LANG_TH => 'contract_lang.th',
        self::STORE_CONTRACT_LANG_EN => 'contract_lang.en',
        self::STORE_CONTRACT_LANG_ZH => 'contract_lang.zh',
        self::STORE_CONTRACT_LANG_LA => 'contract_lang.la',
    ];

    public static $contract_is_master = [
        self::CONTRACT_IS_MASTER_YES => 'contract_is_master.1',
        self::CONTRACT_IS_MASTER_NO => 'contract_is_master.2',
        self::CONTRACT_IS_LOI_YES => 'contract_is_master.3',
    ];

    /** 其他合同 - 合同分类 start (table: contract_category) ***/
    const CONTRACT_TEMPLATE_PURCHASE = 1;
    const CONTRACT_TEMPLATE_SALES = 2;
    const CONTRACT_TEMPLATE_SALES_STANDARD = 21;
    const CONTRACT_TEMPLATE_SALES_NOT_STANDARD = 22;
    /** 其他合同 - 合同分类 end  ***/

    //////////////////////////////////////////
    const VENDOR_OWNERSHIP_CHINA = 1;
    const VENDOR_OWNERSHIP_THAILAND = 2;
    const VENDOR_OWNERSHIP_MALAYSIA = 6;

    const VENDOR_PREFIX_CHINA = 'CNS';
    const VENDOR_PREFIX_THAILAND = 'THS';
    public const WF_OTHER_CONTRACT_TYPE = 95;//其他合同类
    public const WF_CONTRACT_TYPE1 = 1;// 采购合同(TODO: 实际应用中, 该biz_type已当成了其他合同的默认类型, biz_type与flow_id的定义比较混乱, 后续需统一梳理)
    public const WF_CONTRACT_TYPE2 = 2;// 销售合同
    public const WF_CONTRACT_TYPE3 = 3;// 市场合同
    public const WF_CONTRACT_TYPE4 = 4;// 行政合同
    public const WF_CONTRACT_TYPE6 = 6;// 人力合同
    public const WF_CONTRACT_TYPE7 = 7;// 运营合同
    public const WF_LOAN_TYPE = 8;//借款
    public const WF_PURCHASE_APPLY = 9;//业务付款-采购申请单
    public const WF_PURCHASE_ORDER = 10;//业务付款-采购订单
    public const WF_PURCHASE_PAYMENT = 11;//业务付款-采购付款申请单
    public const WF_PURCHASE_ACCEPTANCE = 12;//业务付款-采购验收单
    public const WF_PURCHASE_SAMPLE = 92;//业务样品-采购
    public const WF_MATERIAL_ASSET = 94;//新资产-资产申请-biz type值

    /** 此处枚举类型之前跟转岗有冲突，防止后面踩坑，注意一下此处的枚举值 20/21/22/23/24 **/
    public const WF_CONTRACT_TYPE20 = 20;// 销售合同-标准主合同-定结
    public const WF_CONTRACT_TYPE21 = 21;// 销售合同-标准主合同-现结
    public const WF_CONTRACT_TYPE22 = 22;// 销售合同-非标准主合同
    public const WF_CONTRACT_TYPE23 = 23;// 销售合同-附属合同-授权内
    public const WF_CONTRACT_TYPE24 = 24;// 销售合同-附属合同-授权外

    //薪资扣款
    public const WF_FLOW_ID_WAGES_APPLY_FLASH_EXPRESS = 25;
    public const WF_FLOW_ID_WAGES_APPLY_FLASH_HOME_OPERATION = 326;
    public const WF_FLOW_ID_WAGES_APPLY_OTHERS = 68;
    public const WF_FLOW_ID_WAGES_APPLY_CROWD_SOURCING = 379;
    public const WF_FLOW_ID_WAGES_APPLY_PERSONAL_AGENT = 397;

    //合同审批流枚举(默认的)
    public const WF_CONTRACT_TYPE32 = 32;// 非销售合同
    public const WF_CONTRACT_TYPE59 = 59;// 销售合同-标准主合同-定结
    public const WF_CONTRACT_TYPE60 = 60;// 销售合同-标准主合同-现结
    public const WF_CONTRACT_TYPE61 = 61;// 销售合同-非标准主合同
    public const WF_CONTRACT_TYPE62 = 62;// 销售合同-附属合同-授权内
    public const WF_CONTRACT_TYPE63 = 63;// 销售合同-附属合同-授权外
    public const WF_CONTRACT_TYPE64 = 64;// 合同审批流程(FFM)
    public const WF_CONTRACT_TYPE81 = 81;// 销售合同(FMM)
    public const WF_CONTRACT_TYPE82 = 82;// 销售合同(fcommerce)
    public const WF_CONTRACT_TYPE86 = 86;// 销售合同-SPC
    public const WF_CONTRACT_TYPE87 = 87;// 销售合同-SPC-CEO
    public const WF_CONTRACT_TYPE89 = 89;// 销售合同-Sales
    public const WF_CONTRACT_TYPE90 = 90;// 销售合同-Sales-补充协议授权内

    // Bulky Business部门销售合同
    public const WF_CONTRACT_TYPE133 = 133;// 销售合同-bulky(含CFO,含部门合规):标准主合同定结,补充协议-授权内,补充协议-授权外,非标准主合同
    public const WF_CONTRACT_TYPE134 = 134;// 销售合同-bulky(不含CFO,不含部门合规):标准主合同现结

    // PMD 部门的销售合同
    public const WF_CONTRACT_TYPE96 = 96;// 标准-主合同-现结)
    public const WF_CONTRACT_TYPE97 = 97;// 其他(非 [标准-主合同-现结])
    public const WF_SALE_CONTRACT_PMD_TYPE242 = 242;// 标准-主-定结
    public const WF_SALE_CONTRACT_PMD_TYPE243 = 243;// 非标准-主
    public const WF_SALE_CONTRACT_PMD_TYPE244 = 244;// 附属-授权范围内
    public const WF_SALE_CONTRACT_PMD_TYPE245 = 245;// 附属-非授权范围内

    // Retail 部门的销售合同
    public const WF_SALE_CONTRACT_RETAIL_TYPE246 = 246;// 主-定结
    public const WF_SALE_CONTRACT_RETAIL_TYPE247 = 247;// 附属-授权范围内
    public const WF_SALE_CONTRACT_RETAIL_TYPE248 = 248;// 附属-非授权范围内

    // JVB Operations部门销售合同
    public const WF_CONTRACT_JVB_408 = 408;// 销售合同-JVB Operations(现结): 标准主合同现结
    public const WF_CONTRACT_JVB_409 = 409;// 销售合同-JVB Operations(非现结): 标准主合同定结,补充协议-授权内,补充协议-授权外,非标准主合同

    //电子合同商务审核
    public const WF_BUSINESS_ELECTRONIC_CONTRACT_FLOW_ID = 328;
    public const WF_BUSINESS_ELECTRONIC_CONTRACT_RETAIL_FLOW_ID = 328; // Retail 部门

    // 其他合同中Fcommerce公司的审批流
    public const WF_FLOW_ID_CONTRACT_FCOMMERCE_COMPANY = 259;// 其他合同-Fcommerce公司的审批ID
    public const WF_FLOW_ID_CONTRACT_FLASH_HOME = 384; //其他合同 flash home Operation

    //薪资发放审批流Id
    public const WF_FLOW_ID_SALARY_APPLY_FLASH_EXPRESS = 43;
    public const WF_FLOW_ID_SALARY_APPLY_FLASH_HOME_OPERATION = 327;
    public const WF_FLOW_ID_SALARY_APPLY_OTHERS = 69;

    public const WF_SALARY_APPLY = 43; // 薪资发放审批
    public const WF_SALARY_APPLY_WF_ID = 43; // 薪资发放审批
    public const WF_JOB_TRANSFER_TYPE = 69; //转岗
    public const WF_JOB_TRANSFER_BP_TYPE = 669; //转岗

    public const WF_ACCESS_DATA_WORK_ORDER_WF_ID = 45;// 取数需求工单审批

    // 报价审批
    public const CRM_SUBMITTER_LIJUN = 34014; // 提交人:李俊
    public const CRM_SUBMITTER_EVA = 119100; // 提交人:Eva
    public const CRM_SUBMITTER_LIKE_SALES_MANAGER = 117226; // 提交人:和销售经理走一条审批流的

    public const WF_CRM_QUOTATION = 53; // 报价审批 biz_type
    public const WF_CRM_QUOTATION_WF_ID = 50; // 报价审批 flow_id
    public const WF_CRM_QUOTATION_WF_ID_PH = 88; // 报价审批 flow_id(菲律宾)
    public const WF_CRM_QUOTATION_WF_ID_PH_SALES = 113; // 报价审批 flow_id(菲律宾-sales-销售专员)
    public const WF_CRM_QUOTATION_WF_ID_PH_SALES_SUPERVISOR = 114; // 报价审批 flow_id(菲律宾-sales-销售主管)
    public const WF_CRM_QUOTATION_WF_ID_PH_SALES_MANAGER = 115; // 报价审批 flow_id(菲律宾-sales-销售经理)
    public const CRM_JOB_TITLE_BS = 16;  //Branch supervisor(菲律宾)
    public const CRM_JOB_TITLE_DM = 269;  //District Manager(菲律宾)
    public const CRM_JOB_TITLE_AM = 79;  //Area Manager(菲律宾)
    public const CRM_JOB_TITLE_SALES = 1536;  //销售专员1 (菲律宾)
    public const CRM_JOB_TITLE_SALES_2 = 1674;  //销售专员2 (菲律宾)
    public const CRM_JOB_TITLE_SALES_SUPERVISOR = 1537;  //销售主管(菲律宾)
    public const CRM_JOB_TITLE_SALES_MANAGER = 1535;  //销售经理(菲律宾)

    public const WF_CRM_QUOTATION_SALES_ID = 98; // 报价审批Sales部门大件审批流 flow_id
    public const WF_CRM_QUOTATION_PMD_ID = 257; // 报价审批PMD部门审批流 flow_id
    public const WF_CRM_QUOTATION_SHOP_ID = 100; // 报价审批 Shop部门大件审批流 flow_id
    public const WF_CRM_QUOTATION_NETWORK_ID = 101; // 报价审批Network部门大件审批流 flow_id
    public const WF_CRM_QUOTATION_NETWORK_BULKY_ID = 256; // 报价审批 Network Bulky部门审批流flow_id
    public const WF_CRM_QUOTATION_NETWORK_BULKY_KC_ID = 103; // 报价审批 Network Bulky部门-KA客户/C码客户其他折扣项审批流 flow_id
    public const WF_CRM_QUOTATION_NETWORK_BULKY_C_ID = 104; // 报价审批 Network Bulky部门-小C客户其他折扣项审批流flow_id
    public const WF_CRM_QUOTATION_BULKY_BUSINESS = 135; // 报价审批 (Bulky Business Development部门)
    public const WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES = 249; // 报价审批 flow_id(菲律宾-sales-销售专员)
    public const WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES_SUPERVISOR = 250; // 报价审批 flow_id(菲律宾-sales-销售主管)
    public const WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES_MANAGER = 251; // 报价审批 flow_id(菲律宾-sales-销售经理)
    public const WF_CRM_QUOTATION_NETWORK_ALL_ID = 265; // 报价审批 network部门审批流, 产品倾向把一个部门的审批流建到一起,后续逐步把network所有折扣集中到这一个审批流
    public const WF_CRM_QUOTATION_SHOP_ALL_ID = 266; // 报价审批 shop部门审批流, 产品倾向把一个部门的审批流建到一起,后续逐步把shop所有折扣集中到这一个审批流
    public const WF_CRM_QUOTATION_SALES_ALL_ID = 267; // 报价审批 sales部门审批流
    public const WF_CRM_QUOTATION_HOME_ALL_ID = 274; // 报价审批 flash home部门审批流
    public const WF_CRM_QUOTATION_NEW_WF_ID_PH_USER_ID = 286; // 报价审批 flow_id(菲律宾-user)
    public const WF_CRM_QUOTATION_JVB_OPERATIONS = 407; // 报价审批 (JVB Operations 部门)


    //采购申请单审批流ID
    public const WF_PURCHASE_APPLY_FLOW = 287;//采购申请单审批流-v16911-资产部&&Flash Express
    public const WF_PURCHASE_APPLY_FLOW_ASSET_OTHER = 291;//采购申请单审批流-v16911-资产部&&其他公司
    public const WF_PURCHASE_APPLY_FLOW_IT = 292;//采购申请单审批流-v16911-IT部&&Flash Express
    public const WF_PURCHASE_APPLY_FLOW_IT_OTHER = 293;//采购申请单审批流-v16911-IT部&&其他公司
    public const WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE = 294;//采购申请单审批流-v16911-其他部门&&F-commerce
    public const WF_PURCHASE_APPLY_FLOW_OTHER = 295;//采购申请单审批流-v16911-其他部门&&其他公司
    public const WF_PURCHASE_APPLY_FLOW_PMD = 415;//采购申请单审批流-v21791-PMD
    public const WF_PURCHASE_APPLY_F_COMMERCE_FLOW = 260;//15424 采购申请单-FCommerce审批流(目前马来,越南)

    //采购订单审批流id
    public const WF_PURCHASE_ORDER_OTHER_FLOW = 299;//其他公司兜底审批流；v16911-新增
    public const WF_PURCHASE_ORDER_ONLY_FFM = 300;//Fulfillment审批流；v16911由128->300
    public const WF_PURCHASE_ORDER_ONLY_PAY = 301;//Flash Pay审批流；v16911由131->301
    public const WF_PURCHASE_ORDER_ONLY_MONEY = 302;//Flash money、FlashPico审批流；v16911由129->302
    public const WF_PURCHASE_ORDER_ONLY_COMMERCE = 303;//F-commerce审批流；v16911由130->303
    public const WF_PURCHASE_ORDER_ONLY_SUPPLY = 304;//Supply Chain Management审批流；v16911由132->304
    public const WF_PURCHASE_ORDER_ONLY_EXPRESS = 305;//FlashExpress审批流；v16911-新增
    public const WF_PURCHASE_ORDER_FFM = 306;//FlashFullfillment、FlashPay、FlashMoney审批流；v16911由85->306
    public const WF_PURCHASE_ORDER_FLASH_HOME = 308;// Flash Home相关BU审批流 v17061

    public const WF_PURCHASE_ORDER_FLOW = 84;//v16911-废弃
    public const WF_PURCHASE_ORDER_DEFAULT_FLOW = 117;// 默认的新流程审批流；v16911-废弃
    public const WF_PURCHASE_ORDER_LA_EXPRESS = 234;//老挝FlashExpress审批流；v16911-废弃

    // 16911-p4-给财务发送提醒针对采购订单只关心新的审批流，这里增加是为了防止后期新增审批，这里也需要同步改下
    public static $WF_PURCHASE_ORDER_FLOW_IDS = [
        Enums::WF_PURCHASE_ORDER_OTHER_FLOW,
        Enums::WF_PURCHASE_ORDER_ONLY_FFM,
        Enums::WF_PURCHASE_ORDER_ONLY_PAY,
        Enums::WF_PURCHASE_ORDER_ONLY_MONEY,
        Enums::WF_PURCHASE_ORDER_ONLY_COMMERCE,
        Enums::WF_PURCHASE_ORDER_ONLY_SUPPLY,
        Enums::WF_PURCHASE_ORDER_ONLY_EXPRESS,
        Enums::WF_PURCHASE_ORDER_FFM,
        Enums::WF_PURCHASE_ORDER_FLASH_HOME
    ];




    //采购付款申请单审批流id
    public const WF_PURCHASE_PAYMENT_FFM = 37;
    public const WF_PURCHASE_PAYMENT_FLOW = 34;
    public const WF_PURCHASE_PAYMENT_DEFAULT_FLOW = 118;// 默认的新流程审批流
    public const WF_PURCHASE_PAYMENT_ONLY_FFM = 123;//Fulfillment审批流
    public const WF_PURCHASE_PAYMENT_ONLY_MONEY = 124;//Flash money审批流
    public const WF_PURCHASE_PAYMENT_ONLY_COMMERCE = 125;//F-commerce审批流
    public const WF_PURCHASE_PAYMENT_ONLY_PAY = 126;//Flash Pay审批流
    public const WF_PURCHASE_PAYMENT_ONLY_SUPPLY = 127;//Supply Chain Management审批流
    public const WF_PURCHASE_PAYMENT_LA_EXPRESS = 235;//老挝FlashExpress审批流
    public const WF_PURCHASE_PAYMENT_FLASH_HOME = 309;// Flash Home 相关BU审批流 v17061


    public const WF_LOAN_APPLY_WF_ID = 31;// 借款申请 flow_id
    public const WF_LOAN_APPLY_COMPANY_WF_ID = 91;//借款申请（FFM等）-指定公司 flow_id
    public const WF_LOAN_APPLY_OUTSOURCING = 137;// 借款申请 外协事项
    public const WF_LOAN_APPLY_OTHER = 138;// 借款申请 其它事项
    public const WF_FLOW_ID_LOAN_APPLY_FLASH_LAO = 240;// 借款申请 - Flash Express 公司默认审批流(LA/MY/PH 非Network Management及子部门的)
    public const WF_LOAN_APPLY_COMMON_WF_ID = 329;// 借款申请 通用flow_id
    public const WF_LOAN_APPLY_PMD_WF_ID = 416;// 借款申请 - 申请人所属一级部门等于PMD-Thailand-新增审批流 通用flow_id

    public const WF_FLOW_ID_LOAN_APPLY_FLASH_EXPRESS = 385;//借款申请 - Flash Express 公司(MY,PH&网点类型是PH Network Management及子部门的)
    public const WF_FLOW_ID_LOAN_APPLY_NOT_FLASH_EXPRESS = 386;//借款申请 - 非Flash Express 公司(MY,PH)
    public const WF_FLOW_ID_LOAN_APPLY_FLASH_EXPRESS_NOT_NW = 387;//借款申请 - Flash Express 公司(PH&非网点类型Network Management及子部门的)

    public const WF_FLOW_ID_LOAN_RETURN_FLASH_LAO = 241;// 借款归还 - Flash LAO 公司默认审批流

    public const WF_LOAN_BACK_WF_ID = 71; // 借款归还 flow_id
    public const WF_LOAN_BACK_COMPANY_WF_ID = 92; // 借款归还（FFM等）-指定公司 flow_id
    public const WF_LOAN_BACK_COMPANY_FLASH_TH_WF_ID = 273; // 借款归还（Flash express）-指定公司 flow_id
    public const WF_LOAN_BACK_UNIFY_WF_ID = 388; // 借款归还 - 不分公司统一审批流
    public const WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_WF_ID = 404; // 电子合同-乙方商务复核签字审批
    public const WF_CONTRACT_ELECTRONIC_RETAIL_WF_ID = 405; // 电子合同-合同内容审核-Retail
    public const WF_CONTRACT_ELECTRONIC_RETAIL_REVIEW_SIGN_WF_ID = 406; // 电子合同-签字复核审核-Retail


    public const WF_REIMBURSEMENT_TYPE = 13;


    public const WF_STORE_RENTING_CONTRACT_TYPE = 14;   //网点房屋租赁合同
    public const WF_STORE_RENTING_CONTRACT_INVALID_TYPE = 98;   //网点房屋租赁合同-作废
    public const WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE = 99;   //网点房屋租赁合同-终止
    public const WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE = 100;   //网点房屋租赁合同-续签
    public const WF_CONTRACT_GPMD_BIZ_TYPE = 101;   //GPMD 平台合同biz
    public const WF_CONTRACT_ELECTRONIC_BIZ_TYPE = 102;   //电子合同商务审核
    public const WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE = 106;   //电子合同 - 乙方商务签字复核审批

    public const WF_WAREHOUSE_STATUS_CHANGE_BIZ_TYPE = 103; // 仓库管理-状态变更审批 - biz_type
    public const WF_WAREHOUSE_STORE_CHANGE_BIZ_TYPE = 104; // 仓库管理-网点变更审批 - biz_type
    public const WF_WAREHOUSE_PRICE_BIZ_TYPE = 107; // 仓库管理-仓库报价审核 - biz_type

    public const WF_AGENCY_PAYMENT_BIZ_TYPE = 105; // 代理支付-审核
    public const WF_BUDGET_WITHHOLDING_BIZ_TYPE = 108; // 预算管理-费用预提-审核


    const WF_PAYMENT_STORE_RENTING_TYPE = 29; // 付款管理 - 网点租房付款申请

    const ORDINARY_PAYMENT_BIZ_TYPE = 30; // 普通付款

    const WF_CHEQUE_APPLY_BIZ_TYPE = 96; // 支票申请

    const WF_ACCESS_DATA_WORK_ORDER_TYPE = 50; // 取数需求工单

    const WF_RESERVE_FUND_APPLY = 51;//备用金-申请单
    const WF_RESERVE_FUND_RETURN = 52;//52;//备用金归还-审批

    const DEPOSIT_RETURN_BIZ_TYPE = 93; // 押金归还

    const DEPOSIT_FLOW_ID_IN_COST_COMPANY = 236; // 押金归还-押金没有损失包含Fulfilment, Flash Money, Flash Pay, F commerce时 包含审批流
    const DEPOSIT_FLOW_ID_NOTIN_COST_COMPANY = 237; // 押金归还-押金有损失包含 Fulfilment, Flash Money, Flash Pay, F commerce时
    const DEPOSIT_FLOW_ID_INES_COST_COMPANY = 238; // 押金归还-押金没有损失不包含Fulfilment, Flash Money, Flash Pay, F commerce时 包含审批流
    const DEPOSIT_FLOW_ID_NOTINES_COST_COMPANY = 239; // 押金归还-押金有损失包含 Fulfilment, Flash Money, Flash Pay, F commerce时



    //备用金-申请单 审批流(flow_id)
    const WF_RESERVE_FUND_APPLY_TYPE46 = 46;
    const WF_RESERVE_FUND_APPLY_TYPE73 = 73;
    const WF_RESERVE_FUND_APPLY_TYPE74 = 74;
    const WF_RESERVE_FUND_APPLY_TYPE75 = 75;
    //备用金-申请单 泰国
    const WF_RESERVE_FUND_APPLY_TYPE139 = 139;//network
    const WF_RESERVE_FUND_APPLY_TYPE140 = 140;//shop
    const WF_RESERVE_FUND_APPLY_TYPE141 = 141;//hub
    const WF_RESERVE_FUND_APPLY_TYPE142 = 142;//network_bulky

    const WF_RESERVE_FUND_APPLY_TYPE337 = 337;//network && hub
    const WF_RESERVE_FUND_APPLY_TYPE338 = 338;//shop (retail)
    const WF_RESERVE_FUND_APPLY_TYPE339 = 339;//非快递公司

    //备用金-归还 泰国
    const WF_RESERVE_FUND_APPLY_TYPE143 = 143;//network
    const WF_RESERVE_FUND_APPLY_TYPE144 = 144;//shop
    const WF_RESERVE_FUND_APPLY_TYPE145 = 145;//hub
    const WF_RESERVE_FUND_APPLY_TYPE146 = 146;//network_bulky

    // 网点备用金归还审批流
    const WF_RESERVE_FUND_RETURN_TYPE47 = 47;
    const WF_RESERVE_FUND_RETURN_TYPE48 = 48;
    const WF_RESERVE_FUND_RETURN_TYPE49 = 49;

    const  WF_RESERVE_FUND_APPLY_FFM_WF_ID = 105; // 备用金申请审批流ID
    const  WF_RESERVE_FUND_RETURN_FFM_WF_ID = 106; // 备用金归还审批流ID

    const WF_WAGES_TYPE = 15;//薪酬扣款审批-待审核
    const WF_HC_AUDIT = 16;//hc预算管理-待审核

    const WF_LOAN_BACK_TYPE = 58;   //借款归还

    const WF_VENDOR_WF_ID = 67; // 供应商审批 flow_id 包含flash
    const WF_VENDOR_WF_F_ID = 136; // 供应商审批 flow_id 不包含flash
    const WF_VENDOR_FLASH_HOME_ID = 307; // 供应商审批 Flash Home相关BU的
    const WF_VENDOR_WF_GRADE_ID = 258; // 供应商分级管理审批

    const WF_VENDOR_BIZ_TYPE = 59; // 供应商审批 业务类型
    const WF_VENDOR_GRADE_BIZ_TYPE = 97; // 供应商分级管理审批 业务类型

    //支付模块
    const WF_PAY_TYPE = 76;    //付款模块 biz_type
    const WF_PAY_TYPE_FLOW = 76;    //付款模块 flow_id
    const WF_PAY_TYPE_FLOW_FFM = 116;    //付款模块(Flash Fulfillment/F-Commerce/Flash Pay) flow_id
    const WF_PAY_TYPE_FLASH_EXPRESS = 275;//支付模块-泰国（FlashExpress）
    const WF_PAY_TYPE_FLASH_FULLFILLMENT = 276;//支付模块-泰国（FlashFullfillment）
    const WF_PAY_TYPE_FLASH_PAY = 277;//支付模块-泰国（FlashPay）
    const WF_PAY_TYPE_FLASH_MONEY = 278;//支付模块-泰国（FlashMoney）
    const WF_PAY_TYPE_FLASH_HR = 279;//支付模块-泰国（FlashHr）
    const WF_PAY_TYPE_FCOMMERCE = 280;//支付模块-泰国（FCommerce）
    const WF_PAY_TYPE_FLASH_THAILAND_HOLDING = 281;//支付模块-泰国（FlashThailand、FlashHolding）
    const WF_PAY_TYPE_SHOP_WINNER = 282;//支付模块-泰国（ShopWinnerCoLtd）
    const WF_PAY_TYPE_FLASH_INCORPORATION_PICO = 283;//支付模块-泰国（FlashIncorporation、FlashPico）
    const WF_PAY_TYPE_FLASH_HOME = 284;//支付模块-泰国（FlashHomeOperation、FlashHomeHolding）

    const BUDGET_OB_TYPE = 60;    //财务预算模块审批
    const WF_BUDGET_ADJUST_TYPE = 91;    //预算调整审批

    public const WF_NODE_APPLY = 1;
    public const WF_NODE_COND = 2;
    public const WF_NODE_ACTION = 3;
    public const WF_NODE_REJECTED = 4;
    public const WF_NODE_CC = 4; //抄送使用4, 之前的WF_NODE_REJECTED没有用
    public const WF_NODE_APPROVED = 5;
    public const WF_NODE_FINAL = 6;

    // 节点标签定义: 节点的附属含义扩展
    public const WF_NODE_TAG_PM = 3;    //PM节点(采购经理)
    public const WF_NODE_TAG_ASSET_MANAGER = 10;//资产经理
    public const WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR = 11;//集团采购总监

    public const WF_NODE_TAG_AP_LOCAL= 5;    //AP Local
    public const WF_NODE_TAG_AP_BEIJING= 6;    //AP北京
    public const WF_NODE_TAG_TAX_LOCAL= 7;    //Tax Local
    public const WF_NODE_TAG_TAX_BEIJING= 8;    //Tax北京

    public const WF_NODE_TAG_APS_BEIJING = 4;    //APS北京（财务经理）
    public const WF_NODE_TAG_APS_LOCAL = 9;    //APS local（财务经理）

    public const WF_NODE_TAG_APD_BEIJING = 12;//APD北京（财务总监）
    public const WF_NODE_TAG_APD_LOCAL = 13;//APD local（财务总监）

    // 16911-p4-给财务发送提醒针对财务各子节点的审批流，这里增加是为了防止后期新增审批，这里也需要同步改下
    public static $FINANCE_WF_NODE_TAGS = [
        self::WF_NODE_TAG_AP_LOCAL,
        self::WF_NODE_TAG_AP_BEIJING,
        self::WF_NODE_TAG_TAX_LOCAL,
        self::WF_NODE_TAG_TAX_BEIJING,
        self::WF_NODE_TAG_APS_BEIJING,
        self::WF_NODE_TAG_APS_LOCAL,
        self::WF_NODE_TAG_APD_BEIJING,
        self::WF_NODE_TAG_APD_LOCAL
    ];


    // 节点审批逻辑类型
    public const WF_NODE_LOGIC_AUDIT_TYPE_DEFAULT = 0;// 默认的自动同意
    public const WF_NODE_LOGIC_AUDIT_TYPE_AUTO_APPROVAL = 3;// 待审批人含提交人,直接自动通过
    public const WF_NODE_LOGIC_AUDIT_TYPE_AUTO_APPROVAL_IGNORE = 4;//v16911需求，自动通过，无需点击审批，属于上级部门领导同意，低层级部门自动同意

    // 节点审批类型
    public const WF_NODE_AUDIT_TYPE_OR_SIGN = 0; // 或签
    public const WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN = 1; // 审批人会签
    public const WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN = 2; // 子节点会签
    public const WF_NODE_AUDIT_TYPE_COMMON_COUNTERSIGN = 1; // 通用会签(含审批人及子节点会签: 多用于状态展示和逻辑合并判断)
    // 节点审批类型翻译key -- 目前用在审批流画布展示中
    public static $workflow_node_audit_type_items = [
        self::WF_NODE_AUDIT_TYPE_OR_SIGN => 'workflow_node_audit_type_default',
        self::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN => 'workflow_node_audit_type_countersign',
        self::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN => 'workflow_node_audit_type_sub_countersign'
    ];

    // 审批流状态
    public const WF_STATE_PENDING = 1;
    public const WF_STATE_REJECTED = 2;
    public const WF_STATE_APPROVED = 3;
    public const WF_STATE_CANCEL = 4;
    public const WF_STATE_AUTO_APPROVED = 5;// 自动通过状态

    // 审批流操作
    public const WF_ACTION_APPLY = 0;
    public const WF_ACTION_APPROVE = 1;
    public const WF_ACTION_REJECT = 2;
    public const WF_ACTION_NULL = 3;// 审批流未被处理的默认枚举
    public const WF_ACTION_CANCEL = 6;
    public const WF_ACTION_AUTO_APPROVE = 8;// 自动同意
    public const WF_ACTION_COUNTERSIGN_APPROVE = 10; // 节点审批人会签同意操作
    public const WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE = 11; // 子节点会签同意操作
    public const WF_ACTION_CARBON_COPY = 12; // 抄送

    // 审批同意的行为组合
    public static $approval_actions = [
        self::WF_ACTION_APPROVE,
        self::WF_ACTION_AUTO_APPROVE
    ];

    // 审批流日志展示
    public static $actions = [
        self::WF_ACTION_APPLY => 'flow_audit_action.0',
        self::WF_ACTION_APPROVE => 'flow_audit_action.1',
        self::WF_ACTION_REJECT  => 'flow_audit_action.2',
        self::WF_ACTION_NULL => 'flow_audit_action.3',
        self::WF_ACTION_CANCEL  => 'flow_audit_action.6',
        self::WF_ACTION_AUTO_APPROVE  => 'flow_audit_action.8',
        self::WF_ACTION_CARBON_COPY  => 'flow_audit_action.12',
    ];

    // 审批节点标识: 审批日志中的节点展示名称
    public const WF_NODE_FLAG_APPLY = 1;
    public const WF_NODE_FLAG_CANCEL = 2;
    public const WF_NODE_FLAG_AUDIT = 3;
    public const WF_NODE_FLAG_CC = 4;
    public static $wf_node_flag_item = [
        self::WF_NODE_FLAG_APPLY => 'flow_node_flag_' . self::WF_NODE_FLAG_APPLY,
        self::WF_NODE_FLAG_CANCEL => 'flow_node_flag_' . self::WF_NODE_FLAG_CANCEL,
        self::WF_NODE_FLAG_AUDIT => 'flow_node_flag_' . self::WF_NODE_FLAG_AUDIT,
        self::WF_NODE_FLAG_CC => 'flow_node_flag_' . self::WF_NODE_FLAG_CC,
    ];

    // 审批行为与审批节点表示的关系
    public static $wf_action_and_node_flag_rel = [
        self::WF_ACTION_APPLY => self::WF_NODE_FLAG_APPLY,
        self::WF_ACTION_CANCEL => self::WF_NODE_FLAG_CANCEL,
        self::WF_ACTION_APPROVE => self::WF_NODE_FLAG_AUDIT,
        self::WF_ACTION_REJECT => self::WF_NODE_FLAG_AUDIT,
        self::WF_ACTION_AUTO_APPROVE => self::WF_NODE_FLAG_AUDIT,
        self::WF_ACTION_NULL => self::WF_NODE_FLAG_AUDIT,
        self::WF_ACTION_CARBON_COPY => self::WF_NODE_FLAG_CC,
    ];

    // 是否过了AP_TH节点 (老的逻辑标识, 后续视机会调整): 0-未过; 1-已过
    const BIZ_IS_AFTER_AP_TH_NODE_NO = 0;
    const BIZ_IS_AFTER_AP_TH_NODE_YES = 1;

    /****************************物料/资产审核***********************************/
    const WF_WMS_TYPE = 9;
    const WF_ASSET_TYPE = 16;
    const WF_ASSET_PUBLIC_TYPE = 19;

    const ASSET_AUDIT_STATUS_PENDING = 1;
    const ASSET_AUDIT_STATUS_APPROVAL = 2;
    const ASSET_AUDIT_STATUS_REJECTED = 3;

    public static $asset_audit_status = [
        self::ASSET_AUDIT_STATUS_PENDING => 'asset_audit_status.1',
        self::ASSET_AUDIT_STATUS_APPROVAL => 'asset_audit_status.2',
        self::ASSET_AUDIT_STATUS_REJECTED => 'asset_audit_status.3',
    ];

    const WMS_AUDIT_STATUS_PENDING = 1;
    const WMS_AUDIT_STATUS_APPROVAL = 2;
    const WMS_AUDIT_STATUS_REJECTED = 3;

    public static $wms_audit_status = [
        self::WMS_AUDIT_STATUS_PENDING => 'wms_audit_status.1',
        self::WMS_AUDIT_STATUS_APPROVAL => 'wms_audit_status.2',
        self::WMS_AUDIT_STATUS_REJECTED => 'wms_audit_status.3',
    ];



    /****************************借款审核***********************************/
    const TRAFFIC_TOOLS_PLANE = 1;
    const TRAFFIC_TOOLS_TRAIN = 2;
    const TRAFFIC_TOOLS_CAR = 3;
    const TRAFFIC_TOOLS_OTHER = 4;
    public static $loan_traffic_tools = [
        self::TRAFFIC_TOOLS_PLANE => 'traffic_tools.1',
        self::TRAFFIC_TOOLS_TRAIN => 'traffic_tools.2',
        self::TRAFFIC_TOOLS_CAR => 'traffic_tools.3',
    ];

    const TRAFFIC_ONEWAY = 1;
    const TRAFFIC_ROUNDTRIP = 2;
    public static $loan_traffic_single = [
        self::TRAFFIC_ONEWAY => 'traffic_is_single.1',
        self::TRAFFIC_ROUNDTRIP => 'traffic_is_single.2',
    ];

    const TRAFFIC_STATUS_PENDING = 1;  //待审核
    const TRAFFIC_STATUS_REJECTED = 3; //已拒绝
    const TRAFFIC_STATUS_APPROVAL = 2; //已通过
    const TRAFFIC_STATUS_WITHDRAW = 4; //已撤回

    public static $loan_traffic_status = [
        self::TRAFFIC_STATUS_PENDING => 'contract_status.1',
        self::TRAFFIC_STATUS_REJECTED => 'contract_status.2',
        self::TRAFFIC_STATUS_APPROVAL => 'contract_status.3',
    ];


    const BANK_TYPE_TMB = 1;
    const BANK_TYPE_SCB = 2;
    const BANK_TYPE_PH_UB = 21;
    const BANK_TYPE_MY_MB = 15;//马来银行
    const BANK_TYPE_LA_BCEL= 53;//老挝银行
    const BANK_TYPE_MY_MAY = 54;//马来银行
    const BANK_TYPE_ID_BCA = 55;//印尼银行
    const BANK_TYPE_MY_BSN = 83;//马来银行（BSN）
    public static $bank_type = [
        0                       => '',
        self::BANK_TYPE_TMB     => 'TTB',
        self::BANK_TYPE_SCB     => 'SCB',
        self::BANK_TYPE_PH_UB   => 'UB',
        self::BANK_TYPE_MY_MB   => 'CIMB',
        self::BANK_TYPE_LA_BCEL => 'BCEL',
        self::BANK_TYPE_MY_MAY  => 'Maybank',
        self::BANK_TYPE_ID_BCA  => 'BCA',
        self::BANK_TYPE_MY_BSN  => 'BSN',
    ];

    const LOAN_PAY_STATUS_PENDING = 1; //待支付
    const LOAN_PAY_STATUS_PAY = 2;  //已支付
    const LOAN_PAY_STATUS_NOTPAY = 3; //未支付

    public static $loan_pay_status = [
        self::LOAN_PAY_STATUS_PENDING => 'loan_pay_status.1',
        self::LOAN_PAY_STATUS_PAY => 'loan_pay_status.2',
        self::LOAN_PAY_STATUS_NOTPAY => 'loan_pay_status.3',
    ];

    //与合同审核状态一致，但名字稍微有区别
    public static $loan_status = [
        self::CONTRACT_STATUS_PENDING => 'loan_status.1',
        self::CONTRACT_STATUS_REJECTED => 'loan_status.2',
        self::CONTRACT_STATUS_APPROVAL => 'loan_status.3',
        self::CONTRACT_STATUS_CANCEL => 'loan_status.4',
    ];

    const AUDITOR_TYPE_STAFF_INFO_ID = 1;                   //指定员工ID
    const AUDITOR_TYPE_STAFF_ROLE    = 2;                   //指定角色 position_id
    const AUDITOR_TYPE_SUBMITTER_DIRECT_SUPERIOR = 3;       //直接上级
    const AUDITOR_TYPE_SUBMITTER_DEPARTMENT_MANAGER = 4;    //提交人所在部门的经理
    const AUDITOR_TYPE_SUBMITTER_NODE_DEPARTMENT = 5;       //二级部门负责人
    const AUDITOR_TYPE_SUBMITTER_DEPARTMENT = 6;            //一级部门负责人，
    const AUDITOR_TYPE_COO = 7;                             //COO||CPO审批，有的部门跳过。

    const AUDITOR_TYPE_BRANCH_MANAGER = 8;                  //报销，网点主管
    const AUDITOR_TYPE_BRANCH_LEVEL1 = 9;                   //报销，网点审批Level1，Supervisor
    const AUDITOR_TYPE_BRANCH_LEVEL2 = 10;                  //报销，网点审批Level2, Manager
    const AUDITOR_TYPE_BRANCH_LEVEL3 = 11;                  //报销，网点审批level3, Senior Manager
    const AUDITOR_TYPE_BRANCH_LEVEL4 = 12;                  //报销，网点审批level4, Director
    const AUDITOR_TYPE_BRANCH_MANAGER_BY_POSITION = 13;     //报销，网点主管根据职位
    const AUDITOR_TYPE_BRANCH_BUSINESS_MANAGER = 14;        //报销，Network业务线Manager
    const AUDITOR_TYPE_BRANCH_BUSINESS_SENIOR_MANAGER = 15; //报销，Network业务线SENIOR_MANAGER
    const AUDITOR_TYPE_COMPANY_MANAGER = 16;                //公司负责人
    const AUDITOR_TYPE_SHOP_BRANCH_SENIOR_MANAGER = 17;     //报销，Shop，SENIOR_MANAGER
    const AUDITOR_TYPE_ACCESS_DATA_SYS_SUBMITTER_DEPARTMENT_MANAGER = 18;     //取数工单系统:提交人所属部门负责人
    const AUDITOR_TYPE_ACCESS_DATA_SYS_DATA_DEPARTMENT_MANAGER = 19;     //取数工单系统:数据部门负责人
    const AUDITOR_TYPE_ACCESS_DATA_SYS_RELATED_DEPARTMENT_MANAGER = 20;     //取数工单系统:相关部门负责人

    const AUDITOR_TYPE_JOB = 21; // 根据职位ID job_id 获取职位下的人
    const AUDITOR_TYPE_DM_APPLY = 22;// 菲律宾报价管理 v1.5需求，增加申请人是DM职位时，一级审批人为组织架构中的申请人管辖片区的负责人
    const AUDITOR_TYPE_COMPANY_C_LEVEL_GROUP_CEO_MANAGER = 23;// 先找公司负责人不存在找c-level负责人 不存在找group ceo
    const AUDITOR_TYPE_HC_HRBP = 24;// 11684需求, 增加HRBP逻辑, 从HCM的 [HC预算BP管辖范围配置] 取值

    const AUDITOR_TYPE_HUB_AREA_MANAGER = 26;// 13363需求找员工所属区域的对应负责人，只取职位ID等于Hub Area Manager[724]取值
    const AUDITOR_TYPE_CROSS_DEPARTMENT_ID = 25; // 12558需求, 跨部门一级部门负责人
    const AUDITOR_TYPE_DYNAMIC_UID = 27; // 13620需求, 动态获取审批人
    const AUDITOR_TYPE_NODE_AM_BY_ORG = 28; //根据组织架构找大区负责人,和找am负责人的区别在于，am是通过找到大区负责人在去根据职位匹配，新的是直接找大区负责人在看负责人在职状态
    const AUDITOR_TYPE_DESIGNATIVE_ORG = 29; // 找指定组织架构的负责人
    const AUDITOR_TYPE_GROUP_ORG = 30; // 找指定分组审批人
    const AUDITOR_TYPE_NODE_DEPARTMENT_GROUP_ORG = 36;// 找指定分组审批人 - 所属部门ID
    const AUDITOR_TYPE_DEPARTMENT_ORG = 32; //根据指定部门id找负责人
    const AUDITOR_TYPE_ASSIGN_DEPARTMENT_MANAGER = 33; //自定义部门id找负责人
    const AUDITOR_TYPE_ASSIGN_DEPARTMENT_COMPANY_MANAGER = 34; //自定义部门id找公司负责人
    const AUDITOR_TYPE_ASSIGN_DEPARTMENT_C_LEVEL_MANAGER = 35; //自定义部门id找CLevel负责人
    const AUDITOR_TYPE_SUBMITTER_THREE_DEPARTMENT = 37;//三级部门负责人



    const AUDITOR_TYPE_ORDINARY_PAYMENT_DM_ORG = 31; //根据网点查找片区负责人，和查找dm负责人的区别在于，dm是通过找到片区负责人在去根据职位匹配，新的是直接找片区负责人在看负责人在职状态
    const JOB_TITLE_BRANCH_SUPERVISOR = 16;                 //Branch Supervisor职位id
    const JOB_TITLE_DISTRICT_MANAGER = 269;                 //District manager职位id
    const JOB_TITLE_AREA_MANAGER = 79;                      //Area manager职位id
    const JOB_TITLE_SHOP_AREA_MANAGER = 11;                 //Shop area manager职位id
    const JOB_TITLE_CPO = 632;                              //CPO
    const JOB_TITLE_HUB_AREA_MANAGER = 724;                      //职位为HUB_AREA_MANAGER724
    const TH_JOB_TITLE_PICKUP_DRIVER = 1844;                //泰国pickup drive

    const LOAN_TYPE_TRAVEL = 1;   //差旅费
    const LOAN_TYPE_PURCHASE = 2;  //采购
    const LOAN_TYPE_ABROAD =3 ;    //长期驻外
    const LOAN_TYPE_OFFICIAL = 4;  //公务活动
    const LOAN_TYPE_OTHER = 5;     //其他


    const LOAN_PAY_TYPE_CASH =1;   //现金
    const LOAN_PAY_TYPE_BANK =2;   //银行转账
    const LOAN_PAY_TYPE_TMB = 2;
    const LOAN_PAY_TYPE_SCB = 3;


    const LOAN_TRAVEL_FLAG_SUBMIT = 0;  //可以提交
    const LOAN_TRAVEL_FLAG_NOTSUBMIT = 1; //不可以提交，差旅为空，或者状态不是通过
    const LOAN_TRAVEL_FLAG_SUBMITED = 2; //不可以提交,该差旅提交过


    const LOAN_PAY_SIGN = 1; //签收
    const LOAN_PAY_NOTSIGN = 2; //没签收

    //职等职级 职等1.Staff/2.Supervisor/3.Manager/4.Executive
    const LEVEL_GRADE_STAFF = 1;
    const LEVEL_GRADE_SUPERVISOR = 2;
    const LEVEL_GRADE_MANAGER = 3;
    const LEVEL_GRADE_EXECUTIVE = 4;

    public static $staff_level_grade = [
        self::LEVEL_GRADE_STAFF => 'Staff',
        self::LEVEL_GRADE_SUPERVISOR => 'Supervisor',
        self::LEVEL_GRADE_MANAGER => 'Manager',
        self::LEVEL_GRADE_EXECUTIVE => 'Executive',
    ];

    const LOAN_BACK_STATUS_NOT = 1;     //借款归还状态 - 未归还
    const LOAN_BACK_STATUS_ING = 2;     //借款归还状态 - 正在归还（或者被报销占用正在审批）
    const LOAN_BACK_STATUS_BACK = 3;    //借款归还装填 - 已经归还



    public static $loan_back_status = [
        self::LOAN_BACK_STATUS_NOT => 'loan_back_status.1',
        self::LOAN_BACK_STATUS_ING => 'loan_back_status.2',
        self::LOAN_BACK_STATUS_BACK => 'loan_back_status.3',
    ];

    /****************************业务付款***********************************/
    const PURCHASE_APPLY_STATUS_PENDING     = 1;//采购申请单待审核
    const PURCHASE_APPLY_STATUS_REJECTED    = 2;//采购申请单已拒绝
    const PURCHASE_APPLY_STATUS_APPROVAL    = 3;//采购申请单已通过
    const PURCHASE_APPLY_STATUS_WITHDRAW    = 4;//采购申请单已撤回

    const PURCHASE_ORDER_STATUS_PENDING     = 1;//采购定单待审核
    const PURCHASE_ORDER_STATUS_REJECTED    = 2;//采购定单已拒绝
    const PURCHASE_ORDER_STATUS_APPROVAL    = 3;//采购定单已通过
    const PURCHASE_ORDER_STATUS_WITHDRAW    = 4;//采购定单已撤回

    const PURCHASE_APPLY_TYPE_DEFAULT   = 1;//申请类型-采购（非咨询费、广告费）
    const PURCHASE_APPLY_TYPE_AD        = 2;//申请类型-广告
    const PURCHASE_APPLY_TYPE_FA        = 3;//申请类型-咨询

    const PURCHASE_PRODUCT_FA_CATEGORY  = 5;//固定产品分类
    const PURCHASE_PRODUCT_AD_CATEGORY  = 30;//广告产品分类
    const PURCHASE_PRODUCT_CONSULT_CATEGORY  = 35;//咨询产品分类

    const PURCHASE_PAY_TYPE_BANK    = 1;//采购付款方式-银行
    const PURCHASE_PAY_TYPE_CASH    = 2;//采购付款方式-现金
    const PURCHASE_PAY_TYPE_CHEQUE    = 3;//采购付款方式-支票


    /*----报销----*/

    const REIMBURSEMENT_EXPENSE_TRAVEL=1;
    const REIMBURSEMENT_EXPENSE_LOCAL =2;

    public static $reimbursement_expense_type = [
        self::REIMBURSEMENT_EXPENSE_TRAVEL => 're_field_travel',
        self::REIMBURSEMENT_EXPENSE_LOCAL => 're_field_local',
    ];

    public static $company_types = [
        'FlashLaos' => 40001,
        'FlashFullfillment' => 20001,
        'FlashPay' => 60001,
        'FlashMoney' => 30001,
        'FCommerce' => 50001,
        'MalaysiaFulfillment' => 244
    ];

    const REIMBURSEMENT_EXPENSE_FUEL = 7;               //油费
    const REIMBURSEMENT_EXPENSE_WATER_AND_ELE = 25;     //水电费
    const REIMBURSEMENT_EXPENSE_DRINKING_WATER = 26;    //饮水费
    const REIMBURSEMENT_EXPENSE_ISLAND = 29;            //跨岛费
    const REIMBURSEMENT_EXPENSE_FAMILY_VISIT = 41;      //探亲费
    const REIMBURSEMENT_EXPENSE_MORALE = 20;            //员工团建费

    const REIMBURSEMENT_EXPENSE_AD = 21;     //预算对应产品id=广告牌税

    const REIMBURSEMENT_HEADER_WF_ID = 39; // 报销总部审批流ID
    const REIMBURSEMENT_FFM_WF_ID = 44; // 报销FFM-审批流ID
    const REIMBURSEMENT_GGP_WF_ID = 57; // 报销-广告牌审批流ID
    const REIMBURSEMENT_NETWORK_WF_ID = 40; // 报销-network审批流ID
    const REIMBURSEMENT_SHOP_WF_ID = 41; // 报销-门店shop审批流ID
    const REIMBURSEMENT_HUB_WF_ID = 42; // 报销-hub审批流ID
    const REIMBURSEMENT_COMMERCE_WF_ID = 108; // 报销-Commerce子公司审批流ID
    const REIMBURSEMENT_PAY_WF_ID = 109; // 报销-Pay子公司审批流ID
    const REIMBURSEMENT_MONEY_WF_ID = 110; // 报销-Money子公司审批流ID
    const REIMBURSEMENT_DEFAULT_WF_ID = 111; // 报销-非FFM/FC/Pay/Money子公司审批流ID


    const REIMBURSEMENT_INTEL_STORE_WF_ID = 93; //审批流 报销申请 当申请人费用所属网点=header office，走报销网点的审批流
    const REIMBURSEMENT_APPLY_WORKFLOW_NODE_ID = 94; //普通付款>我的申请走-具体网点的时候
    const BUDGET_OBJECT_FLOW_ID = 95;//财务预算审批流ID

    const PURCHASE_ACCEPTANCE_FLOW_ID = 112;//采购验收单审批流ID
    const PURCHASE_SAMPLE_FLOW_ID = 232;//采购样品确认单审批流ID
    const PURCHASE_ACCEPTANCE_FLOW_ID_V1 = 233;//采购样品确认单审批流ID
    const PURCHASE_ACCEPTANCE_FLOW_ID_V2 = 288;//采购验收单 验收类别CCTV安装及拆除项目-HUB

    const CHEQUE_EXAMINE_FLOW_ID = 252;//支票审批流

    /*---报销模块: 报销实质 与 预算科目 对应关系 Start---*/
    // 报销实质
    const REIMBURSEMENT_TYPE_OTHER = 0; // 其他
    const REIMBURSEMENT_TYPE_WELFARE_OR_MORALE = 1; // 福利费/团建费
    const REIMBURSEMENT_TYPE_EXTERNAL_COURIER = 2; // 劳务成本-外协
    const REIMBURSEMENT_TYPE_TAX = 3;              //网点类型为shop的税金且详情是广告牌税=22---限制的比较死
    public static $reimbursement_type_items = [
        self::REIMBURSEMENT_TYPE_OTHER => 'reimbursement_type_0',
        self::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE => 'reimbursement_type_1',
        self::REIMBURSEMENT_TYPE_EXTERNAL_COURIER => 'reimbursement_type_2',
        self::REIMBURSEMENT_TYPE_TAX    => 'reimbursement_type_3'
    ];
    // 预算科目
    const BUDGET_OBJECT_WELFARE_CODE = '008'; // 员工福利费level_code
    const BUDGET_OBJECT_MORALE_CODE = '009'; // 员工团建费
    const BUDGET_OBJECT_EXTERNAL_COURIER_CODE = '007'; // 劳务成本-外协
    const BUDGET_OBJECT_TAX_CODE = '005';       //税金及附加

    // 对应关系
    public static $budget_object_reimbursement_type_map = [
        self::BUDGET_OBJECT_WELFARE_CODE => self::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE,
        self::BUDGET_OBJECT_MORALE_CODE => self::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE,
        self::BUDGET_OBJECT_EXTERNAL_COURIER_CODE => self::REIMBURSEMENT_TYPE_EXTERNAL_COURIER,
        self::BUDGET_OBJECT_TAX_CODE => self::REIMBURSEMENT_TYPE_TAX
    ];

    //预算科目id
    const BUDGET_OBJECT_OUT_WORKER_ID = 6; // 劳务成本-外包
    const BUDGET_OBJECT_EXTERNAL_COURIER_ID = 7; // 劳务成本-外协


    //错误提示对应关系，也可以用00 $i+1
    public static $reimbursement_type_error = [
        self::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE => 'reimbursement_type_error_002',
        self::REIMBURSEMENT_TYPE_EXTERNAL_COURIER => 'reimbursement_type_error_003',
        self::REIMBURSEMENT_TYPE_TAX    => 'reimbursement_type_error_004'
    ];

    /*---报销模块: 报销实质 与 预算科目 对应关系 End---*/

    /*-----附件类型-----*/
    const OSS_BUCKET_TYPE_LOAN = 1;// 借款
    const OSS_BUCKET_TYPE_REIMBURSEMENT = 2;        //报销
    const OSS_BUCKET_TYPE_PURCHASE_ORDER = 3;       //采购订单
    const OSS_BUCKET_TYPE_PURCHASE_PAYMENT = 4;     //采购付款单
    const OSS_BUCKET_TYPE_CONTRACT_FILE =5;         //合同管理-合同文件
    const OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT =6;   //合同管理-合同附件
    const OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE = 7;    //合同归档-合同文件
    const OSS_BUCKET_TYPE_PURCHASE_APPLY = 8;     //采购付款申请单
    const OSS_BUCKET_TYPE_WAGES_PAYMENT = 9;
    const OSS_BUCKET_TYPE_WAGES_ITEM = 10;
    const OSS_BUCKET_TYPE_FRY_ATTACH = 11;
    const OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT = 12; // 网点租房付款申请
    const OSS_BUCKET_TYPE_ORDINARYPAYMENT = 13; // 普通付款

    const OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE = 14;//取数工单数据文件
    const OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE = 15;//取数工单数据文件

    const OSS_BUCKET_TYPE_RESERVE_FUND_APPLY = 16;     //备用金申请单
    const OSS_BUCKET_TYPE_RESERVE_FUND_RETURN = 17;     //备用金归还申请单
    const OSS_BUCKET_TYPE_RESERVE_FUND_RETURN_BANK_FLOW = 18;     //备用金归还申请单-银行流水

    const OSS_BUCKET_TYPE_LOAN_BACK = 19;      //借款归还

    const OSS_BUCKET_TYPE_VENDOR_BACK = 21;      //供应商
    const OSS_BUCKET_TYPE_ACCESS_DATA_FORM = 22; //取数工单表单

    const OSS_BUCKET_TYPE_ORDINARYPAYMENT_MAIN = 20;        //普通付款主表

    const OSS_BUCKET_TYPE_PAY_MODULE = 23;      //付款模块附件
    const OSS_BUCKET_TYPE_PAY_MODULE_SUPPLEMENT = 59;      //付款模块补充附件


    const OSS_BUCKET_TYPE_BUDGET_SOURCE_DATA = 26;//预算源数据附件
    const OSS_BUCKET_TYPE_BUDGET_IMPORT_OSS = 62;//预算导入-导入-附件

    const OSS_BUCKET_TYPE_BUDGET_ADJUST_ADD = 24;      //预算调整业务添加附件
    const OSS_BUCKET_TYPE_BUDGET_ADJUST_REJECT = 25;      //预算审核业务驳回附件
    const OSS_BUCKET_TYPE_BUDGET_ADJUST_ADD_MAIN = 34;      //预算调整业务主数据附件
    const OSS_BUCKET_TYPE_BUDGET_WITHHOLDING_ADD = 72;  //预算管理-预算预提附件

    const OSS_BUCKET_TYPE_BUDGET_ADJUST_EXCEL = 27;      //预算调整上传的数据文件
    const OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE = 32;      //采购验收单上传的数据文件

    const OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS = 0;//供应商资质附件类型
    const OSS_SUB_TYPE_VENDOR_BACK_BIR_2303 = 1;//供应商bir2303附件类型
    const OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION = 2;//供应商等级认证附件
    const OSS_SUB_TYPE_REGISTRATION_FILE = 3;// 注册文件(公司/个人)
    const OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE = 4;// 银行对账单信头
    const OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE = 5;// 所得税表头(公司/个人)
    const OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE = 6;// SST登记表/SST登记信


    const OSS_BUCKET_TYPE_LOAN_CONSENT = 33;//借款-同意书
    const OSS_BUCKET_TYPE_PURCHASE_SAMPLE = 34;//采购样品确认附件

    const OSS_BUCKET_TYPE_PURCHASE_PAYMENT_SU = 35;//采购付款补充附件

    const OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE_SU = 58;//采购验收单it附件



    const OSS_SUB_TYPE_PURCHASE_SAMPLE_PRODUCT = 1;//采购样品确认-产品需求附件
    const OSS_SUB_TYPE_PURCHASE_SAMPLE_SUPPLY = 2;//采购样品确认-供应商样品附件
    const OSS_SUB_TYPE_PURCHASE_SAMPLE_PICTURE = 3;//采购样品确认-样品照片附件
    const OSS_SUB_TYPE_PURCHASE_SAMPLE_TEST = 4;//采购样品确认-样品测试报告附件

    const OSS_PAYMENT_STORE_TYPE_CHEQUE_APPLY_ADD = 50;      //支票申请日志附件
    const OSS_REPLACE_INFO_TYPE_CHEQUE_APPLY_ADD = 51;      //支票申请替换日志附件
    const OSS_ABOLISH_INFO_TYPE_CHEQUE_APPLY_ADD = 52;      //支票申请作废日志附件

    const OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_ADD = 40;      //押金管理-普通付款-归还附件 归还表主键id
    const OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_EDIT_LOG_ADD = 41;  //押金管理-普通付款-操作日志附件 log主键

    const OSS_REIMBURSEMENT_TYPE_DEPOSIT_ADD = 42;      //押金管理-报销-归还附件
    const OSS_REIMBURSEMENT_TYPE_DEPOSIT_EDIT_LOG_ADD = 43;      //押金管理-报销-操作日志附件

    const OSS_PURCHASE_PAYMENT_TYPE_DEPOSIT_ADD = 44;      //押金管理-采购-归还附件
    const OSS_PURCHASE_PAYMENT_TYPE_DEPOSIT_EDIT_LOG_ADD = 45;      //押金管理-采购-操作日志附件

    const OSS_PAYMENT_STORE_TYPE_DEPOSIT_ADD = 46;      //押金管理-租房-归还附件
    const OSS_PAYMENT_STORE_TYPE_DEPOSIT_EDIT_LOG_ADD = 47;      //押金管理-租房-操作日志附件

    const OSS_ADMINISTRATION_TICKET_HANDLE_ADD = 53;      //行政工单回复上传图片

    const OSS_MATERIAL_TYPE_WMS_APPLY = 54; //耗材申请附件

    const OSS_BUCKET_TYPE_GPMD_CONTRACT_FILE = 55;         //GPMD合同管理-合同文件
    const OSS_BUCKET_TYPE_GPMD_CONTRACT_ATTACHMENT = 56;   //GPMD合同管理-合同附件

    const OSS_BUCKET_TYPE_WORKFLOW_ATTACHMENT = 57;//审批日志-附件
    const OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT_SUPPLEMENT = 60; // 网点租房付款补充附件

    const OSS_BUCKET_TYPE_AGENCY_PAYMENT_ATTACHMENT = 63;//代理支付-附件
    const OSS_BUCKET_TYPE_AGENCY_PAYMENT_SUPPLEMENT_ATTACHMENT = 64;//代理支付-补充附件

    // 电子合同-甲方上传的相关资料
    const OSS_BUCKET_TYPE_ELECTRONIC_CONTRACT_CLIENT_RELATED_FILE = 65;

    /*** 仓库管理-附件枚举 ***/
    const OSS_BUCKET_TYPE_WAREHOUSE_REQUIREMENT_INFO_FILE = 76;//仓库管理-仓库需求管理-添加-附件
    const OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE = 67;//仓库管理-仓库线索管理-平面图-附件
    const OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE = 68;//仓库管理-仓库线索管理-附件
    const OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_DOOR_FILE = 69;//仓库管理-仓库线索管理-验仓结果-大门照片
    const OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_ROAD_FILE = 70;//仓库管理-仓库线索管理-验仓结果-门口道路照片
    const OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE = 71;//处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-附件
    const OSS_SUB_TYPE_WAREHOUSE_THREAD_PRICE = 0;//处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-子类型-本次报价附件
    const OSS_SUB_TYPE_WAREHOUSE_THREAD_LAST_PRICE = 1;//处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-子类型-上次报价附件




    //附件上传批量分类id
    public static $oss_deposit_type_add = [
        '1' => [
            'mian' => self::OSS_REIMBURSEMENT_TYPE_DEPOSIT_ADD, 'log' => self::OSS_REIMBURSEMENT_TYPE_DEPOSIT_EDIT_LOG_ADD],
        '2' => [
            'mian' => self::OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_ADD, 'log' => self::OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_EDIT_LOG_ADD],

        '3' => ['mian' => self::OSS_PURCHASE_PAYMENT_TYPE_DEPOSIT_ADD, 'log' => self::OSS_PURCHASE_PAYMENT_TYPE_DEPOSIT_EDIT_LOG_ADD],

        '4' => ['mian' => self::OSS_PAYMENT_STORE_TYPE_DEPOSIT_ADD, 'log' => self::OSS_PAYMENT_STORE_TYPE_DEPOSIT_EDIT_LOG_ADD],

    ];
    /********************租房合同语言************************************/
    const LANG_ZH = 3;
    const LANG_EN = 2;
    const LANG_TH = 1;
    public static $langEnumsTxt = [
        self::LANG_ZH => '中文',
        self::LANG_EN => ' English',
        self::LANG_TH => 'ภาษาไทย',
    ];
    const RENT_BY_MONTH = 1;  //(1.月租,2.年租 3季租4半年)
    const RENT_BY_YEAR = 2;
    const RENT_BY_SEASON = 3;
    const RENT_BY_HALF_YEAR= 4;
    public static $rent_by_items = [
        self::RENT_BY_MONTH => 'rent_by_month',
        self::RENT_BY_YEAR => ' rent_by_year',
        self::RENT_BY_SEASON => 'rent_by_season',
        self::RENT_BY_HALF_YEAR => 'rent_by_half_year',
    ];

    const CONTRACT_CONTRACT_TYPE_COMPANY = 1;
    const CONTRACT_CONTRACT_TYPE_PERSION = 2;
    const CONTRACT_CONTRACT_TYPE_AGENT = 3;

    public static $contactTypeEmunsTxt = [
        self::CONTRACT_CONTRACT_TYPE_COMPANY => '公司',
        self::CONTRACT_CONTRACT_TYPE_PERSION => '个人',
        self::CONTRACT_CONTRACT_TYPE_AGENT => '中介',
    ];


    const DEPARTMENT_ID_IT_PRODUCTS = 6;    //IT&Product部门ID
    const DEPARTMENT_ID_HRBP = 49;          //HR Business Partner
    const DEPARTMENT_ID_TA = 50;            //Talent Acquisition
    const DEPARTMENT_ID_BP = 168;           //Business Partner

    const HC_BUDGET_LIST_ALL_STAFF_INFO_ID = [56207, 55578];

    //部门架构 常亮设置
    const CEO_ID = 1;//group_boss表中ceo的ID值
    const COO_ID = 2;//group_boss表中coo的ID值
    const CFO_ID = 3;//group_boss表中cfo的ID值
    const CPO_ID = 4;//group_boss表中cpo的ID值
    const TYPE_COMPANY_DEPARTMENT = 2;//公司类型部门
    const TYPE_GROUP_DEPARTMENT = 3;//group 类型部门
    const TYPE_GROUP_CEO = 5;//ceo 级别
    const TYPE_GROUP_BOSS = 4;//gropu boss 基本
    const TYPE_COMPANY = 1;//公司

    // 网点租房合同审批流业务号
    const CONTRACT_STORE_RENTING_NOT_FFM_WF_ID = 70;
    const CONTRACT_STORE_RENTING_FFM_WF_ID = 65;
    const CONTRACT_STORE_RENTING_LOI_NETWORK_WF_ID = 77;
    const CONTRACT_STORE_RENTING_LOI_HUB_WF_ID = 78;
    const CONTRACT_STORE_RENTING_LOI_OTHER_WF_ID = 79;
    //  租房合同续签
    const CONTRACT_STORE_RENTING_NOT_FFM_RENEWAL_WF_ID = 268;
    const CONTRACT_STORE_RENTING_FFM_RENEWAL_WF_ID = 272;
    const CONTRACT_STORE_RENTING_LOI_NETWORK_RENEWAL_WF_ID = 269;
    const CONTRACT_STORE_RENTING_LOI_HUB_RENEWAL_WF_ID = 270;
    const CONTRACT_STORE_RENTING_LOI_OTHER_RENEWAL_WF_ID = 271;

    const CONTRACT_STORE_RENTING_LOI_NETWORK_WF_1_ID = 340;
    const CONTRACT_STORE_RENTING_NETWORK_WF_ID = 341;
    const CONTRACT_STORE_RENTING_HUB_WF_ID = 342;
    const CONTRACT_STORE_RENTING_OTHER_WF_ID = 343;

    const CONTRACT_STORE_RENTING_LOI_NETWORK_RENEWAL_WF_1_ID = 380;
    const CONTRACT_STORE_RENTING_NETWORK_RENEWAL_WF_ID = 381;
    const CONTRACT_STORE_RENTING_HUB_WF_RENEWAL_ID = 382;
    const CONTRACT_STORE_RENTING_OTHER_RENEWAL_WF_ID = 383;




    //gpmd 平台合同审批

    const CONTRACT_GPMD_WF_ID = 285;


    // 网点租房合同-作废-审批流
    const CONTRACT_INVALID_WF_ID = 261;
    // 网点租房合同-终止-审批流
    const CONTRACT_TERMINAL_WF_ID = 262;
    // 网点租房合同-作废-loi-审批流
    const CONTRACT_INVALID_LOI_WF_ID = 263;
    // 网点租房合同-终止-loi-审批流
    const CONTRACT_TERMINAL_LOI_WF_ID = 264;
    /**************************** 付款管理 - 网点租房付款审核 Start ***********************************/
    // 网点租房付款审批流业务号
    const PAYMENT_STORE_RENTING_WF_ID = 35;
    const PAYMENT_STORE_RENTING_FFM_FLOW_ID = 66;//FFM 审批流ID
    const PAYMENT_STORE_RENTING_HUB_OTHER_FLOW_ID = 344;//网点租房付款审批流(HUB及其他部门费用) 审批流ID
    const PAYMENT_STORE_RENTING_NW_FLOW_ID = 345;//网点租房付款审批流(费用公司等于快递且NW)审批流ID

    const PAYMENT_STORE_RENTING_NOT_DELIVERY_NOT_CONTRACT_FLOW_ID = 330;//th 费用所属公司为非快递公司 无关联合同
    const PAYMENT_STORE_RENTING_NOT_DELIVERY_CONTRACT_FLOW_ID = 331;//th 费用所属公司为非快递公司 有关联合同
    const PAYMENT_STORE_RENTING_AT_WPM_NOT_CONTRACT_FLOW_ID = 332;//th 申请人属于WPM部门 无关联合同
    const PAYMENT_STORE_RENTING_AT_WPM_CONTRACT_FLOW_ID = 333;//th 申请人属于WPM部门 有关联合同
    const PAYMENT_STORE_RENTING_FME_NOT_CONTRACT_FLOW_ID = 334;//my 其他申请人 无关联合同CPO时
    const PAYMENT_STORE_RENTING_FME_CONTRACT_FLOW_ID = 335;//my 其他申请人 有关联合同
    const PAYMENT_STORE_RENTING_NOT_CPO_FLOW_ID  = 336;//my 费用所属公司为Flash Malaysia Express 申请人不属于快递且不属于CPO时


    // 申请编号前缀
    const PAYMENT_STORE_RENTING_APPLY_NO_PREFIX = 'FK';

    // 申请状态
    const PAYMENT_APPLY_STATUS_PENDING = 1;  // 待审核
    const PAYMENT_APPLY_STATUS_REJECTED = 2; // 已驳回
    const PAYMENT_APPLY_STATUS_APPROVAL = 3; // 已通过
    const PAYMENT_APPLY_STATUS_WITHDRAW = 4; // 已撤回
    public static $payment_apply_status = [
        self::PAYMENT_APPLY_STATUS_PENDING => 'payment_apply_status.1',
        self::PAYMENT_APPLY_STATUS_REJECTED => 'payment_apply_status.2',
        self::PAYMENT_APPLY_STATUS_APPROVAL => 'payment_apply_status.3',
        self::PAYMENT_APPLY_STATUS_WITHDRAW => 'payment_apply_status.4',
    ];

    // 申请状态
    const ACCEPTANCE_APPLY_STATUS_PENDING = 1;  // 待审核
    const ACCEPTANCE_APPLY_STATUS_REJECTED = 2; // 已驳回
    const ACCEPTANCE_APPLY_STATUS_APPROVAL = 3; // 已通过
    const ACCEPTANCE_APPLY_STATUS_WITHDRAW = 4; // 已撤回
    public static $acceptance_apply_status = [
        self::ACCEPTANCE_APPLY_STATUS_PENDING => 'acceptance_apply_status.1',
        self::ACCEPTANCE_APPLY_STATUS_REJECTED => 'acceptance_apply_status.2',
        self::ACCEPTANCE_APPLY_STATUS_APPROVAL => 'acceptance_apply_status.3',
        self::ACCEPTANCE_APPLY_STATUS_WITHDRAW => 'acceptance_apply_status.4',
    ];

    // 支付状态
    const PAYMENT_PAY_STATUS_PENDING = 1; // 待支付
    const PAYMENT_PAY_STATUS_PAY = 2;  // 已支付
    const PAYMENT_PAY_STATUS_NOTPAY = 3; // 未支付
    const PAYMENT_PAY_STATUS_ING = 4; // 支付中
    const PAYMENT_PAY_STATUS_PART_PAY = 99; // 部分支付，只针对租房付款
    public static $payment_pay_status = [
        self::PAYMENT_PAY_STATUS_PENDING => 'payment_pay_status.1',
        self::PAYMENT_PAY_STATUS_PAY => 'payment_pay_status.2',
        self::PAYMENT_PAY_STATUS_NOTPAY => 'payment_pay_status.3',
        self::PAYMENT_PAY_STATUS_ING => 'payment_pay_status.4'
    ];


    // 付款方式: 全局配置, 修改请慎重, 参见 GlobalEnums; 若修改，请修改 GlobalEnums 配置
    // 新的业务模块，可以直接调用 GlobalEnums 的配置
    const PAYMENT_METHOD_CASH = GlobalEnums::PAYMENT_METHOD_CASH; // 现金 预留
    const PAYMENT_METHOD_BANK_TRANSFER = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER; // 银行转账 默认
    const PAYMENT_METHOD_CHECK = GlobalEnums::PAYMENT_METHOD_CHECK; // 支票
    public static $payment_method = [
        self::PAYMENT_METHOD_CASH => 'global.payment.method.1',
        self::PAYMENT_METHOD_BANK_TRANSFER => 'global.payment.method.2',
        self::PAYMENT_METHOD_CHECK => 'global.payment.method.3',
    ];
    // 付款方式默认项
    public static $payment_method_default = [
        self::PAYMENT_METHOD_BANK_TRANSFER
    ];

    // 付款币种
    public static $payment_currency_default = [
        GlobalEnums::CURRENCY_THB
    ];

    // 合同状态
    const PAYMENT_CONTRACT_STATUS_Y = 1;
    const PAYMENT_CONTRACT_STATUS_N = 0;
    public static $payment_contract_status = [
        self::PAYMENT_CONTRACT_STATUS_Y => 'payment_contract_status_1',
        self::PAYMENT_CONTRACT_STATUS_N => 'payment_contract_status_0',
    ];

    // 金额详情上传模板下载key
    const PAYMENT_STORE_RENTING_DETAIL_UPLOAD_TEMPLATE_KEY = 'payment_store_renting_upload_template_';
    // 预算调整模板
    const BUDGET_ADJUST_UPLOAD_TEMPLATE_KEY = 'budget_adjust_upload_template_';

    // 费用所属网点
    const PAYMENT_COST_STORE_TYPE_01 = 1;
    const PAYMENT_COST_STORE_TYPE_02 = 2;
    public static $payment_cost_store_type = [
        self::PAYMENT_COST_STORE_TYPE_01 => 'payment_cost_store_type_1',// 总部
        self::PAYMENT_COST_STORE_TYPE_02 => 'payment_cost_store_type_2',// 网点
    ];

    //费用所属网点针对报销是返的做兼容处理
    public static $payment_cost_store_type_try = [
        self::PAYMENT_COST_STORE_TYPE_02 => 'payment_cost_store_type_1',// 总部
        self::PAYMENT_COST_STORE_TYPE_01 => 'payment_cost_store_type_2',// 网点
    ];

    // 总部网点名称
    const PAYMENT_HEADER_STORE_ID = 'TH00000001';
    const PAYMENT_HEADER_STORE_NAME = 'Head Office';
    const PAYMENT_BRANCH_STORE_NAME = 'Branch';
    const HEAD_OFFICE_STORE_FLAG = -1;// 网点为总部的标识, 对应hr_staff_info 表的sys_store_id字段值

    // 会计科目规则配置中的所属总部/网点, 枚举值与业务模块所属总部/网点保持一致, 枚举label无需走翻译, 涉及逻辑
    public static $organization_type_map = [
        self::PAYMENT_COST_STORE_TYPE_01 => self::PAYMENT_HEADER_STORE_NAME,
        self::PAYMENT_COST_STORE_TYPE_02 => self::PAYMENT_BRANCH_STORE_NAME,
    ];

    // 支付人工号配置key
    const PAYMENT_PAY_STAFF_IDS_KEY = 'store_renting_payment_pay_staff_id';


    /**************************** 付款管理 - 网点租房付款审核 End ***********************************/

    /**************************** 普通付款 strat ******************************************/
    // 网点租房付款审批流业务号
    const ORDINARY_PAYMENT_WF_ID = 38;

    //普通付款 网点 审批流业务号

    const ORDINARY_PAYMENT_NETWORK_ID = 51;
    const ORDINARY_PAYMENT_SHOP_ID = 52;
    const ORDINARY_PAYMENT_HUB_ID = 53;
    const ORDINARY_PAYMENT_SHOP_TAX_ID= 83;//门店类型税金且费用类型广告牌税
    const ORDINARY_PAYMENT_FFM = 54;
    const ORDINARY_PAYMENT_FC = 55;
    const ORDINARY_PAYMENT_PAY = 56;
    const ORDINARY_PAYMENT_LA = 80;
    const ORDINARY_PAYMENT_MONEY = 107;

    // 申请编号前缀
    const ORDINARY_PAYMENT_APPLY_NO_PREFIX = 'PTFK';

    // 支付人工号配置key
    const ORDINARY_PAYMENT_PAY_STAFF_IDS_KEY = 'ordinary_payment_pay_staff_id';

    // 支付状态：1-待支付; 2-已支付; 3-未支付
    const ORDINARY_PAYMENT_STATUS_PENDING = 1; //待支付
    const ORDINARY_PAYMENT_STATUS_PAY = 2;  //已支付
    const ORDINARY_PAYMENT_STATUS_NOTPAY = 3; //未支付

    // 支付状态语言包
    public static $ordinary_payment_pay_status = [
        self::ORDINARY_PAYMENT_STATUS_PENDING => 'ordinary_payment_pay_status.1',
        self::ORDINARY_PAYMENT_STATUS_PAY => 'ordinary_payment_pay_status.2',
        self::ORDINARY_PAYMENT_STATUS_NOTPAY => 'ordinary_payment_pay_status.3',
    ];

    // 审核状态: 1-待审核; 2-已驳回; 3-已通过; 4-已撤回
    const ORDINARY_PAYMENT_APPROVAL_STATUS_PENDING = 1;  //待审核
    const ORDINARY_PAYMENT_APPROVAL_STATUS_REJECTED = 2; //已驳回
    const ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED = 3; //已通过
    const ORDINARY_PAYMENT_APPROVAL_STATUS_WITHDRAW = 4; //已撤回

    // 收款人类型
    const PAYEE_TYPE_VENDOR = 1; //供应商
    const PAYEE_TYPE_PERSONAL = 2; //个人
    // 审核状态语言包
    public static $ordinary_payment_approval_status = [
        self::ORDINARY_PAYMENT_APPROVAL_STATUS_PENDING => 'ordinary_payment_approval_status.1',
        self::ORDINARY_PAYMENT_APPROVAL_STATUS_REJECTED => 'ordinary_payment_approval_status.2',
        self::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED => 'ordinary_payment_approval_status.3',
        self::ORDINARY_PAYMENT_APPROVAL_STATUS_WITHDRAW => 'ordinary_payment_approval_status.4',
    ];

    // 付款分类&费用类型
    public static $ordinary_payment_category = [
        ['label'=>'ordinary_payment_category_1', 'id'=>1, 'tax_item'=>[]], //自有车队-住宿费
        ['label'=>'ordinary_payment_category_2', 'id'=>2, 'tax_item'=>[]], //自有车队-洗车费
        ['label'=>'ordinary_payment_category_3', 'id'=>3, 'tax_item'=>[]], //自有车队-GPS/PPE等
        ['label'=>'ordinary_payment_category_4', 'id'=>4, 'tax_item'=>[]], //自有车队-车辆年税
        ['label'=>'ordinary_payment_category_5', 'id'=>5, 'tax_item'=>[]], //自有车队-车险
        ['label'=>'ordinary_payment_category_6', 'id'=>6, 'tax_item'=>[]], //自有车队-车辆维修/保养
        ['label'=>'ordinary_payment_category_7', 'id'=>7, 'tax_item'=>[]], //专业服务-咨询费
        ['label'=>'ordinary_payment_category_8', 'id'=>8, 'tax_item'=>[]], //专业服务-审计费
        ['label'=>'ordinary_payment_category_9', 'id'=>9, 'tax_item'=>[]], //专业服务-融资顾问费
        ['label'=>'ordinary_payment_category_10', 'id'=>10, 'tax_item'=>[]], //专业服务-律师费
        ['label'=>'ordinary_payment_category_11', 'id'=>11, 'tax_item'=>[]], //招聘费
        ['label'=>'ordinary_payment_category_12', 'id'=>12, 'tax_item'=>[]], //油费
        ['label'=>'ordinary_payment_category_13', 'id'=>13, 'tax_item'=>[]], //销售分佣
        ['label'=>'ordinary_payment_category_14', 'id'=>14,               //税金及附加
            'tax_item'=>[
                ['label'=>'ordinary_payment_category_14_1','id'=>1], //税金及附加-营业税
                ['label'=>'ordinary_payment_category_14_2','id'=>2], //税金及附加-雇佣税
                ['label'=>'ordinary_payment_category_14_3','id'=>3], //税金及附加-土地税
                ['label'=>'ordinary_payment_category_14_4','id'=>4], //税金及附加-车牌税
                ['label'=>'ordinary_payment_category_14_5','id'=>5], //税金及附加-房产税
                ['label'=>'ordinary_payment_category_14_6','id'=>6], //税金及附加-PND1
                ['label'=>'ordinary_payment_category_14_7','id'=>7], //税金及附加-PND3
                ['label'=>'ordinary_payment_category_14_8','id'=>8], //税金及附加-PND53
                ['label'=>'ordinary_payment_category_14_9','id'=>9], //税金及附加-PP30
                ['label'=>'ordinary_payment_category_14_10','id'=>10], //税金及附加-PP36
                ['label'=>'ordinary_payment_category_14_11','id'=>11], //税金及附加-PND54
            ]
        ],
        ['label'=>'ordinary_payment_category_15', 'id'=>15, 'tax_item'=>[]], //水电费
        ['label'=>'ordinary_payment_category_16', 'id'=>16,               // 签证管理费
            'tax_item'=>[
                ['label'=>'ordinary_payment_category_16_1','id'=>1], //签证管理费-签证费
            ]
        ],
        ['label'=>'ordinary_payment_category_17', 'id'=>17, 'tax_item'=>[]], //公关传播
        ['label'=>'ordinary_payment_category_18', 'id'=>18, 'tax_item'=>[]], //员工福利费
        ['label'=>'ordinary_payment_category_19', 'id'=>19, 'tax_item'=>[]], //电话费
        ['label'=>'ordinary_payment_category_20', 'id'=>20, 'tax_item'=>[]], //第三方运费-支干线
        ['label'=>'ordinary_payment_category_21', 'id'=>21, 'tax_item'=>[]], //仓储费

    ];
    /**************************** 普通付款 end ******************************************/


    /**************************** 薪资发放审批 start ******************************************/

    const SALARY_TYPE_MONEY = 1; //薪资
    const SALARY_TYPE_COMMISSION = 2;  //提成
    public static $salary_type = [
        self::SALARY_TYPE_MONEY => 'salary_type.1',
        self::SALARY_TYPE_COMMISSION => 'salary_type.2',
    ];

    const SALARY_PAY_TYPE_TRANSFER = 1; //付款方式-转账
    const SALARY_PAY_TYPE_CASH = 2;     //付款方式-现金
    public static $salary_pay_type = [
        self::SALARY_PAY_TYPE_TRANSFER => 'salary_pay_type.1',
        self::SALARY_PAY_TYPE_CASH => 'salary_pay_type.2',
    ];

    //薪资发放公司ID
    public static $salary_company_list = [
        1 => 'Flash Express',
        2 => 'Flash Fulfillment',
        3 => 'Flash Pay',
        4 => 'Flash Money',
        5 => 'F Commerce',
    ];

    //TH=薪资发放公司ID
    public static $salary_company_list_th = [
        1 => 'Flash Express',
        2 => 'Flash Fulfillment',
        3 => 'Flash Pay',
        4 => 'Flash Money',
        5 => 'F Commerce',
        6 => 'Flash Home Operation',
        7 => 'Flash Incorporation',
    ];

    //薪资发放公司ID与部门ID的映射
    public static $salary_company_map = [
        1 => 1,     //Flash Express
        2 => 20001, //Flash Fulfillment
        3 => 60001, //Flash Pay
        4 => 30001, //Flash Money
        5 => 50001, //F Commerce
        6 => 1222,  //Flash Home Operation
    ];

    /**************************** 薪资发放审批 end ******************************************/
    //转岗类型
    const JOB_TRANSFER_TYPE_IN_DEP  = 1; //部门内转岗
    const JOB_TRANSFER_TYPE_OUT_DEP = 2; //跨部门转岗
    public static $job_transfer_type = [
        self::JOB_TRANSFER_TYPE_IN_DEP  => 'job_transfer_type.1', //部门内转岗
        self::JOB_TRANSFER_TYPE_OUT_DEP => 'job_transfer_type.2', //跨部门转岗
    ];

    //转岗状态
    const JOB_TRANSFER_STATE_PENDING = 1;   //待转岗
    const JOB_TRANSFER_STATE_NO = 2;        //未转岗
    const JOB_TRANSFER_STATE_SUCCESS = 3;   //转岗成功
    const JOB_TRANSFER_STATE_FAIL = 4;      //转岗失败
    public static $job_transfer_state = [
        self::JOB_TRANSFER_STATE_PENDING => 'job_transfer_state.1', //待转岗
        self::JOB_TRANSFER_STATE_NO      => 'job_transfer_state.2', //未转岗
        self::JOB_TRANSFER_STATE_SUCCESS => 'job_transfer_state.3', //转岗成功
        self::JOB_TRANSFER_STATE_FAIL    => 'job_transfer_state.4', //转岗失败
    ];

    //转岗审核状态
    const BY_APPROVAL_STATUS_PENDING = 1;
    const BY_APPROVAL_STATUS_APPROVAL = 2;
    const BY_APPROVAL_STATUS_REJECT = 3;
    const BY_APPROVAL_STATUS_CANCEL = 4;
    const BY_APPROVAL_STATUS_OVER_TIME = 5;

    //车辆类型
    const CAR_OWNER_PERSONAL_STATUS = 1; //使用个人车辆
    const CAR_OWNER_COMPANY_STATUS = 2; //租用公司车辆
    const CAR_OWNER_BORROW_STATUS = 3;//借用车辆

    //数据来源
    const DATA_FROM_BY = 1;
    const DATA_FROM_OA = 2;
    public static $job_transfer_source = [
        self::DATA_FROM_BY => 'BY',// Backyard系统
        self::DATA_FROM_OA => 'OA',// OA系统
    ];

    // 基础数据获取类型
    const FETCH_DATA_LIST = 1;   // 列表页
    const FETCH_DATA_EXPORT = 2; // 导出excel
    const FETCH_DATA_DETAIL = 3; // 查看详细

    /*组织架构-职位体系 start*/

    //职级
    public static $job_level = [
        ['id'=>'0' ,'name'=>'F0' ],
        ['id'=>'12','name'=>'F12'],
        ['id'=>'13','name'=>'F13'],
        ['id'=>'14','name'=>'F14'],
        ['id'=>'15','name'=>'F15'],
        ['id'=>'16','name'=>'F16'],
        ['id'=>'17','name'=>'F17'],
        ['id'=>'18','name'=>'F18'],
        ['id'=>'19','name'=>'F19'],
        ['id'=>'20','name'=>'F20'],
        ['id'=>'21','name'=>'F21'],
        ['id'=>'22','name'=>'F22'],
        ['id'=>'23','name'=>'F23'],
        ['id'=>'24','name'=>'F24'],
    ];

    //工作天数&轮休规则
    const WORKING_DAY_REST_TYPE_51 = 51;
    const WORKING_DAY_REST_TYPE_52 = 52;
    const WORKING_DAY_REST_TYPE_61 = 61;
    const WORKING_DAY_REST_TYPE_62 = 62;
    const WORKING_DAY_REST_TYPE_91 = 91;
    public static $working_day_rest_type = [
        self::WORKING_DAY_REST_TYPE_51 => 'working_day_rest_type_51',
        self::WORKING_DAY_REST_TYPE_52 => 'working_day_rest_type_52',
        self::WORKING_DAY_REST_TYPE_61 => 'working_day_rest_type_61',
        self::WORKING_DAY_REST_TYPE_62 => 'working_day_rest_type_62',
    ];

    public static $all_working_day_rest_type = [
        self::WORKING_DAY_REST_TYPE_51 => 'working_day_rest_type_51',
        self::WORKING_DAY_REST_TYPE_52 => 'working_day_rest_type_52',
        self::WORKING_DAY_REST_TYPE_61 => 'working_day_rest_type_61',
        self::WORKING_DAY_REST_TYPE_62 => 'working_day_rest_type_62',
        self::WORKING_DAY_REST_TYPE_91 => 'working_day_rest_type_91',
    ];


    /*组织架构-职位体系 end*/

    /********转岗状态********/
    const JOBTRANSFER_STATE_TO_BE_TRANSFERED = 1; //待转岗
    const JOBTRANSFER_STATE_NOT_TRANSFERED   = 2; //未转岗
    const JOBTRANSFER_STATE_TRANSFERED       = 3; //已转岗
    const JOBTRANSFER_STATE_TRANSFERE_ERR    = 4; //转岗失败

    /**************************** 支付模块 start -- 16325需求已经挪到payEnums中，后续会删掉，不要引用这里的额常量了******************************************/
    const PAY_WHERE_EMPTY = 0; //空
    const PAY_WHERE_IN = 1;    //境内
    const PAY_WHERE_OUT= 2;    //境外

    public static $pay_where_id_to_lang_key = [
        self::PAY_WHERE_EMPTY => 'pay_where.0',
        self::PAY_WHERE_IN      => 'pay_where.1',
        self::PAY_WHERE_OUT => 'pay_where.2'
    ];

    const PAYMENT_MODULE_PAY_STATUS_PENDING = 1; // 待支付
    const PAYMENT_MODULE_PAY_STATUS_PAY = 2;  // 已支付
    const PAYMENT_MODULE_PAY_STATUS_NOTPAY = 3; // 未支付
    const PAYMENT_MODULE_PAY_STATUS_ING = 4; // 支付中
    const PAYMENT_MODULE_PAY_STATUS_FAILED = 5; // 支付失败
    const PAYMENT_MODULE_PAY_STATUS_BANKING = 6; // 银行支付中
    public static $payment_module_pay_status = [
        self::PAYMENT_MODULE_PAY_STATUS_PENDING => 'payment_pay_status.1',
        self::PAYMENT_MODULE_PAY_STATUS_PAY => 'payment_pay_status.2',
        self::PAYMENT_MODULE_PAY_STATUS_NOTPAY => 'payment_pay_status.3',
        self::PAYMENT_MODULE_PAY_STATUS_ING => 'payment_pay_status.4',
        self::PAYMENT_MODULE_PAY_STATUS_FAILED => 'payment_pay_status.5',
        self::PAYMENT_MODULE_PAY_STATUS_BANKING => 'payment_pay_status.6'
    ];

    //是否支付
    const IS_PAY_YES = 1; //是
    const IS_PAY_NO = 2; //否
    public static $payment_is_pay_key = [
        self::IS_PAY_YES => 'payment_is_pay_yes',
        self::IS_PAY_NO => 'payment_is_pay_no',
    ];
    /**************************** 支付模块 end ******************************************/

    /**************************** 报价单模块 start ******************************************/
    const CONSUMER_TYPE_XIAOC = 1;
    const CONSUMER_TYPE_KA = 2;
    const CONSUMER_TYPE_CRM = 3;

    const SETTLEMENT_TYPE_SPOT = 1;
    const SETTLEMENT_TYPE_REGULAR = 2;

    const DEPARTMENT_TYPE_SALES = 1;
    const DEPARTMENT_TYPE_PMD = 2;
    const DEPARTMENT_TYPE_SHOP = 3;
    const DEPARTMENT_TYPE_NETWORK = 4;
    const DEPARTMENT_TYPE_NETWORK_BULKY = 7;
    const DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT = 8;
    const DEPARTMENT_TYPE_FLASH_HOME = 9;
    const DEPARTMENT_TYPE_USER = 10; // network下的虚拟出的user 部门
    const DEPARTMENT_TYPE_JVB = 11; // JVB Operations部门


    const MS_CONSUMER_TYPE_KA = 1; //KA
    const MS_CONSUMER_TYPE_COD = 2; //COD账户
    const MS_CONSUMER_TYPE_SON = 3; //分销商子客户
    /**************************** 报价单模块 end ******************************************/

    //审批流可视化 start

    const WORKFLOW_VALID = 1;   //审批流启用
    const WORKFLOW_INVALID = 2; //审批流停用
    const WORKFLOW_NOT_DELETED = 0; //审批流没有删除
    const WORKFLOW_DELETED = 0; //审批流已删除

    //节点类型：跟前端约定枚举
    const NODE_TYPE_INIT  = 1; //发起节点
    const NODE_TYPE_BRANCH  = 2; //条件分支
    const NODE_TYPE_COND  = 3; //条件节点
    const NODE_TYPE_AUDIT  = 4; //审批节点
    const NODE_TYPE_CC  = 5; //抄送节点
    const NODE_TYPE_FINAL  = 99; //结束节点

    //前端传参
    const FRONTEND_NODE_T_OR_SIGN = 1;  //或签审批节点
    const FRONTEND_NODE_T_COUNTERSIGN = 2;  //会签审批节点

    //后端定义节点类型
    const BACKEND_NODE_T_INIT = 0; //发起节点
    const BACKEND_NODE_T_OR_SIGN  = 1; //或签审批节点
    const BACKEND_NODE_T_CC = 2;  //抄送节点
    const BACKEND_NODE_T_COUNTERSIGN = 3; //会签审批节点
    const BACKEND_NODE_T_FINAL = 99; //结束节点

    //审批流可视化配置

    const NODE_AUDITOR_TYPE_SPECIFY_STAFF = 1; //指定员工
    const NODE_AUDITOR_TYPE_SPECIFY_SUPERIOR = 2; //指定上级
    const NODE_AUDITOR_TYPE_CONT_SUPERIOR = 3; //连续上级
    const NODE_AUDITOR_TYPE_ORG_MANAGER = 4; //指定部门负责人
    const NODE_AUDITOR_TYPE_SPECIFY_ROLE = 5; //申请人角色
    const NODE_AUDITOR_TYPE_SPECIFY_POSITION = 6; //申请人职位
    const NODE_AUDITOR_TYPE_STORE_MANAGER = 7; //网点负责人
    const NODE_AUDITOR_TYPE_FORM = 8; //表单项相关
    const NODE_AUDITOR_TYPE_ORG_AREA_MANAGER = 9; //组织区域负责人
    const NODE_AUDITOR_TYPE_SPEC_ORG_MGR = 10; //指定职能负责人
    const NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR = 11; //指定组织负责人
    const NODE_AUDITOR_TYPE_BU_CL_MGR = 12; //申请人BU/Clevel负责人
    const NODE_AUDITOR_TYPE_HASH_TABLE = 13; //[废弃]根据哈希表查找指定审批人（目前用来配置分拨大区经理）
    const NODE_AUDITOR_TYPE_HUB_STANDARDIZATION_AND_ADMIN = 14; //分拨标准化or行政
    const NODE_AUDITOR_TYPE_HUB_AREA_MANAGER = 15; //分拨大区经理
    const NODE_AUDITOR_TYPE_HUB_STORE_MANAGER = 16; //分拨经理
    const NODE_AUDITOR_TYPE_STAFF_SELF = 17; //员工本人
    const NODE_AUDITOR_TYPE_COMPLEX = 99; //存在多个节点逻辑复合

    //条件类型-公用条件
    const AUDIT_COND_SUBMITTER_DEPARTMENT = 1; //申请人所属部门
    const AUDIT_COND_SUBMITTER_JOB_TITLE  = 2; //申请人职位
    const AUDIT_COND_SUBMITTER_JOB_GRADE  = 3; //申请人职级
    const AUDIT_COND_SUBMITTER_SEX        = 4; //申请人性别
    const AUDIT_COND_SUBMITTER_STATE      = 5; //申请人是否在职
    const AUDIT_COND_SUBMITTER_PROBATION  = 6; //申请人是否在试用期
    const AUDIT_COND_SUBMITTER_ROLE       = 7; //申请人角色
    const AUDIT_COND_SUBMITTER_NATIONALITY= 8; //申请人国籍
    const AUDIT_COND_SUBMITTER_STORE_TYPE = 9; //申请人所属网点类型
    const AUDIT_COND_SUBMITTER_STORE      = 10; //申请人所属网点

    const OPERATE_EQ    = 1; //等于
    const OPERATE_GT    = 2; //大于
    const OPERATE_GE    = 3; //大于等于
    const OPERATE_LT    = 4; //小于
    const OPERATE_LE    = 5; //小于等于
    const OPERATE_NE    = 6; //不等于
    const OPERATE_IN    = 7; //包含
    const OPERATE_BELONG_TO = 8; //属于
    const OPERATE_NOT_IN    = 9; //不包含
    const OPERATE_NOT_BELONG_TO = 10; //不属于

    const NODE_SUBMITTER_ALL = 0; //申请人节点类型-全部
    const NODE_SUBMITTER_PART = 1; //申请人节点类型-部分员工


    const NODE_TYPE_SUBMITTER = 1; //节点类型-根据申请人查找
    const NODE_TYPE_FORM = 2; //节点类型-根据表单查找
    const SYS_DEPARTMENT_ONE_ID = 34; //一级部门==Network Bulky

    const SELECT_TYPE_OPTION    = 1;    //option
    const SELECT_TYPE_OPTION_IS_YES = 1; //是
    const SELECT_TYPE_OPTION_IS_NO = 2; //否

    const SELECT_TYPE_API       = 2;    //接口
    const SELECT_TYPE_INPUT     = 3;    //输入框
    const SELECT_TYPE_NUM_INPUT     = 4;    //数字输入框
    const CONDITION_TYPE_API    = 1;    //1=特殊类型(需前端调用接口处理) 2= options
    const CONDITION_TYPE_OPTION = 2;    //1=特殊类型(需前端调用接口处理) 2= options

    //审批流可视化 end

    // 网点状态: 1-激活; 2-未激活; 0-全部
    const STORE_STATE_ACTIVE = 1;
    const STORE_STATE_NOT_ACTIVE = 2;
    const STORE_STATE_ALL = 0;

    //网点类型枚举
    const STORE_CATEGORY_SP = 1;
    const STORE_CATEGORY_DC = 2;
    const STORE_CATEGORY_SHOP_PICKUP_ONLY = 4;
    const STORE_CATEGORY_SHOP_PICKUP_DELIVERY = 5;
    const STORE_CATEGORY_FH = 6;
    const STORE_CATEGORY_USHOP = 7;
    const STORE_CATEGORY_HUB = 8;
    const STORE_CATEGORY_OS = 9;
    const STORE_CATEGORY_BDC = 10;
    const STORE_CATEGORY_FFM = 11;
    const STORE_CATEGORY_BHUB = 12;
    const STORE_CATEGORY_CDC = 13;
    const STORE_CATEGORY_PDC = 14;

    // 网点类型名称
    const STORE_CATEGORY_SP_LABEL = 'SP';
    const STORE_CATEGORY_DC_LABEL = 'DC';
    const STORE_CATEGORY_SHOP_PICKUP_ONLY_LABEL = 'SHOP(pickup-only)';
    const STORE_CATEGORY_SHOP_PICKUP_DELIVERY_LABEL = 'SHOP(pickup&delivery)';
    const STORE_CATEGORY_FH_LABEL = 'FH';
    const STORE_CATEGORY_USHOP_LABEL = 'USHOP';
    const STORE_CATEGORY_HUB_LABEL = 'Hub';
    const STORE_CATEGORY_OS_LABEL = 'OS';
    const STORE_CATEGORY_BDC_LABEL = 'BDC';
    const STORE_CATEGORY_FFM_LABEL = 'FFM';
    const STORE_CATEGORY_BHUB_LABEL = 'B-Hub';
    const STORE_CATEGORY_CDC_LABEL = 'CDC';
    const STORE_CATEGORY_PDC_LABEL = 'PDC';

    // 全部网点类型枚举关系
    public static $store_category = [
        self::STORE_CATEGORY_SP => self::STORE_CATEGORY_SP_LABEL,
        self::STORE_CATEGORY_DC => self::STORE_CATEGORY_DC_LABEL,
        self::STORE_CATEGORY_SHOP_PICKUP_ONLY => self::STORE_CATEGORY_SHOP_PICKUP_ONLY_LABEL,
        self::STORE_CATEGORY_SHOP_PICKUP_DELIVERY => self::STORE_CATEGORY_SHOP_PICKUP_DELIVERY_LABEL,
        self::STORE_CATEGORY_FH => self::STORE_CATEGORY_FH_LABEL,
        self::STORE_CATEGORY_USHOP => self::STORE_CATEGORY_USHOP_LABEL,
        self::STORE_CATEGORY_HUB => self::STORE_CATEGORY_HUB_LABEL,
        self::STORE_CATEGORY_OS => self::STORE_CATEGORY_OS_LABEL,
        self::STORE_CATEGORY_BDC => self::STORE_CATEGORY_BDC_LABEL,
        self::STORE_CATEGORY_FFM => self::STORE_CATEGORY_FFM_LABEL,
        self::STORE_CATEGORY_BHUB => self::STORE_CATEGORY_BHUB_LABEL,
        self::STORE_CATEGORY_CDC => self::STORE_CATEGORY_CDC_LABEL
    ];

    const CONTRACT_PAY_TYPE_TRANSFER = 1;
    const CONTRACT_PAY_TYPE_TICKET = 2;

    public static $contract_pay_type_map = [
        self::CONTRACT_PAY_TYPE_TRANSFER => 'contract_pay_type.1',
        self::CONTRACT_PAY_TYPE_TICKET => 'contract_pay_type.2',
    ];

    const BUDGET_OBJECT_PRODUCT_DOCTOR_ID = 871; // 科目明细医疗费ID
    const BUDGET_OBJECT_FULI = 8; // 员工福利费
    const PROBATION_STATUS_PASS = 4; // 已转正

    //IT工单审核权限
    const IT_TICKET_AUDIT_POWER = 'it_ticket_audit_power';

    //越南翻译
    const VN_LOCALE = 'vi';
    const VI_EXPORT_JOB_GROUP_ID = 'ID Nhóm chức vụ';    //职组ID
    const VI_EXPORT_JOB_GROUP_NAME = 'Tên gọi nhóm chức vụ';    //职组名称
    const VI_EXPORT_JOB_COUNT = 'Số lượng chức vụ';     //职位数量
    const VI_EXPORT_IN_JOB_NUMBER = 'Số người làm việc';    //在职人数
    const VI_EXPORT_PROFESSIONAL_COMPETENCE = 'Năng lực chuyên ngành';  //专业胜任力
    const VI_EXPORT_PROFESSIONAL_COMPETENCE_DESC = 'Mô tả năng lực';    //专业胜任力描述
    const VI_EXPORT_JD_ID = 'JDID'; //JDID
    const VI_EXPORT_JD_NAME = 'Tên JD';   //jd_name
    const VI_EXPORT_BELONG_JOB_GEROUP = 'Chức vụ trực thuộc';     //所属职组
    const VI_EXPORT_BELONG_CHILD_JOB_GEROUP = 'Nhóm chức vụ phụ trực thuộc';   //所属子职组
    const VI_EXPORT_STAFF_COUNT = 'Số lượng nhân viên';   //员工数量
    const VI_EXPORT_JD_DESCRIPTION = 'Mô tả JD';    //JD描述
    const VI_EXPORT_COMPENTENCE_NAME = 'Tên năng lực';      //胜任力名称
    const VI_EXPORT_COMPENTENCE_TYPE = 'Loại năng lực';    //胜任力类型
    const VI_EXPORT_RELATE_JOB_TITLE = 'Liên kết chức vụ'; //关联职位
    const VI_EXPORT_JOB_TITLE_MANAGEMENT_COUNT = 'Số lượng Quản lý chức vụ'; //职位管理数量

    //发送邮件类型
    const SEND_MAIL_TYPE_REJECT = 1;// 审批驳回
    const SEND_MAIL_TYPE_PASS = 2;// 审批最终通过

    const NODE_MARK_DEFAULT = 0;  //无标记
    const NODE_MARK_APPROVAL = 1; //标记审批
    const NODE_MARK_AUDIT = 2;    //标记审核

    //泰国版佛历
    const BUDDHIST_CALENDAR_YEAR = 543;

    const PAYMENT_PUSH_RECORD_STATUS_PENDING = 0;//数据是否推送

    const PUSH_TARGET_FMS = 'fms';
    const PUSH_TARGET_FBI = 'fbi';
}
