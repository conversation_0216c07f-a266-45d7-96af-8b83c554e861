<?php

use App\Library\Database;
use App\Library\Logger;
use App\Library\Mailer;
use Phalcon\Cache\Backend\Redis;
use Phalcon\Cache\Frontend\Data;
use Phalcon\Db\Profiler;
use Phalcon\Di\FactoryDefault\Cli as CliDi;
use Phalcon\Logger\Adapter\File;

$di = new CliDi();

/**
 * Shared configuration service
 */
$di->setShared('config', function () {
    return include APP_PATH . "/config/config.php";
});

/**
 *
 */
$di->set('profiler', function(){
    return new Profiler();
}, true);

/**
 *
 */
$di->setShared('cache',function (){
    $config = $this->getConfig();
    $front = new Data(
        ['lifetime'=> $config->redis->lifitime]
    );
    return new Redis(
        $front,
        [
            'prefix'        => $config->redis->prefix,
            "host"          => $config->redis->host,
            "port"          => $config->redis->port,
            "auth"          => $config->redis->auth,
            "persistent"    => false,
            'index'         => $config->redis->db,
        ]
    );
});

/**
 *
 */
$di->setShared('logger', function (){
    $config = $this->getConfig();
    $logPath = APP_PATH . '/runtime/tasklog/';
    if (!is_dir($logPath)) {
        mkdir($logPath);
    }
    $loggerFile = $logPath . '/'. date("Ymd") . '.log';
    $logger = new Logger(['adapter' => 'file','name'=>$loggerFile,]);
    $logger->setLogLevel($config->application->logLevel);

    return $logger;
});

/**
 *
 */
$di->setShared('sqlLogger', function (){
    $logPath = APP_PATH . '/runtime/tasklog';
    if (!is_dir($logPath)) {
        $oldmask = umask(0);
        mkdir($logPath,0777,true);
        umask($oldmask);
    }

    $loggerFile = $logPath . '/sql_'. date("Ymd") . '.log';
    $logger = new Logger(['adapter' => 'file','name'=>$loggerFile,]);
    $logger->setLogLevel(\Phalcon\Logger::DEBUG);

    return $logger;
});

/**
 *
 */
$di->setShared('db_oa', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_oa->host,
        'port'     => $config->database_oa->port,
        'username' => $config->database_oa->username,
        'password' => $config->database_oa->password,
        'dbname'   => $config->database_oa->dbname,
        'charset'  => $config->database_oa->charset,
    ];

    return new Database($params,$di);
});
/**
 * oa-归档
 */
$di->setShared('db_arch_oa', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_arch_oa->host,
        'port'     => $config->database_arch_oa->port,
        'username' => $config->database_arch_oa->username,
        'password' => $config->database_arch_oa->password,
        'dbname'   => $config->database_arch_oa->dbname,
        'charset'  => $config->database_arch_oa->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_fle', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_fle->host,
        'port'     => $config->database_fle->port,
        'username' => $config->database_fle->username,
        'password' => $config->database_fle->password,
        'dbname'   => $config->database_fle->dbname,
        'charset'  => $config->database_fle->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_backyard', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_backyard->host,
        'port'     => $config->database_backyard->port,
        'username' => $config->database_backyard->username,
        'password' => $config->database_backyard->password,
        'dbname'   => $config->database_backyard->dbname,
        'charset'  => $config->database_backyard->charset,
    ];

    return new Database($params,$di);
});

$di->setShared('db_rbackyard', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_rbackyard->host,
        'port'     => $config->database_rbackyard->port,
        'username' => $config->database_rbackyard->username,
        'password' => $config->database_rbackyard->password,
        'dbname'   => $config->database_rbackyard->dbname,
        'charset'  => $config->database_rbackyard->charset,
    ];

    return new Database($params,$di);
});

/**
 *
 */
$di->setShared('db_rbi', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_rbi->host,
        'port'     => $config->database_rbi->port,
        'username' => $config->database_rbi->username,
        'password' => $config->database_rbi->password,
        'dbname'   => $config->database_rbi->dbname,
        'charset'  => $config->database_rbi->charset,
    ];

    return new Database($params,$di);
});


$di->setShared('mailer', function () use($di){

    $config = $this->getConfig();
    return new Mailer($config);
});


$di->setShared('redis', function(){
    $config = $this->getConfig();
    $redis = new \Redis();
    $redis->pconnect($config->redis->host, $config->redis->port);
    $redis->auth($config->redis->auth);
    $redis->setOption(\Redis::OPT_PREFIX, $config->redis->prefix);
    $redis->select($config->redis->db);
    return $redis;
});