<?php

use App\Library\Database;
use App\Library\Logger;
use App\Library\Mailer;
use App\Library\LntMailer;
use Phalcon\Cache\Backend\Redis;
use Phalcon\Cache\Frontend\Data;
use Phalcon\Db\Profiler;
use Phalcon\Di\FactoryDefault;
use Phalcon\Events\Manager;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\Url as UrlResolver;
use Phalcon\Mvc\View;
use Phalcon\Mvc\View\Engine\Php as PhpEngine;
use Phalcon\Mvc\View\Engine\Volt as VoltEngine;

/**
 * The FactoryDefault Dependency Injector automatically registers
 * the services that provide a full stack framework.
 */
$di = new FactoryDefault();

/**
 * Shared configuration service
 */
$di->setShared('config', function () {
    return include APP_PATH . "/config/config.php";
});

/**
 * The URL component is used to generate all kind of urls in the application
 */
$di->setShared('url', function () {
    $config = $this->getConfig();

    $url = new UrlResolver();
    $url->setBaseUri($config->application->baseUri);

    return $url;
});

/**
 * Setting up dispatcher
 */
$di->setShared('dispatcher', function (){
    $eventsManager = new Manager();

    /**
     *
     */
    $eventsManager->attach('dispatch',new \App\Plugins\DispatchPlugin());
    /**
     * CORS
     */
    $eventsManager->attach('dispatch',new \App\Plugins\CORSPlugin());

    /**
     * Check if the user is allowed to access certain action using the SecurityPlugin
     */
    $eventsManager->attach('dispatch', new \App\Plugins\SecurityPlugin());

    /**
     * Handle exceptions and not-found exceptions using NotFoundPlugin
     */
    $eventsManager->attach('dispatch', new \App\Plugins\ExceptionPlugin());

    $dispatcher = new Dispatcher();
    $dispatcher->setEventsManager($eventsManager);

    return $dispatcher;
});

/**
 * Setting up router
 */
$di->setShared('router', function () {
    return include APP_PATH . "/config/router.php";
});

/**
 * Setting up the view component
 */
$di->setShared('view', function () {
    $config = $this->getConfig();

    $view = new View();
    $view->setDI($this);
    $view->setViewsDir($config->application->viewsDir);

    $view->registerEngines([
        '.volt' => function ($view) {
            $config = $this->getConfig();

            $volt = new VoltEngine($view, $this);

            $volt->setOptions([
                'compiledPath' => $config->application->cacheDir,
                'compiledSeparator' => '_'
            ]);

            return $volt;
        },
        '.phtml' => PhpEngine::class

    ]);

    return $view;
});

/**
 *
 */
$di->setShared('request', function (){
    return new App\Library\Request();
});

/**
 *
 */
$di->setShared('response', function (){
    return new App\Library\Response();
});

/**
 *
 */
$di->set('profiler', function(){
    return new Profiler();
}, true);
/**
 *
 */
$di->setShared('cache',function (){
    $config = $this->getConfig();
    $front = new Data(
        ['lifetime'=> $config->redis->lifetime]
    );
    return new Redis(
        $front,
        [
            'prefix'        => $config->redis->prefix,
            "host"          => $config->redis->host,
            "port"          => $config->redis->port,
            "auth"          => $config->redis->auth,
            "persistent"    => false,
            'index'         => $config->redis->db,
        ]
    );
});

/**
 *
 */
$di->setShared('logger', function (){
    $config = $this->getConfig();
    $logPath = APP_PATH . '/runtime/log';
    if (!is_dir($logPath)) {
        $oldmask = umask(0);
        mkdir($logPath,0777,true);
        umask($oldmask);
    }

    $loggerFile = $logPath . '/'. date("Ymd") . '.log';
    $logger = new Logger(['adapter' => 'file','name'=>$loggerFile,]);
    $logger->setLogLevel($config->application->logLevel);

    return $logger;
});

/**
 *
 */

$di->setShared('sqlLogger', function (){
    $logPath = APP_PATH . '/runtime/log';
    if (!is_dir($logPath)) {
        $oldmask = umask(0);
        mkdir($logPath,0777,true);
        umask($oldmask);
    }

    $loggerFile = $logPath . '/sql_'. date("Ymd") . '.log';
    $logger = new Logger(['adapter' => 'file','name'=>$loggerFile,]);
    $logger->setLogLevel(\Phalcon\Logger::DEBUG);

    return $logger;
});
/**
 *
 */
$di->setShared('redis', function(){
    $config = $this->getConfig();
    $redis = new \Redis();
    $redis->pconnect($config->redis->host, $config->redis->port);
    $redis->auth($config->redis->auth);
    $redis->setOption(\Redis::OPT_PREFIX, $config->redis->prefix);
    $redis->select($config->redis->db);
    return $redis;
});

/**
 *
 */
$di->setShared('db_oa', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_oa->host,
        'port'     => $config->database_oa->port,
        'username' => $config->database_oa->username,
        'password' => $config->database_oa->password,
        'dbname'   => $config->database_oa->dbname,
        'charset'  => $config->database_oa->charset,
    ];

    return new Database($params,$di);
});
/**
 * oa-归档
 */
$di->setShared('db_arch_oa', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_arch_oa->host,
        'port'     => $config->database_arch_oa->port,
        'username' => $config->database_arch_oa->username,
        'password' => $config->database_arch_oa->password,
        'dbname'   => $config->database_arch_oa->dbname,
        'charset'  => $config->database_arch_oa->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_fle', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_fle->host,
        'port'     => $config->database_fle->port,
        'username' => $config->database_fle->username,
        'password' => $config->database_fle->password,
        'dbname'   => $config->database_fle->dbname,
        'charset'  => $config->database_fle->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_backyard', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_backyard->host,
        'port'     => $config->database_backyard->port,
        'username' => $config->database_backyard->username,
        'password' => $config->database_backyard->password,
        'dbname'   => $config->database_backyard->dbname,
        'charset'  => $config->database_backyard->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_rbi', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_rbi->host,
        'port'     => $config->database_rbi->port,
        'username' => $config->database_rbi->username,
        'password' => $config->database_rbi->password,
        'dbname'   => $config->database_rbi->dbname,
        'charset'  => $config->database_rbi->charset,
    ];

    return new Database($params,$di);
});
/**
 *
 */
$di->setShared('db_rbackyard', function() use($di) {

    $config = $this->getConfig();
    $params = [
        'host'     => $config->database_rbackyard->host,
        'port'     => $config->database_rbackyard->port,
        'username' => $config->database_rbackyard->username,
        'password' => $config->database_rbackyard->password,
        'dbname'   => $config->database_rbackyard->dbname,
        'charset'  => $config->database_rbackyard->charset,
    ];

    return new Database($params,$di);
});

$di->set('mailer', function () use($di){

    $config = $this->getConfig();

    return new Mailer($config);
});


$di->set('lnt_mailer', function () use($di){
    $config = $this->getConfig();
    return new LntMailer($config);
});