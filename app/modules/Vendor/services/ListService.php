<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorPaymentDetailModel;
use App\Models\oa\VendorPurchaseRelationModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    public static $validate_list_search = [
        'pageSize' => 'IntGt:0',                           //每页条数
        'pageNum' => 'IntGt:0',                            //页码
        'vendor_id' => 'StrLenGeLe:0,32',                        //供应商编号
        'vendor_name' => 'StrLenGeLe:0,200',                //供应商名称
        'company_nature' => 'IntGt:0',                   //供应商性质
        'certificate_type' => 'IntGt:0',               //证件类型
        'audit_status' => 'IntGt:0',               //审核状态


    ];

    public static $not_must_params = [
        'vendor_id',
        'vendor_name',
        'company_nature',
        'certificate_type',
        'audit_status',
        'application_module',
        'grade_status',
        'grade',
        'grade_approve_status',
        'product_category',
    ];

    //供应商搜索-非必填参数
    public static $not_must_search_vendor = [
        'application_module_id',
        'source'
    ];
    //供应商搜索-参数验证
    public static $validate_search_vendor = [
        'search_name' => 'StrLenGeLe:0,128',//搜索关键字(供应商编号最长32, 供应商名称最长128)
        'application_module_id' => 'IntIn:' . VendorEnums::VALIDATE_VENDOR_MODULE,
        'source' => 'IntIn:' . VendorEnums::VALIDATE_VENDOR_SEARCH_SOURCE,
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $condition
     * @param int $sub_module_flag
     * @param bool $is_download
     * @param int $limit
     * @return array
     */
    public function getList($condition, $sub_module_flag = self::SUB_MODULE_TYPE_INFO, $is_download = false, $limit = 0)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        if ($limit) {
            $page_size = $limit;
            $page_num  = 1;
        }
        $offset = $page_size * ($page_num-1);

        $columns = [
            'main.id',
            'main.vendor_id',
            'main.vendor_name',
            'main.ownership',
            'main.company_nature',
            'main.certificate_type',
            'main.identification_no',
            'main.company_address',
            'main.sap_supplier_no',
            'main.kingdee_supplier_no',
            'main.created_at',
            'main.status',
            'main.audit_stage',
            'main.audit_rejected_count',
            'main.audit_approved_count',
            'main.tax_number',
            'main.create_id',
            'main.updated_id',
            'main.use_company_id',
            'main.grade',
            'main.grade_status',
            'main.grade_approve_status',
            'main.sales_and_service_tax_number',
        ];

        if ($is_download) {
            $columns[0] = 'distinct main.id';
            $columns = array_merge($columns, [
                'detail.contact',
                'detail.contact_phone',
                'detail.contact_email',
                'detail.bank_pay_name',
                'detail.bank_account_name',
                'detail.bank_no',
                'detail.payment_method',
                'detail.swift_code'
            ]);
        }

        // 审核模块的已处理列表, 展示处理时间
        if ($sub_module_flag == self::SUB_MODULE_TYPE_AUDIT && isset($condition['process_status']) && $condition['process_status'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
            $columns[] = 'log.audit_at';
        }

        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => Vendor::class]);
            $builder = $this->getCondition($builder, $sub_module_flag, $condition, $is_download);

            $builder->columns('COUNT(DISTINCT main.id) AS total');
            $count = (int) $builder->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns($columns);

                // 非审核模块: 列表排序规则
                if (!in_array($sub_module_flag, [self::SUB_MODULE_TYPE_AUDIT])) {
                    $builder->orderBy('main.id DESC');
                }

                if (!$is_download) {
                    $builder->groupBy('main.id');
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $sub_module_flag);
            }

            $data= [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('vendor-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];

    }

    /**
     * @param $builder
     * @param $sub_module_flag
     * @param $condition
     * @param $is_download
     * @return mixed
     */
    private function getCondition($builder, $sub_module_flag, $condition, $is_download = false)
    {
        $vendor_id = $condition['vendor_id'] ?? '';
        $vendor_name = $condition['vendor_name'] ?? '';
        $company_nature = $condition['company_nature'] ?? 0;
        $certificate_type = $condition['certificate_type'] ?? 0;
        $audit_status = $condition['audit_status'] ?? 0;
        $sap_supplier_no = $condition['sap_supplier_no'] ?? 0;
        $application_module = $condition['application_module'] ?? '';
        $grade_approve_status = $condition['grade_approve_status'] ?? '';
        $grade = $condition['grade'] ?? [];
        $grade_status = $condition['grade_status'] ?? [];
        $product = $condition['product_category'] ?? [];
        $kingdee_supplier_no = $condition['kingdee_supplier_no'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $process_status = $condition['process_status'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $process_status = in_array($process_status, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $process_status : GlobalEnums::AUDIT_TAB_PENDING;

        // 审批列表
        if ($sub_module_flag == self::SUB_MODULE_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $process_status, [Enums::WF_VENDOR_BIZ_TYPE], $condition['uid'], 'main');

        } else if ($sub_module_flag == self::SUB_MODULE_TYPE_REPLY) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'main'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_VENDOR_BIZ_TYPE], $condition['uid'], $biz_table_info);

        }

        if ($is_download) {
            $builder->leftjoin(VendorPaymentDetailModel::class, 'detail.main_id = main.id', 'detail');
        }
        if (!empty($application_module)) {
            $builder->leftjoin(VendorApplicationModuleRelModel::class, 'amr.vendor_id = main.vendor_id ', 'amr');
            $builder->andWhere('amr.application_module_id = :application_module_id:', ['application_module_id' => $application_module]);
        }

        if (!empty($vendor_id)) {
            $builder->andWhere('main.vendor_id = :vendor_id:', ['vendor_id' => $vendor_id]);
        }

        if (!empty($company_nature)) {
            $builder->andWhere('main.company_nature = :company_nature:', ['company_nature' => $company_nature]);
        }
        if (!empty($sap_supplier_no)) {
            $builder->andWhere('main.sap_supplier_no = :sap_supplier_no:', ['sap_supplier_no' => $sap_supplier_no]);
        }
        if (!empty($certificate_type)) {
            $builder->andWhere('main.certificate_type = :certificate_type:', ['certificate_type' => $certificate_type]);
        }

        if (!empty($audit_status)) {
            $builder->andWhere('main.status = :status:', ['status' => $audit_status]);
        }

        if (!empty($vendor_name)) {
            $builder->andWhere('main.vendor_name LIKE :vendor_name:', ['vendor_name' => '%' . $vendor_name . '%']);
        }
        //等级审核状态
        if (!empty($grade_approve_status)) {
            $builder->andWhere('main.grade_approve_status = :grade_approve_status:', ['grade_approve_status' => $grade_approve_status]);
        }

        //等级
        if (!empty($grade)) {
            $builder->inWhere('main.grade', $grade);
        }

        //等级状态
        if (!empty($grade_status)) {
            $builder->inWhere('main.grade_status', $grade_status);
        }

        //主供/服务
        if (!empty($product)) {
            $builder->leftjoin(VendorPurchaseRelationModel::class, 'vpr.vendor_id = main.vendor_id', 'vpr');
            $builder->inWhere('vpr.product_category', $product);
        }

        if(!empty($kingdee_supplier_no)){
            $builder->andWhere('main.kingdee_supplier_no LIKE :kingdee_supplier_no:', ['kingdee_supplier_no' => '%' . $kingdee_supplier_no . '%']);
        }

        return $builder;
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items, $sub_module_flag)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $coo_company_list = (new PurchaseService())->getCooCostCompany();
        $use_company_arr  = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');

        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();
        $t = static::$t;
        //等级枚举
        $grade_item = VendorEnums::$vendor_grade_item;
        //等级状态枚举
        $grade_status_item = VendorEnums::$vendor_grade_status_item;
        //分级审核状态
        $grade_approved_status_item = Enums::$contract_status;

        foreach ($items as &$item) {
            $use_company_id = explode(',', $item['use_company_id']);
            $use_company = [];
            foreach ($use_company_id as $company) {
                $use_company[]= $use_company_arr[$company] ?? '';
            }

            $use_company = implode(',',$use_company);

            $item['use_company_name']=$use_company;
            $item['ownership'] = $static_item['ownership_item'][$item['ownership']] ? $t[$static_item['ownership_item'][$item['ownership']]] ?? '' : '';
            $item['company_nature'] = $static_item['company_nature_item'][$item['company_nature']] ? $t[$static_item['company_nature_item'][$item['company_nature']]] ?? '' : '';
            $item['certificate_type'] = $static_item['certificate_type_item'][$item['certificate_type']] ? $t[$static_item['certificate_type_item'][$item['certificate_type']]] ?? '' : '';
            if (isset($item['payment_method']) && VendorEnums::PAYMENT_METHOD_BANK == $item['payment_method']) {
                $item['bank_pay_name'] = !empty($static_item['bank_item'][$item['bank_pay_name']]) ? $t[$static_item['bank_item'][$item['bank_pay_name']]] ?? '' : '';
            }

            if(isset($item['payment_method'])){
                $item['payment_method_title'] = $t[VendorEnums::$payment_method[$item['payment_method']]]??'';//支付方式
            }
            //供应商等级 供应商等级状态 分级审核状态
            $item['grade_text']                = $t[$grade_item[$item['grade']] ?? ''] ?? '';
            $item['grade_status_text']         = $t[$grade_status_item[$item['grade_status']] ?? ''] ?? '';
            $item['grade_approve_status_text'] = $t[$grade_approved_status_item[$item['grade_approve_status']] ?? ''] ?? '';

            $item['status_label'] = Enums::$contract_status[$item['status']] ? $t[Enums::$contract_status[$item['status']]] ?? '' : '';
            //供应商信息页面返回是否可以显示作废按钮
            if ($sub_module_flag == BaseService::SUB_MODULE_TYPE_INFO) {
                $item['is_show_invalid'] = false;
                if ($item['status'] == Enums::WF_STATE_APPROVED && $item['grade_status'] == VendorEnums::VENDOR_GRADE_STATUS_NORMAL && $item['grade_approve_status'] != Enums::WF_STATE_PENDING) {
                    $item['is_show_invalid'] = true;
                }
            }
        }

        return $items;
    }

    /**
     * @param $condition
     * @param $user_info
     * @return array
     * @throws GuzzleException
     */
    public function getExportData($condition, $user_info)
    {
        try {
            $condition['uid'] = $user_info['id'];
            $result = $this->getList($condition, self::SUB_MODULE_TYPE_INFO, true);
            $result = $result['data']??['items'=>[]];

            $country_code = get_country_code();

            $new_data = [];
            if (!empty($result['items'])) {
                foreach ($result['items'] as $val ) {
                    if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                        $new_data[] = [
                            $val['vendor_name'],
                            $val['vendor_id'],
                            $val['ownership'],
                            $val['company_nature'],
                            $val['certificate_type'],
                            $val['identification_no'],
                            $val['company_address'],
                            $val['sap_supplier_no'],
                            $val['kingdee_supplier_no'],
                            $val['use_company_name'],
                            is_null($val['contact'])?'':$val['contact'],
                            is_null( $val['contact_phone'])?'':$val['contact_phone'],
                            is_null($val['contact_email'])?'':$val['contact_email'],
                            $val['payment_method_title']??'',
                            $val['bank_pay_name']??'',
                            is_null( $val['bank_account_name'])?'': $val['bank_account_name'],
                            is_null($val['bank_no'])?'':$val['bank_no'],
                            is_null( $val['swift_code'])?'':$val['swift_code'],
                            $val['created_at'],
                            $val['tax_number'],
                            $val['sales_and_service_tax_number'],
                            $val['status_label'],
                            $val['create_id'],
                            $val['updated_id'],
                        ];
                    } else {
                        $new_data[] = [
                            $val['vendor_name'],
                            $val['vendor_id'],
                            $val['ownership'],
                            $val['company_nature'],
                            $val['certificate_type'],
                            $val['identification_no'],
                            $val['company_address'],
                            $val['sap_supplier_no'],
                            $val['kingdee_supplier_no'],
                            $val['use_company_name'],
                            is_null($val['contact'])?'':$val['contact'],
                            is_null( $val['contact_phone'])?'':$val['contact_phone'],
                            is_null($val['contact_email'])?'':$val['contact_email'],
                            $val['payment_method_title']??'',
                            $val['bank_pay_name']??'',
                            is_null( $val['bank_account_name'])?'': $val['bank_account_name'],
                            is_null($val['bank_no'])?'':$val['bank_no'],
                            is_null( $val['swift_code'])?'':$val['swift_code'],
                            $val['created_at'],
                            $val['tax_number'],
                            $val['status_label'],
                            $val['create_id'],
                            $val['updated_id'],
                        ];
                    }
                }
            }
            unset($result);

            if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                $header = [
                    self::$t['supplier.001'],
                    self::$t['supplier.015'],//OA供应商编号
                    self::$t['supplier.002'],
                    self::$t['supplier.003'],
                    self::$t['supplier.004'],
                    self::$t['supplier.005'],
                    self::$t['supplier.006'],
                    self::$t['supplier.013'],
                    self::$t['supplier.039'],//金碟供应商编号
                    self::$t['supplier.025'],//使用公司
                    self::$t['supplier.007'],
                    self::$t['supplier.008'],
                    self::$t['supplier.009'],
                    self::$t['supplier.017'],//支付方式
                    self::$t['supplier.010'],
                    self::$t['supplier.011'],
                    self::$t['supplier.012'],
                    self::$t['supplier.021'],//Swift Code
                    self::$t['supplier.016'],//创建时间
                    self::$t['supplier.014'],
                    self::$t['supplier.042'], // MY 销售和服务税登记号码
                    self::$t['supplier.022'],//审批状态
                    self::$t['supplier.023'],//申请人工号
                    self::$t['supplier.024'],//更新人工号
                ];

            } else {
                $header = [
                    self::$t['supplier.001'],
                    self::$t['supplier.015'],//OA供应商编号
                    self::$t['supplier.002'],
                    self::$t['supplier.003'],
                    self::$t['supplier.004'],
                    self::$t['supplier.005'],
                    self::$t['supplier.006'],
                    self::$t['supplier.013'],
                    self::$t['supplier.039'],//金碟供应商编号
                    self::$t['supplier.025'],//使用公司
                    self::$t['supplier.007'],
                    self::$t['supplier.008'],
                    self::$t['supplier.009'],
                    self::$t['supplier.017'],//支付方式
                    self::$t['supplier.010'],
                    self::$t['supplier.011'],
                    self::$t['supplier.012'],
                    self::$t['supplier.021'],//Swift Code
                    self::$t['supplier.016'],//创建时间
                    self::$t['supplier.014'],
                    self::$t['supplier.022'],//审批状态
                    self::$t['supplier.023'],//申请人工号
                    self::$t['supplier.024'],//更新人工号
                ];
            }

            $file_name = 'SupplierInfoDownload_' . date('YmdHis');

            $result = $this->exportExcel($header, $new_data, $file_name);
            if ($result['code'] == ErrCode::$SUCCESS) {
                $result['message'] = 'success';
                $result['data'] = $result['data'];
            } else {
                $result['code'] = ErrCode::$SYSTEM_ERROR;
                $result['message'] = 'error';
                $result['data'] = '';
            }


        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('供应商导出异常:' . $e->getMessage());

            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = $e->getMessage();
            $result['data'] = '';
        }

        return $result;
    }

    /**
     * 根据供应商类别供应商名称搜索
     * @param $search_name
     * @param int $application_module_id
     * @param string $vendor_id 如果传了vendor_id 就通过vendor_id查找
     * @param int $source 查询来源
     * @return array
     */
    public function searchVendorTypeList($search_name, $application_module_id = 2, $vendor_id = '', $source = VendorEnums::VENDOR_SEARCH_OTHER)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        if (empty($search_name)) {
            return [
                'code' => $code,
                'message' => 'success',
                'data' => $data,
            ];
        }
        try {
            $vendor_name = '%' . $search_name . '%';
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                'main.vendor_id,main.vendor_name,main.company_address,main.tax_number,main.sap_supplier_no,main.ownership,d.payment_method,d.contact,d.contact_phone,d.contact_email,
            d.bank_pay_name,d.bank_account_name,d.bank_no,d.swift_code');
            $builder->from(['main' => Vendor::class]);
            if (!empty($application_module_id)) {
                $builder->leftjoin(
                    VendorApplicationModuleRelModel::class,
                    'main.vendor_id= c.vendor_id',
                    'c'
                );
                $builder->andWhere('c.application_module_id=:application_module_id:', ['application_module_id' => $application_module_id]);
            }
            $builder->leftjoin(
                VendorPaymentDetailModel::class,
                'main.id= d.main_id',
                'd'
            );
            $builder->andWhere('main.status =:status:', ['status' => Enums::WF_STATE_APPROVED]);
            if (!empty($vendor_id)) {
                $builder->andWhere('main.vendor_id = :vendor_id:', ['vendor_id' => $vendor_id]);
            } else {
                if ($application_module_id == VendorEnums::VENDOR_PURCHASE) {
                    $builder->andWhere('main.vendor_name like :vendor_name: or main.vendor_id like :vendor_name_two:', ['vendor_name' => $vendor_name, 'vendor_name_two' => $vendor_name]);
                } else {
                    $builder->andWhere('main.vendor_name like :vendor_name:', ['vendor_name' => $vendor_name]);
                }
            }

            if (in_array($source, [VendorEnums::VENDOR_SEARCH_PURCHASE_ORDER, VendorEnums::VENDOR_SEARCH_PURCHASE_PAYMENT])) {
                //采购订单和采购付款申请单 添加搜索限制 等级状态=正常 等级为注册和认证 分级审理不是待审批
                $builder->andWhere('main.grade_status = :grade_status:', ['grade_status' => VendorEnums::VENDOR_GRADE_STATUS_NORMAL]);
                $builder->andWhere('main.grade_approve_status != :grade_approve_status:', ['grade_approve_status' => Enums::WF_STATE_PENDING]);
                $builder->inWhere('main.grade', [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION]);
            } elseif (in_array($source, [VendorEnums::VENDOR_SEARCH_PURCHASE_SAMPLE, VendorEnums::VENDOR_SEARCH_ASSETS_ACCOUNT])) {
                //采购申请-样品确认 和 资产台账 添加搜索时供应商不可选择作废，草稿状态的供应商
                $builder->notInWhere('main.grade_status', [VendorEnums::VENDOR_GRADE_STATUS_DRAFT, VendorEnums::VENDOR_GRADE_STATUS_INVALID]);
            } elseif ($source == VendorEnums::VENDOR_SEARCH_CONTRACT_ADD) {
                //其他合同新增  供应商状态为 正常
                $builder->andWhere('main.grade_status = :grade_status:', ['grade_status' => VendorEnums::VENDOR_GRADE_STATUS_NORMAL]);
            }
            $res = $builder->getQuery()->execute()->toArray();
            // 获取静态配置code 与 翻译key
            $static_item = self::getStaticTranslationCodeItem();
            $t = static::$t;
            $search_data = [];
            foreach ($res as &$item) {
                if ($item['payment_method'] == VendorEnums::PAYMENT_METHOD_BANK) {
                    $item['bank_pay_name']    =isset($static_item['bank_item'][$item['bank_pay_name'] ?? '']) ? $t[$static_item['bank_item'][$item['bank_pay_name'] ?? '']] ?? '' : '';
                }
                $search_data[$item['vendor_id']]['payment_method_items'][] = [
                    'vendor_id'          => $item['vendor_id'],
                    'vendor_name'        => $item['vendor_name'],
                    'contact'            => $item['contact'],
                    'contact_phone'      => $item['contact_phone'],
                    'contact_email'      => $item['contact_email'],
                    'account_name'       => $item['bank_account_name'],
                    'account'            => $item['bank_no'],
                    'payment_method_val' => VendorEnums::PAYMENT_METHOD_BANK,
                    'bank_account_name'  => $item['bank_account_name'],
                    'bank_pay_name'      => $item['bank_pay_name'],
                    'payment_method'     => $t[VendorEnums::$payment_method[$item['payment_method']]] . "<br/>" . $item['bank_pay_name'],
                    'swift_code'         => $item['swift_code'],
                ];
                $search_data[$item['vendor_id']]['vendor_id']              = $item['vendor_id'];
                $search_data[$item['vendor_id']]['vendor_name']            = $item['vendor_name'];
                $search_data[$item['vendor_id']]['company_address']        = $item['company_address'];
                $search_data[$item['vendor_id']]['tax_number']             = $item['tax_number'];
                $search_data[$item['vendor_id']]['sap_supplier_no']        = $item['sap_supplier_no'];
                $search_data[$item['vendor_id']]['ownership']              = $item['ownership'];
            }
            $data = array_values($search_data);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('搜索供应商失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];

    }

    /**
     * 找最新的request
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($id)
    {
        $vendor = Vendor::getFirst([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $id],
        ]);
        if (empty($vendor)) {
            return [];
        }

        $vendor = $vendor->toArray();

        $req = VendorFlowService::getInstance()->getRequest($vendor['id']);
        if (!empty($req)) {
            unset($vendor['id']);
            $workflow_arr = $req->toArray();
        }

        return (object)array_merge($vendor, $workflow_arr ?? []);
    }


    /**
     * @param $condition
     * @param $uid
     * @param int $sub_module_flag
     * @param bool $if_download
     * @return array
     */
    public function replyList($condition, $uid = null, $sub_module_flag = self::SUB_MODULE_TYPE_INFO, $if_download = false)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num-1);

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('
                main.id,
                main.vendor_id,
                main.vendor_name,
                main.ownership,
                main.company_nature,
                main.certificate_type,
                main.identification_no,
                main.company_address,
                main.sap_supplier_no,
                main.kingdee_supplier_no,
                main.created_at,
                main.status,
                main.audit_stage,
                main.audit_rejected_count,
                main.audit_approved_count,
                main.tax_number,
                main.use_company_id,
                main.grade,
                main.grade_status,
                main.grade_approve_status,
                reply.id as ask_id,
                reply.created_at as add_time
            ');

            $builder->from(['main' => Vendor::class]);
            $builder = $this->getCondition($builder, $sub_module_flag, $condition);
            $builder->groupBy('main.id');
            $count = $builder->getQuery()->execute()->count();

            $items = [];
            if ($count) {
                if (!in_array($sub_module_flag, [self::SUB_MODULE_TYPE_REPLY])) {
                    $builder->orderBy('main.id DESC');
                }

                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $sub_module_flag);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->warning('vendor-reply-list-failed:' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 根据普通付款单据上保理对象的供应商编号组获取供应商的信息和银行账号组
     * @param array $factoring_vendor_ids 供应商编号组
     * @return array
     */
    public function getOrdinaryPaymentFactoringVendorList($factoring_vendor_ids)
    {
        $search_data = [];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.vendor_id, main.vendor_name, main.company_address, main.tax_number, main.sap_supplier_no, d.payment_method, d.contact, d.contact_phone, d.contact_email, d.bank_pay_name, d.bank_account_name, d.bank_no, d.swift_code');
        $builder->from(['main' => Vendor::class]);
        $builder->leftjoin(VendorApplicationModuleRelModel::class, 'main.vendor_id = c.vendor_id', 'c');
        $builder->leftjoin(VendorPaymentDetailModel::class, 'main.id = d.main_id', 'd');
        $builder->where('c.application_module_id = :application_module_id:', ['application_module_id' => VendorEnums::VENDOR_ORDINARY_PAYMENT]);
        $builder->inWhere('main.vendor_id', $factoring_vendor_ids);
        $res = $builder->getQuery()->execute()->toArray();

        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();
        foreach ($res as &$item) {
            if ($item['payment_method'] == VendorEnums::PAYMENT_METHOD_BANK) {
                $item['bank_pay_name'] = isset($static_item['bank_item'][$item['bank_pay_name'] ?? '']) ? static::$t[$static_item['bank_item'][$item['bank_pay_name'] ?? '']] ?? '' : '';
            }
            $search_data[$item['vendor_id']]['payment_method_items'][] = [
                'vendor_id' => $item['vendor_id'],
                'vendor_name' => $item['vendor_name'],
                'contact' => $item['contact'],
                'contact_phone' => $item['contact_phone'],
                'contact_email' => $item['contact_email'],
                'account_name' => $item['bank_account_name'],
                'account' => $item['bank_no'],
                'payment_method_val' => VendorEnums::PAYMENT_METHOD_BANK,
                'bank_account_name' => $item['bank_account_name'],
                'bank_pay_name' => $item['bank_pay_name'],
                'payment_method' => static::$t[VendorEnums::$payment_method[$item['payment_method']]] . "<br/>" . $item['bank_pay_name'],
                'swift_code' => $item['swift_code']
            ];
            $search_data[$item['vendor_id']]['vendor_id'] = $item['vendor_id'];
            $search_data[$item['vendor_id']]['vendor_name'] = $item['vendor_name'];
            $search_data[$item['vendor_id']]['company_address'] = $item['company_address'];
            $search_data[$item['vendor_id']]['tax_number'] = $item['tax_number'];
            $search_data[$item['vendor_id']]['sap_supplier_no'] = $item['sap_supplier_no'];
        }
        return $search_data;
    }
}
