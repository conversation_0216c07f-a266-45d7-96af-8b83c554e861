<?php
/**
 * 付款管理 - 网点租房付款 2020.12
 */

namespace App\Modules\Payment\Controllers;

use App\Library\Enums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Payment\Services\BaseService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Payment\Services\StoreRentingCancelService;
use App\Modules\Payment\Services\StoreRentingDetailService;
use App\Modules\Payment\Services\StoreRentingListService;
use App\Modules\Payment\Services\StoreRentingUpdateService;
use App\Modules\Payment\Services\StoreRentingPaymentFlowService;
use App\Library\OssHelper;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StoreRentingController extends BaseController
{
    /**
     * 网点租房付款申请 - 列表页 - 默认选项配置
     * @Token
     * @return mixed
     */
    public function getSearchPageDefaultOptionsAction()
    {
        try {
            $data = StoreRentingListService::getSearchDefault($this->user['id']);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);

        } catch (Exception $e) {
            $this->logger->warning('付款管理 - 网点租房付款 - 获取支付/审核状态列表异常: ' . $e->getMessage());

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 网点租房付款申请 - 创建页 - 基本信息 默认值
     * @Permission(action='payment.apply.store_renting')
     * @return mixed
     */
    public function getCreatePageBaseInfoDefaultAction()
    {
        try {
            $res = StoreRentingAddService::getInstance()->getCreatePageBaseInfoDefaultData($this->user);
            return $this->returnJson($res['code'], $res['message'], $res['data']);

        } catch (Exception $e) {
            $this->logger->warning('付款管理 - 网点租房付款 - 获取创建页默认数据异常: ' . $e->getMessage());

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 网点租房付款申请 - 创建页 - 选项 - 默认值
     * @Token
     * @return mixed
     */
    public function getCreatePageOptionsDefaultAction()
    {
        try {
            $res = StoreRentingAddService::getInstance()->getCreatePageOptionsData($this->user);
            return $this->returnJson($res['code'], $res['message'], $res['data']);

        } catch (Exception $e) {
            $this->logger->warning('付款管理 - 网点租房付款 - 获取创建页默认配置项异常: ' . $e->getMessage());

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 网点租房付款申请 - 创建页 - 合同列表搜索
     * @Permission(action='payment.apply.store_renting')
     * @return mixed
     */
    public function getContractListAction()
    {
        $data = $this->request->get();
        try {
            $data = BaseService::handleParams($data, StoreRentingAddService::$not_must_params);
            Validation::validate($data, BaseService::$validate_contract_search_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = StoreRentingAddService::getInstance()->getContractList($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 网点租房付款申请 - 创建页 - 网点列表搜索
     * @Permission(menu='payment')
     * @return mixed
     */
    public function getStoreListAction()
    {
        $data = $this->request->get();
        try {
            $data = BaseService::handleParams($data, StoreRentingAddService::$not_must_params);
            Validation::validate($data, BaseService::$validate_store_search_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = StoreRentingAddService::getInstance()->getStoreList($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 网点租房付款申请 - 金额详情上传模板下载
     * @Permission(action='payment.apply.store_renting')
     * @return mixed
     */
    public function amountDetailTemplateDownloadAction()
    {
        try {
            $res = StoreRentingAddService::getInstance()->getAmountDetailUploadTemplate();
            return $this->returnJson($res['code'], $res['message'], $res['data']);

        } catch (Exception $e) {
            $this->logger->warning('付款管理 - 网点租房付款 - 金额详情模板下载异常: ' . $e->getMessage());

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 网点租房付款申请 - 创建页 - 金额详情批量上传
     * @Permission(action='payment.apply.store_renting')
     *
     * @return Response|ResponseInterface
     * @throws GuzzleException
     */
    public function amountDetailUploadAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
            $param = $this->request->get();
            if (empty($excel_file)) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('excel_file_empty'), []);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
//                ->setSkipRows(0)
                ->setType([
                    3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    5 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    7 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    10 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    11 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    15 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    16 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    17 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    20 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    22 => \Vtiful\Kernel\Excel::TYPE_STRING,
//                    \Vtiful\Kernel\Excel::TYPE_INT,
                ])
                ->getSheetData();

            // 提取Excel Header
            $excel_header_notice = array_shift($excel_data);
            $excel_header_column = array_shift($excel_data);
            $excel_header_column[] = $this->t['payment_upload_result_title'];
            $excel_header_column[] = $this->t['payment_upload_result_hint'];

            $lock_key = md5('store_rent_payment_batch_import_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($excel_data, $param) {
                return StoreRentingAddService::getInstance()->amountDetailBatchUpload($excel_data, $param);
            }, $lock_key, 10);

            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column);
                $excel_extra_config = [
                    'file_name' =>  date('YmdHis') . '_' . $this->user['id'] . '_payment__store_renting_detail.xlsx',
                    'file_desc' => $excel_header_notice[0],
                    'end_column_char' => 'Q',
                    'column_width' => 15,
//                    'header_column_font_color' => \Vtiful\Kernel\Format::COLOR_RED,
//                    'header_columns' => ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N'],
//                    'header_column_row' => 2,
                ];
                $path = self::customizeExcelToFile($excel_header_notice, $upload_result_data,  $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
                if (!empty($oss_result['object_url'])) {
                    // 下载导入结果
                    $res['data']['upload_result_file']['file_name'] = $this->t['payment_upload_result_file_name'].'.xlsx';
                    $res['data']['upload_result_file']['file_url'] = $oss_result['object_url'];
                }
            }

        } catch (Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('付款管理 - 网点租房付款 - 金额详情批量上传, 异常: ' . $res['message']);
        }

        if (isset($res['data']['excel_data'])) {
            unset($res['data']['excel_data']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 申请创建
     * @Permission(action='payment.apply.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/20521
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function createAction()
    {
        $data = $this->request->get();
        $this->logger->info('付款管理 - 网点租房付款 - 创建 - 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        $data = BaseService::handleParams($data, StoreRentingAddService::$not_must_params);
        // 去除数组元素两侧空白字符
        $data = trim_array($data);
        $data = StoreRentingAddService::getInstance()->extendValidation($data);
        $res = StoreRentingAddService::getInstance()->add($data, $this->user);

        $this->logger->info('付款管理 - 网点租房付款 - 创建 - 响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款申请 - 列表
     * @Permission(action='payment.apply.store_renting')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_search);

        $res = StoreRentingListService::getInstance()->getList($params, $this->user, StoreRentingListService::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款审核 - 列表
     * @Permission(action='payment.audit.store_renting')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_search);

        $res = StoreRentingListService::getInstance()->getList($params, $this->user, StoreRentingListService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款申请 - 详情
     * @Permission(action='payment.apply.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/20662
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function applyDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail_param);
        $res = StoreRentingDetailService::getInstance()->getAuditDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款申请 - 详情
     * @Permission(action='payment.apply.store_renting')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function editDetailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, BaseService::$validate_detail_param);

        $res = StoreRentingDetailService::getInstance()->editDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款审核 - 详情
     * @Permission(action='payment.audit.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/20677
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail_param);
        $res = StoreRentingDetailService::getInstance()->getAuditDetail($id, $this->user['id'], true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款支付 - 详情
     * @Permission(action='payment.pay.store_renting')
     * @return mixed
     */
    public function payDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, BaseService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = StoreRentingDetailService::getInstance()->getAuditDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 网点租房付款 - 申请 - 下载
     * @Permission(action='payment.apply.store_renting')
     * @return mixed
     */
    public function applyDownloadAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');

        try {
            Validation::validate($data, BaseService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('store_renting_payment_apply_download_' . $id . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($id){
            return StoreRentingDetailService::getInstance()->download($id, $this->user['id']);
        }, $lock_key, 60);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 网点租房付款 - 支付 - 下载
     * @Permission(action='payment.pay.store_renting')
     * @return mixed
     */
    public function payDownloadAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');

        try {
            Validation::validate($data, BaseService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('store_renting_payment_pay_download_' . $id . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($id){
            return StoreRentingDetailService::getInstance()->download($id, $this->user['id'],true);
        }, $lock_key, 60);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 网点租房付款 - 审核通过
     * @Permission(action='payment.audit.store_renting')
     */
    public function approveAction()
    {
        $id = $this->request->get('id','int');
        $remark = $this->request->get('remark','trim', '');
        $amount_detail = $this->request->get('amount_detail');

        // 请求参数
        $this->logger->info('付款管理 - 网点租房付款 - 审批通过请求参数: ' . json_encode($this->request->get(), JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate(['id'=>$id, 'note'=>$remark], StoreRentingPaymentFlowService::$validate_approve);

            if (!empty($amount_detail) && !is_array($amount_detail)) {
                $amount_detail = json_decode($amount_detail, true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    throw new ValidationException('amount_detail param error', ErrCode::$VALIDATE_ERROR);
                }
            }
            if (!empty($amount_detail) && is_array($amount_detail)) {
                foreach ($amount_detail as $detail) {
                    Validation::validate($detail, BaseService::getValidateUpdateParam());
                }
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 金额详情参数
        $this->logger->info('付款管理 - 网点租房付款 - 审批通过 - 金额详情参数: ' . json_encode($amount_detail, JSON_UNESCAPED_UNICODE));

        $res = (new StoreRentingPaymentFlowService())->approve($id, $remark, $this->user, $amount_detail);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 网点租房付款申请 - 审核驳回
     * @Permission(action='payment.audit.store_renting')
     */
    public function rejectAction()
    {
        $id = $this->request->get('id','int',0);
        $remark = $this->request->get('remark','trim','');

        $this->logger->info('付款管理 - 网点租房付款 - 驳回 - 请求参数: ' . json_encode($this->request->get(), JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate(['id'=>$id,'note'=>$remark], StoreRentingPaymentFlowService::$validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = (new StoreRentingPaymentFlowService())->reject($id, $remark, $this->user);

        $this->logger->info('付款管理 - 网点租房付款 - 驳回 - 响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 网点租房付款申请 - 撤销
     * @Permission(action='payment.apply.store_renting')
     *
     * @return Response|ResponseInterface
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $main_id = $this->request->get('id','int');
        $remark = $this->request->get('remark', 'trim');

        $this->logger->info('付款管理 - 网点租房付款 - 撤回请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, StoreRentingCancelService::$validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = (new StoreRentingPaymentFlowService())->cancel($main_id, $remark, $this->user);
        $this->logger->info('付款管理 - 网点租房付款 - 撤回响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 网点租房付款申请 - 支付
     * @Permission(action='payment.pay.store_renting')
     * @return mixed
     */
    public function payAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');

        $this->logger->info('付款管理 - 网点租房付款 - 支付请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            $data = BaseService::handleParams($data, StoreRentingAddService::$not_must_params);

            Validation::validate($data, BaseService::$validate_pay_param);
            // 非老挝必填校验
            if ('LA' != get_country_code()) {
                Validation::validate($data, BaseService::$validate_pay_required_param);
            }
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = StoreRentingUpdateService::getInstance()->pay($id, $data, $this->user);
        $this->logger->info('付款管理 - 网点租房付款 - 支付响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 付款支付 - 列表
     * @Permission(action='payment.pay.store_renting')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function payListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_search);

        $res = StoreRentingListService::getInstance()->getList($params, $this->user, StoreRentingListService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 网点租房付款 - 数据查询 - 列表
     * @Permission(action='payment.data.query')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_search);

        $res = StoreRentingListService::getInstance()->getList($params, $this->user, StoreRentingListService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 网点租房付款 - 数据查询 - 详情
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/20683
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail_param);
        $res = StoreRentingDetailService::getInstance()->getAuditDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点租房付款 - 数据导出
     * @Permission(action='payment.data.export')
     *
     * @return mixed
     * @throws Exception
     */
    public function dataExportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_export_params);

        // 加锁处理
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_DATA_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
             return StoreRentingListService::getInstance()->dataExport($params, StoreRentingListService::LIST_TYPE_DATA, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);

    }

    /**
     * 网点租房付款 - 数据 - 下载
     * @Permission(action='payment.data.download')
     * @return mixed
     */
    public function dataDownloadAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');

        try {
            Validation::validate($data, BaseService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('store_renting_payment_data_download_' . $id . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($id){
            return StoreRentingDetailService::getInstance()->download($id, $this->user['id'],true);
        }, $lock_key, 60);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 9888【OA|网点租房付款】功能优化
     * @wiki https://l8bx01gcjr.feishu.cn/docs/doccn6asK3e9qKZKg6TFddMMfod
     * 2. 当【网点名称】=Header Office，根据【费用所属部门】取对应的成本中心
     * 3. 当【网点名称】=具体网点，则根据具体网点取对应的成本中心
     *
     * @param['department_id'] 部门ID（网点ID）
     * @param['type'] 1:总部，2网点
     * @return Response|ResponseInterface
     *@api https://yapi.flashexpress.pub/project/133/interface/api/30743
     * @Token
     */
    public function getPcCodeAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param,BaseService::$validate_department);
            if ($param['type'] == BaseService::COST_TYPE_HEAD_OFFICE) {
                Validation::validate($param,BaseService::$validate_cost_department_id);
            } else if ($param['type'] == BaseService::COST_TYPE_SYS_STORE) {
                Validation::validate($param,BaseService::$validate_cost_store_id);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StoreRentingAddService::getInstance()->getPcCode($param['department_id'], $param['type']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 根据费用类型获取对应的核算科目列表
     * 9888【OA|网点租房付款】功能优化
     * @wiki https://l8bx01gcjr.feishu.cn/docs/doccn6asK3e9qKZKg6TFddMMfod
     *
     * @return Response|ResponseInterface
     */
    public function getLedgerInfoAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param,BaseService::$validate_cost_type_id);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StoreRentingAddService::getInstance()->getLedgerInfoByCostTypeId($param['cost_type_id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
    * 网点租房付款申请 -手动添加 -金额详情 相关合同详情
    * @Token
    * @Date: 2021-11-17 14:30
    * @author: peak pan
    * @return:
    **/
    public function getRentingBankAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, StoreRentingAddService::$get_relate_contract);
            $res = StoreRentingAddService::getInstance()->rentingBank($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 网点租房付款申请 - 创建页 - 费用所属中心列表搜索
     * @Permission(action='payment.apply.store_renting')
     */
    public function getCostCenterListAction()
    {
        $data = $this->request->get();
        try {
            $data = BaseService::handleParams($data, StoreRentingAddService::$not_must_params);
            Validation::validate($data, BaseService::$validate_cost_center_search_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = StoreRentingAddService::getInstance()->getCostCenterList($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 网点租房付款 - 付款审核 - 列表
     * @Permission(action='payment.audit.store_renting')
     *
     * @return mixed
     * @throws Exception
     */
    public function auditExportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StoreRentingListService::$not_must_params);
        Validation::validate($params, StoreRentingListService::$validate_list_search);

        // 加锁处理
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_AUDIT_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return StoreRentingListService::getInstance()->dataExport($params, StoreRentingListService::LIST_TYPE_AUDIT, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 数据查询-添加补充附件
     * @Permission(action='payment.data.add_attachment')
     * @return mixed
     * @throws ValidationException
     */
    public function addSupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_supplement);

        // 加锁处理
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_SUPPLEMENT_LOCK_PREFIX . $data['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return StoreRentingAddService::getInstance()->addFile($data);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);

    }

    /**
     * 网点租房付款 - 我的申请 - 补充附件 - 提交
     * @Permission(action='payment.apply.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87236
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function applyAddSupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_supplement);
        $data['type'] = 2;

        // 加锁处理
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_SUPPLEMENT_LOCK_PREFIX . $data['id'] . $this->user['id']);
        $res = $this->atomicLock(function () use ($data) {
            return StoreRentingAddService::getInstance()->addFile($data, $this->user);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }


    /**
     * 网点租房付款 - 我的申请 - 创建 - 批量导入数据
     * @Permission(action='payment.apply.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87248
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function importAddAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_IMPORT_LOCK_PREFIX . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return StoreRentingAddService::getInstance()->importAdd($excel_file, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 网点租房付款 - 我的申请 - 创建 - 批量导入数据 - 查询最后一次导入成功的结果
     * @Permission(action='payment.apply.store_renting')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87245
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAddResultAction()
    {
        $result = StoreRentingAddService::getInstance()->getImportResult($this->user['id']);
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 网点租房付款 - 付款审核-待处理-批量审核-汇总数据
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87722
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function batchAuditSummaryAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BaseService::$validate_batch_audit_summary);
        $res = StoreRentingListService::getInstance()->batchAuditSummary($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据查询-批量下载
     * @Permission(action='payment.data.batch_download')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87737
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function batchDownloadAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BaseService::$validate_batch_audit_summary);
        $lock_key = md5(RedisKey::PAYMENT_STORE_RENTING_DATA_BATCH_DOWNLOAD_LOCK_PREFIX . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::PAYMENT_STORE_RENTING_BATCH_DOWNLOAD, $params);
        }, $lock_key, 5);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
