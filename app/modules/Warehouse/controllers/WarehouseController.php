<?php
/**
 * 仓库管理
 */

namespace App\Modules\Warehouse\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\WarehouseEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Warehouse\Services\AuditService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class WarehouseController extends BaseController
{
    /**
     * 静态枚举配置
     * @Permission(menu='warehouse_manage')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80102
     * @return Response|ResponseInterface
     */
    public function enumsAction()
    {
        $data = WarehouseService::getInstance()->getStaticEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取网点类型枚举
     * @Permission(menu='warehouse_manage')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83255
     * @return Response|ResponseInterface
     */
    public function getStoreCategoryAction()
    {
        $list = ContractStoreRentingService::getInstance()->getAllStoreCate();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * 仓库添加
     * @Permission(action='contract.warehouse.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80012
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        WarehouseService::saveCommonParamsValidation($params);

        $lock_key = md5(RedisKey::CONTRACT_WAREHOUSE_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return WarehouseService::getInstance()->commonAdd($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 检测仓库名称是否重复
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82697
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkNameAction()
    {
        $params                       = trim_array($this->request->get());
        $validation['warehouse_name'] = 'Required|StrLenGeLe:1,100|>>>:' . $this->t['warehouse_add_name_error'];
        $validation['id']             = 'Required|Int|>>>:' . $this->t->_('params_error', ['param' => 'id']);
        Validation::validate($params, $validation);
        $res = WarehouseService::getInstance()->checkName($params['warehouse_name'], $params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取空仓数量
     * @Permission(menu='contract.warehouse')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83273
     * @return Response|ResponseInterface
     */
    public function getVacantTotalAction()
    {
        $res = WarehouseService::getInstance()->getVacantWarehouseTotal();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 仓库信息列表
     * @Permission(action='contract.warehouse.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80007
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $res    = WarehouseService::getInstance()->list($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库信息列表-导出
     * @Permission(action='contract.warehouse.info_export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86570
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function infoExportAction()
    {
        $params = trim_array($this->request->get());

        // 加锁处理
        $lock_key = md5(RedisKey::CONTRACT_WAREHOUSE_INFO_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            // 大于指定数量, 异步任务 导出
            $total_count = WarehouseService::getInstance()->infoExportCount($params);
            if ($total_count > WarehouseService::getInstance()::SYNC_EXPORT_MAX_COUNT) {
                $result         = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'],
                    DownloadCenterEnum::FINANCE_WAREHOUSE_MANAGEMENT_INFO, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url'      => '',
                ];
            } else {
                // 同步导出
                $params['pageSize']    = WarehouseService::getInstance()::SYNC_EXPORT_MAX_COUNT;
                $params['pageNum']     = GlobalEnums::DEFAULT_PAGE_NUM;
                $params['total_count'] = $total_count;
                $result                = WarehouseService::getInstance()->syncExportInfoData($params);
                $result['data']        = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url'      => is_string($result['data']) ? $result['data'] : '',
                ];
            }

            return $result;
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 仓库信息详情
     * @Permission(action='contract.warehouse.detail')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80022
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id' => 'Required|IntGt:0|>>>:param error[id]',// 仓库主键ID
        ];
        Validation::validate($params, $validation);

        $res = WarehouseService::getInstance()->getDetail($params['id'], WarehouseService::DETAIL_VIEW_TYPE_COMMON,
            $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 更新仓库状态-详情
     * @Permission(action='contract.warehouse.change_status_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82706
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function changeStatusDetailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id' => 'Required|IntGt:0|>>>:param error[id]',// 仓库主键ID
        ];
        Validation::validate($params, $validation);

        $res = WarehouseService::getInstance()->getDetail($params['id'],
            WarehouseService::DETAIL_VIEW_TYPE_CHANGE_STATUS, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 编辑仓库信息-详情
     * @Permission(action='contract.warehouse.edit_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86552
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editDetailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id' => 'Required|IntGt:0|>>>:param error[id]',// 仓库主键ID
        ];
        Validation::validate($params, $validation);

        $res = WarehouseService::getInstance()->getDetail($params['id'], WarehouseService::DETAIL_VIEW_TYPE_EDIT_INFO,
            $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 编辑仓库信息 - 提交
     * @Permission(action='contract.warehouse.edit_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86555
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function editSubmitAction()
    {
        $params = trim_array($this->request->get());
        WarehouseService::saveCommonParamsValidation($params, true);

        $lock_key = md5(RedisKey::CONTRACT_WAREHOUSE_EDIT_SUBMIT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return WarehouseService::getInstance()->editSave($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 仓库信息-编辑网点信息-详情
     * @Permission(action='contract.warehouse.change_store_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82715
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editStoreDetailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id' => 'Required|IntGt:0|>>>:param error[id]',// 仓库主键ID
        ];
        Validation::validate($params, $validation);

        $res = WarehouseService::getInstance()->getDetail($params['id'], WarehouseService::DETAIL_VIEW_TYPE_EDIT_STORE,
            $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 更新仓库状态 - 提交
     * @Permission(action='contract.warehouse.change_status_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80017
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function changeStatusSubmitAction()
    {
        $params = trim_array($this->request->get());
        WarehouseService::changeStatusSaveValidation($params);

        $lock_key = md5('warehouse_change_status_submit_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return WarehouseService::getInstance()->changeStatusSave($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 编辑网点信息-提交
     * @Permission(action='contract.warehouse.change_store_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80027
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function changeStoreSubmitAction()
    {
        $params = trim_array($this->request->get());
        $params = WarehouseService::changeStoreSaveValidation($params);

        $lock_key = md5('warehouse_change_store_submit_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return WarehouseService::getInstance()->changeStoreSave($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 仓库列表搜索[租房合同]
     *
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82679
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'keywords' => 'Required|StrLenGe:1|>>>:param error[keywords]',
        ];
        Validation::validate($params, $validation);

        $res = WarehouseService::getInstance()->getSearchList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库变更审批详情[From 变更记录 / 审批记录]
     * @Permission(menu='contract.warehouse')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84080
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = ['change_apply_id' => 'Required|IntGt:0|>>>:param error[change_apply_id]'];
        Validation::validate($params, $validation);

        $res = AuditService::getInstance()->getDetail($params['change_apply_id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 续签报价
     * @Permission(action='contract.warehouse.price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89198
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function priceSubmitAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5(RedisKey::CONTRACT_WAREHOUSE_PRICE_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->addPrice($params, $this->user, WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_WAREHOUSE);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 简易仓库信息列表
     * @Permission(menu='warehouse_manage')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90986
     * @return Response|ResponseInterface
     */
    public function simpleListAction()
    {
        $params = trim_array($this->request->get());
        $res    = WarehouseService::getInstance()->simpleList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
