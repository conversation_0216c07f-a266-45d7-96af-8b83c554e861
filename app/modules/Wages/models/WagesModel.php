<?php
namespace App\Modules\Wages\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\WagesEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BankListModel;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;
use App\Modules\Wages\Services\AddService;
use App\Repository\HrStaffRepository;

class WagesModel extends Base implements BankFlowModelInterface,PayModelInterface
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('wages');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = 10 and deleted=0"
                ],
                "alias" => "ItemAttachments",
            ]
        );
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = 9 and deleted=0"
                ],
                "alias" => "PaymentAttachments",
            ]
        );
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'no = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }

    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'no in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'no in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }

    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE,
            'no' => $this->no,
            'amount' => floatval($this->actually_amount),
            'currency' => $this->pay_currency,
            'status'   => $this->status,
            'pay_status' => $this->pay_status
        ];
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found reimbursement or reimbursement pay_status is error');
        }

        $item = [];
        $item['payer_bank'] = $data['bank_name'];
        $item['payer_no'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];
        $item['payed_at'] = date("Y-m-d H:i:s");
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_remark'] = $data['ticket_no'];
        $item['payer_id'] = $data['create_id'];
        $item['pay_from'] = 2;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("薪酬扣款-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $sql = 'update wages set 
                         payer_bank="' . $data['bank_name'] . '",
                         payer_no="' . $data['bank_account'] . '",
                         pay_date="' . $data['date'] . '",
                         payed_at="' . date("Y-m-d H:i:s") . '",
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         pay_remark="' . $data['ticket_no'] . '",
                         payer_id="' . $data['create_id'] . '",
                         pay_from=2 where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('薪资扣款付款-批量更新失败==' . $sql);
        }
        return true;
    }

    public function cancel(array $user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found wages or wages pay_status is error');
        }

        $item = [];
        $item['payer_bank'] = null;
        $item['payer_no'] = null;
        $item['pay_date'] = null;
        $item['payed_at'] = null;
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PENDING;    //是否已付款
        $item['pay_remark'] = null;
        $item['payer_id'] = null;
        $item['pay_from'] = 1;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("薪酬扣款-撤销支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;

    }

    public function batch_confirm($ids, $data)
    {
        // TODO: Implement batch_confirm() method.
        return true;
    }

    /**
     * 获得支付模块需要数据
     * @return array
     */
    public function getPayData()
    {
        $amount_total_have_tax = bcadd($this->amount_without_tax, $this->tax, 2);//含税金额总计(含wht) = 不含税金额总计+VAT总计
        $arr = [
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE,//薪资发放
            'no' => $this->no,//申请单号
            'apply_staff_id' => $this->apply_id,//申请人单号
            'apply_staff_name' => $this->apply_name,//申请人姓名
            'cost_department_id' => '',//费用所属部门id
            'cost_department_name' => '',//费用所属部门名字
            'cost_company_id' => $this->exp_company_id,//费用所属公司id
            'cost_company_name' => $this->exp_company_name,//费用所属公司名字
            'apply_date' => $this->apply_date,//单号申请时间
            'pay_method' => $this->pay_method,//支付方式
            'pay_where' => Enums\PayEnums::PAY_WHERE_IN,//境内/境外支付-境内
            'currency' => $this->pay_currency,//币种
            'amount_total_no_tax' => $this->amount_without_tax,//不含税金额总计
            'amount_total_vat' => $this->tax,//vat总计
            'amount_total_have_tax' => $amount_total_have_tax,//含税金额总计(含wht) = 不含税金额总计+VAT总计
            'amount_total_wht' => $this->wht_amount,//wht总计
            'amount_total_have_tax_no_wht' => bcsub($amount_total_have_tax, $this->wht_amount, 2),  //含税金额总计（含VAT不含WHT）= 不含税金额总计+VAT总计-WHT总计
            'amount_loan' => 0, //冲减借款金额,
            'amount_reserve' => 0,//冲减备用金额
            'amount_discount' => 0,//折扣
            'amount_total_actually' => $this->actually_amount,//实付金额
            'amount_remark' => $this->item_detail//备注
        ];

        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
            $arr['default_planned_pay_date'] = date('Y-m-d');//应付日期
            $arr['planned_pay_date']         = date('Y-m-d');//计划支付日期
        }

        //组装收款人信息
        $arr['pays'][] = [
            'staff_info_id' => $this->payee_staff_id,//收款人工号
            'bank_name' => $this->payee_bank,//收款人银行
            'bank_account' => $this->payee_no,//收款人账号
            'bank_account_name' => $this->payee_name,//收款人户名,发放类型
            'amount' => $this->actually_amount//付款金额
        ];
        return $arr;
    }

    /**
     * 支付或者未支付
     * @param array $data
     * @return array
     */
    public function getPayCallBackData($data)
    {
        $new = [];
        $pay_status = $data['pay_status'];
        if ($pay_status == Enums\PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            //三级审批人-支付；已支付
            $new['pay_date'] = $data['pay_bank_flow_date'] ?? date('Y-m-d');
            $new['pay_status'] = $pay_status;
            $new['payer_bank'] = $data['pay_bank_name'];
            $new['payer_no'] = $data['pay_bank_account'];
            $new['pay_remark'] = $data['pay_remark'] ?? '';
        } else if ($pay_status == Enums\PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
            //申请人-撤回；未支付
            $new['pay_status'] = $pay_status;
            $new['pay_remark'] = $data['cancel_reason'] ?? '';
        }
        return $new;
    }

    /**
     * 更新支付信息-收款明细-刷新
     * @param string $no
     * @param int $pay_id
     * @return array
     * @throws ValidationException
     */
    public function getBankInfo(string $no, int $pay_id)
    {
        $self_data = self::findFirst([
            'conditions' => 'no = :no: and status = :status: and pay_status = :pay_status:',
            'bind' => [
                'no' => $no,
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
        ]);
        if (!$self_data) {
            return [];
        } else if ($self_data->apply_type == WagesEnums::APPLY_TYPE_WAGES) {
            //薪酬扣款
            return [];
        } else if ($self_data->apply_type == WagesEnums::APPLY_TYPE_CROWD_SOURCING) {
            return (new AddService())->getBankInfo($no);
        } else if ($self_data->apply_type == WagesEnums::APPLY_TYPE_PERSONAL_AGENT) {
            if (!$self_data->payee_staff_id) {
                return [];
            }

            $user_info = (new HrStaffRepository())->getStaffById($self_data->payee_staff_id);
            if (empty($user_info)) {
                return [];
            }
            $bank_info = BankListModel::findFirst($user_info['bank_type']);
            return [
                'type' => 1,
                'items' => [
                    'bank_name' => isset($bank_info->bank_short_name) ? ($bank_info->bank_short_name ? $bank_info->bank_short_name : $bank_info->bank_name) : '',//银行名称简称,为空取银行名称
                    'bank_account' => $user_info['bank_no'],//银行卡号
                    'bank_account_name' => $user_info['name'],//银行卡名称
                ]
            ];
        }
    }

    /**
     * 打上支付模块标记
     * @return bool
     */
    public function updatePayTag(): bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module' => Enums\PayEnums::BIZ_DATA_IS_PAY_MODULE_YES]) === false) {
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'no = :no: AND status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind' => [
                'no' => $data['payment_no'],
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 本模块的单据, 不可变更收款人信息
        if (empty($main_model) || $main_model->apply_type == WagesEnums::APPLY_TYPE_WAGES) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info = $data['pay'][0] ?? [];
        $sync_data = [
            'payee_name' => $pay_info['bank_account_name'],
            'payee_bank' => $pay_info['bank_name'],
            'payee_no' => $pay_info['bank_account'],
        ];

        if ($main_model->i_update($sync_data) === false) {
            throw new BusinessException('薪酬扣款单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($main_model), ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

        return true;
    }
}
