<?php

namespace App\Modules\Wages\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Enums\WagesEnums;
use App\Library\Exception\BusinessException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Services\PayService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Wages\Models\WagesModel;
use App\Repository\DepartmentRepository;
use App\Util\RedisKey;
use App\Library\Validation\ValidationException;
use App\Modules\Pay\Models\Payment;
use App\Modules\Reimbursement\Services\AddService as ApplyAddService;
use Exception;

class AddService extends BaseService
{
    public static $validate = [
        'no' => 'Str',
        'apply_date'=>'Date',
        'apply_id' =>'Str',
        "apply_name"=> "Str",
        "apply_department_id"=>"Str",
        "apply_department_name"=>"Str",
        "apply_company_name"=>"Str",
        "apply_center_code"=>"Str",
        "apply_store_id"=>"Str",
        "apply_store_name"=>"Str",
        "exp_pay_date_min"=>"Str",
        "exp_pay_date_max" =>"Str",
        "exp_company_id" =>"Int",
        "exp_company_name" =>"Str",
        "pay_method"=>'Int',
        "pay_currency"=>"StrIn:1,2,3",
        "payee_name"=>"Str",
        "payee_no"=>"Str",
        "payee_bank"=>"Str",
        "item_type"=>'Int',
        "item_detail"=>"Str",
        "term_start"=>"Date",
        "term_end"=>"Date",
        "invoice_no"=>"Str",
        "amount_without_tax"=>'Float',
        "tax"=>'Float',
        "amount"=>'Float',
        "tax_rate"=>'Float',
        'payment_attachments'=>'Arr',
        'item_attachments' => 'Arr',
        'wht_category_id' => 'Int',
        'wht_tax_rate_id' => 'Float',
        'wht_amount' => 'Float',
        'actually_amount' => 'Float'

    ];


    protected static $item_exp_companys = [
        '1'     => 'Flash Express',
        '60001' => 'Flash Pay',
        '30001' => 'Flash Money',
        '20001' => 'Flash Fullfillment',
        '50001' => 'F Commerce',
    ];

    protected static $item_exp_companys_th = [
        '1'     => 'Flash Express',
        '60001' => 'Flash Pay',
        '30001' => 'Flash Money',
        '20001' => 'Flash Fullfillment',
        '50001' => 'F Commerce',
        '1222'  => 'Flash Home Operation',
        '858'   => 'Flash Incorporation',
    ];


    //增加到对应的业务块数据
    public static $add_external_validate = [
        'module_type'       => 'Required|StrLenGeLe:5,500|>>>:module_type error',
        'crowdsourcing_no' => 'Required|StrLenGeLe:1,50|>>>:crowdsourcing_no error',
        'term_date'         => 'Required|Date|>>>:term_date error',
        'payee_no'          => 'Required|StrLenGeLe:1,64|>>>:payee_no error',
        'payee_name'        => 'Required|StrLenGeLe:1,128|>>>:payee_name error',
        'payee_bank'        => 'Required|StrLenGeLe:1,128|>>>:payee_bank error',
        'amount'            => 'Required|FloatGtLt:0,************.99|>>>:amount error',
        'wht_category_id'   => 'Required|IntGt:0|>>>:wht_category_id error',
        'wht_tax_rate_id'   => 'Required|FloatGeLe:0,100.00|>>>:wht_tax_rate_id error',
        'wht_amount'        => 'Required|FloatGtLt:0,************.99|>>>:wht_amount error',
        'actually_amount'   => 'Required|FloatGtLt:0,************.99|>>>:actually_amount error',
        'add_date_time'     => 'Required|DateTime|>>>:add_date_time error',
        'batch_id'          => 'Required|StrLenGeLe:1,128|>>>:batch_id error',
        'remark'            => 'StrLenGeLe:0,2000|>>>:remark error',
        'is_personal_agent' => 'IntGe:0',
        'term_end' => 'IfIntEq:is_personal_agent,1|Required|Date|>>>:term_end error',
        'payee_staff_id' => 'IfIntEq:is_personal_agent,1|Required|IntGt:0',
    ];

    //查询业务块数据
    public static $search_external_validate = [
        'module_type'       => 'Required|StrLenGeLe:5,500|>>>:module_name error',
        'no'                => 'StrLenGeLe:0,50|>>>:no error',
        'crowdsourcing_no' => 'IfNotExist:no|Required|StrLenGeLe:1,50|>>>:crowdsourcing_no error',
        'batch_id'          => 'IfNotExist:no|Required|StrLenGeLe:1,128|>>>:batch_id error',
    ];


    /**
     *
     * @return array
     */
    public function getItemTypes(): array
    {
        $types = $this->getExpenseTypeConfig();
        foreach ($types as $type) {
            $itemTypes[] = ['label' => self::$t->t('item_type.' . $type), 'value' => intval($type)];
        }

        return $itemTypes ?? [];
    }

    /**
     * 获取费用类型配置项
     * @return false|string[]
     */
    public function getExpenseTypeConfig()
    {
        $string = EnvModel::getEnvByCode('common_expense_type', '');
        return $string ? explode(',',$string) : [];
    }



    /**
     * @return mixed
     */
    public function defaultData()
    {
        $data['no'] = static::genSerialNo('XC',RedisKey::WAGES_CREATE_COUNTER, 5);
        $data['apply_date'] = date('Y-m-d');
        $data['item_types'] = $this->getItemTypes();
        $data['wht_category'] = EnumsService::getInstance()->getFormatWhtRateConfig();

        $expCompanyList = [];

        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $companyList = self::$item_exp_companys_th;
        } else {
            $companyList = self::$item_exp_companys;
        }

        foreach ($companyList as $key => $value) {
            $expCompanyList[] = ['label' => $value, 'value' => $key];
        }
        $data['exp_company_list'] = $expCompanyList;
        return $data;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function add($data,$user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();

            $wht_tax_config = EnumsService::getInstance()->getWhtRateMap();
            $wht_category_id = $data['wht_category_id'];
            if (!isset($wht_tax_config[$wht_category_id])) {
                throw new BusinessException(self::$t['payment_upload_error_007'], ErrCode::$VALIDATE_ERROR);
            }
            if (!isset($wht_tax_config[$wht_category_id]['rate_list'][$data['wht_tax_rate_id']])) {
                throw new BusinessException(self::$t['payment_upload_error_008'], ErrCode::$VALIDATE_ERROR);
            }
            $paymentAttachments = $data['payment_attachments'];
            $itemAttachments = $data['item_attachments'];
            unset($data['payment_attachments']);
            unset($data['item_attachments']);
            $data = $this->handleData($data,$user);

            if(empty($data['wht_amount']) || empty($data['actually_amount'])) {
                $data['wht_amount'] = $data['amount_without_tax'] * ($data['wht_tax_rate']/100);
                $data['actually_amount'] = $data['amount'] - $data['wht_amount'];
            }
            $data['execute_status'] = WagesEnums::WAGES_EXECUTE_STATUS_SUCCESS;
            $model = new WagesModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('薪酬申请创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }
            if (!empty($paymentAttachments)) {
                $attachments = [];
                foreach ($paymentAttachments as $attachment) {
                    $tmp = [] ;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_WAGES_PAYMENT;
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachments[] = $tmp;
                }
                $attach_bool = (new AttachModel())->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('薪酬创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            if (!empty($itemAttachments)) {
                $attachments = [];
                foreach ($itemAttachments as $attachment) {
                    $tmp = [] ;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_WAGES_ITEM;
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachments[] = $tmp;
                }
                $attach_bool = (new AttachModel())->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('薪酬创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            $flow_bool = (new WagesFlowService())->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException('审批流创建失败', ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }
            $db->commit();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            //$message = static::$t->_('retry_later');
            $message = $e->getMessage();
            $real_message = $e->getMessage();
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning(__METHOD__ . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    public function handleData($data, $user)
    {
        $wht_rate_config = EnumsService::getInstance()->getWhtRateMap();
        $wht_rate_config = isset($wht_rate_config[$data['wht_category_id']]) ? $wht_rate_config[$data['wht_category_id']] : [];

        $data['exp_pay_date_min'] = empty($data['exp_pay_date_min']) ? null:$data['exp_pay_date_min'];
        $data['exp_pay_date_max'] = empty($data['exp_pay_date_max']) ? null:$data['exp_pay_date_max'];

        $data['wht_category'] = $wht_rate_config['label'] ?? '';
        $data['wht_tax_rate'] = $data['wht_tax_rate_id'];

        $data['created_at'] = date("Y-m-d H:i:s");
        $data['created_id'] = $user['id'];
        $data['status'] = Enums::WF_STATE_PENDING;
        $data['pay_status'] = Enums::LOAN_PAY_STATUS_PENDING;

        unset($data['wht_category_id']);
        unset($data['wht_tax_rate_id']);
        return $data;
    }


    /**
     * 众包/快递个人代理数据写入
     *
     * @param array $params
     * @return  array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addExternal($params)
    {
        $flash_pay_bank = PayService::getInstance()->getFlashPayBank();
        $flash_pay_bank = array_keys($flash_pay_bank);
        if (!in_array($params['payee_bank'], $flash_pay_bank, true)) {
            throw new ValidationException(static::$t->_('crowd_sourcing_data_payee_bank_err', ['flash_pay_bank' => implode(',', $flash_pay_bank)]), ErrCode::$VALIDATE_ERROR);
        }

        //获取配置的申请人信息
        $is_personal_agent = $params['is_personal_agent'] ?? 0;
        $apply_user_code = $is_personal_agent == WagesEnums::IS_PERSONAL_AGENT_YES ? 'personal_agent_apply_user' : 'crowd_sourcing_apply_user';
        $user = EnumsService::getInstance()->getSettingEnvValue($apply_user_code);
        if (empty($user)) {
            throw new ValidationException(static::$t->_('crowd_sourcing_apply_user_null'), ErrCode::$VALIDATE_ERROR);
        }
        $user = ApplyAddService::getInstance()->getUserMetaFromBi($user, 1);
        if ($user['state'] != StaffInfoEnums::STAFF_STATE_IN || $user['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
            throw new ValidationException(static::$t->_('crowd_sourcing_oa_user_not_null'), ErrCode::$VALIDATE_ERROR);
        }

        $user            = $user['apply_user'];

        //TODO 产品要求默认是1  但是需要fms传值，所以校验传值内容
        $wht_tax_config = EnumsService::getInstance()->getWhtRateMap();
        $wht_category_id = $params['wht_category_id'];
        if (!isset($wht_tax_config[$wht_category_id])) {
            throw new ValidationException(static::$t->_('payment_upload_error_007'), ErrCode::$VALIDATE_ERROR);
        }

        if (!isset($wht_tax_config[$wht_category_id]['rate_list'][$params['wht_tax_rate_id']])) {
            throw new ValidationException(static::$t->_('payment_upload_error_008'), ErrCode::$VALIDATE_ERROR);
        }

        $model = new WagesModel();

        $wages = $model::findFirst([
            'conditions' => 'crowd_sourcing_no = :crowd_sourcing_no: and pay_status in ({pay_status:array})',
            'bind' => [
                'crowd_sourcing_no' => $params['crowdsourcing_no'],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
            ]
        ]);

        if (!empty($wages)) {
            throw new ValidationException(static::$t->_('crowd_sourcing_existed_data'), ErrCode::$CROWD_SOURCING_NO_ADD_ERROR);
        }

        $default_data                  = $this->defaultData();
        if ($is_personal_agent == WagesEnums::IS_PERSONAL_AGENT_YES) {
            $item_type = WagesEnums::PERSONAL_AGENT_COST_TYPE;//费用类型 - 快递个人代理
            $apply_type = WagesEnums::APPLY_TYPE_PERSONAL_AGENT;//单据类型 - 快递个人代理
            $term_end = $params['term_end'];//费用结束日期
            //个人代理，需要存储特定的公司信息
            $exp_company_id = EnumsService::getInstance()->getSettingEnvValue(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_COMPANY_KEY, 0);
            $personal_agent_company_info = [];
            if ($exp_company_id) {
                $personal_agent_company_info = (new DepartmentRepository())->getDepartmentDetail($exp_company_id);
            }
            //申请人公司
            $apply_company_name = $personal_agent_company_info ? $personal_agent_company_info['name'] : '';
            $exp_company_name = $apply_company_name;//费用所属公司名称
        } else {
            $item_type = WagesEnums::CROWD_SOURCING_COST_TYPE;//费用类型 - 众包费用
            $apply_type =  WagesEnums::APPLY_TYPE_CROWD_SOURCING;//众包
            $term_end = $params['term_date'];//费用结束日期
            $apply_company_name = $user['apply_company_name'];
            $exp_company = $default_data['exp_company_list'][0];
            $exp_company_id = $exp_company['value'];//费用所属公司id
            $exp_company_name = $exp_company['label'];//费用所属公司名称
        }
        $data                          = [];
        $data['no']                    = $default_data['no'];
        $data['apply_id']              = $user['apply_id'];
        $data['apply_name']            = $user['apply_name'];
        $data['apply_company_name']    = $apply_company_name;
        $data['apply_department_id']   = $user['apply_department_id'];
        $data['apply_department_name'] = $user['apply_department_name'];
        $data['apply_store_id']        = $user['apply_store_id'];
        $data['apply_store_name']      = $user['apply_store_name'];
        $data['apply_center_code']     = $user['apply_center_code'];
        $data['pay_method']            = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER;//银行转账
        $data['pay_currency']          = GlobalEnums::CURRENCY_THB;//thb
        $data['wht_category_id']       = 1;//WHT类别：PND3; PND53;
        $data['tax']                   = 0.00;//税额
        $data['tax_rate']              = 0.00;//税率
        $data['created_id']            = $user['apply_id'];//添加人
        $data['invoice_no']            = '';//发票编号
        $data['item_type']             = $item_type;//费用类型
        $data['pay_from']              = 1;//付款来源，1本模块，2银行流水
        $data['exp_company_id']        = $exp_company_id;//费用所属公司id
        $data['exp_company_name']      = $exp_company_name;//费用所属公司名称
        $data['is_pay_module']      = PayEnums::BIZ_DATA_IS_PAY_MODULE_YES;//是否进入了支付模块 0.否 1.是
        $data['apply_type']         = $apply_type;//1 薪酬扣款 2 众包 3 个人代理
        $data['apply_date']         = date('Y-m-d', strtotime($params['add_date_time']));//申请日期
        $data['payee_bank']         = $params['payee_bank'];//收款人开户行
        $data['payee_name']         = $params['payee_name'];//收款人姓名
        $data['payee_no']           = $params['payee_no'];//收款人账号
        $data['item_detail']        = $params['remark'];//费用明细-备注
        $data['term_start']         = $params['term_date'];//费用时间开始
        $data['term_end']           = $term_end;//费用时间结束
        $data['amount_without_tax'] = $params['amount'];//不含税金额 (应付金额)
        $data['amount']             = $params['amount'];//含税金额(应付金额)
        $data['wht_amount']         = $params['wht_amount'];//WHT金额：默认=WHT税率*金额
        $data['actually_amount']    = $params['actually_amount'];//实付金额: 默认 = 金额-WHT金额
        $data['crowd_sourcing_no']  = $params['crowdsourcing_no'];//众包/个人代理单号
        $data['batch_id']           = $params['batch_id'];//批次id
        $user['id']                 = $user['apply_id'];
        $data['wht_tax_rate_id']    = $params['wht_tax_rate_id'];
        $data['payee_staff_id'] = $params['payee_staff_id'] ?? 0;
        $data                       = $this->handleData($data, $user);
        $bool                       = $model->i_create($data);
        if ($bool === false) {
            throw new BusinessException('众包/快递个人代理数据创建失败=' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($model), ErrCode::$CROWD_SOURCING_NO_ADD_ERROR);
        }
        $res['no']              = $data['no'];
        $res['actually_amount'] = $data['actually_amount'];//实付金额
        return $res;
    }


    /**
     * 众包数据写入
     * @param array $params
     * @return  array
     **@throws ValidationException
     */
    public function searchExternal($params)
    {
        $model = new WagesModel();
        if (empty($params['no'])) {
            //当OA单号空的时候，众包编号和批次号不能为空
            if (empty($params['batch_id']) || empty($params['crowdsourcing_no'])) {
                throw new ValidationException(self::$t['crowd_sourcing_no_not_null'], ErrCode::$VALIDATE_ERROR);
            }
            $wages = $model::findFirst([
                'conditions' => 'crowd_sourcing_no = :crowd_sourcing_no: and  batch_id = :batch_id: ',
                'bind' => [
                    'crowd_sourcing_no' => $params['crowdsourcing_no'],
                    'batch_id' => $params['batch_id']
                ]
            ]);
        } else {
            $wages = $model::findFirst([
                'conditions' => 'no = :no:',
                'bind' => [
                    'no' => $params['no']
                ]
            ]);
        }
        if (empty($wages)) {
            throw new ValidationException(self::$t['empty_data'], ErrCode::$VALIDATE_ERROR);
        }

        $payment = Payment::findFirst([
            'conditions' => 'no = :no:',
            'bind' => [
                'no' => $wages->no
            ]
        ]);
        if (empty($payment)) {
            throw new ValidationException(self::$t['crowd_sourcing_data_being_processed'], ErrCode::$CROWD_SOURCING_NO_SEARCH_DATA_NULL_ERROR);
        }

        $res['no']             = $payment->no;
        $res['pay_status']     = $payment->pay_status;
        $res['payer_date']     = $payment->out_trade_at ? (string)strtotime(show_time_zone($payment->payer_date)) : '';//操作时间
        $res['out_trade_at']   = $payment->out_trade_at ? (string)strtotime($payment->out_trade_at) : ($payment->pay_bank_flow_date ? (string)strtotime($payment->pay_bank_flow_date) : '');
        $reason =  $payment->not_pay_reason ? $payment->not_pay_reason : $payment->cancel_reason;
        $res['not_pay_reason'] = $payment->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY ? $reason : '';
        return $res;
    }


    /**
     * 脚本创建审批任务
     * @return object
     **/
    public function getAddFlowTask()
    {
        return WagesModel::find([
            'conditions' => 'apply_type in ({apply_type:array}) and execute_status = :execute_status:',
            'bind' => [
                'apply_type' => [WagesEnums::APPLY_TYPE_CROWD_SOURCING, WagesEnums::APPLY_TYPE_PERSONAL_AGENT],
                'execute_status' => WagesEnums::WAGES_EXECUTE_STATUS_PENDING
            ]
        ]);
    }

    /**
     * 同步支付状态到众包系统
     * @param object $wages 薪酬数据
     * @param object $item 支付数据
     * @return bool
     * @api  https://yapi.flashexpress.pub/project/538/interface/api/78927
     */

    public function postCrowdSourcingPayStatus(object $wages, object $item)
    {
        try {
            $data['oa_no']                   = $wages->no;
            $data['driver_crowdsourcing_no'] = $wages->crowd_sourcing_no;
            $data['batch_id']                = $wages->batch_id;

            $operate_time                    = strtotime(show_time_zone($item->updated_at));//操作时间
            $pay_time                        = $item->out_trade_at ? $item->out_trade_at : $item->pay_bank_flow_date;
            $data['operate_time']            = $operate_time;
            $data['pay_time']                = $pay_time ? strtotime($pay_time) : '';
            $data['actually_amount']         = $item->amount_total_actually;
            $trade_state             = $item->pay_status == Enums::PAYMENT_PAY_STATUS_PAY ? Enums::PAYMENT_PAY_STATUS_PAY : Enums::PAYMENT_PAY_STATUS_NOTPAY;

            $data['trade_state']  =  (string)$trade_state;
            //产品确定支付原因为空推撤回原因
            $data['trade_reason']            = $item->not_pay_reason ? $item->not_pay_reason : $item->cancel_reason;
            $this->logger->info('crowd_sourcing postCrowdSourcingPayStatus request_params :【' . json_encode($data, JSON_UNESCAPED_UNICODE) . '】');
            //联调的时候打开
            $res = $this->getCrowdSourcingResult($data, WagesEnums::$crowd_sourcing_config['crowd_sourcing_pay_stats'], 'th');
        } catch (Exception $e) {
            $res = ['code' => ErrCode::$DEFAULT ,'msg' => $e->getMessage()];
            $this->logger->error('众包同步状态失败 失败原因是:' . $e->getMessage() . '; 对应的数据是 ' . json_encode($wages->toArray(), JSON_UNESCAPED_UNICODE) . '/' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));
        }
        return $res;

    }


    /**
     * 众包查询银行信息
     * @param object $wages 薪酬数据
     * @return bool
     **@api  https://yapi.flashexpress.pub/project/538/interface/api/78927
     */

    public function postCrowdSourcingPayeeInfo(object $wages)
    {
        if (empty($wages->crowd_sourcing_no)) {
            return [];
        }
        try {
            $data['driver_crowdsourcing_no'] = $wages->crowd_sourcing_no;
            $res = $this->getCrowdSourcingResult($data, WagesEnums::$crowd_sourcing_config['crowd_sourcing_payee_info']);
            return $res['code'] == ErrCode::$SUCCESS ? $res['data'] : [];
        } catch (Exception $e) {
            $real_message = $e->getMessage();
            $this->logger->error('众包查询银行信息失败 失败原因是:' . $real_message . '; 对应的数据是 ' . json_encode($wages->toArray(), JSON_UNESCAPED_UNICODE));
        }
        return true;

    }


    /**
     * 封装数据到众包系统
     *
     * @param array $params 参数组
     * @param string $route 路由地址
     * @param string $language 回传系统语言
     * @return bool|mixed|string
     * @throws Exception
     */
    public function getCrowdSourcingResult(array $params, string $route, string $language = '')
    {
            $crowd_sourcing_url = env('crowd_sourcing_url');
            $json_params = json_encode($params, JSON_UNESCAPED_UNICODE);
            $header[]    = 'Content-type: application/json;charset=utf-8';
            $header[]    = 'Accept: application/json';
            $header[]    = 'Accept-Language: ' . !empty($language) ? $language : static::$language;
            $this->logger->info('crowd_sourcing getCrowdSourcingResult request_params :【' . $crowd_sourcing_url . $route . $json_params . '】');
            $res_data     = curl_request($crowd_sourcing_url . $route, $json_params, 'POST', $header);
            $res_data_arr = json_decode($res_data, true);
            $this->logger->info('crowd_sourcing getCrowdSourcingResult response_result:【' . json_encode($res_data_arr, JSON_UNESCAPED_UNICODE) . '】');
            if ($res_data_arr['code'] != ErrCode::$SUCCESS) {
                //调取众包接口异常
                throw new Exception('crowd_sourcing request failed');
            }
            return $res_data_arr;

    }


    /**
     * 更新支付信息-收款明细-刷新
     *
     * @param string $no
     * @return array
     * @throws ValidationException
     */
    public function getBankInfo(string $no)
    {
        $wages      = WagesModel::findFirst(
            [
                'conditions' => 'no = :no: and  apply_type = :apply_type:',
                'bind'       => ['no' => $no, 'apply_type' => WagesEnums::APPLY_TYPE_CROWD_SOURCING]
            ]
        );
        if (empty($wages)) {
            return [];
        }
        $payee_info = $this->postCrowdSourcingPayeeInfo($wages);
        if (empty($payee_info['payee_no']) || empty($payee_info['payee_name']) || empty($payee_info['payee_bank'])) {
            throw new ValidationException(static::$t->_('crowd_sourcing_payee_info_err'), ErrCode::$VALIDATE_ERROR);
        }
        $flash_pay_bank = PayService::getInstance()->getFlashPayBank();
        $flash_pay_bank = array_keys($flash_pay_bank);
        if (!in_array($payee_info['payee_bank'], $flash_pay_bank, true)) {
            throw new ValidationException(static::$t->_('crowd_sourcing_data_payee_bank_err', ['flash_pay_bank' => implode(',', $flash_pay_bank)]), ErrCode::$VALIDATE_ERROR);
        }

        return [
            'type'  => 1,
            'items' => [
                'bank_account'      => (string)$payee_info['payee_no'],
                'bank_account_name' => $payee_info['payee_name'],
                'bank_name'         => $payee_info['payee_bank'],
            ]
        ];
    }

    /**
     * 获取费用类型枚举
     */
    public function getOptionsDefault()
    {
        $data['item_types'] = $this->getItemTypes();
        return $data;
    }

    /**
     * 同步支付状态到快递系统
     * @param object $wages 薪酬数据
     * @param object $item 支付数据
     * @return bool
     * @api  https://console-docs.apipost.cn/preview/b744b86861b5558e/2b71ea45be61999d?target_id=8db96647-d85e-4bc4-85d2-81fa959cc4ff
     */
    public function postBIPayStatus(object $wages, object $item)
    {
        try {
            $data['pay_no'] = $wages->no;
            $data['crowdsourcing_no'] = $wages->crowd_sourcing_no;
            $data['batch_id'] = $wages->batch_id;
            $trade_state = $item->pay_status == Enums::PAYMENT_PAY_STATUS_PAY ? Enums::PAYMENT_PAY_STATUS_PAY : Enums::PAYMENT_PAY_STATUS_NOTPAY;
            $data['pay_state']  =  (string)$trade_state;
            $data['actually_amount'] = $item->amount_total_actually;
            $pay_time = $item->out_trade_at ? $item->out_trade_at : $item->pay_bank_flow_date;
            $data['pay_time'] = zero_time_zone($pay_time);

            //产品确定支付原因为空推撤回原因
            $data['fail_reason'] = $item->not_pay_reason ? $item->not_pay_reason : $item->cancel_reason;
            $bi_rpc = new ApiClient('bi_new_svc', '', WagesEnums::$bi_route_list['pay_status'], 'th');
            $bi_rpc->setParams([$data]);
            $rpc_res = $bi_rpc->execute();
            $res = $rpc_res['result'] ?? [];
        } catch (Exception $e) {
            $res = ['code' => ErrCode::$DEFAULT ,'msg' => $e->getMessage()];
            $this->logger->error('同步支付状态到快递系统失败， 失败原因是:' . $e->getMessage() . '; 对应的数据是 单据信息组：' . json_encode($wages->toArray(), JSON_UNESCAPED_UNICODE) . '/ 单据支付信息组：' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));
        }
        return $res;
    }

}
