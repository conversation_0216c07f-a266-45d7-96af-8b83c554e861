<?php

namespace App\Modules\Loan\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Loan\Services\AddService;

class Loan extends Base implements BankFlowModelInterface,PayModelInterface
{
    //public $id;

    public $lno;

    public $lname;

    public $create_name;

    public $create_id;

    public $create_phone;

    public $create_email;

    public $create_department_id;

    public $create_department_name;

    public $create_company_id;

    public $create_company_name;

    public $create_date;

    public $cost_center_id;

    public $cost_center_name;

    public $type;

    public $type_other;

    public $pay_type;

    public $currency;

    public $event_name;

    public $amount;

    public $is_fina;

    public $finished_at;

    public $back_at;

    public $event_info;

    public $status;

    public $pay_status;

    public $is_finished;

    public $refuse_reason;

    public $created_at;

    public $updated_at;

    public $cancel_reason;

    public $back_date;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('loan');

        $this->hasOne(
            'id',
            LoanPayBank::class,
            'loan_id',
            [
                "alias" => "Bank"
            ]
        );

        $this->hasOne(
            'id',
            LoanTravel::class,
            'loan_id',
            [
                "alias" => "Travel"
            ]
        );

        $this->hasOne(
            'id',
            LoanPay::class,
            'loan_id',
            [
                "alias" => "Pay"
            ]
        );

        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key',
            [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_LOAN . " and deleted=0"
                ],
                "alias" => "File",
            ]
        );

        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key',
            [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_LOAN_BACK . " and deleted=0 and sub_type = 0"
                ],
                "alias" => "Backs",
            ]
        );

        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key',
            [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_LOAN_CONSENT . " and deleted=0"
                ],
                "alias" => "FileConsent",
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }


    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'lno = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }


    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'lno in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'lno in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }




    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN,
            'no' => $this->lno,
            'amount' => bcdiv($this->amount,1000,2),
            'currency' => $this->currency,
            'status'   => $this->status,
            'pay_status' => $this->pay_status
        ];
    }

    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found loan or loan pay_status is error');
        }

        $item = [];
        $item['sign_name'] = $this->create_name;    //签收人
        $item['is_sign'] = Enums::LOAN_PAY_SIGN;    //是否签收，是
        $item['create_id'] = $data['create_id'];
        $item['create_name'] = $data['create_name'];
        $item['create_department_name'] = $data['create_department_name'] ?? '';
        $item['create_job_title_name'] = $data['create_job_title_name'] ?? '';
        $item['pay_bank_name'] = $data['bank_name'];
        $item['pay_bank_account'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];          //银行流水日期 = 流水交易日期
        $item['pay_transaction_date'] = $data['date'];          //过账日期
        $item['sign_date'] = $data['date'];          //签收日期 = 流水交易日期

        $item['pay_type'] = 2;  //TTB;
        if ($data['bank_name'] == 'SCB') {
            $item['pay_type'] = 3;// SCB
        }
        $item['pay_from'] = 2;
        $item['created_at'] = date('Y-m-d H:i:s');


        //如果没有付过款=肯定没有付款，除非数据有错误
        $item['loan_id'] = $this->id;
        $loan_pay = new LoanPay();
        $bool = $loan_pay->i_create($item);
        if ($bool === false) {
            throw new BusinessException("借款申请付款-创建失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        $bool = $this->i_update(["pay_status" => Enums::LOAN_PAY_STATUS_PAY, "updated_at" => date("Y-m-d H:i:s")]);
        if ($bool === false) {
            throw new BusinessException("借款申请付款-更新借款申请失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data){
        $batchData = [];

        $item = [];
        $item['sign_name'] = '';    //签收人
        $item['is_sign'] = Enums::LOAN_PAY_SIGN;    //是否签收，是
        $item['create_id'] = $data['create_id'];
        $item['create_name'] = $data['create_name'];
        $item['create_department_name'] = $data['create_department_name'] ?? '';
        $item['create_job_title_name'] = $data['create_job_title_name'] ?? '';
        $item['pay_bank_name'] = $data['bank_name'];
        $item['pay_bank_account'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];          //银行流水日期 = 流水交易日期
        $item['pay_transaction_date'] = $data['date'];          //过账日期

        $item['sign_date'] = $data['date'];          //签收日期 = 流水交易日期
        $item['created_at'] = date('Y-m-d H:i:s');

        $item['pay_type'] = 2;  //TTB;
        if ($data['bank_name'] == 'SCB') {
            $item['pay_type'] = 3;// SCB
        }
        $item['pay_from'] = 2;
        foreach ($ids as $id){
            $item['loan_id'] = $id;

            $batchData[] = $item;
        }

        $loan_pay = new LoanPay();
        $bool = $loan_pay->batch_insert($batchData);
        if ($bool === false) {
            throw new BusinessException("借款申请付款-批量创建失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        //15873需求，支付成功后，需要将借款单状态置为未开始归还状态
        $sql = 'update loan set pay_status =' . Enums::LOAN_PAY_STATUS_PAY . ',loan_status=' . Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN . ',updated_at="' . date("Y-m-d H:i:s") . '" where id in (' . implode(',',$ids) . ')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if($bool === false){
            throw new BusinessException('借款申请付款-批量更新失败=='.$sql);
        }
        return true;
    }






    public function cancel($user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found loan or loan pay_status is error');
        }

        //删除支付信息
        $this->getPay()->delete();

        $bool = $this->i_update(["pay_status" => Enums::LOAN_PAY_STATUS_PENDING, "updated_at" => date("Y-m-d H:i:s")]);
        if ($bool === false) {
            throw new BusinessException("借款申请付款-更新借款申请失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        //借款没有，直接返回true就行
        return true;
    }


    /**
     * 获得支付模块需要数据
     * @return array
     */
    public function getPayData()
    {
        $arr = [
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN,
            'no' => $this->lno,
            'apply_staff_id' => $this->create_id,
            'apply_staff_name' => $this->create_name,
            'apply_date' => $this->create_date,
            'pay_method' => $this->pay_type,
            'currency' => $this->currency,
            'amount_total_no_tax' => bcdiv($this->amount, 1000, 2),       //不含税金额
            'amount_total_vat' => 0,                                                //税额
            'amount_total_have_tax' => bcdiv($this->amount, 1000, 2),    //含税金额（含VAT含WHT）
            'amount_total_wht' => 0,                          //wht总计
            'amount_total_have_tax_no_wht' => bcdiv($this->amount, 1000, 2),   //含税金额总计（含VAT不含WHT）
            'amount_loan' => 0,                                                          //冲减借款金额,
            'amount_reserve' => 0,
            'amount_discount' => 0,                           //折扣
            'amount_total_actually' => bcdiv($this->amount, 1000, 2),        //实付金额
            'amount_remark' => $this->event_info,                                         //备注
            'cost_company_id' => $this->create_company_id,
            'cost_company_name' => $this->create_company_name,
            'default_planned_pay_date' => date('Y-m-d'),//应付日期
            'planned_pay_date' => date('Y-m-d'),//计划支付日期
        ];

        if (!empty($this->create_node_department_id)) {
            $arr['cost_department_id'] = $this->create_node_department_id;
            $arr['cost_department_name'] = $this->create_node_department_name;
        } else {
            $arr['cost_department_id'] = $this->create_department_id;
            $arr['cost_department_name'] = $this->create_department_name;
        }
        ////这个费用公司id，没存。所以我自己根据费用部门找。
        //$arr['cost_company_id'] = SysDepartmentModel::getCompanyIdByDepartmentId($arr['cost_department_id']);
        ////费用公司名字
        //$arr['cost_company_name'] = '';
        //if (!empty($arr['cost_company_id'])) {
        //    $arr['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($arr['cost_company_id']);
        //}

        $arr['pays'] = [];

        $bank = $this->getBank();
        if (!empty($bank)) {
            $tmp = [];
            $tmp['bank_name'] = $bank->bank_type;       //收款人银行名称
            $tmp['bank_account'] = $bank->account;      //收款人账号
            $tmp['bank_account_name'] = $bank->name;    //收款人户名
            $tmp['amount'] = $arr['amount_total_actually'];
            $arr['pays'][] = $tmp;
        }
        return $arr;
    }

    public function getPayCallBackData($data)
    {
        $new = [];
        $pay_status = $data['pay_status'];
        if ($pay_status == Enums::LOAN_PAY_STATUS_PAY) {
            $new['is_sign'] = Enums::LOAN_PAY_SIGN;
            $new['mark'] = $data['pay_remark'] ?? '';
            $new['pay_date'] = $data['pay_bank_flow_date'] ?? date("Y-m-d H:i:s");
            $new['sign_date'] = date("Y-m-d H:i:s");
            $new['pay_type'] = 2;           //借款是写死的银行，根据pay_type判断。1是现金，2是TTB，3是SCB
            $new['pay_bank_name'] = $data['pay_bank_name'] ?? '';
            $new['pay_bank_account'] = $data['pay_bank_account'] ?? '';
        } else {
            $new['is_sign'] = Enums::LOAN_PAY_NOTSIGN;
            $new['not_sign_reason'] = $data['not_pay_reason'];
            $new['pay_date'] = null;
        }

        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     * @param $no
     * @param $pay_id
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no,$pay_id){
        $self_data = self::findFirst(
            [
                'conditions' => 'lno=:nos: and status = :status: and pay_status = :pay_status:',
                'bind' => [
                    'nos' => $no,
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
                ]
            ]
        );
        if (!isset($self_data->create_id)){
            return [];
        }
        $apply_info = AddService::getInstance()->getUserMetaFromBi($self_data->create_id);
        $bank_info = [];
        $bank_info['bank_name'] = $apply_info['bank']['bank_type']??'';
        $bank_info['bank_account'] = $apply_info['bank']['account']??'';
        $bank_info['bank_account_name'] = $apply_info['bank']['name']??'';
        return [
            'type' => 1,
            'items' => $bank_info
        ];
    }

    //打上支付模块标记
    public function updatePayTag():bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module'=>1]) === false){
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'lno = :no: AND status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind' => [
                'no' => $data['payment_no'],
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 本模块的单据, 不可变更收款人信息
        if (empty($main_model)) {
            return true;
        }

        // 借款单的收款人信息
        $pay_bank_model = $main_model->getBank();
        if (empty($pay_bank_model)) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($pay_bank_model->toArray(), JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info = $data['pay'][0] ?? [];
        $sync_data = [
            'name' => $pay_info['bank_account_name'],
            'bank_type' => $pay_info['bank_name'],
            'account' => $pay_info['bank_account'],
        ];

        if ($pay_bank_model->i_update($sync_data) === false) {
            throw new BusinessException('借款申请单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($pay_bank_model), ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($pay_bank_model->toArray(), JSON_UNESCAPED_UNICODE));

        return true;
    }
}
