<?php
/**
 * 商品管理
 */

namespace App\Modules\Shop\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Enums\ShopEnums;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\InteriorGoodsAutoPricePolicyModel;
use App\Models\oa\SysImageGalleryModel;
use App\Modules\Common\Models\EnvModel as OaEnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Shop\Models\InteriorOrders;
use App\Modules\Shop\Models\SysStoreGoodsModel;
use App\Modules\Shop\Models\InteriorGoodsModel;
use App\Modules\Shop\Models\InteriorGoodsSkuModel;
use App\Models\backyard\SettingEnvModel as ByEnvModel;
use App\Models\backyard\InteriorGoodsManageDepartmentPermissionModel;
use App\Models\backyard\InteriorGoodsStoreCateRelModel;
use App\Repository\DepartmentRepository;
use App\Repository\oa\SysImageGalleryRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class GoodsManagementService extends BaseService
{

    //非必需的筛选条件
    public static $not_must_params = [
        'goods_name',
        'barcode',
        'department_name',
        'pageSize',
        'pageNum',
    ];

    //列表搜索
    public static $validate_list_search = [
        'goods_name' => 'StrLenGeLe:0,100',
        'barcode'    => 'StrLenGeLe:0,30',
        'pageSize'   => 'IntGt:0', //每页条数
        'pageNum'    => 'IntGt:0', //页码
    ];


    //查看 、 详情
    public static $validate_detail = [
        'id' => 'Required|IntGe:1|>>>:params error id',
    ];

    //查看商品更多部门
    public static $validate_manage_department_list = [
        'department_name' => 'StrLenGeLe:0,100|>>>:params error department_name',
        'permission_type' => 'Required|IntIn:' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE . ',' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE . '|>>>:params error permission_type',
        'goods_id'        => 'Required|IntGe:1|>>>:params error goods_id',
        'pageSize'        => 'IntGt:0', //每页条数
        'pageNum'         => 'IntGt:0', //页码
    ];

    //自动定价策略
    public static $validate_auto_price_policy = [
        'policy'              => 'Required|ArrLenGeLe:1,10',
        'policy[*].day_begin' => 'Required|IntGeLe:1,1000',
        'policy[*].day_end'   => 'Required|IntGeLe:1,1000',
        'policy[*].discount'  => 'Required|FloatGtLe:0,100',
    ];


    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 添加/编辑更新的校验
     *
     * @param array $params
     * @param bool $is_update
     * @return void
     * @throws ValidationException
     */
    public static function checkSubmitValidation($params = [], $is_update = false)
    {
        $validation = [
            'goods_type'                 => 'Required|IntIn:' . ShopEnums::GOODS_TYPE_WORK_CLOTHES . ',' . ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|>>>:params error goods_type',
            'goods_name_zh'              => 'Required|StrLenGeLe:1,80|>>>:' . static::$t->_('mall_goods_submit_check_1'),
            'goods_name_en'              => 'Required|StrLenGeLe:1,80|>>>:' . static::$t->_('mall_goods_submit_check_2'),
            'goods_name_local'           => 'Required|StrLenGeLe:0,80|>>>:' . static::$t->_('mall_goods_submit_check_3'),
            'status'                     => 'Required|IntIn:0,1|>>>:' . static::$t->_('mall_goods_submit_check_5'),
            'is_limit_buy'               => 'Required|IntIn:0,1|>>>:params error[is_limit_buy]',
            'limit_buy_store_cate_list'  => 'ArrLenGeLe:0,100|>>>:params error[limit_buy_store_cate_list]',
            'is_free'                    => 'Required|IntIn:0,1|>>>:params error[is_free]',
            'free_num'                   => 'IfIntEq:is_free,1|Required|IntGeLe:1,99|>>>:' . static::$t->_('mall_goods_submit_check_9'),
            'free_buy_store_cate_list'   => 'ArrLenGeLe:0,100|>>>:params error[free_buy_store_cate_list]',
            'img_path'                   => 'Required|ArrLenGeLe:1,5|>>>:params error img_path',
            'img_path[*]'                => 'Required|Url|>>>:params error img_path',
            'sku_list'                   => 'Required|ArrLenGe:1|>>>:' . static::$t->_('mall_goods_submit_check_10'),
            'sku_list[*].goods_sku_code' => 'Required|StrLenGeLe:1,30|>>>:' . static::$t->_('mall_goods_submit_check_7'),
            'sku_list[*].attr_1'         => 'Required|StrLenGeLe:1,20|>>>:params error attr_1',
            'sku_list[*].sort_number'    => 'Required|IntGeLe:1,999999999|>>>:' . static::$t->_('params_error',
                    ['param' => 'sort_number']),
            'sku_list[*].price'          => 'Required|FloatGeLe:0,999999999999.99|>>>:' . static::$t->_('mall_goods_submit_check_8'),
            'sku_list[*].status'         => 'Required|IntIn:0,1|>>>:' . static::$t->_('mall_goods_submit_check_6'),

            'manage_department_purchase' => 'ArrLenGeLe:0,100|>>>:params error manage_department_purchase',
            'manage_department_free'     => 'ArrLenGeLe:0,100|>>>:params error manage_department_free',

            'buy_hire_type'  => 'ArrLenGeLe:0,100|>>>:params error[buy_hire_type]',
            'free_hire_type' => 'ArrLenGeLe:0,100|>>>:params error[free_hire_type]',
            'is_auto_price'  => 'IfIntEq:goods_type,' . ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|Required|IntIn:' . ShopEnums::IS_AUTO_PRICE_NO . ',' . ShopEnums::IS_AUTO_PRICE_YES,
        ];

        if (!empty($params['size_img_path'])) {
            $validation['size_img_path'] = 'Url|>>>:params error size_img_path';
        }

        if ($is_update) {
            $validation['id'] = 'Required|IntGe:1|>>>:params error id ';
        }
        //无头件商品价值必须传递
        if ($params['goods_type'] && $params['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
            $validation['sku_list[*].commodity_value'] = 'Required|FloatGeLe:0,999999999999.99';
        }


        Validation::validate($params, $validation);

        // sku的sort_number 不能重复
        $sort_number_count = array_unique(array_filter(array_column($params['sku_list'], 'sort_number')));
        if (count($sort_number_count) < count($params['sku_list'])) {
            throw new ValidationException(static::$t->_('mall_goods_submit_check_16'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 获取商品状态、商品尺码、网点类型权限配置
     *
     * @return array
     */
    public static function getEnumsSetting()
    {
        // 商品状态
        $goods_status = [];
        foreach (ShopEnums::$goods_status_item as $status => $lang_key) {
            $goods_status[] = [
                'value' => $status,
                'label' => static::$t->_($lang_key),
            ];
        }
        //工服、无头件
        $goods_type_item = [];
        foreach (ShopEnums::$goods_type as $type_value => $type_lang_key) {
            $goods_type_item[] = [
                'value' => $type_value,
                'label' => static::$t->_($type_lang_key),
            ];
        }

        //支付凭证状态
        $payment_voucher_status = [];
        foreach (ShopEnums::$payment_voucher_status as $payment_voucher_value => $payment_voucher_lang_key) {
            $payment_voucher_status[] = [
                'value' => $payment_voucher_value,
                'label' => static::$t->_($payment_voucher_lang_key),
            ];
        }

        // 商品尺码
        $goods_size = self::getGoodsSizeSetting();
        $goods_size = array_map(function ($val) {
            return [
                'value' => $val,
                'label' => $val,
            ];
        }, $goods_size);

        // 商品权限(网点类型枚举)
        $store_cate = self::getGoodsStoreCate();
        //雇佣类型枚举
        $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum(true);

        //是否自动定价
        $is_auto_price = [];
        foreach (ShopEnums::$is_auto_price as $value => $label) {
            $is_auto_price[] = [
                'value' => $value,
                'label' => static::$t->_($label),
            ];
        }
        return [
            'goods_type'             => $goods_type_item,
            'goods_status'           => $goods_status,
            'payment_voucher_status' => $payment_voucher_status,
            'goods_size'             => $goods_size,
            'store_cate'             => $store_cate,
            'hire_type_enum'         => $hire_type_enum,
            'is_auto_price'          => $is_auto_price,
        ];
    }

    /**
     * 添加商品
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function addOne(array $params = [], array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];

        $db = $this->getDI()->get('db_backyard');
        $db->begin();

        try {
            $sku_list = $params['sku_list'];

            // 在售状态的尺码不允许重复
            $sku_total         = count($sku_list);
            $unique_size_total = count(array_unique(array_map('strtolower', array_column($sku_list, 'attr_1'))));
            if ($unique_size_total < $sku_total) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_11'), ErrCode::$VALIDATE_ERROR);
            }

            // sku 唯一性校验: 本次提交的是否重复
            $all_barcode_item = array_column($sku_list, 'goods_sku_code');
            $repeat_barcode   = array_repeat_element($all_barcode_item);
            if (!empty($repeat_barcode)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_12',
                    ['repeat_barcode' => implode('、', $repeat_barcode)]), ErrCode::$VALIDATE_ERROR);
            }

            //无头件的商品不可以免费
            if ($params['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE && $params['is_free'] == ShopEnums::FREE_BUY_STATUS_YES) {
                throw new ValidationException(static::$t->_('goods_type_unclaimed_not_free'), ErrCode::$VALIDATE_ERROR);
            }

            //如果开启了限购 判断权限和部门不能全部为空
            if ($params['is_limit_buy'] == ShopEnums::LIMIT_BUY_STATUS_YES) {
                if (empty($params['limit_buy_store_cate_list']) && empty($params['manage_department_purchase'])) {
                    throw new ValidationException(static::$t->_('purchase_goods_manage_department_permission_null_err'),
                        ErrCode::$VALIDATE_ERROR);
                }
            } else {
                $params['limit_buy_store_cate_list'] = [];
                $params['buy_hire_type']             = [];
            }

            //如果开启了免费 判断权限和部门不能全部为空
            if ($params['is_free'] == ShopEnums::FREE_BUY_STATUS_YES) {
                if (empty($params['free_buy_store_cate_list']) && empty($params['manage_department_free'])) {
                    throw new ValidationException(static::$t->_('free_goods_manage_department_permission_null_err'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            // sku 编号是否被占用: 和库里的是否重复
            $exist_barcode = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_sku_code IN ({codes:array})',
                'bind'       => ['codes' => $all_barcode_item],
                'columns'    => ['goods_sku_code'],
            ])->toArray();
            $exist_barcode = array_column($exist_barcode, 'goods_sku_code');
            if (!empty($exist_barcode)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_12',
                    ['repeat_barcode' => implode('、', $exist_barcode)]));
            }

            // 免费: 免费数量 和 网点关联
            if ($params['is_free'] == ShopEnums::FREE_BUY_STATUS_NO) {
                $params['free_num']                 = 0;
                $params['free_buy_store_cate_list'] = [];
                $params['free_hire_type']           = [];
            }

            // spu 添加
            $spu_data    = [
                'goods_name_zh'  => $params['goods_name_zh'],
                'goods_name_th'  => $params['goods_name_local'],
                'goods_name_en'  => $params['goods_name_en'],
                'img_path'       => implode(',', $params['img_path']),
                'size_img_path'  => $params['size_img_path'],
                'goods_type'     => $params['goods_type'],
                'status'         => ShopEnums::GOODS_STATUS_ON_SALE,
                'free_num'       => $params['free_num'],
                'is_free'        => $params['is_free'],
                'is_limit_buy'   => $params['is_limit_buy'],
                'buy_hire_type'  => $params['buy_hire_type'] ? implode(',', $params['buy_hire_type']) : '',
                'free_hire_type' => $params['free_hire_type'] ? implode(',', $params['free_hire_type']) : '',
                'is_auto_price'  => $params['is_auto_price'] ?? ShopEnums::IS_AUTO_PRICE_NO,//是否自动定价
                'created_id'     => $user['id'],
                'updated_id'     => $user['id'],
            ];
            $goods_model = new InteriorGoodsModel();
            if ($goods_model->i_create($spu_data) === false) {
                throw new BusinessException('商品添加 - spu创建失败, 原因可能是: ' . get_data_object_error_msg($goods_model) . '; 数据: ' . json_encode($spu_data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 最新写入的spu id
            if ($goods_model->i_update(['goods_cate' => $goods_model->id]) === false) {
                throw new BusinessException("商品添加 - spu的goods_cate更新失败[spu_id = {$goods_model->id}], 原因可能是: " . get_data_object_error_msg($goods_model),
                    ErrCode::$BUSINESS_ERROR);
            }


            // sku 批量添加
            foreach ($sku_list as &$sku) {
                $sku['goods_id']        = $goods_model->id;
                $sku['goods_name_en']   = $goods_model->goods_name_en . ' ' . $sku['attr_1'];
                $sku['goods_name_th']   = trim($goods_model->goods_name_th . ' ' . $sku['attr_1']);
                $sku['goods_name_zh']   = $goods_model->goods_name_zh . ' ' . $sku['attr_1'];
                $sku['img_path']        = $params['img_path'][0];
                $sku['status']          = ShopEnums::GOODS_STATUS_ON_SALE;
                $sku['created_id']      = $user['id'];
                $sku['updated_id']      = $user['id'];
                $sku['commodity_value'] = $sku['commodity_value'] ? $sku['commodity_value'] : '0.00';
            }

            $sku_model = new InteriorGoodsSkuModel();
            if (!empty($sku_list) && $sku_model->batch_insert($sku_list, 'db_backyard') === false) {
                throw new BusinessException('商品添加 - sku批量创建失败, 数据: ' . json_encode($sku_list, JSON_UNESCAPED_UNICODE),
                    ErrCode::$BUSINESS_ERROR);
            }
            // 免费商品权限配置: 网点-免费商品关系
            if (!empty($params['free_buy_store_cate_list'])) {
                $this->addStoreCateRel([
                    'id'              => $goods_model->id,
                    'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    'store_cate'      => $params['free_buy_store_cate_list'],
                ], $user, false);
            }

            // 可购买商品权限配置: 网点-商品关系
            if (!empty($params['limit_buy_store_cate_list'])) {
                $this->addStoreCateRel([
                    'id'              => $goods_model->id,
                    'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE,
                    'store_cate'      => $params['limit_buy_store_cate_list'],
                ], $user, false);
            }

            // 商品限购
            if ($params['is_limit_buy'] == ShopEnums::LIMIT_BUY_STATUS_YES) {
                $limit_buy_goods_ids = [$goods_model->id];

                $limit_buy_goods_model = ByEnvModel::findFirst([
                    'conditions' => 'code = :code:',
                    'bind'       => ['code' => ShopEnums::LIMIT_BUY_GOODS_IDS_CODE],
                ]);

                if (empty($limit_buy_goods_model)) {
                    $limit_buy_goods_model         = new ByEnvModel();
                    $limit_buy_goods_model->code   = ShopEnums::LIMIT_BUY_GOODS_IDS_CODE;
                    $limit_buy_goods_model->remark = '工服限制购买goods_id';
                } else {
                    $limit_buy_goods_ids = array_merge($limit_buy_goods_ids,
                        explode(',', $limit_buy_goods_model->set_val));
                    $limit_buy_goods_ids = array_unique(array_filter($limit_buy_goods_ids));
                }

                $limit_buy_goods_model->set_val = implode(',', $limit_buy_goods_ids);
                if ($limit_buy_goods_model->save() === false) {
                    throw new BusinessException("商品添加 - 限购商品[{$goods_model->id}]配置失败[interior_buy_limit_goods_id], 原因可能是: " . get_data_object_error_msg($limit_buy_goods_model) . '; 数据: ' . json_encode($limit_buy_goods_model->toArray(),
                            JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //提交的数据存在限购部门
            if (!empty($params['manage_department_purchase'])) {
                $this->batchInsertManageDepartment($params['manage_department_purchase'],
                    ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE, $goods_model, $user);
            }
            //提交的数据存在免费部门
            if (!empty($params['manage_department_free'])) {
                $this->batchInsertManageDepartment($params['manage_department_free'],
                    ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE, $goods_model, $user);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城 - 商品后台管理 - 商品信息添加异常: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城 - 商品后台管理 - 商品信息添加异常: ' . $e->getMessage());
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 更新商品信息
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function updateOne(array $params = [], array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        $db = $this->getDI()->get('db_backyard');
        $db->begin();

        try {
            // spu 是否存在
            $goods_model = InteriorGoodsModel::findFirst($params['id']);
            if (empty($goods_model)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_13') . "[{$params['id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            $sku_list = $params['sku_list'];

            // spu, sku 状态校验 spu 停售, sku 须为全部停售
            $sku_status_item = array_unique(array_column($sku_list, 'status'));
            if ($params['status'] == ShopEnums::GOODS_STATUS_HALT_SALE && in_array(ShopEnums::GOODS_STATUS_ON_SALE,
                    $sku_status_item)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_14'), ErrCode::$VALIDATE_ERROR);
            }

            // spu 在售, sku 须至少一个为在售
            if ($params['status'] == ShopEnums::GOODS_STATUS_ON_SALE && count($sku_status_item) == 1 && in_array(ShopEnums::GOODS_STATUS_HALT_SALE,
                    $sku_status_item)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_14'), ErrCode::$VALIDATE_ERROR);
            }

            // 在售状态的sku
            $on_sale_sku_item = array_filter(array_map(function ($v) {
                if ($v['status'] == ShopEnums::GOODS_STATUS_ON_SALE) {
                    return $v;
                }
            }, $sku_list));

            // 在售状态的尺码不允许重复
            $unique_size_total = count(array_unique(array_map('strtolower',
                array_column($on_sale_sku_item, 'attr_1'))));
            if ($unique_size_total < count($on_sale_sku_item)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_11'), ErrCode::$VALIDATE_ERROR);
            }

            // sku 唯一性校验: 本次提交的是否重复
            $all_barcode_item = array_column($sku_list, 'goods_sku_code');
            $repeat_barcode   = array_repeat_element($all_barcode_item);
            if (!empty($repeat_barcode)) {
                throw new ValidationException(static::$t->_('mall_goods_submit_check_12',
                    ['repeat_barcode' => implode('、', $repeat_barcode)]), ErrCode::$VALIDATE_ERROR);
            }

            //如果开启了限购 判断权限和部门不能全部为空
            if ($params['is_limit_buy'] == ShopEnums::LIMIT_BUY_STATUS_YES) {
                if (empty($params['limit_buy_store_cate_list']) && empty($params['manage_department_purchase'])) {
                    throw new ValidationException(static::$t->_('purchase_goods_manage_department_permission_null_err'),
                        ErrCode::$VALIDATE_ERROR);
                }
            } else {
                //关闭购买权限，雇佣类型直接清掉
                $params['buy_hire_type'] = [];
            }

            //如果开启了免费 判断权限和部门不能全部为空
            if ($params['is_free'] == ShopEnums::FREE_BUY_STATUS_YES) {
                if (empty($params['free_buy_store_cate_list']) && empty($params['manage_department_free'])) {
                    throw new ValidationException(static::$t->_('free_goods_manage_department_permission_null_err'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            // 校验 是否存在 不是 当前 spu 下的 sku
            $abnormal_barcode = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_id != :goods_id: AND goods_sku_code IN ({codes:array})',
                'bind'       => ['goods_id' => $goods_model->id, 'codes' => $all_barcode_item],
                'columns'    => ['goods_sku_code'],
            ])->toArray();
            if (!empty($abnormal_barcode)) {
                $abnormal_barcode = implode('、', array_column($abnormal_barcode, 'goods_sku_code'));
                throw new ValidationException(static::$t->_('mall_goods_submit_check_15',
                    ['abnormal_barcode' => $abnormal_barcode]), ErrCode::$VALIDATE_ERROR);
            }

            // 免费: 免费数量 和 网点关联
            if ($params['is_free'] == ShopEnums::FREE_BUY_STATUS_NO) {
                $params['free_num']                 = 0;
                $params['free_buy_store_cate_list'] = [];
                $params['free_hire_type']           = [];
            }

            // spu 更新
            $spu_data = [
                'goods_type'     => $params['goods_type'],
                'goods_name_zh'  => $params['goods_name_zh'],
                'goods_name_th'  => $params['goods_name_local'],
                'goods_name_en'  => $params['goods_name_en'],
                'img_path'       => implode(',', $params['img_path']),
                'size_img_path'  => $params['size_img_path'],
                'goods_type'     => $params['goods_type'],
                'is_free'        => $params['is_free'],
                'is_limit_buy'   => $params['is_limit_buy'],
                'status'         => $params['status'],
                'free_num'       => $params['free_num'],
                'buy_hire_type'  => $params['buy_hire_type'] ? implode(',', $params['buy_hire_type']) : '',
                'free_hire_type' => $params['free_hire_type'] ? implode(',', $params['free_hire_type']) : '',
                'is_auto_price'  => $params['is_auto_price'] ?? ShopEnums::IS_AUTO_PRICE_NO,//是否自动定价
                'updated_id'     => $user['id'],
            ];

            if ($goods_model->i_update($spu_data) === false) {
                throw new BusinessException("商品编辑 - spu更新失败[{$goods_model->id}], 原因可能是: " . get_data_object_error_msg($goods_model) . '; 数据: ' . json_encode($spu_data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // sku 批量添加/更新
            $exist_barcode_models = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_id = :goods_id: AND goods_sku_code IN ({codes:array})',
                'bind'       => ['goods_id' => $goods_model->id, 'codes' => $all_barcode_item],
            ]);

            $sku_list = array_column($sku_list, null, 'goods_sku_code');

            $InteriorGoodsSkuModel = new InteriorGoodsSkuModel();
            foreach ($exist_barcode_models as $exist_model) {
                $_sku_new_data = $sku_list[$exist_model->goods_sku_code];
                $_sku          = [
                    'goods_id'        => $goods_model->id,
                    'goods_sku_code'  => $_sku_new_data['goods_sku_code'],
                    'sort_number'     => $_sku_new_data['sort_number'],
                    'goods_name_en'   => $goods_model->goods_name_en . ' ' . $_sku_new_data['attr_1'],
                    'goods_name_th'   => $goods_model->goods_name_th . ' ' . $_sku_new_data['attr_1'],
                    'goods_name_zh'   => $goods_model->goods_name_zh . ' ' . $_sku_new_data['attr_1'],
                    'img_path'        => $params['img_path'][0],//详情只需要存第一张图片
                    'attr_1'          => $_sku_new_data['attr_1'],
                    'price'           => $_sku_new_data['price'],
                    'status'          => $_sku_new_data['status'],
                    'commodity_value' => $_sku_new_data['commodity_value'],
                    'updated_id'      => $user['id'],
                ];

                $success = $db->updateAsDict(
                    $InteriorGoodsSkuModel->getSource(),
                    $_sku,
                    [
                        'conditions' => 'id = ?',
                        'bind'       => [$exist_model->id],
                    ]
                );
                if ($success === false) {
                    throw new BusinessException("商品编辑 - 已存在的sku[{$exist_model->id}]更新失败, 原因可能是: " . get_data_object_error_msg($db) . '; 数据: ' . json_encode($_sku,
                            JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                unset($sku_list[$exist_model->goods_sku_code]);
            }

            // 本次新增的
            $new_sku_list = [];
            foreach ($sku_list as $sku) {
                $new_sku_list[] = [
                    'goods_id'        => $goods_model->id,
                    'sort_number'     => $sku['sort_number'],
                    'goods_sku_code'  => $sku['goods_sku_code'],
                    'attr_1'          => $sku['attr_1'],
                    'goods_name_en'   => $goods_model->goods_name_en . ' ' . $sku['attr_1'],
                    'goods_name_th'   => $goods_model->goods_name_th . ' ' . $sku['attr_1'],
                    'goods_name_zh'   => $goods_model->goods_name_zh . ' ' . $sku['attr_1'],
                    'price'           => $sku['price'],
                    'status'          => $sku['status'],
                    'img_path'        => $params['img_path'][0],
                    'commodity_value' => $sku['commodity_value'] ? $sku['commodity_value'] : '0.00',
                    'created_id'      => $user['id'],
                    'updated_id'      => $user['id'],
                ];
            }

            $sku_model = new InteriorGoodsSkuModel();
            if (!empty($new_sku_list) && $sku_model->batch_insert($new_sku_list, 'db_backyard') === false) {
                throw new BusinessException('商品编辑 - 新增的sku批量创建失败,数据: ' . json_encode($new_sku_list,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            if ($params['is_free'] == ShopEnums::FREE_BUY_STATUS_YES) {
                $this->addStoreCateRel([
                    'id'              => $goods_model->id,
                    'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    'store_cate'      => $params['free_buy_store_cate_list'],
                ], $user, true);
                $this->batchInsertManageDepartment($params['manage_department_free'],
                    ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE, $goods_model, $user, true);
            } else {
                //如果关闭免费  删除网点类型和部门配置的数据
                $this->delInteriorGoodsStoreCateRel($goods_model->id, ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    $user);
                $this->delBatchInsertManageDepartment($goods_model->id, ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    $user);
            }

            // 可购买商品权限配置: 网点-商品关系
            if ($params['is_limit_buy'] == ShopEnums::FREE_BUY_STATUS_YES) {
                $this->addStoreCateRel([
                    'id'              => $goods_model->id,
                    'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE,
                    'store_cate'      => $params['limit_buy_store_cate_list'],
                ], $user, true);
                $this->batchInsertManageDepartment($params['manage_department_purchase'],
                    ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE, $goods_model, $user, true);
            } else {
                //如果关闭限购  删除网点类型和部门配置的数据
                $this->delInteriorGoodsStoreCateRel($goods_model->id, ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE,
                    $user);
                $this->delBatchInsertManageDepartment($goods_model->id,
                    ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE, $user);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城 - 商品后台管理 - 商品信息更新异常: ' . $e->getMessage() . $e->getTraceAsString());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');

            $this->logger->error('员工商城 - 商品后台管理 - 商品信息更新异常: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = 'success';
        $data      = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['main' => InteriorGoodsModel::class]);
            $builder->leftjoin(InteriorGoodsSkuModel::class, 'main.id = sku.goods_id', 'sku');

            if (!empty($condition['goods_name'])) {
                $builder->andWhere('main.goods_name_zh LIKE :goods_name: OR main.goods_name_th LIKE :goods_name: OR main.goods_name_en LIKE :goods_name:',
                    [
                        'goods_name' => '%' . $condition['goods_name'] . '%',
                    ]);
            }

            if (!empty($condition['barcode'])) {
                $builder->andWhere('sku.goods_sku_code = :goods_sku_code:',
                    ['goods_sku_code' => $condition['barcode']]);
            }

            if (isset($condition['goods_status']) && array_key_exists($condition['goods_status'],
                    ShopEnums::$goods_status_item)) {
                $builder->andWhere('main.status = :goods_status:', ['goods_status' => $condition['goods_status']]);
            }

            if (!empty($condition['goods_type'])) {
                $builder->andWhere('main.goods_type = :goods_type:', ['goods_type' => $condition['goods_type']]);
            }

            $count_info = $builder->columns('COUNT(DISTINCT(main.id)) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count;
            $items      = [];
            if ($count > 0) {
                $builder->columns([
                    'DISTINCT main.id',
                    'main.img_path',
                    'main.goods_name_zh',
                    'main.goods_name_en',
                    'main.goods_name_th AS goods_name_local',
                    'main.status',
                    'main.free_num',
                    'main.goods_type',
                    'main.is_free',
                    'main.is_limit_buy',
                ]);

                $builder->limit($page_size, $offset);
                $builder->orderBy('main.id DESC');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListData($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => (int)$count,
                ],
            ];
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');

            $this->logger->error('员工商城 - 商品后台管理 - 列表异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getExportList(array $condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['sku' => InteriorGoodsSkuModel::class]);
            $builder->leftjoin(InteriorGoodsModel::class, 'spu.id = sku.goods_id', 'spu');

            if (!empty($condition['goods_name'])) {
                $builder->andWhere('spu.goods_name_zh LIKE :goods_name: OR spu.goods_name_th LIKE :goods_name: OR spu.goods_name_en LIKE :goods_name:',
                    [
                        'goods_name' => '%' . $condition['goods_name'] . '%',
                    ]);
            }

            if (!empty($condition['barcode'])) {
                $builder->andWhere('sku.goods_sku_code = :goods_sku_code:',
                    ['goods_sku_code' => $condition['barcode']]);
            }

            if (isset($condition['goods_status']) && array_key_exists($condition['goods_status'],
                    ShopEnums::$goods_status_item)) {
                $builder->andWhere('spu.status = :goods_status:', ['goods_status' => $condition['goods_status']]);
            }

            $builder->columns([
                'sku.goods_sku_code',
                'spu.goods_type',
                'spu.goods_name_zh',
                'spu.goods_name_en',
                'spu.goods_name_th',
                'sku.attr_1',
                'sku.price',
                'sku.status',
            ]);

            $builder->orderBy('sku.id DESC');
            $items = $builder->getQuery()->execute()->toArray();
            foreach ($items as $k => $v) {
                $v['goods_type'] = static::$t->_(ShopEnums::$goods_type[$v['goods_type']]);
                $v['status']     = static::$t->_(ShopEnums::$goods_status_item[$v['status']]);
                $items[$k]       = array_values($v);
            }
            // Excel
            $data = $this->exportSkuExcel($items);
            if ($data['code'] == ErrCode::$SUCCESS && !empty($data['data'])) {
                $data = $data['data'];
            } else {
                throw new ValidationException('Excel File Generation failed, Please try again!',
                    ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城 - 商品后台管理 - 导出异常: ' . $e->getMessage() . $e->getTraceAsString());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城 - 商品后台管理 - 导出异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理列表数据结构
     *
     * @param array $items
     * @return array
     */
    public function handleListData(array $items)
    {
        if (empty($items)) {
            return [];
        }
        // 限购的商品
        $limit_buy_goods_ids = ByEnvModel::getValByCode(ShopEnums::LIMIT_BUY_GOODS_IDS_CODE, '');
        $limit_buy_goods_ids = explode(',', $limit_buy_goods_ids);

        // 限购件数
        $limit_buy_num = ByEnvModel::getValByCode(ShopEnums::LIMIT_BUY_NUM_CODE, '');


        $goods_store_cate_map = $this->getGoodsIdsStoreMap($items);

        foreach ($items as &$v) {
            // 状态
            $v['status_label'] = static::$t->_(ShopEnums::$goods_status_item[$v['status']]);

            $v['is_limit_buy_label'] = static::$t->_(ShopEnums::$limit_buy_status_item[$v['is_limit_buy']]);

            // 限购件数
            $v['limit_buy_num'] = $v['is_limit_buy'] == ShopEnums::LIMIT_BUY_STATUS_YES ? $limit_buy_num : 0;

            $v['is_free_label'] = static::$t->_(ShopEnums::$free_buy_status_item[$v['is_free']]);

            // 免费权限
            $free_buy_store_cate            = $goods_store_cate_map[$v['id'] . '_' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE] ?? [];
            $v['free_buy_store_cate_label'] = implode(',', array_unique($free_buy_store_cate));
            // 购买权限
            $limit_buy_store_cate            = $goods_store_cate_map[$v['id'] . '_' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE] ?? [];
            $v['limit_buy_store_cate_label'] = implode(',', array_unique($limit_buy_store_cate));

            $v['goods_type_name'] = static::$t->_(ShopEnums::$goods_type[$v['goods_type']]);
            $v['img_path']        = explode(',', $v['img_path'])[0] ?? '';
        }
        return $items;
    }

    /**
     * 处理详情数据结构
     *
     * @param array $data
     * @return array
     */
    public function handleDetailData(array $data)
    {
        if (empty($data)) {
            return [];
        }

        // 限购的商品
        $limit_buy_goods_ids = ByEnvModel::getValByCode(ShopEnums::LIMIT_BUY_GOODS_IDS_CODE, '');
        $limit_buy_goods_ids = explode(',', $limit_buy_goods_ids);

        $goods_store_cate_map    = $this->getGoodsIdsStoreMap([$data], 'store_cate_id');
        $data['goods_type_name'] = static::$t->_(ShopEnums::$goods_type[$data['goods_type']]);

        $data['img_path']     = explode(',', $data['img_path']);
        $data['is_free']      = (int)$data['is_free'];
        $data['is_limit_buy'] = (int)$data['is_limit_buy'];
        // 状态
        $data['status_label'] = static::$t->_(ShopEnums::$goods_status_item[$data['status']]);
        // 限购件数
        $data['limit_buy_num'] = ByEnvModel::getValByCode(ShopEnums::LIMIT_BUY_NUM_CODE, '');

        $free_buy_store_cate_list         = $goods_store_cate_map[$data['id'] . '_' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE] ?? [];
        $data['free_buy_store_cate_list'] = !empty($free_buy_store_cate_list) ? array_values(array_unique($free_buy_store_cate_list)) : [];

        // 购买权限
        $limit_buy_store_cate_list         = $goods_store_cate_map[$data['id'] . '_' . ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE] ?? [];
        $data['limit_buy_store_cate_list'] = !empty($limit_buy_store_cate_list) ? array_values(array_unique($limit_buy_store_cate_list)) : [];

        // sku 状态格式化
        foreach ($data['sku_list'] as &$v) {
            $v['status_label'] = static::$t->_(ShopEnums::$goods_status_item[$v['status']]);
        }
        //购买权限部门
        $data['manage_department_purchase'] = $this->getGoodsManageDepartmentList([
            'goods_id'        => $data['id'],
            'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE,
        ])['data']['items'];
        //免费权限部门
        $data['manage_department_free'] = $this->getGoodsManageDepartmentList([
            'goods_id'        => $data['id'],
            'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
        ])['data']['items'];
        $data['buy_hire_type']          = $data['buy_hire_type'] ? explode(',', $data['buy_hire_type']) : [];
        $data['free_hire_type']         = $data['free_hire_type'] ? explode(',', $data['free_hire_type']) : [];
        $data['is_auto_price_label']    = static::$t->_(ShopEnums::$is_auto_price[$data['is_auto_price']]);
        return $data;
    }

    /**
     * 生成sku导出的Excel文件
     *
     * @param $items
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    private function exportSkuExcel($items)
    {
        $header = [
            self::$t->_('mall_excel_field_goods_barcode'),    // barcode
            self::$t->_('mall_excel_field_goods_type'),       // 商品类型
            self::$t->_('mall_excel_field_goods_zh_name'),    // 商品中文名称
            self::$t->_('mall_excel_field_goods_en_name'),    // 商品英文名称
            self::$t->_('mall_excel_field_goods_local_name'), // 商品语言当地名称
            self::$t->_('mall_excel_field_goods_size'),       // 尺码
            self::$t->_('mall_excel_field_goods_price'),      // 价格
            self::$t->_('mall_excel_field_goods_status'),     // 状态
        ];

        $fileName = 'barcode_' . date('YmdHis"') . '.xlsx';
        return $this->exportExcel($header, $items, $fileName);
    }

    /**
     * 修改
     *
     * @Token
     * @Date: 2022-02-18 16:50
     * @return:
     **@author: peak pan
     */
    public function saveInteriorOrders($params)
    {
        $code           = ErrCode::$SUCCESS;
        $message        = '';
        $interiorOrders = InteriorOrders::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        $info           = [];
        if (!empty($interiorOrders)) {
            $info = $interiorOrders->toArray();
            //修复多邮编同步scm失败问题，修改为只同步第一个邮编到scm
            if ($params['receive_postal_code']) {
                $postal_code_arr               = explode(',', $params['receive_postal_code']);
                $params['receive_postal_code'] = $postal_code_arr[0];
            }
            $ac = new ApiClient('by', '', 'save_interior_orders_scm', static::$language);
            $ac->setParams([
                $params,
            ]);
            $res  = $ac->execute();
            $code = $res['result']['code'] ?? '';
        } else {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }
        $info_data = [];
        if (!empty($code)) {
            $info_data = InteriorOrders::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ])->toArray();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS ? $info_data : '',
        ];
    }

    /**
     * 详情
     *
     * @param int $goods_id
     * @return mixed
     */
    public function getDetail(int $goods_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $spu_info = InteriorGoodsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $goods_id],
                'columns'    => [
                    'id',
                    'goods_name_zh',
                    'goods_name_en',
                    'goods_name_th AS goods_name_local',
                    'img_path',
                    'size_img_path',
                    'free_num',
                    'status',
                    'goods_type',
                    'img_path',
                    'is_free',
                    'is_limit_buy',
                    'buy_hire_type',
                    'free_hire_type',
                    'is_auto_price',

                ],
            ]);

            if (empty($spu_info)) {
                throw new ValidationException('Goods information does not exist!', ErrCode::$VALIDATE_ERROR);
            }

            $data = $spu_info->toArray();

            // sku列表
            $data['sku_list'] = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_id = :goods_id:',
                'bind'       => ['goods_id' => $spu_info['id']],
                'columns'    => ['goods_sku_code', 'attr_1', 'price', 'status', 'commodity_value', 'sort_number'],
            ])->toArray();

            $data = $this->handleDetailData($data);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城 - 商品后台管理 - 查看详情异常: ' . $e->getMessage() . $e->getTraceAsString());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城 - 商品后台管理 - 查看详情异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取商品尺码
     */
    private static function getGoodsSizeSetting()
    {
        $env = OaEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind'       => ['code' => 'interior_goods_size_enums'],
        ]);

        return !empty($env) ? explode(',', $env->val) : [];
    }

    /**
     * 获取商品对应的网点类型枚举
     */
    private static function getGoodsStoreCate()
    {
        return SysStoreGoodsModel::find([
            'columns' => ['id AS value', 'sys_store_cate AS label'],
        ])->toArray();
    }

    /**
     * 获取商品id与有权限网点的关系
     *
     * @param array $arr
     * @param string $columns 返回指定字段数据
     * @return array
     */
    public function getGoodsIdsStoreMap(array $arr, string $columns = 'sys_store_cate')
    {
        $goods_ids            = array_column($arr, 'id');
        $goods_store_cate_arr = InteriorGoodsStoreCateRelModel::find([
            'conditions' => 'goods_id in ({goods_id:array})',
            'bind'       => ['goods_id' => $goods_ids],
        ])->toArray();

        $permission_type_arr = $res = [];
        $goods_store_ids     = $this->getGoodsStoreId();
        foreach ($goods_store_cate_arr as $goods_store) {
            $goods_id_key = $goods_store['goods_id'] . '_' . $goods_store['permission_type'];
            if (in_array($goods_id_key, $permission_type_arr)) {
                $res[$goods_id_key][] = $columns == 'store_cate_id' ? $goods_store_ids[$goods_store[$columns]] : $goods_store[$columns];
            } else {
                $res[$goods_id_key][] = $columns == 'store_cate_id' ? $goods_store_ids[$goods_store[$columns]] : $goods_store[$columns];
            }
        }
        return $res;
    }

    /**
     * 购买权限部门,免费权限部门
     *
     * @param array $condition 查询条件组
     * @return array
     */
    public function getGoodsManageDepartmentList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? 100 : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.permission_type, main.goods_id, main.department_id, main.department_name, main.is_include_sub, main.created_id, main.created_at';

                $builder->columns($columns);
                $builder->from(['main' => InteriorGoodsManageDepartmentPermissionModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id ' . ($condition['sort'] ?? 'DESC'));
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('get_goods_manage_department_list_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     *
     * @param array $condition 筛选条件组
     * @return int
     */
    public function getListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => InteriorGoodsManageDepartmentPermissionModel::class]);
        $builder = $this->getCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->total);
    }


    /**
     * 组装查询条件
     *
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return object
     */
    public function getCondition(object $builder, array $condition)
    {
        $permission_type = !empty($condition['permission_type']) ? $condition['permission_type'] : '';
        $goods_id        = !empty($condition['goods_id']) ? trim($condition['goods_id']) : '';
        $department_name = !empty($condition['department_name']) ? $condition['department_name'] : '';

        if (!empty($permission_type)) {
            $builder->andWhere('main.permission_type = :permission_type:', ['permission_type' => $permission_type]);
        }
        if (!empty($goods_id)) {
            $builder->andWhere('main.goods_id = :goods_id:', ['goods_id' => $goods_id]);
        }

        if (!empty($department_name)) {
            $builder->andWhere('main.department_id like :department_id: or main.department_name like :department_name:',
                ['department_id' => $department_name . '%', 'department_name' => '%' . $department_name . '%']);
        }

        return $builder;
    }

    /**
     * 格式化列表数据
     *
     * @param array $department_arr 数据
     * @return array
     */
    private function handleListItems($department_arr)
    {
        if (empty($department_arr)) {
            return [];
        }
        $department_ids       = array_unique(array_column($department_arr, 'department_id'));
        $dept_ids             = (new DepartmentRepository())->getDepartmentByIds($department_ids);
        $ancestry             = array_column($dept_ids, 'ancestry', 'id');
        $ancestry_ids         = array_column($dept_ids, 'ancestry');
        $total_department_ids = array_merge($department_ids, $ancestry_ids);
        $department_info      = (new DepartmentRepository())->getDepartmentByIds($total_department_ids);
        $department_array     = array_column($department_info, 'name', 'id');
        foreach ($department_arr as &$department) {
            $department['ancestry']      = $ancestry[$department['department_id']] ?? '';
            $department['ancestry_name'] = $department_array[$department['ancestry']] ?? '';
        }
        return $department_arr;
    }

    /**
     * 批量添加商品部门权限数据
     *
     * @param array $manage_department_arr 提交的数据
     * @param int $permission_type 1  购买权限 2 免费权限
     * @param object $goods_model 商品主数据对象
     * @param array $user 当前登录人数据
     * @param bool $is_update 是否是修改  true 是 false 否
     **@throws BusinessException
     */
    public function batchInsertManageDepartment(
        array $manage_department_arr,
        int $permission_type = ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE,
        object $goods_model,
        array $user,
        bool $is_update = false
    ) {
        if ($is_update) {
            //如果是修改记录之前的数据再删除
            $this->delBatchInsertManageDepartment($goods_model->id, $permission_type, $user);
        }
        if (!empty($manage_department_arr)) {
            $department_arr_name = (new DepartmentRepository())->getDepartmentByIds(array_column($manage_department_arr,
                'department_id'));
            $department_arr_name = array_column($department_arr_name, 'name', 'id');
            $department_purchase = $insert_department_purchase = [];
            foreach ($manage_department_arr as $purchase) {
                $department_purchase['permission_type'] = $permission_type;
                $department_purchase['goods_id']        = $goods_model->id;
                $department_purchase['department_id']   = $purchase['department_id'];
                $department_purchase['department_name'] = $department_arr_name[$purchase['department_id']] ?? '';
                $department_purchase['is_include_sub']  = $purchase['is_include_sub'];
                $department_purchase['created_id']      = $user['id'];
                $department_purchase['created_at']      = date('Y-m-d H:i:s');
                $insert_department_purchase[]           = $department_purchase;
            }
            $manage_department_model = new InteriorGoodsManageDepartmentPermissionModel();
            if (!empty($department_purchase) && $manage_department_model->batch_insert($insert_department_purchase,
                    'db_backyard') === false) {
                throw new BusinessException('商品添加-部门批量创建失败, 数据: ' . json_encode($department_purchase,
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_GOODS_ADD_ERROR);
            }
        }
    }


    /**
     * 批量添加商品网点类型关联表
     *
     * @param array $data 需要添加的参数
     * @param array $user 操作人
     * @param bool $is_update 师傅是编辑
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addStoreCateRel(array $data, array $user, bool $is_update = false)
    {
        if ($is_update) {
            $this->delInteriorGoodsStoreCateRel($data['id'], $data['permission_type'], $user);
        }

        if (!empty($data['store_cate'])) {
            $store_cate = array_column(SysStoreGoodsModel::find()->toArray(), null, 'id');
            if (empty($store_cate)) {
                //网点商品表需要配置
                throw new ValidationException(static::$t->_('goods_submit_sys_store_goods_not_null_err'),
                    ErrCode::$VALIDATE_ERROR);
            }
            $insert_rel = [];
            foreach ($data['store_cate'] as $purchase) {
                $rel['goods_id']           = $data['id'];
                $rel['permission_type']    = $data['permission_type'];
                $rel['store_cate_id']      = $store_cate[$purchase]['store_cate_id'];
                $rel['sys_store_cate']     = $store_cate[$purchase]['sys_store_cate'];
                $rel['created_id']         = $user['id'];
                $rel['created_at']         = date('Y-m-d H:i:s');
                $rel['sys_store_goods_id'] = $purchase;
                $insert_rel[]              = $rel;
            }

            $insert_rel                 = $this->handleStoreCateRel($insert_rel, $store_cate);
            $goods_store_cate_rel_model = new InteriorGoodsStoreCateRelModel();
            if (!empty($insert_rel) && $goods_store_cate_rel_model->batch_insert($insert_rel,
                    'db_backyard') === false) {
                throw new BusinessException('商品添加-权限批量创建失败, 数据: ' . json_encode($insert_rel, JSON_UNESCAPED_UNICODE),
                    ErrCode::$INTERIOR_GOODS_ADD_ERROR);
            }
        }
    }


    /**
     * 处理数据中包含SHOP的数据拆分 [eg ,历史网店类型为4、5、7 存在了一个字段上，新的会拆开分别存储，生成三条数据]
     *
     * @param array $insert_rel 数据
     * @param array $store_cate sys_store_goods映射数据
     * @return  array
     **/
    public function handleStoreCateRel(array $insert_rel, array $store_cate)
    {
        $res = [];
        foreach ($insert_rel as $insert_value) {
            $store_cate_id_arr   = explode(',', $store_cate[$insert_value['sys_store_goods_id']]['store_cate_id']);
            $store_cate_id_count = count($store_cate_id_arr);
            unset($insert_value['sys_store_goods_id']);
            if ($store_cate_id_count > 1) {
                foreach ($store_cate_id_arr as $store_cate_id_item) {
                    $insert_value['store_cate_id'] = $store_cate_id_item;
                    $res[]                         = $insert_value;
                }
                continue;
            }
            $res[] = $insert_value;
        }
        return $res;
    }


    /**
     * 删除网点类型数据
     *
     * @param int $goods_id 商品id
     * @param int $permission_type 商品权限分类  1 网点类型   2 部门
     * @param array $user 操作人数据
     **/
    public function delInteriorGoodsStoreCateRel(int $goods_id, int $permission_type, array $user)
    {
        $goods_store_cate_rel_obj = InteriorGoodsStoreCateRelModel::find([
            'conditions' => 'goods_id = :goods_id: and permission_type = :permission_type:',
            'bind'       => [
                'goods_id'        => $goods_id,
                'permission_type' => $permission_type,
            ],
        ]);
        $goods_store_cate_rel_arr = $goods_store_cate_rel_obj->toArray();
        //删除之前的数据
        if (!empty($goods_store_cate_rel_arr)) {
            $this->logger->info('员工商城 - 商品后台管理 - 更新前商品网点类型关联表数据: ' . json_encode($goods_store_cate_rel_arr,
                    JSON_UNESCAPED_UNICODE));
            $goods_store_cate_rel_del = $goods_store_cate_rel_obj->delete();
            if ($goods_store_cate_rel_del === false) {
                throw new BusinessException('员工商城 - 商品后台管理 - 更新商品删除之前的购买和免费权限数据失败, 原因可能是: ' . get_data_object_error_msg($goods_store_cate_rel_obj) . '; 数据: ' . $goods_id,
                    ErrCode::$INTERIOR_GOODS_DEL_ERROR);
            }
        }
    }

    /**
     * 删除网点类型数据
     *
     * @param int $goods_id 商品id
     * @param int $permission_type 商品权限分类  1 网点类型   2 部门
     * @param array $user 操作人数据
     **/
    public function delBatchInsertManageDepartment(int $goods_id, int $permission_type, array $user)
    {
        $department_obj = InteriorGoodsManageDepartmentPermissionModel::find([
            'conditions' => 'permission_type = :permission_type: and goods_id = :goods_id:',
            'bind'       => [
                'permission_type' => $permission_type,
                'goods_id'        => $goods_id,
            ],
        ]);

        $department_arr = $department_obj->toArray();
        //删除之前的数据
        if (!empty($department_arr)) {
            $this->logger->info('员工商城 - 商品后台管理 - 更新前商品数据: ' . json_encode($department_arr, JSON_UNESCAPED_UNICODE));
            $department_del = $department_obj->delete();
            if ($department_del === false) {
                throw new BusinessException('员工商城 - 商品后台管理 - 更新商品删除之前的部门失败, 原因可能是: ' . get_data_object_error_msg($department_obj) . '; 数据: ' . $goods_id . '类型' . $permission_type,
                    ErrCode::$INTERIOR_GOODS_DEL_ERROR);
            }
        }
    }


    /**
     * 处理表sys_store_goods数据
     **/
    public function getGoodsStoreId()
    {
        $result = SysStoreGoodsModel::find([
            'columns' => ['id', 'store_cate_id', 'sys_store_cate'],
        ])->toArray();

        $res = [];
        foreach ($result as $val) {
            $goods_ids = explode(',', $val['store_cate_id']);
            if (count($goods_ids) > 1) {
                foreach ($goods_ids as $id) {
                    $res[] = [
                        'id'            => $val['id'],
                        'store_cate_id' => $id,
                    ];
                }
                continue;
            }
            $res[] = [
                'id'            => $val['id'],
                'store_cate_id' => $val['store_cate_id'],
            ];
        }
        return array_column($res, 'id', 'store_cate_id');
    }

    /**
     * 自动定价策略-添加
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function saveInteriorAutoPricePolicy($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $policy = [];
            foreach ($params['policy'] as $key => $item) {
                //折扣比例：大于0小于100的数字，最多支持2位小数
                if (!preg_match('/^(?!0(\.0+)?$)([1-9]\d?(\.\d{1,2})?|0\.\d{1,2})$/', $item['discount'])) {
                    throw new ValidationException(static::$t->_('policy_discount_error'), ErrCode::$VALIDATE_ERROR);
                }
                if ($item['day_begin'] > $item['day_end']) {
                    throw new ValidationException(static::$t->_('policy_day_error'), ErrCode::$VALIDATE_ERROR);
                }
                $item['key'] = $key;
                $this->compareDay($item, $params['policy']);
                $policy[] = [
                    'day_begin' => $item['day_begin'],
                    'day_end'   => $item['day_end'],
                    'discount'  => $item['discount'],
                    'create_id' => $user['id'],
                ];
            }
            if ($policy) {
                //删除原来的
                $bool = InteriorGoodsAutoPricePolicyModel::find()->delete();
                if ($bool === false) {
                    throw new BusinessException('业务配置 - 自动定价策略-添加-删除原策略失败', ErrCode::$BUSINESS_ERROR);
                }
                //新增新的
                $interior_goods_auto_price_policy = new InteriorGoodsAutoPricePolicyModel();
                $bool                             = $interior_goods_auto_price_policy->batch_insert($policy,
                    'db_backyard');
                if ($bool === false) {
                    throw new BusinessException('业务配置 - 自动定价策略-添加-新增策略写入失败', ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if ($real_message) {
            $this->logger->warning('saveInteriorAutoPricePolicy_save_error:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $bool ?? false,
        ];
    }

    /**
     * 检测策略里的上架日期是否有交叉
     *
     * @param array $curr 当前策略
     * @param array $policy_list 策略列表
     * @throws ValidationException
     */
    public function compareDay($curr, $policy_list)
    {
        // 删除列表中的当前待验证的项
        unset($policy_list[$curr['key']]);
        foreach ($policy_list as $data) {
            // 日期范围是否交叉
            if (($curr['day_begin'] >= $data['day_begin']) && ($curr['day_begin'] <= $data['day_end'])) {
                throw new ValidationException(static::$t->_('policy_day_error'), ErrCode::$VALIDATE_ERROR);
            }

            if (($curr['day_end'] >= $data['day_begin']) && ($curr['day_end'] <= $data['day_end'])) {
                throw new ValidationException(static::$t->_('policy_day_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
    }

    /**
     * 员工商城 - 自动定价策略-列表
     *
     * @return array
     */
    public function getInteriorAutoPricePolicyList()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items' => [],
        ];
        try {
            $data['items']       = InteriorGoodsAutoPricePolicyModel::find([
                'columns' => 'day_begin, day_end, discount',
                'order'   => 'day_begin ASC',
            ])->toArray();
            $max_day_end         = $data['items'] ? max(array_column($data['items'], 'day_end')) : 0;
            $data['max_day_end'] = $max_day_end + 1;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('业务配置-员工商城 - 自动定价策略-列表获取失败, ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取计算商品价值的 『第一阶段的定价折扣比例』因子
     */
    protected function getPolicyDiscount()
    {
        return InteriorGoodsAutoPricePolicyModel::findFirst([
                'columns' => 'day_begin, day_end, discount',
                'order'   => 'day_begin ASC',
            ])->discount ?? 0;
    }

    /**
     * 计算商品价值 公式
     *
     * @param string $price 商品价格
     * @param string $policy_discount 第一阶段的定价折扣比例
     * @param string $pay_handling_fee Pay支付手续费
     * @return mixed
     */
    protected function calculateCommodityValueFormula(
        string $price,
        string $policy_discount = '0',
        string $pay_handling_fee = '0'
    ) {
        $commodity_value = $policy_discount ? round(($price - $pay_handling_fee) / ($policy_discount / 100),
            2) : $price;
        return sprintf('%.2f', $commodity_value > 0 ? $commodity_value : 0);
    }

    /**
     * 计算商品价值
     *
     * @param float $price 商品价格
     * @return array
     */
    public function calculateCommodityValue($price)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = $price;
        try {
            $policy_discount  = $this->getPolicyDiscount();
            $pay_handling_fee = $this->getPayHandlingFee();
            $data             = $this->calculateCommodityValueFormula($price, $policy_discount, $pay_handling_fee);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('业务配置-员工商城 - 自动定价策略-列表获取失败, ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取pay支付手续费
     */
    protected function getPayHandlingFee()
    {
        return ByEnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => ShopEnums::PAY_HANDLING_FEE],
            ])->set_val ?? '0';
    }


    /**
     * 图片文件批量导出
     *
     * @param array $excel_data
     * @param array $user
     * @return array
     * @throws GuzzleException
     */
    public function generateBatchLink(array $excel_data = [], array $user = [])
    {
        $code     = ErrCode::$SUCCESS;
        $message  = static::$t->_('success');
        $file_url = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $header = [
                self::$t->_('shop_generate_link_file_name'), // 文件名称
                self::$t->_('shop_generate_link_file_url'),  // 文件链接
            ];

            // 写入员工商城图库
            $items = [];
            $keys  = [];
            foreach ($excel_data as &$row) {
                $items[] = [
                    $row['object_name'],
                    $row['object_url'],
                ];

                $object_key = md5($row['object_url']);

                $row['object_key'] = $object_key;
                $row['created_id'] = $user['id'];
                $row['created_at'] = date('Y-m-d H:i:s');

                $keys[] = $object_key;
            }

            // 批量录入图库: 删除已有的, 重新写入
            $image_models = SysImageGalleryRepository::getInstance()->getList($keys);
            if (!empty($image_models)) {
                $image_models->delete();
            }

            if ((new SysImageGalleryModel())->batch_insert($excel_data) === false) {
                throw new BusinessException('批量写入图库失败,data=' . json_encode($excel_data, JSON_UNESCAPED_UNICODE),
                    ErrCode::$BUSINESS_ERROR);
            }

            $file_name  = 'File_Link_Download_' . date('YmdHis"') . '.xlsx';
            $export_res = $this->exportExcel($header, $items, $file_name);
            if (empty($export_res['data'])) {
                throw new ValidationException(static::$t->_('shop_generate_link_file_error'), ErrCode::$VALIDATE_ERROR);
            }

            $file_url = $export_res['data'];

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-商品管理-下载生成链接异常, 原因可能是=' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-商品管理-下载生成链接异常, 原因可能是=' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $file_url,
        ];
    }

    /**
     * 批量导入文件添加到上传中心, 等待异步处理
     *
     * @param array $user
     * @return bool|mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function addBatchImportToImportCenter(array $user)
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
        }

        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if ($extension != 'xlsx') {
            throw new ValidationException(static::$t->_('bank_flow_upload_file_type_error'), ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType(ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_SHOP_GOODS_HEADLESS])
            ->getSheetData();

        $this->logger->info(['excel_file_data' => $excel_data]);

        // Excel空数据校验: 不含表头
        $excel_content_data = array_slice($excel_data, 1);
        if (empty($excel_content_data)) {
            throw new ValidationException(static::$t->_('bank_flow_data_empty'), ErrCode::$VALIDATE_ERROR);
        }

        // 单次导入最多行数校验
        if (count($excel_content_data) > ShopEnums::SHOP_GOODS_HEADLESS_MAX_IMPORT_LIMIT) {
            throw new ValidationException(static::$t->_('shop_batch_import_error_001',
                ['max_limit' => ShopEnums::SHOP_GOODS_HEADLESS_MAX_IMPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
        }

        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . mt_rand(10, 10000) . '_' . $file->getName();
        $file->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info(['excel_file_data_oss_result' => $oss_result]);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(static::$t->_('file_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'],
            ImportCenterEnums::TYPE_SHOP_GOODS_HEADLESS);

        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => static::$t->_('success'),
        ];
    }

    /**
     * 处理批量导入的数据(无头件)
     *
     * @param array $excel_data
     * @param array $user
     * @return array
     * @throws GuzzleException
     */
    public function batchHandleImportGoodsData(array $excel_data, array $user)
    {
        // 返回结果
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        // 表头
        $excel_header = array_shift($excel_data);

        // Excel业务数据行处理
        // 1. 构建spu => sku_list 结构
        $check_spu_item      = [];
        $image_keys          = []; // 图片key
        $excel_barcode_count = []; // 要导入的barcode统计
        foreach ($excel_data as $k => $v) {
            $v              = trim_array(array_slice($v, 0, 13));

            $excel_data[$k] = $v;

            // 跳过空白行
            $v_data = implode(',', array_filter($v));
            if (empty($v_data)) {
                continue;
            }

            // 商品编号
            $goods_no = strtolower($v[0]);

            // 商品数统计
            if (isset($check_spu_item[$goods_no]['goods_no_count'])) {
                $check_spu_item[$goods_no]['goods_no_count'] += 1;
            } else {
                $check_spu_item[$goods_no]['goods_no_count'] = 1;
            }

            // 商品中文名称
            $check_spu_item[$goods_no]['goods_name_zh_item'][strtolower($v[1])] = 1;

            // 商品英文名称
            $check_spu_item[$goods_no]['goods_name_en_item'][strtolower($v[2])] = 1;

            // 商品当地名称
            $check_spu_item[$goods_no]['goods_name_local_item'][strtolower($v[3])] = 1;

            // 尺码
            $check_spu_item[$goods_no]['sku_attr_item'][strtolower($v[4])] = 1;

            // barcode
            $barcode                                             = strtolower($v[5]);
            $check_spu_item[$goods_no]['barcode_item'][$barcode] = 1;

            if (isset($excel_barcode_count[$barcode])) {
                $excel_barcode_count[$barcode] += 1;
            } else {
                $excel_barcode_count[$barcode] = 1;
            }

            // sku 销售价格
            $check_spu_item[$goods_no]['sku_price_item'][] = $v[6];

            // 主图 1-5
            $check_spu_item[$goods_no]['img_path_item'][1][$v[7]]  = 1; // 主图1
            $check_spu_item[$goods_no]['img_path_item'][2][$v[8]]  = 1;
            $check_spu_item[$goods_no]['img_path_item'][3][$v[9]]  = 1;
            $check_spu_item[$goods_no]['img_path_item'][4][$v[10]] = 1;
            $check_spu_item[$goods_no]['img_path_item'][5][$v[11]] = 1;

            // 规格图
            $check_spu_item[$goods_no]['size_img_path_item'][$v[12]] = 1;

            // 主图 key
            $image_keys[] = md5($v[7]);
            $image_keys[] = md5($v[8]);
            $image_keys[] = md5($v[9]);
            $image_keys[] = md5($v[10]);
            $image_keys[] = md5($v[11]);

            // 规格图
            $image_keys[] = md5($v[12]);
        }

        // 图库取文件
        $image_keys         = array_values(array_unique($image_keys));
        $image_gallery_list = [];
        if (!empty($image_keys)) {
            $image_gallery_list = SysImageGalleryRepository::getInstance()->getList($image_keys)->toArray();
            $image_gallery_list = array_column($image_gallery_list, 'object_key');
        }

        // 库中已有的barcode
        $exist_barcode_list   = [];
        $excel_goods_sku_code = array_values(array_filter(array_keys($excel_barcode_count)));
        if (!empty($excel_goods_sku_code)) {
            $exist_barcode_list = InteriorGoodsSkuModel::find([
                'conditions' => 'goods_sku_code IN ({goods_sku_code:array})',
                'bind'       => ['goods_sku_code' => $excel_goods_sku_code],
                'columns'    => ['goods_sku_code'],
            ])->toArray();

            $exist_barcode_list = array_map('strtolower', array_column($exist_barcode_list, 'goods_sku_code'));
        }

        // 第一阶段的定价折扣比例配置
        $policy_discount = $this->getPolicyDiscount();

        // Pay支付手续费
        $pay_handling_fee = $this->getPayHandlingFee();

        // 2. 文件逐行校验
        $error_list       = [];
        $correct_spu_list = [];
        $error_num_count  = 0;
        $excel_num_total  = count($excel_data);
        foreach ($excel_data as $row_k => $row_v) {
            // 跳过空白行
            $row_data = implode(',', array_filter($row_v));
            if (empty($row_data)) {
                continue;
            }

            $error_remark = [];

            $goods_no = strtolower($row_v[0] ?? ''); // 商品编号

            // 取商品在上述spu中待校验的结构
            $check_goods_info = $check_spu_item[$goods_no] ?? [];

            $goods_name_zh    = $row_v[1] ?? ''; // 商品中文名称
            $goods_name_en    = $row_v[2] ?? ''; // 商品英文名称
            $goods_name_local = $row_v[3] ?? ''; // 商品当地语言名称
            $goods_sku_attr_1 = $row_v[4] ?? ''; // 商品尺码
            $goods_sku_code   = $row_v[5] ?? ''; // barcode
            $goods_sku_price  = $row_v[6] ?? ''; // 销售价格

            // 多个主图 , 间隔
            $goods_img_path_item = [
                1 => $row_v[7] ?? '',  // 主图1
                2 => $row_v[8] ?? '',  // 主图2
                3 => $row_v[9] ?? '',  // 主图3
                4 => $row_v[10] ?? '', // 主图4
                5 => $row_v[11] ?? '', // 主图5
            ];

            $goods_size_img_path = $row_v[12] ?? ''; // 规格图

            if (mb_strlen($goods_no) < 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_002');
            }

            // 中文名校验
            $goods_name_zh_len = mb_strlen($goods_name_zh);
            if ($goods_name_zh_len < 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_003');
            } elseif ($goods_name_zh_len > 80) {
                $error_remark[] = static::$t->_('shop_batch_import_error_004');
            }
            if (count($check_goods_info['goods_name_zh_item']) > 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_005');
            }

            // 英文名校验
            $goods_name_en_len = mb_strlen($goods_name_en);
            if ($goods_name_en_len < 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_006');
            } elseif ($goods_name_en_len > 80) {
                $error_remark[] = static::$t->_('shop_batch_import_error_007');
            }
            if (count($check_goods_info['goods_name_en_item']) > 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_008');
            }

            // 当地语言名称校验
            if (mb_strlen($goods_name_local) > 80) {
                $error_remark[] = static::$t->_('shop_batch_import_error_009');
            }
            if (count($check_goods_info['goods_name_local_item']) > 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_010');
            }

            // 尺码
            $goods_sku_attr_len = mb_strlen($goods_sku_attr_1);
            if ($goods_sku_attr_len < 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_011');
            } elseif ($goods_sku_attr_len > 20) {
                $error_remark[] = static::$t->_('shop_batch_import_error_012');
            }

            if (count($check_goods_info['sku_attr_item']) != $check_goods_info['goods_no_count']) {
                $error_remark[] = static::$t->_('shop_batch_import_error_013');
            }

            // barcode
            $barcode_check_res = $this->checkExcelBarcodeFormat($goods_sku_code, $excel_barcode_count, $exist_barcode_list);
            $error_remark      = array_merge($error_remark, $barcode_check_res);

            // 销售价格
            $price_check_res = $this->checkExcelSkuPriceFormat($goods_sku_price);
            $error_remark    = array_merge($error_remark, $price_check_res);

            // 主图校验
            foreach ($check_goods_info['img_path_item'] as $img_num => $img_path_list) {
                $curr_goods_img_path_len = mb_strlen($goods_img_path_item[$img_num]);

                // 主图1 必填校验
                if ($img_num == 1 && $curr_goods_img_path_len < 1) {
                    $error_remark[] = static::$t->_('shop_batch_import_error_019', ['img_path_num' => $img_num]);
                }

                // 主图是否是图库中的地址
                if ($curr_goods_img_path_len > 0 && !in_array(md5($goods_img_path_item[$img_num]),
                        $image_gallery_list)) {
                    $error_remark[] = static::$t->_('shop_batch_import_error_020', ['img_path_num' => $img_num]);
                }

                // 主图链接是否完全相等
                if (count($img_path_list) > 1) {
                    $error_remark[] = static::$t->_('shop_batch_import_error_021', ['img_path_num' => $img_num]);
                }
            }

            // 规格图
            if (mb_strlen($goods_size_img_path) > 0 && !in_array(md5($goods_size_img_path), $image_gallery_list)) {
                $error_remark[] = static::$t->_('shop_batch_import_error_022');
            }
            if (count($check_goods_info['size_img_path_item']) > 1) {
                $error_remark[] = static::$t->_('shop_batch_import_error_023');
            }

            // 校验有误的
            if (!empty($error_remark)) {
                $row_v[13]    = implode('; ', $error_remark);
                $error_list[] = $row_v;

                $error_num_count++;
                continue;
            }

            // spu下的sku是否全部符合规定
            if ($this->otherSkuInfoIsError($check_goods_info, $excel_barcode_count, $exist_barcode_list)) {
                $row_v[13]    = '';
                $error_list[] = $row_v;
                continue;
            }

            // 当前行 及 同一个商品的sku校验通过
            $correct_spu_info = $correct_spu_list[$goods_no] ?? [];
            if (empty($correct_spu_info)) {
                $correct_spu_info = [
                    'goods_name_zh'      => $goods_name_zh,
                    'goods_name_en'      => $goods_name_en,
                    'goods_name_th'      => $goods_name_local,
                    'img_path'           => implode(',', array_filter($goods_img_path_item)),
                    'size_img_path'      => $goods_size_img_path,
                    'info'               => '',
                    'status'             => ShopEnums::GOODS_STATUS_ON_SALE,
                    'free_num'           => 0,
                    'created_id'         => $user['id'],
                    'updated_id'         => $user['id'],
                    'goods_type'         => ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE,
                    'is_free'            => ShopEnums::FREE_BUY_STATUS_NO,
                    'is_limit_buy'       => ShopEnums::LIMIT_BUY_STATUS_NO,
                    'is_auto_price'      => ShopEnums::IS_AUTO_PRICE_YES,
                    'goods_sku_img_path' => $goods_img_path_item[1],
                ];
            }

            $sort_number                    = $correct_spu_list[$goods_no]['sku_list'] ? count($correct_spu_list[$goods_no]['sku_list']) + 1 : 1;
            $correct_spu_info['sku_list'][] = [
                'sort_number'     => $sort_number,
                'goods_sku_code'  => $goods_sku_code,
                'goods_name_zh'   => $correct_spu_info['goods_name_zh'] . ' ' . $goods_sku_attr_1,
                'goods_name_en'   => $correct_spu_info['goods_name_en'] . ' ' . $goods_sku_attr_1,
                'goods_name_th'   => trim($correct_spu_info['goods_name_th'] . ' ' . $goods_sku_attr_1),
                'img_path'        => $correct_spu_info['goods_sku_img_path'],
                'attr_1'          => $goods_sku_attr_1,
                'price'           => $goods_sku_price,
                'commodity_value' => $this->calculateCommodityValueFormula($goods_sku_price, $policy_discount,
                    $pay_handling_fee),
                'status'          => ShopEnums::GOODS_STATUS_ON_SALE,
                'created_id'      => $user['id'],
                'updated_id'      => $user['id'],
            ];

            $correct_spu_list[$goods_no] = $correct_spu_info;
        }

        $data['error_num']       = $error_num_count;
        $data['success_num']     = $excel_num_total - $error_num_count;
        $data['result_file_url'] = '';

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            // 写入校验通过的spu 和 sku
            $this->batchAddExcelGoods($correct_spu_list);

            // 将有误的写入错误文件
            $file_name        = 'Goods_Batch_Import_Result_' . date('YmdHis') . '.xlsx';
            $excel_header[13] = static::$t->_('check_result');
            $export_res       = $this->exportExcel($excel_header, $error_list, $file_name);
            if (empty($export_res['data'])) {
                throw new BusinessException('生成结果文件失败, 请检查', ErrCode::$BUSINESS_ERROR);
            }

            $data['result_file_url'] = $export_res['data'];

            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        }

        if ($code != ErrCode::$SUCCESS) {
            $this->logger->error('商品管理-异步导入无头件异常, 原因可能是=' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 批量写入spu 和 sku
     *
     * @param array $goods_list
     * @return bool
     * @throws BusinessException
     */
    protected function batchAddExcelGoods(array $goods_list = [])
    {
        if (empty($goods_list)) {
            return true;
        }

        foreach ($goods_list as $spu_info) {
            $sku_list = $spu_info['sku_list'];
            unset($spu_info['sku_list'], $spu_info['goods_sku_img_path']);

            $goods_model = new InteriorGoodsModel();
            if ($goods_model->i_create($spu_info) === false) {
                throw new BusinessException('spu创建失败,原因可能是=' . get_data_object_error_msg($goods_model) . ';data=' . json_encode($spu_info,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            if ($goods_model->i_update(['goods_cate' => $goods_model->id]) === false) {
                throw new BusinessException('spu创建时, goods_cate反更新失败,原因可能是=' . get_data_object_error_msg($goods_model) . ';data=' . json_encode($spu_info,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            foreach ($sku_list as &$sku_info) {
                $sku_info['goods_id'] = $goods_model->id;
            }

            $sku_add_res = (new InteriorGoodsSkuModel())->batch_insert($sku_list, 'db_backyard');
            if ($sku_add_res === false) {
                throw new BusinessException('sku批量创建失败,data=' . json_encode($sku_list, JSON_UNESCAPED_UNICODE),
                    ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 价格校验
     *
     * @param string $price
     * @return array
     */
    protected function checkExcelSkuPriceFormat(string $price)
    {
        static $price_check_list = [];
        if (isset($price_check_list[$price])) {
            return $price_check_list[$price];
        }

        if (mb_strlen($price) < 1) {
            $error_list[] = static::$t->_('shop_batch_import_error_017');
        } elseif ($price <= 0 || !preg_match('/(^[1-9]+[0-9]*(\.[0-9]{1,2})?$)|(^0\.[0-9]{1,2}$)/', $price)) {
            $error_list[] = static::$t->_('shop_batch_import_error_018');
        }

        $error_list               = $error_list ?? [];
        $price_check_list[$price] = $error_list;
        return $error_list;
    }

    /**
     * barcode校验
     *
     * @param string $barcode
     * @param array $excel_barcode_count
     * @param array $exist_barcode_list
     * @return array
     */
    protected function checkExcelBarcodeFormat(string $barcode, array $excel_barcode_count, array $exist_barcode_list)
    {
        static $barcode_check_list = [];
        if (isset($barcode_check_list[$barcode])) {
            return $barcode_check_list[$barcode];
        }

        $barcode_len = mb_strlen($barcode);
        if ($barcode_len < 1) {
            $error_list[] = static::$t->_('shop_batch_import_error_014');
        } elseif ($barcode_len > 30) {
            $error_list[] = static::$t->_('shop_batch_import_error_015');
        }

        $barcode_lower = strtolower($barcode);
        $barcode_count = $excel_barcode_count[$barcode_lower] ?? 0;
        if ($barcode_len > 0 && ($barcode_count > 1 || in_array($barcode_lower, $exist_barcode_list))) {
            $error_list[] = static::$t->_('shop_batch_import_error_016');
        }

        $error_list                   = $error_list ?? [];
        $barcode_check_list[$barcode] = $error_list;
        return $error_list;
    }

    /**
     * spu下的其他sku校验
     *
     * @param array $spu_info
     * @param array $excel_barcode_count
     * @param array $exist_barcode_list
     * @return bool
     */
    protected function otherSkuInfoIsError(array $spu_info, array $excel_barcode_count, array $exist_barcode_list)
    {
        // sku 价格格式是否全部合规
        foreach ($spu_info['sku_price_item'] as $price) {
            if (!empty($this->checkExcelSkuPriceFormat($price))) {
                return true;
            }
        }

        // sku barcode 是否全部合规
        foreach ($spu_info['barcode_item'] as $barcode => $v) {
            if (!empty($this->checkExcelBarcodeFormat($barcode, $excel_barcode_count, $exist_barcode_list))) {
                return true;
            }
        }

        return false;
    }

}

