<?php

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Modules\Contract\Models\SysDepartmentStoreTypeModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Shop\Models\HeadquartersAddressModel;
use App\Modules\User\Models\DepartmentModel;
use App\Library\Enums\InventoryCheckEnums;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\StoreRepository;
use App\Modules\Warehouse\Services\WarehouseService;

class StoreService extends BaseService
{
    /**
     * @return array
     */
    public function getSysStoreList()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name');
        $builder->from(SysStoreModel::class);
        $builder->where('state = 1');
        $builder->limit(0, 10000);
        $items = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $items,
        ];
    }

    /**
     * 根据特定条件获取网点列表
     * @param array $params
     * @param string $columns
     * @return mixed
     */
    public function getSysStoreListByCondition(array $params, $columns = 'id,name,sap_pc_code')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(SysStoreModel::class);
        //$builder->where('state = 1 and LOWER(name) not LIKE :virtual_name:', ['virtual_name' => '%virtual%']);
        $builder->where('state = 1');
        if (!empty($params['store_id'])) {
            if (is_array($params['store_id'])) {
                $builder->andWhere('id IN ({ids:array})', ['ids' => $params['store_id']]);
            } else {
                $builder->andWhere('id LIKE :id:', ['id' => "%{$params['store_id']}%"]);
            }
        }

        if (!empty($params['store_name'])) {
            if (is_array($params['store_name'])) {
                $builder->inWhere('name', $params['store_name']);
            } else {
                $builder->andWhere('name LIKE :name:', ['name' => "%{$params['store_name']}%"]);
            }
        }
        if (!empty($params['limit'])) {
            $builder->limit($params['limit']);
        }
        if (isset($params['use_state']) && $params['use_state'] !== '') {
            $builder->andWhere('use_state = :use_state:', ['use_state' => $params['use_state']]);
        }
        //不含或禁用某些网点类型
        if (!empty($params['not_contain_category'])) {
            if (is_array($params['not_contain_category'])) {
                $builder->notInWhere('category', $params['not_contain_category']);
            } else {
                $builder->andWhere('category != :category:', ['category' => $params['not_contain_category']]);
            }
        }
        //网点类型不是FH、SHOP、USHOP时，网点营业状态为营业，use_state=1
        if (!empty($params['not_in_category'])) {
            $builder->andWhere('IF(category NOT IN (' . implode(',', $params['not_in_category']) .'),use_state = 1,use_state >=0)');
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据总部名称搜索
     * @param array $params
     * @return array
     */
    public function getHeaderListByCondition(array $params = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, office_name name, province_code, city_code, district_code, address, ' . Enums::HEAD_OFFICE_STORE_FLAG . ' as headquarters');
        $builder->from(HeadquartersAddressModel::class);
        //按照总部地址名称搜索
        if (isset($params['store_name']) && !empty($params['store_name'])) {
            $builder->andWhere('office_name LIKE :name:', ['name' => "%{$params['store_name']}%"]);
        }
        if (!empty($params['limit'])) {
            $builder->limit($params['limit']);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据网点id 获取网点信息
     * @param string $store_id
     * @return array
     */
    public function getSysStoreInfo(string $store_id = '')
    {
        if (empty($store_id)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(SysStoreModel::class);
        $builder->where('id = :id:', ['id' => $store_id]);
        $builder->andWhere('state = :state:', ['state' => 1]);
        $builder->columns('id, name, category, short_name, phone');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 通过部门ID获得费用网点
     * @param $id  integer 费用部门id
     * @return array
     */
    public function getSysStoreListByDepartmentId($id)
    {
        //部门和网点类型关联关系：
        //[25]hub——[8]hub
        //2. [4]Network Management——[1]SP,[2]DC,[10]BDC
        //[13]shop management——[4]shop(pickup only),[5]shop(pickup&delivery),[7]ushop,[9]OS
        //[65]flash freight hub——[12]B-HUB

        //公司id,node_department_id
        // 根据国家走不同的映射关系
        $countryCode = strtoupper(env('country_code', 'TH'));
        if ('TH' == $countryCode) {
            $hash = [
                25=>[8],
                4=>[1,2,10],
                13=>[4,5,7,9],
                65=>[12],
                Enums::$company_types['FlashFullfillment']=>[11]
            ];
        } elseif ('PH' == $countryCode) {
            $hash = [
                126=>[8],
                125=>[1,2,10],
                123=>[4,5,7,9],
                25=>[11]
            ];
        } else {
            $hash = []; //后面扩展
        }

        //通过当前费用部门id获得一级部门id
        $id = DepartmentModel::getFirstDepartmentIdByDepartmentId($id);
        $is_ffm = (new DepartmentService())->isFFMByDepartmentId($id);
        //如果是ffm公司，肯定不会跟上面重复
        if($is_ffm){
            $id = Enums::$company_types['FlashFullfillment'];
        }

        if(!isset($hash[$id])){
            return ["items"=>[[
                'id' => "-1",
                'name' => 'Head Office'
            ]]];
        }

        $ids = $hash[$id];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name,sap_pc_code');
        $builder->from(SysStoreModel::class);
        $builder->andWhere('state = 1');
        $builder->andWhere("category in ({ids:array})",['ids'=>$ids]);
        $items = $builder->getQuery()->execute()->toArray();
        $items = array_merge([[
            'id' => "-1",
            'name' => 'Head Office'
        ]], $items);
        return [
            'items' => $items,
        ];
    }


    /**
     * 判断该网点是否是 shop类型网点
     * @param $store_id
     * @return bool
     */
    public function isShopStore($store_id)
    {
        if ($store_id == -1) {
            return false;
        }
        $store = SysStoreModel::findFirst(
            [
                'conditions' => 'id=:id:',
                'bind' => ['id' => $store_id]
            ]
        );
        if (!empty($store) && in_array($store->category, [4, 5, 7])) {
            return true;
        }
        return false;
    }

    /**
     * 获取所有部门网点类型
     * @param $department_id
     * @return array
     */
    public function getDepartStoreType($department_id = 0)
    {
        return SysDepartmentStoreTypeModel::getStoreListByDepartmentId($department_id);
    }

    /**
     * 获取所有正常的网点
     *
     * @param bool $header 是否包含总部 默认包含 添加sap_pc_code
     * @param array $condition
     * @param string $type
     * @return mixed
     */
    public function getAllStoreList(bool $header = true, $condition = [], $type = '')
    {
        // 全量数据, 增加缓存60min
        $cache_data = [];

        // 去掉缓存
//        $is_cache = empty($condition['sap_pc_code']) && empty($condition['name']) && empty($type);
//        if ($is_cache) {
//            $cache_key = md5('oa_all_store_list_cache');
//            $cache_data = $this->getCache($cache_key);
//        }

        if (empty($cache_data)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('id, name, sap_pc_code, category');
            $builder->from(SysStoreModel::class);
            $builder = $this->getCondition($condition, $builder, $type);
            $list    = $builder->getQuery()->execute()->toArray();

//            if ($is_cache) {
//                $this->setCache($cache_key, json_encode($list), 3600);
//            }
        } else {
            $list = json_decode($cache_data, true);
        }

        if ($header) {
            array_unshift($list, ['id' => '-1', 'name' => Enums::PAYMENT_HEADER_STORE_NAME, 'sap_pc_code' => '']);
        }

        return $list;
    }
    /**
     * 获取对应网点的pc_code
     * @param string $store_id 网点ID
     * @return string
     */
    public function getPcCodeByStoreId($store_id)
    {
        $item = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $store_id]]);
        if (!empty($item)) {
            return $item->sap_pc_code;
        }
        return "";
    }

    /**
     * 网点搜索条件
     *
     * @param $condition
     * @param $builder
     * @param string $type
     * @return mixed
     */
    public function getCondition($condition, $builder, $type = '')
    {
        $flag = false;
        $size = 50;

        if (isset($condition['pagesize']) && $condition['pagesize']) {
            $size = $condition['pagesize'];
        }

        $builder->where('state = :state:', ['state' => Enums::STORE_STATE_ACTIVE]);

        if (!empty($type)) {
            $flag = true;
            //新的业务，调用物料盘点使用网点
            $builder->andWhere('category != :category:', ['category' => InventoryCheckEnums::CATEGORY_ID]);
        }

        if (!empty($condition['store_category_item'])) {
            $flag = true;
            $builder->inWhere('category', $condition['store_category_item']);
        }

        if (!empty($condition['name_item'])) {
            $flag = true;
            $builder->inWhere('name', $condition['name_item']);
        }

        if (isset($condition['sap_pc_code']) && $condition['sap_pc_code']) {
            $flag = true;
            $builder->andWhere('sap_pc_code  LIKE :sap_pc_code:', ['sap_pc_code' => '%' . $condition['sap_pc_code'] . '%']);
        }

        if (isset($condition['name']) && $condition['name']) {
            $flag = true;
            $builder->andWhere('name LIKE :name: ', ['name' => '%' . $condition['name'] . '%']);
        }

        if ($flag) {
            $builder->limit($size);
        }

        return $builder;
    }

    /**
     * 获取收获地址列表
     * @param array $params 查询参数
     * @return array
     */
    public function getAddressList($params)
    {
        //限制条数
        $limit = empty($params['pageSize']) ? 20 : $params['pageSize'];
        // 搜索总部地址
        $params['limit'] = $limit;
        $headquartersAddress = $this->getHeaderListByCondition($params);
        if (!empty($headquartersAddress)) {
            $limit = $limit - count($headquartersAddress);
        }
        $others = [];
        if ($limit > 0) {
            if (empty($params['type'])) {
                //Others
                $others = [
                    [
                        'id' => 0,
                        'name' => 'Others',
                        'province_code' => '',
                        'city_code' => '',
                        'district_code' => '',
                        'address' => '',
                        'headquarters'=>''
                    ]
                ];
                $limit = $limit - 1;
            }
        }
        //网点地址
        $storeAddress = [];
        if ($limit > 0) {
            $params['limit'] = $limit;
            $storeAddress = $this->getSysStoreListByCondition($params, 'id, name, province_code, city_code, district_code, detail_address as address, id as headquarters');
        }
        return array_merge($headquartersAddress, $others, $storeAddress);
    }

    /**
     * 获取网点类型
     */
    public function getStoreCategoryList()
    {
        $store_type_list = (new StoreRepository())->getSysStoreType();
        return sort_array_by_fields($store_type_list, 'id', SORT_ASC);
    }

    /**
     * 网点搜索
     *
     * @param array $params
     * @return array|mixed
     */
    public function searchStoreList(array $params)
    {
        $list = [];

        $head_store_info = [
            'id' => Enums::PAYMENT_HEADER_STORE_ID,
            'name' => Enums::PAYMENT_HEADER_STORE_NAME,
            'province_region_name' => '',// 省份大区
            'full_detail_address' => '',// 网点地址
        ];

        // 非总部的网点搜索
        if ($params['category'] != Enums::HEAD_OFFICE_STORE_FLAG) {
            // 网点
            $columns = 'id, name, manage_region, province_code, city_code, district_code, detail_address, postal_code';
            $list = (new StoreRepository())->searchSysStore($params, $columns);

            // 获取省份大区、详细地址拼接
            // 获取网点的省/市/区/大区/地址
            $list = WarehouseService::getInstance()->getAreaInfo($list);

            // 大区
            $manage_region_map = RegionRepository::getInstance()->getListByIds(array_filter(array_unique(array_column($list, 'manage_region'))));
            foreach ($list as &$store) {
                // 大区
                $region_name = $manage_region_map[$store['manage_region']] ?? '';

                // 网点省份大区
                $store['province_region_name'] = $store['province_name'] . ',' . $region_name;

                // 详细地址[网点信息用: 网点详细地址和省/市/区的拼接]
                $store['full_detail_address'] = trim($store['detail_address'] . ' ' . $store['district_name'] . '-' . $store['city_name'] . '-' . $store['province_name'] . '-' . $store['postal_code']);

                unset($store['province_name'], $store['city_name'], $store['district_name'], $store['province_code'], $store['city_code'], $store['district_code'], $store['detail_address'], $store['manage_region'], $store['postal_code']);
            }
        }

        // 含总部名称的搜索(总部网点类型 或 无网点类型的)
        if (($params['category'] == Enums::HEAD_OFFICE_STORE_FLAG || empty($params['category'])) && (!empty($params['name']) && mb_substr_count(strtolower(Enums::PAYMENT_HEADER_STORE_NAME), strtolower($params['name'])))) {
            array_unshift($list, $head_store_info);
        }

        return $list;
    }

    /**
     * 网点搜索, 总部 + 网点
     *
     * @param array $params
     * @return array|mixed
     */
    public function searchAllStoreList(array $params)
    {
        $list = [];

        $head_store_info = [
            'id' => Enums::PAYMENT_HEADER_STORE_ID,
            'name' => Enums::PAYMENT_HEADER_STORE_NAME,
        ];

        // 网点搜索
        $params['state'] = !empty($params['state']) ? $params['state'] : Enums::STORE_STATE_ALL;
        $list = (new StoreRepository())->searchSysStore($params);

        // 是否含总部名称
        if (mb_substr_count(strtolower(Enums::PAYMENT_HEADER_STORE_NAME), strtolower($params['name']))) {
            array_unshift($list, $head_store_info);
        }

        return $list;
    }

    /**
     * 获取某网点的大区、片区信息
     * @param string $store_id 网点编号
     * @return array
     */
    public function getStoreRegionAndPiece($store_id)
    {
        $data = [
            'region_id' => '',
            'region_name' => '',
            'piece_id' => '',
            'piece_name' => ''
        ];
        $store_info = (new StoreRepository())->getStoreDetail($store_id);
        if (empty($store_info)) {
            return $data;
        }

        //获取大区
        if ($store_info['manage_region']) {
            $region_info = RegionRepository::getInstance()->getRegionInfoById($store_info['manage_region'], 2);
            if ($region_info) {
                $data['region_id'] = $region_info['id'];
                $data['region_name'] = $region_info['name'];
            }
        }

        //获取片区
        if ($store_info['manage_piece']) {
            $piece_info = PieceRepository::getInstance()->getPieceInfoById($store_info['manage_piece'], 2);
            if ($piece_info) {
                $data['piece_id'] = $piece_info['id'];
                $data['piece_name'] = $piece_info['name'];
            }
        }
        return $data;
    }


    /**
     * 搜索网点主管
     * @param array $params 参数组
     * @param integer $limit 条数
     * @return array|mixed
     */
    public function searchStoreManager(array $params, int $limit = GlobalEnums::DEFAULT_PAGE_SIZE)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('DISTINCT hr.staff_info_id, hr.name');
        $builder->from(['main' => SysStoreModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hr.staff_info_id = main.manager_id', 'hr');
        //在职(不包含待离职)
        $builder->where('hr.state = :state: and hr.wait_leave_state = :wait_leave_state:', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
        //支持工号和姓名全模糊搜索
        if (!empty($params['name'])) {
            $builder->andWhere('hr.name LIKE :name: OR hr.staff_info_id LIKE :name:', ['name' => '%' . $params['name'] . '%']);
        }
        $builder->limit($params['limit'] ?? $limit);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取网点的大区、片区信息
     * @param array $store_id_list 网点编号
     * @return array
     */
    public function getStoreListRegionAndPiece(array $store_id_list)
    {
        if (empty($store_id_list)) {
            return [];
        }

        $columns = [
            'store.id AS store_id',
            'region.id AS region_id',
            'region.name AS region_name',
            'piece.id AS piece_id',
            'piece.name AS piece_name',
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['store' => SysStoreModel::class]);
        $builder->leftJoin(SysManageRegionModel::class, 'store.manage_region = region.id', 'region');
        $builder->leftJoin(SysManagePieceModel::class, 'store.manage_piece = piece.id', 'piece');
//        $builder->where('region.deleted = :deleted: AND piece.deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('store.id', $store_id_list);

        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list, null, 'store_id');
    }
}
