<?php
namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\SysAttachmentDownloadLogModel;
use App\Models\oa\SysAttachmentModel;
use Exception;

class AttachmentService extends BaseService
{

    private static $instance;
    const BUSINESS_TYPE_DEFAULT = '';
    const BUSINESS_TYPE_ELECTRONIC = 'contract_electronic';
    const BUSINESS_TYPE_ELECTRONIC_1 = 'contract_electronic_1';
    const BUSINESS_TYPE_RELATED_FILE_LIST = 'related_file_list_file';
    const BUSINESS_TYPE_CONTRACT_PDF_REJECT_NAME = 'contract_pdf_reject_name';
    const BUSINESS_TYPE_CONTRACT_PDF_NONEED_FILE = 'contract_pdf_noneed_file';
    const BUSINESS_TYPE_CONTRACT_ARCHIVE_CONTRACT_FILE = 'contract_archive_contract_file';

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 附件表信息
     * @param int $id 附件id
     * @return mixed
     * @throws ValidationException
     */
    public function getSysAttachmentInfoById($id)
    {
        $sys_module_info = SysAttachmentModel::findFirst([
            'conditions' => 'id = :id: AND deleted = :deleted:',
            'bind'       => [
                'id'      => $id,
                'deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
        if (empty($sys_module_info)) {
            throw new ValidationException(static::$t->_('common_sys_attachment_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $sys_module_info;
    }

    /**
     * 下载附件
     * @param array $params 请求参数组
     * @param array $user_info 当前登陆者信息组
     * @return mixed
     * @throws ValidationException
     */
    public function downSysAttachment($params, $user_info)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $business_type = !empty($params['business_type']) ? $params['business_type'] : self::BUSINESS_TYPE_DEFAULT;
            //区分查看来源1新增、2查看，新增时无需记录下载日志
            if ($params['source_type'] == 1) {
                $object_key = $params['object_key'];
            } else {
                $business_data = $this->getBusinessAttachmentData($business_type,$params['id']);
                if (!empty($business_data)){
                    $object_key        = $business_data['object_key'] ?? '';
                    $sys_attachment_id = 0;
                    $file_name         = '';
                    $oss_bucket_type   = 0;
                    $sub_type          = 0;
                }else{
                    $sys_attachment_info = $this->getSysAttachmentInfoById($params['id']);
                    $object_key = $sys_attachment_info->object_key;
                    $sys_attachment_id = $sys_attachment_info->id;
                    $file_name = $sys_attachment_info->file_name ? $sys_attachment_info->file_name : '';
                    $oss_bucket_type = $sys_attachment_info->oss_bucket_type;
                    $sub_type = $sys_attachment_info->sub_type;
                }
                $sys_attachment_download_log = new SysAttachmentDownloadLogModel();
                $add_data                    = [
                    'staff_id'          => $user_info['id'],
                    'staff_name'        => $user_info['name'],
                    'business_type'     => $business_type,
                    'business_id'       => $params['id'],
                    'sys_attachment_id' => $sys_attachment_id ?? 0,
                    'file_name'         => $file_name ?? '',
                    'oss_bucket_type'   => $oss_bucket_type ?? 0,
                    'sub_type'          => $sub_type ?? 0,
                    'created_at'        => date('Y-m-d H:i:s'),
                ];
                $bool = $sys_attachment_download_log->i_create($add_data);
                if ($bool === false) {
                    $this->logger->warning('系统附件下载记录-失败：' . json_encode($add_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($sys_attachment_download_log));
                    throw new ValidationException(static::$t->_('common_download_sys_attachment_save_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //下载oss地址
            $data = OssHelper::downloadFileHcm($object_key);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取业务附件数据
     * @param $business_type
     * @param $id
     * @return array
     * @throws ValidationException
     */
    public function getBusinessAttachmentData($business_type,$id): array
    {
        switch ($business_type) {
            case self::BUSINESS_TYPE_ELECTRONIC:
                $data = ContractElectronicModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $id]
                ]);
                if (empty($data)) {
                    throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                }
                $return_data['object_key'] = !empty($data->file_url) ? $data->file_url : '';
                break;
            case self::BUSINESS_TYPE_RELATED_FILE_LIST:
            case self::BUSINESS_TYPE_CONTRACT_PDF_REJECT_NAME:
            case self::BUSINESS_TYPE_CONTRACT_PDF_NONEED_FILE:
            case self::BUSINESS_TYPE_CONTRACT_ARCHIVE_CONTRACT_FILE:
            case self::BUSINESS_TYPE_ELECTRONIC_1:
                $return_data['object_key'] = $id;
                break;
            default:
                $return_data = [];
        }
        return $return_data ?? [];
    }
}
