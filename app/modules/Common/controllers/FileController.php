<?php

namespace App\Modules\Common\Controllers;

use app\library\ApiClient;
use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\AttachmentService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class FileController extends BaseController
{

    /**
     * @Token
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function getUploadTokenAction()
    {
        $file = $this->request->get('file');
        //文件存储目录
        $dir  = $this->request->get('type', 'upper');
        $dirs = [
            'WORK_ORDER',
        ];
        if (!in_array($dir, $dirs)) {
            //return $this->returnJson(0, 'type error', []);
        }

        // V22094 是否私有化 true是，false否
        $is_act_private = $this->request->get('is_act_private');
        $is_act_private = $is_act_private == true ? $is_act_private : false;
        $return = OssHelper::uploadFileHcm($file, $is_act_private);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $return);
    }

    /**
     * 文件下载
     * @Token
     * @return Response
     */
    public function downloadAction()
    {
        $params = $this->request->get();

        try {
            // 去除数组元素两侧空白字符
            $params = trim_array($params);

            Validation::validate($params, [
                'file_name' => 'StrLenGeLe:0,512|>>>:file name error',
                'file_url'  => 'Required|StrLenGeLe:10,512|>>>:file url error',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $http_header          = @get_headers($params['file_url'], true);
        $http_response_header = $http_header[0] ?? 'header null';
        if (!stripos($http_response_header, '200')) {
            $message = 'oss response error: ' . $http_response_header;
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $message);
        }

        $file_suffix = substr($params['file_url'], strrpos($params['file_url'], '.'));
        $file_name   = $params['file_name'] ?? 'tmpFile_' . date('YmdHis') . $file_suffix;

        $this->response
            ->setHeader('Content-Type', $http_header['Content-Type'])
            ->setHeader('Content-Disposition', 'attachment; filename="' . $file_name . '"');

        if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) || empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
            $this->response->setHeader('Content-Length', $http_header['Content-Length']);
        }

        // 如下设置 Content-Type 可以生效
//        echo file_get_contents($res['data']['file_url']);

        // 如下设置Content-Type 未生效, 被重置
        $this->response->setFileToSend($params['file_url'], $file_name);
        return $this->response;
    }

    /**
     * 下载附件
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91004
     * @return mixed
     * @throws ValidationException
     */
    public function downUploadFileAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['id' => 'IfIntEq:source_type,2|Required|StrLenGe:1', 'source_type' => 'Required|IntIn:1,2', 'object_key' => 'IfIntEq:source_type,1|Required|StrLenGeLe:2,300']);
        $res = AttachmentService::getInstance()->downSysAttachment($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}