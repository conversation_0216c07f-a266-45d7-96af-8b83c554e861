<?php

namespace App\Modules\WorkflowManagement\Models;

use App\Library\BaseModel;

class ByWorkflowModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('workflow');
    }
    const  IS_VIEW = 1;  // 1是对接了可视化


    //审批类型
    const APPROVAL_TYPE_AT = 1;                        //补卡
    const APPROVAL_TYPE_LE = 2;                        //请假
    const APPROVAL_TYPE_LH = 3;                        //LH(废弃)
    const APPROVAL_TYPE_OVERTIME = 4;                  //加班
    const APPROVAL_TYPE_OVERTIME_OS = 77;              //加班（外协）
    const APPROVAL_TYPE_HC = 6;                        //HC
    const APPROVAL_TYPE_WMS = 9;                       //物料
    const APPROVAL_TYPE_BT = 10;                       //出差
    const APPROVAL_TYPE_VEHICLE = 11;                  //车辆里程
    const APPROVAL_TYPE_FLEET = 12;                    //加班车
    const APPROVAL_TYPE_RN = 13;                       //离职
    const APPROVAL_TYPE_OS = 14;                       //外协
    const APPROVAL_TYPE_ASSETS = 16;                   //个人资产
    const APPROVAL_TYPE_REPORT = 17;                   //举报
    const APPROVAL_TYPE_JT = 18;                       //转岗
    const APPROVAL_TYPE_PUBLIC_ASSETS = 19;            //公共资产
    const APPROVAL_TYPE_JT_OA = 20;                    //转岗申请OA渠道
    const APPROVAL_TYPE_FD_NETWORK = 21;               //运营产品-network(废弃)
    const APPROVAL_TYPE_SALARY = 22;                   //薪资审批
    const APPROVAL_TYPE_ATT_BUS = 23;                  //出差打卡
    const APPROVAL_TYPE_HC_BUDGET = 24;                //HC预算
    const APPROVAL_TYPE_FD_SALES_PMD = 25;             //运营产品-sales/Project Managerment(废弃)
    const APPROVAL_TYPE_FD_SHOP = 26;                  //运营产品-shop(废弃)
    const APPROVAL_TYPE_ADJUST_ROLE = 27;              //增减角色审批
    const APPROVAL_TYPE_CLAIMER = 30;                  //网点理赔
    const APPROVAL_TYPE_FUEL = 31;                     //油费补贴
    const APPROVAL_TYPE_YCBT = 32;                     //黄牌项目出差
    const APPROVAL_TYPE_MESSAGE = 33;                  //消息审批
    const APPROVAL_TYPE_PA = 34;                       //kit 处罚申诉
    const APPROVAL_TYPE_OFFER_SIGNATURE = 35;          //offer签字
    const APPROVAL_TYPE_OPR = 36;                      //外协特殊价格
    const APPROVAL_TYPE_GO = 37;                       //外出申请
    const APPROVAL_TYPE_GO_CI = 38;                    //外出打卡审批 go out clock in
    const APPROVAL_TYPE_SAS = 39;                      //网点申请支援 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SASS = 40;                     //员工申请支援网点 - 目前只有菲律宾在用
    const APPROVAL_TYPE_SYSTEM_CS = 41;                //外部审核- 众包
    const APPROVAL_TYPE_ABNORMAL_EXPENSE = 42;         //异常费用
    const APPROVAL_TYPE_ASSET_V2 = 46;                 //OA资产申请
    const APPROVAL_TYPE_ABNORMAL_EXPENSE_FREIGHT = 47; //异常费用-单次运费调整
    const APPROVAL_TYPE_SYSTEM_DRIVER_BLACKLIST = 48;  //外部审核- 司机黑名单
    const APPROVAL_TYPE_MILEAGE = 49;                  //TH虚假里程
    const APPROVAL_TYPE_WMS_V2 = 50;                   //耗材申请单
    const APPROVAL_TYPE_OUTSOURCING_OT  = 51;          //外协员工加班审批
    const APPROVAL_TYPE_HUB_OS_AT = 52;                //外协员工补卡审批
    const APPROVAL_TYPE_QUITCLAIM = 56;                //Quitclaim审核申请
    const APPROVAL_TYPE_REINSTATEMENT = 59;            //停职恢复在职申请
    const APPROVAL_TYPE_REEMPLOYMENT = 61;             //重新雇佣申请
    const APPROVAL_TYPE_CANCEL_CONTRACT = 63;           //个人代理离职
    const APPROVAL_TYPE_JT_STAGE_TWO = 65;             //转岗二阶段审批流
    const APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT = 68;//公司解约个人代理
    const APPROVAL_TYPE_OA_AGENCY_PAYMENT = 71;        //代理支付
    const APPROVAL_TYPE_SUSPEND_WORK = 72;             //个人代理暂停接单申请
    const APPROVAL_TYPE_IC_RENEWAL = 74;               //个人代理续约合同
    const APPROVAL_TYPE_SUSPENSION_STATE = 80;         //停职申请
    const APPROVAL_TYPE_EXPENSE_WITHHOLDING = 89;         //费用预提
    /**
     * 主键ID
     * @var int
     */
    private $id;

    /**
     * 审批流名称
     * @var string
     */
    private $name;

    /**
     * 关联审批类型
     * @var int
     */
    private $relate_type;

    /**
     * 审批流描述
     * @var string
     */
    private $description;

    /**
     * 创建时间
     * @var
     */
    private $created_at;
    /**
     * @var
     */
    private $updated_at;

    /**
     * 是否启用 1-已启用 2-已停用
     * @var int
     */
    private $state;

    /**
     * 保存前端的审批流请求数据
     * @var string
     */
    private $flow_request;

    /**
     * 审批流版本
     * @var string
     */
    private $version;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId(int $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return int
     */
    public function getRelateType(): int
    {
        return $this->relate_type;
    }

    /**
     * @param int $relate_type
     */
    public function setRelateType(int $relate_type): void
    {
        $this->relate_type = $relate_type;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @param string $description
     */
    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return int
     */
    public function getState(): int
    {
        return $this->state;
    }

    /**
     * @param int $state
     */
    public function setState(int $state): void
    {
        $this->state = $state;
    }

    /**
     * @return string
     */
    public function getFlowRequest()
    {
        return $this->flow_request;
    }

    /**
     * @param string $flow_request
     */
    public function setFlowRequest(string $flow_request): void
    {
        $this->flow_request = $flow_request;
    }

    /**
     * @return string
     */
    public function getVersion(): string
    {
        return $this->version;
    }

    /**
     * @param string $version
     */
    public function setVersion(string $version): void
    {
        $this->version = $version;
    }
}
