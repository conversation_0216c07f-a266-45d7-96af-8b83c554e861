<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/3/18
 * Time: 9:13 PM
 */

namespace App\Modules\Rpc\Controllers;


use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Services\ContractElectronicService;
use App\Modules\CrmQuotation\Services\ApplyService;
use App\Modules\Hc\Controllers\BaseController;
use App\Modules\Loan\Services\AddService;
use App\Modules\Material\Services\LeaveAssetsService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Organization\Services\JobService;
use App\Modules\Organization\Services\StoreService;
use App\Modules\Organization\Services\SysManageRegionPieceService;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\Reimbursement\Services\ListService;
use app\modules\Rpc\callback\AssetReturn;
use app\modules\Rpc\callback\BusinessTripCheck;
use app\modules\Rpc\callback\ByWorkflow;
use app\modules\Rpc\callback\InvoiceCheck;
use app\modules\Rpc\callback\LeaveAsset;
use app\modules\Rpc\callback\AssetTransfer;
use app\modules\Rpc\callback\MaterialAsset;
use app\modules\Rpc\callback\MaterialInventoryCheck;
use app\modules\Rpc\callback\PaperDocument;
use App\Modules\Setting\Services\FinancialService;
use app\modules\Rpc\callback\Kpi;
use App\Modules\TalentReview\Services\PermissionsService as TalentReviewPermissionsService;
use App\Modules\User\Services\UserService;
use App\Modules\Training\Services\TaskService;
use JsonRPC\Server as rpc_server;
use App\Modules\Reimbursement\Services\AddService as re_service;
use App\Modules\BankFlow\Services\AddService as bank_parcel_claim;
use App\Modules\Reimbursement\Services\DetailService;
use app\modules\Rpc\callback\MaterialWms;
use App\Modules\ReserveFund\Services\ApplyService as ReserveFundApplyService;
use App\Modules\LegalAffairs\Services\ComplaintManageService;
use Exception;


class SvcController extends BaseController{

    public function callAction(){
        $server = new rpc_server();
        $procedureHandler = $server->getProcedureHandler();

        $procedureHandler

            //by申请报销 获取报销单号等信息
            ->withCallback('reimbursement_default', function ($locale, $param) {
                $user['id'] = $param['staff_id'];
                $user['name'] = $param['name'];
                return re_service::getInstance()->defaultData($param, $user);
            })
            //by申请 油费报销 列表页
            ->withCallback('reimbursement_list', function ($locale, $param) {
                $condition['apply_id'] = $staff_id = $param['staff_id'];
                $condition['source_type'] = BaseService::LIST_SOURCE_TYPE_2;
                BaseService::setLanguage($locale['locale']);
                if(!empty($param['status'])){
                    //1代审 2 驳回 3 已支付 4 未支付
                    if(in_array($param['status'],array(1,2)))
                        $condition['status'] = intval($param['status']);
                    if($param['status'] == 3)
                        $condition['pay_status'] = 2;
                    if($param['status'] == 4){
                        $condition['status'] = 2;
                        $condition['pay_status'] = 3;
                    }
                }

                $condition['pageNum'] = $param['pageNum'];
                $condition['pageSize'] = $param['pageSize'];
                $condition['created_at'] = $param['created_at'];

                return ListService::getInstance()->getList($condition, [], BaseService::LIST_TYPE_APPLY);
            })

            //by申请报销 获取对应详情页面
            ->withCallback('reimbursement_detail', function ($locale, $param) {
                try {
                    $server = new BudgetService();
                    if ($locale['locale'] == 'zh') {
                        $locale['locale'] = 'zh-CN';
                    }

                    $server::setLanguage($locale['locale']);
                    return DetailService::getInstance()->getCommonDetail($param['id'], $param['staff_id']);
                } catch (\Exception $e) {
                    return $e->getMessage();
                }

            })

            //获取 by申请报销 科目 目前只有 油费 获取所有顶级科目后 by筛选需要的
            ->withCallback('reimbursement_object', function ($locale, $param) {
                $user['department_id'] = $param['department_id'];
                $user['organization_type'] = $param['organization_type'];
                $user['source_type'] =  ListService::LIST_SOURCE_TYPE_2;
                $server = new BudgetService();
                $server::setLanguage($locale['locale']);
                return $server->get_object_by_dep($user,$server::ORDER_TYPE_1);
            })
            ->withCallback('reimbursement_object_product', function ($locale, $param) {
                $object_code = $param['object_code'];
                $server = new BudgetService();
                $server::setLanguage($locale['locale']);
                return $server->get_purchase_product($object_code,$server::ORDER_TYPE_1);
            })


            //by申请报销油费 添加
            ->withCallback('reimbursement_add', function ($locale, $param) {
                try {
                    $user_server = new UserService();
                    $user_info = $user_server->getUserById($param['user_info']['id']);
                    $user = $this->format_user($user_info);

                    $server = new BudgetService();
                    $server::setLanguage($locale['locale']);
                    $res = re_service::getInstance()->one($param['data'], $user);
                    $this->logger->info('reimbursement_add result ' . json_encode($res, JSON_UNESCAPED_UNICODE));
                    return $res;
                } catch (ValidationException $e) {
                    $this->logger->info('reimbursement_add Validation ' . $e->getMessage());
                    return array('code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []);
                } catch (BusinessException $e) {
                    $this->logger->warning('reimbursement_add Business ' . $e->getMessage());
                    return  array('code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []);
                } catch (\Exception $e) {
                    $this->logger->error('reimbursement_add Exception ' . $e->getMessage());
                    return  array('code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []);
                }
            })

            // by申请报销油费: 获取发票抬头列表
            ->withCallback('get_invoice_header_list', function ($locale, $param) {
                try {
                    $this->logger->info('get_invoice_header_list request param ' . json_encode($param, JSON_UNESCAPED_UNICODE));

                    $data = EnumsService::getInstance()->getInvoiceHeaderList();
                    return array('code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $data);
                } catch (\Exception $e) {
                    $this->logger->error('get_invoice_header_list Exception ' . $e->getMessage());
                    return  array('code' => $e->getCode(), 'message'=>$e->getMessage(), 'data' => []);
                }
            })


            //获取可关联的出差编号[已废弃 BY申请入口已关闭]
            ->withCallback('get_business_no', function ($locale, $param) {
                $user['id'] = $param['staff_id'];
                return re_service::getInstance()->getTravel($user['id'],$param['source_type']);
            })

            // 同步CRM报价审批
            ->withCallback('add_crm_quotation_audit', function ($locale, $param) {
                try {
                    $this->logger->info('add_crm_quotation_audit_parameter='.json_encode(['locale'=>$locale,'param'=>$param]));
                    $applyService = new ApplyService();
                    $bool = $applyService->atomicLock(function () use ($applyService, $locale, $param) {

                        $applyService::setLanguage($locale['locale']);
                        return $applyService->addApply($param);

                    }, 'add_crm_quotation_audit_' . $param['quoted_price_list_sn']);

                } catch (ValidationException $validationException) {

                    $this->logger->info('crm_quotation_audit result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];
                } catch (\Exception $exception) {

                    $this->logger->error('crm_quotation_audit result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => '审批流异常，请检查', 'data' => []];
                }

                return ['code' => 0, 'message' => '', 'data' => ['result' => $bool]];
            })

            // 审批流接口
            ->withCallback('crm_quotation_audit_log', function ($locale, $param) {
                try {

                    $applyService = new ApplyService();
                    $applyService::setLanguage($locale['locale']);

                    $auditLogs = $applyService->auditLogsByQuotationNo($param['quoted_price_list_sn']);

                } catch (ValidationException $validationException) {

                    $this->logger->info('crm_quotation_audit_log result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];

                } catch (\Exception $exception) {
                    $this->logger->error('crm_quotation_audit_log result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $exception->getMessage(), 'data' => []];
                }
                return ['code' => 0, 'message' => '', 'data' => $auditLogs];
            })

            // 撤销审批
            ->withCallback('cancel_crm_quotation', function ($locale, $param) {
                try {

                    $applyService = new ApplyService();
                    $bool = $applyService->atomicLock(function () use ($applyService, $locale, $param) {

                        return $applyService->cancel($param['quoted_price_list_sn'], $param['create_id'], '');

                    }, 'cancel_crm_quotation_' . $param['quoted_price_list_sn'], 10, false);


                } catch (ValidationException $validationException) {

                    $this->logger->info('crm_quotation_audit_log result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];
                } catch (\Exception $exception) {

                    $this->logger->error('crm_quotation_audit_log result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $exception->getMessage(), 'data' => []];
                }
                return ['code' => 0, 'message' => '', 'data' => [$bool]];
            })

            // 审批流接口
            ->withCallback('sys_department', function ($locale, $param) {
                try {
                    $this->logger->info('sys_department params='.json_encode(['locale'=>$locale,'param'=>$param]));

                    $applyService = new ApplyService();
                    $data = $applyService->SysDepartmentList($param);

                    $this->logger->info('sys_department result='.json_encode($data));

                } catch (ValidationException $validationException) {

                    $this->logger->info('sys_department result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];

                } catch (\Exception $exception) {
                    $this->logger->error('sys_department result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $exception->getMessage(), 'data' => []];
                }
                return ['code' => 0, 'message' => '', 'data' => $data];
            })->withCallback('training_task_detail', function ($locale, $params) {
                try {
                    Validation::validate($params, [
                        'staff_id'  => 'Required|IntGt:0',
                        'id'        => 'Required|IntGt:0',
                        'version'   => 'Required|IntGt:0',
                        'operate'   => 'Required|IntGt:0',
                    ]);

                    $service = new TaskService();
                    $service::setLanguage($locale['locale']);
                    $data = $service->svcGetTaskInfo($params);
                } catch (ValidationException $validationException) {

                    $this->logger->info('training_task_detail result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                            'Params' => $params,
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];

                } catch (\Exception $exception) {
                    $this->logger->error('training_task_detail result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                            'Params' => $params,
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $exception->getMessage(), 'data' => []];
                }
                $this->logger->info('training_task_detail result' . json_encode([
                        'Locale' => $locale,
                        'Params' => $params,
                        'Result' => $data,
                    ], JSON_UNESCAPED_UNICODE));
                return ['code' => 0, 'message' => 'success', 'data' => $data];
            })->withCallback('training_task_join', function ($locale, $params) {
                try {
                    Validation::validate($params, [
                        'staff_id'  => 'Required|IntGt:0',
                        'id'        => 'Required|IntGt:0',
                        'version'   => 'Required|IntGt:0',
                        'select_type' => 'Required|IntIn:' . implode(',', array_keys(TaskService::TASK_JOIN_DESC)),
                    ]);

                    $service = new TaskService();
                    $service::setLanguage($locale['locale']);
                    $service->peopleJoin($params);
                } catch (ValidationException $validationException) {

                    $this->logger->info('training_task_join result' . json_encode([
                            'Err_File' => $validationException->getFile(),
                            'Err_Line' => $validationException->getLine(),
                            'Err_Msg' => $validationException->getMessage(),
                            'Params' => $params,
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $validationException->getMessage(), 'data' => []];

                } catch (\Exception $exception) {
                    $this->logger->error('training_task_join result' . json_encode([
                            'Err_File' => $exception->getFile(),
                            'Err_Line' => $exception->getLine(),
                            'Err_Msg' => $exception->getMessage(),
                            'Params' => $params,
                        ], JSON_UNESCAPED_UNICODE));

                    return ['code' => -1, 'message' => $exception->getMessage(), 'data' => []];
                }
                $this->logger->info('training_task_join result' . json_encode([
                        'Locale' => $locale,
                        'Params' => $params,
                    ], JSON_UNESCAPED_UNICODE));
                return ['code' => 0, 'message' => 'success', 'data' => []];
            })
            //ms同步数据 添加
            ->withCallback('parcel_claim_add', function ($locale, $param) {
                try{
                    $parcelClaim=bank_parcel_claim::getInstance();
                    $parcelClaim::setLanguage($locale['locale']);
                    return $parcelClaim->add($param);
                }catch (ValidationException $e) {
                   $this->logger->info('parcel_claim_add Validation ' . json_encode([
                            'message' => $e->getMessage(),
                            'param' => $param,
                            'locale' => $locale,
                        ], JSON_UNESCAPED_UNICODE));
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('parcel_claim_add Business ' . json_encode([
                            'message' => $e->getMessage(),
                            'param' => $param,
                            'locale' => $locale,
                        ], JSON_UNESCAPED_UNICODE));
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }catch (\Exception $e){
                    $this->logger->error('parcel_claim_add Exception ' . json_encode([
                            'message' => $e->getMessage(),
                            'param' => $param,
                            'locale' => $locale,
                        ], JSON_UNESCAPED_UNICODE));
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('update_organization_manager_store', function ($locale, $param) {
                try{
                    $api_params = [
                        'id' => $param['store_id'],
                        'manager_id' => $param['manager_id'],
                        'operator_id' => $param['operator_id'],
                        'manager_position_state' => $param['manager_position_state'] ?? null
                    ];
                    $return = (new StoreService())->sync_store_manager_ms($api_params);
                    $this->getDI()->get('logger')->info("update_organization_manager_store_params :" . json_encode($api_params) . ', response:' . json_encode($return));
                    if (isset($return['error'])) {
                        return ['code' => -1, 'message' => $return['error'], 'data' => []];
                    }
                    return ['code' => 0, 'message' => 'success', 'data' => []];
                }catch (ValidationException $e) {
                    $this->logger->warning('update_organization_manager_store Validation ' . $e->getMessage());
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('update_organization_manager_store Business ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }catch (\Exception $e){
                    $this->logger->warning('update_organization_manager_store Exception ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('sync_store_supervisor_manager', function ($locale, $param) {
                //网点主管自动同步为网点负责人
                try {
                    $api_params = [
                        'id'                     => $param['id'],
                        'manager_id'             => $param['manager_id'],
                        'user'                   => $param['user'],
                        'manager_position_state' => $param['manager_position_state'],
                        'confirm'                => OrganizationDepartmentEnums::CONFIRM_YES,
                    ];
                    $result     = (new StoreService())->updateStoreManager($api_params);
                    return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $result];
                } catch (BusinessException $e) {
                    $this->logger->warning('sync_store_supervisor_manager BusinessException ' . $e->getMessage());
                    return ['code' => ErrCode::$BUSINESS_ERROR, 'message' => $e->getMessage()];
                } catch (ValidationException $e) {
                    $this->logger->info('sync_store_supervisor_manager Validation ' . $e->getMessage());
                    return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage()];
                } catch (\Exception $e) {
                    $this->logger->warning('sync_store_supervisor_manager Exception ' . $e->getMessage());
                    return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'error', 'data' => null];
                }
            })
            ->withCallback('update_organization_manager_piece', function ($locale, $param) {
                try{
                    $api_params = [
                        'piece_id' => $param['piece_id'],
                        'manager_id' => $param['manager_id'],
                        'operator_id' => $param['operator_id'],
                        'manager_position_state' => $param['manager_position_state'] ?? null
                    ];
                    $return = (new SysManageRegionPieceService())->sync_manage_piece_manager_ms($api_params);
                    $this->getDI()->get('logger')->info("update_organization_manager_piece_params :" . json_encode($api_params) . ', response:' . json_encode($return));
                    if (isset($return['error'])) {
                        return ['code' => -1, 'message' => $return['error'], 'data' => []];
                    }
                    return ['code' => 0, 'message' => 'success', 'data' => []];
                }catch (ValidationException $e) {
                    $this->logger->warning('update_organization_manager_piece Validation ' . $e->getMessage());
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('update_organization_manager_piece Business ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }catch (\Exception $e){
                    $this->logger->warning('update_organization_manager_piece Exception ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('update_organization_manager_region', function ($locale, $param) {
                try{
                    $api_params = [
                        'region_id' => $param['region_id'],
                        'manager_id' => $param['manager_id'],
                        'operator_id' => $param['operator_id'],
                        'manager_position_state' => $param['manager_position_state'] ?? null
                    ];
                    $return = (new SysManageRegionPieceService())->sync_manage_region_manager_ms($api_params);
                    $this->getDI()->get('logger')->info("update_organization_manager_region_params :" . json_encode($api_params) . ', response:' . json_encode($return));
                    if (isset($return['error'])) {
                        return ['code' => -1, 'message' => $return['error'], 'data' => []];
                    }
                    return ['code' => 0, 'message' => 'success', 'data' => []];
                }catch (ValidationException $e) {
                    $this->logger->warning('update_organization_manager_region Validation ' . $e->getMessage());
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('update_organization_manager_region Business ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }catch (\Exception $e){
                    $this->logger->warning('update_organization_manager_region Exception ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('update_organization_manager_department', function ($locale, $param) {
                try{
                    $api_params = [
                        'department_id' => $param['department_id'],
                        'manager_id' => $param['manager_id'],
                        'operator_id' => $param['operator_id'],
                        'manager_position_state' => $param['manager_position_state'] ?? null
                    ];
                    $return = (new DepartmentService())->sync_manage_department_manager_ms($api_params);
                    $this->getDI()->get('logger')->info("update_organization_manager_department_params :" . json_encode($api_params) . ', response:' . json_encode($return));
                    if (isset($return['error'])) {
                        return ['code' => -1, 'message' => 'error', 'data' => []];
                    }
                    return ['code' => 0, 'message' => 'success', 'data' => []];
                }catch (ValidationException $e) {
                    $this->logger->warning('update_organization_manager_department Validation ' . $e->getMessage());
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('update_organization_manager_department Business ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }catch (\Exception $e){
                    $this->logger->warning('update_organization_manager_department Exception ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('sync_update_talent_review_permission', function ($locale, $params) {
                try{
                    Validation::validate($params, [
                        'staff_info_id' => 'Required|IntGt:0',
                        'type' => 'Required|IntIn:0,1',
                    ]);

                    $staff_info_id = $params['staff_info_id'];
                    $type = $params['type'];
                    if($type == 1) {
                        $return = (new TalentReviewPermissionsService())->addHrbpPermission($staff_info_id);
                    } else {
                        $return = (new TalentReviewPermissionsService())->delHrbpPermission($staff_info_id);
                    }

                    $this->getDI()->get('logger')->info("sync_update_talent_review_permission_params :" . json_encode($params) . ', response:' . json_encode($return));
                    if ($return === false) {
                        return ['code' => -1, 'message' => 'error', 'data' => []];
                    }
                    return ['code' => 0, 'message' => 'success', 'data' => []];
                } catch (ValidationException $e) {
                    $this->logger->warning('update_organization_manager_department Validation ' . $e->getMessage());
                    return array('code' => -1, 'message'=>$e->getMessage());
                }catch (BusinessException $e) {
                    $this->logger->warning('update_organization_manager_department Business ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                } catch (\Exception $e){
                    $this->logger->warning('update_organization_manager_department Exception ' . $e->getMessage());
                    return  array('code' => -1, 'message'=>$e->getMessage());
                }
            })
            ->withCallback('financial_staff_changes', function ($local, $params) {
                //hcm 员工信息变动 oa 财务审批变更
                try {
                    Validation::validate($params, [
                        'operator_id'      => 'Required|IntGt:0',
                        'staff_info_id' => 'Required|IntGt:0',
                        'department_id' => 'Required|IntGt:0',
                    ]);
                    $this->logger->info(['function' => 'financial_staff_changes', 'params' => $params]);
                    $params = [

                        'operator_id'   => $params['operator_id'],   //操作者
                        'dep_id'        => $params['department_id'],   //部门 id
                        'type'          => FinancialService::$redis_list_type_2,
                        'staff_info_id' => $params['staff_info_id']
                    ];
                    $result = FinancialService::getInstance()->redisListAdd($params);
                    $this->logger->info(['function' => 'financial_staff_changes', 'params' => $params, 'result' => $result]);
                    return $result;
                } catch (ValidationException $e) {
                    $this->logger->warning('financial_staff_changes Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return array('code' => ErrCode::$SYSTEM_ERROR, 'message' => 'error', 'data' => null);

                }
            })
            ->withCallback('get_contract_electronic_list', function ($local, $params) {
                //电子合同签约列表
                try {
                    Validation::validate($params, [
                        'storeId'    => 'Required|StrLenGe:1',
                        'signStatus' => 'Required|IntIn:2,3',
                        'pageNum'    => 'Required|IntGt:0',
                        'pageSize'   => 'Required|IntGt:0',

                    ]);
                    $this->logger->info(['function' => 'get_contract_electronic_list', 'params' => $params]);
                    $params = [
                        'storeId'    => $params['storeId'],   //网点id
                        'signStatus' => $params['signStatus'],   //签约状态
                        'pageNum'    => $params['pageNum'],
                        'pageSize'   => $params['pageSize']
                    ];
                    $server = ContractElectronicService::getInstance();
                    $server::setLanguage($local['locale']);
                    $result = $server->signList($params);
                    $this->logger->info(['function' => 'get_contract_electronic_list', 'params' => $params, 'result' => $result]);
                    return $result;
                } catch (ValidationException $e) {
                    $this->logger->warning('get_contract_electronic_list Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return array('code' => ErrCode::$SYSTEM_ERROR, 'message' => 'error', 'data' => null);

                }
            })
            ->withCallback('get_contract_electronic_detail', function ($local, $params) {
                //电子合同预览
                try {
                    Validation::validate($params, [
                        'no' => 'Required|StrLenGe:1',
                    ]);
                    $this->logger->info(['function' => 'get_contract_electronic_detail', 'params' => $params]);

                    $result = ContractElectronicService::getInstance()->getPdfByNo($params['no']);
                    $this->logger->info(['function' => 'get_contract_electronic_detail', 'params' => $params, 'result' => $result]);
                    return $result;
                } catch (ValidationException $e) {
                    $this->logger->warning('get_contract_electronic_detail Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return array('code' => ErrCode::$SYSTEM_ERROR, 'message' => 'error', 'data' => null);

                }
            })
            ->withCallback('sign_contract_electronic', function ($local, $params) {
                //电子合同签名
                try {
                    Validation::validate($params, [
                        'no'                       => 'Required|StrLenGe:1',
                        'signInfo'                 => 'Required|Arr|ArrLenGe:1',
                        'signInfo[*].customerName' => 'Required|StrLenGeLe:1,100',
                        'signInfo[*].jobTitle'     => 'Required|StrLenGeLe:0,100',
                        'signInfo[*].signNameUrl'  => 'Required|StrLenGe:1',
                    ]);

                    $this->logger->info(['function' => 'sign_contract_electronic', 'params' => $params]);
                    $params = [
                        'no'        => $params['no'],   //网点id
                        'sign_info' => $params['signInfo'],
                    ];

                    $contractElectronicService = ContractElectronicService::getInstance();
                    $result                    = $contractElectronicService->atomicLock(function () use ($contractElectronicService, $local, $params) {
                        return $contractElectronicService->signElectronic($params);
                    }, 'contract_electronic_sign_' . $params['no'], 10);
                    $this->logger->info(['function' => 'sign_contract_electronic', 'params' => $params, 'result' => $result]);
                    if (!$result) {
                        return ['code' => ErrCode::$FREQUENT_VISIT_ERROR, 'message' => $this->t['sys_processing'], 'data' => null];
                    }

                    return $result;

                } catch (ValidationException $e) {
                    $this->logger->warning('sign_contract_electronic Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning($e->getTraceAsString() . $e->getMessage());
                    return array('code' => ErrCode::$SYSTEM_ERROR, 'message' => 'error', 'data' => null);

                }
            })
            ->withCallback('get_user_outstanding_amount', function ($locale, $params) {
                //获取员工在财务侧相关业务下未归还金额 @api https://yapi.flashexpress.pub/project/723/interface/api/68362
                try {
                    Validation::validate($params, ['staff_info_id' => 'Required|IntGt:0']);
                    $staff_info_id = $params['staff_info_id'];
                    //获取某员工借款剩余未归还总金额
                    $data['loan_amount'] = AddService::getInstance()->getStaffLoanOutstandingAmountReal($staff_info_id);

                    //获取某员工备用金未归还金额
                    $data['reserve_fund_amount'] = ReserveFundApplyService::getInstance()->getStaffReserveFundOutstandingAmountReal($staff_info_id);
                    $this->getDI()->get('logger')->info('get_user_outstanding_amount_params :' . json_encode($params) . ', response:' . json_encode($data));
                    return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $data];
                } catch (ValidationException $e) {
                    $this->logger->warning('get_user_outstanding_amount_params Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning('get_user_outstanding_amount_params Exception ' . $e->getMessage());
                    return  array('code' => ErrCode::$SYSTEM_ERROR, 'message' => $e->getMessage());
                }
            })->withCallback('batch_get_user_outstanding_amount', function ($locale, $params) {
                //批量获取各员工在财务侧相关业务下未归还金额 @api https://yapi.flashexpress.pub/project/723/interface/api/68367
                try {
                    Validation::validate($params, ['staff_info_id' => 'Required|Arr|ArrLenGeLe:1,500', 'staff_info_id[*]' => 'Required|IntGt:0']);
                    $staff_ids = $params['staff_info_id'];
                    //批量获取各员工借款剩余未归还总金额
                    $data['loan_amount'] = AddService::getInstance()->batchGetStaffLoanOutstandingAmountReal($staff_ids);

                    //批量获取各员工备用金未归还金额
                    $data['reserve_fund_amount'] = ReserveFundApplyService::getInstance()->batchGetStaffReserveFundOutstandingAmountReal($staff_ids);

                    //批量获取各员工资产扣费总额
                    $data['deduct_amount'] = LeaveAssetsService::getInstance()->batchGetStaffDeductAmount($staff_ids);
                    $this->logger->info('get_user_outstanding_amount_params :' . json_encode($params) . ', response:' . json_encode($data));

                    return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $data];
                } catch (ValidationException $e) {
                    $this->logger->warning('get_user_outstanding_amount_params Validation ' . $e->getMessage());
                    return array('code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage());
                } catch (\Exception $e) {
                    $this->logger->warning('get_user_outstanding_amount_params Exception ' . $e->getMessage());
                    return  array('code' => ErrCode::$SYSTEM_ERROR, 'message' => $e->getMessage());
                }
            })
            ->withCallback('get_company_list_for_complaint', function ($locale, $params) {
                try {
                    $data = ComplaintManageService::getInstance()->getCompanyList($params['is_all'] ?? true);
                    $this->logger->info('get_company_list_for_complaint :' . json_encode($params) . ', response:' . json_encode($data));
                    return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $data];
                } catch (Exception $e) {
                    $this->logger->warning('get_company_list_for_complaint Exception ' . $e->getMessage());
                    return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => $e->getMessage()];
                }
            })

            // BY 跳转 OA 鉴权
            ->withCallback('access_authentication', function ($locale, $params) {
                $this->logger->info('access_authentication_request:' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                UserService::setLanguage($locale['locale']);
                $result = UserService::getInstance()->staffAuthentication($params);
                $this->logger->info('access_authentication_response:' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return $result;
            })

            // BY 获取 OA 红点
            ->withCallback('get_user_oa_reddot_count', function ($locale, $params) {
                $this->logger->info('get_user_oa_reddot_count:' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                UserService::setLanguage($locale['locale']);
                $result = UserService::getInstance()->getUserReddotTotalCount($params['staff_id'], $params['src'] ?? '');
                $this->logger->info('get_user_oa_reddot_count:' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return $result;
            })
            // FBI 获取 OA 部门列表
            ->withCallback('getDepartmentFHOList', function ($locale, $params) {
                $this->logger->info('getDepartmentFHOList:' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
                UserService::setLanguage($locale['locale']);
                $result = (new DepartmentService)->getDepartmentFHOList($params);
                $this->logger->info('getDepartmentFHOList:' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return $result;
            })

            //新资产-相关rpc服务接口
            ->withClassAndMethod('get_asset_list', MaterialAsset::class, 'getAssetList')//资产申请-资产清单列表
            ->withClassAndMethod('get_add_default', MaterialAsset::class, 'getAddDefault')//资产申请-输入基本信息回显
            ->withClassAndMethod('get_address_list', MaterialAsset::class, 'getAddressList')//资产申请-选择资产使用地点
            ->withClassAndMethod('get_consignee_list', MaterialAsset::class, 'getConsigneeList')//资产申请-获取收货人
            ->withClassAndMethod('asset_apply', MaterialAsset::class, 'assetApply')//资产申请-提交
            ->withClassAndMethod('asset_apply_cancel', MaterialAsset::class, 'assetApplyCancel')//资产申请-撤回
            ->withClassAndMethod('asset_apply_detail', MaterialAsset::class, 'assetApplyDetail')//资产申请-详情
            ->withClassAndMethod('asset_apply_pass', MaterialAsset::class, 'assetApplyPass')//资产申请-审核通过
            ->withClassAndMethod('asset_apply_reject', MaterialAsset::class, 'assetApplyReject')//资产申请-审核驳回
            ->withClassAndMethod('get_asset_out_storage_list', MaterialAsset::class, 'getAssetOutStorageList')//资产申请-查看出库信息
            ->withClassAndMethod('get_outbound_tracking_info', MaterialAsset::class, 'getOutboundTrackingInfo')//资产申请、耗材申请-查看路由
            ->withClassAndMethod('get_wms_list', MaterialWms::class, 'getWmsSauList')//耗材申请-符合耗材的sau列表
            ->withClassAndMethod('get_wms_add_default', MaterialWms::class, 'getWmsAddDefault')//耗材基本信息回显
            ->withClassAndMethod('wms_apply_detail', MaterialWms::class, 'getWmsApplyDetail')//耗材详情
            ->withClassAndMethod('wms_apply_cancel', MaterialWms::class, 'wmsApplyCancel') //耗材撤回
            ->withClassAndMethod('wms_apply_add', MaterialWms::class, 'wmsApplyAdd') //耗材添加
            ->withClassAndMethod('wms_apply_pass', MaterialWms::class, 'wmsApplyPass')//耗材申请-审核通过
            ->withClassAndMethod('wms_apply_reject', MaterialWms::class, 'wmsApplyReject')//耗材申请-审核驳回
            ->withClassAndMethod('get_wms_out_storage_list', MaterialWms::class, 'getWmsOutStorageList')//耗材申请-查看出库信息
            ->withClassAndMethod('wms_apply_consignee_list', MaterialWms::class, 'getWmsApplyConsigneeList')//获取收货人
            ->withClassAndMethod('wms_consignee_cc_msg', MaterialWms::class, 'getConsigneeCCMsg')//获取抄送收货人站内信消息详情
            ->withClassAndMethod('wms_get_outbound_express_product', MaterialWms::class, 'getOutboundExpressProduct')//耗材申请-根据快递单号查看物料详情
            ->withClassAndMethod('package_allot_get_msg_info', MaterialWms::class, 'getPackageAllotMsgInfo')//耗材调拨单-站内信详情
            ->withClassAndMethod('package_allot_wait_handle_num', MaterialWms::class, 'getPackageAllotWaitHandleNum')//耗材调拨单-耗材调拨待处理数
            ->withClassAndMethod('package_allot_get_options_default', MaterialWms::class, 'getPackageAllotOptionsDefault')//耗材调拨单-获取枚举
            ->withClassAndMethod('package_allot_get_list', MaterialWms::class, 'getPackageAllotList')//耗材调拨单-待处理/已处理-列表
            ->withClassAndMethod('package_allot_detail', MaterialWms::class, 'getPackageAllotInfo')//耗材调拨单-查看
            ->withClassAndMethod('package_allot_detail_express_list', MaterialWms::class, 'getPackageAllotExpressList')//耗材调拨单-查看-查看物流
            ->withClassAndMethod('package_allot_detail_express_route', MaterialWms::class, 'getPackageAllotExpressRoute')//耗材调拨单-查看-查看路由
            ->withClassAndMethod('package_allot_cancel', MaterialWms::class, 'cancelPackageAllot')//耗材调拨单-无法调出
            ->withClassAndMethod('package_allot_confirm_out', MaterialWms::class, 'confirmOutPackageAllot')//耗材调拨单-确认调出
            ->withClassAndMethod('package_allot_confirm_in', MaterialWms::class, 'confirmInPackageAllot')//耗材调拨单-确认调入


            ->withClassAndMethod('change_leader', Kpi::class, 'changeLeader')    //hcm 直线上级变更调用该接口
            //新资产-离职资产管理
            ->withClassAndMethod('create_leave_asset', LeaveAsset::class, 'createLeaveAsset')//离职资产-生成离职资产数据
            ->withClassAndMethod('cancel_leave_asset', LeaveAsset::class, 'cancelLeaveAsset')//离职资产-取消离职(作废离职资产)
            ->withClassAndMethod('leave_asset_manager_change', LeaveAsset::class, 'leaveAssetManagerChange')//离职资产-上级id变更
            ->withClassAndMethod('has_leave_assets', LeaveAsset::class, 'hasLeaveAssets')//离职资产-by端打卡时验证-是否有没处理完的资产
            ->withClassAndMethod('get_assets_by_staff_id', LeaveAsset::class, 'getAssetsByStaffId')//离职资产-by端离职须知
            ->withClassAndMethod('get_assets_detail_by_staff_id', LeaveAsset::class, 'getAssetsDetailByStaffId')//离职资产-by端离职须知-明细
            ->withClassAndMethod('get_leave_assets_detail', LeaveAsset::class, 'getLeaveAssetsDetail')//离职资产-获取员工离职资产详情(hcm-员工离职管理)
            ->withClassAndMethod('get_not_return_assets_list', LeaveAsset::class, 'getNotReturnAssetsList')//离职资产-获取多个员工资产信息(hcm-导出)
            ->withClassAndMethod('get_assets_manager_default', LeaveAsset::class, 'getAssetsManagerDefault')//离职资产-上级确认-获取枚举(by端)
            ->withClassAndMethod('search_assets_manager_barcode', LeaveAsset::class, 'searchAssetsManagerBarcode')//离职资产-上级确认-barcode搜索(by端)
            ->withClassAndMethod('get_assets_manager_list', LeaveAsset::class, 'getAssetsManagerList')//离职资产-上级确认-列表(by端)
            ->withClassAndMethod('get_assets_manager_detail', LeaveAsset::class, 'getAssetsManagerDetail')//离职资产-上级确认-详情(by端)
            ->withClassAndMethod('add_assets_manager_info', LeaveAsset::class, 'addAssetsManagerInfo')//离职资产-上级确认-添加单个(by端)
            ->withClassAndMethod('edit_assets_manager', LeaveAsset::class, 'editSaveBy')//离职资产-上级确认-保存(by端)
            ->withClassAndMethod('get_leave_message_content', LeaveAsset::class, 'getLeaveMessageContent')//离职资产-给主管的站内信(by端)
            ->withClassAndMethod('get_audit_leave_assets', LeaveAsset::class, 'getAuditLeaveAssets')//离职资产-by端离职申请-详情页资产列表
            ->withClassAndMethod('get_leave_assets_manager_count', LeaveAsset::class, 'getLeaveAssetsManagerCount')//离职资产-上级确认-数量(红点)
            ->withClassAndMethod('delete_leave_assets_manager', LeaveAsset::class, 'deleteLeaveAssetsManager')//离职资产-上级确认-批量删除
            ->withClassAndMethod('get_leave_assets_staff_count', LeaveAsset::class, 'getLeaveAssetsStaffCount')//获取员工名下资产统计数据(短信短链接)
            ->withClassAndMethod('transfer_leave_assets_to_personal_agent', LeaveAsset::class, 'transferLeaveAssetsToPersonalAgent')//正式工转个人代理，对应的个人代理工号到岗确认，新工号变成在职的时候正式工离职资产处理逻辑

            ->withClassAndMethod('asset_transfer_get_my_asset_list', AssetTransfer::class, 'getMyAssetList')//资产转移-我的资产-列表
            ->withClassAndMethod('asset_transfer_get_my_asset_by_id', AssetTransfer::class, 'getMyAssetById')//资产转移-提交转移时-我的资产列表
            ->withClassAndMethod('asset_transfer_get_my_asset_detail', AssetTransfer::class, 'getDetail')//资产转移-我的资产-详情
            ->withClassAndMethod('asset_transfer_get_options_default', AssetTransfer::class, 'getOptionDefault')//资产转移-获取枚举
            ->withClassAndMethod('asset_transfer_batch_transfer', AssetTransfer::class, 'batchTransfer')//资产转移-批量转移
            ->withClassAndMethod('asset_transfer_batch_cancel', AssetTransfer::class, 'batchCancel')//资产转移-我的资产-批量撤销
            ->withClassAndMethod('asset_transfer_get_to_be_receiver', AssetTransfer::class, 'getToBeReceiver')//资产转移-待接收数量(小红点)
            ->withClassAndMethod('asset_transfer_get_receiver_list', AssetTransfer::class, 'getReceiverList')//资产转移-待接收-列表
            ->withClassAndMethod('asset_transfer_get_receiver_list_by_id', AssetTransfer::class, 'getReceiverListById')//资产转移-待接收-列表
            ->withClassAndMethod('asset_transfer_get_batch_reject', AssetTransfer::class, 'batchReject')//资产转移-待接收-拒绝
            ->withClassAndMethod('asset_transfer_batch_reception', AssetTransfer::class, 'batchReception')//资产转移-待接收-批量接收
            ->withClassAndMethod('asset_transfer_get_receiver_detail', AssetTransfer::class, 'getReceiverDetail')//资产转移-待接收-详情
            ->withClassAndMethod('asset_transfer_get_asset_pdf', AssetTransfer::class, 'getAssetsPdf')//资产转移-生成资产同意书/资产申请单
            ->withClassAndMethod('asset_transfer_get_assets_by_ids', AssetTransfer::class, 'getAssetsByIds')//资产转移-by站内信-通过资产id获取资产列表
            ->withClassAndMethod('asset_transfer_msg_info', AssetTransfer::class, 'getAssetTransferMsgInfo')//获取资产转移-站内信-资产批量变更提醒[导入转移消息详情]
            ->withClassAndMethod('search_staff', AssetTransfer::class, 'searchStaff')//资产管理-转移-接收人工号



            //新资产-资产盘点-相关rpc服务接口
            ->withClassAndMethod('get_msg_detail', MaterialInventoryCheck::class, 'getInventoryMsgDetail')//资产盘点-资产盘点通知消息详情
            ->withClassAndMethod('task_start', MaterialInventoryCheck::class, 'taskStart')//资产盘点-开始盘点/去盘点
            ->withClassAndMethod('get_task_count', MaterialInventoryCheck::class, 'getTaskCount')//资产盘点-任务清单-待处理、已处理总数
            ->withClassAndMethod('get_task_list', MaterialInventoryCheck::class, 'getTaskList')//资产盘点-任务清单-待处理、已处理列表
            ->withClassAndMethod('get_task_info', MaterialInventoryCheck::class, 'getTaskInfo')//资产盘点-任务详情
            ->withClassAndMethod('get_task_asset_count', MaterialInventoryCheck::class, 'getTaskAssetCount')//资产盘点-资产盘点清单-待盘点、已盘点总数
            ->withClassAndMethod('get_task_asset_list', MaterialInventoryCheck::class, 'getTaskAssetList')//资产盘点-资产盘点清单-待盘点、已盘点列表
            ->withClassAndMethod('task_asset_lose', MaterialInventoryCheck::class, 'taskAssetLose')//资产盘点-资产盘点清单-未盘到
            ->withClassAndMethod('task_asset_batch_lose', MaterialInventoryCheck::class, 'taskAssetBatchLose')//资产盘点-资产盘点清单-批量未盘到
            ->withClassAndMethod('task_asset_match', MaterialInventoryCheck::class, 'taskAssetMatch')//资产盘点-资产盘点清单-盘到
            ->withClassAndMethod('task_asset_batch_match', MaterialInventoryCheck::class, 'taskAssetBatchMatch')//资产盘点-资产盘点清单-批量盘到
            ->withClassAndMethod('task_asset_not_match', MaterialInventoryCheck::class, 'taskAssetNotMatch')//资产盘点-资产盘点清单-信息有误
            ->withClassAndMethod('task_asset_view_not_match', MaterialInventoryCheck::class, 'taskAssetViewNotMatch')//资产盘点-资产盘点清单-查看信息有误
            ->withClassAndMethod('task_asset_update_num', MaterialInventoryCheck::class, 'taskAssetUpdateNum')//资产盘点-资产盘点清单-修改数量
            ->withClassAndMethod('task_asset_barcode_search', MaterialInventoryCheck::class, 'taskAssetBarcodeSearch')//资产盘点-资产盘点清单-搜索资产
            ->withClassAndMethod('task_asset_add', MaterialInventoryCheck::class, 'taskAssetAdd')//资产盘点-资产盘点清单-添加资产
            ->withClassAndMethod('task_asset_del', MaterialInventoryCheck::class, 'taskAssetDel')//资产盘点-资产盘点清单-删除资产
            ->withClassAndMethod('task_asset_scan_code', MaterialInventoryCheck::class, 'taskAssetScanCode')//资产盘点-资产盘点清单-扫码盘点
            ->withClassAndMethod('task_asset_done', MaterialInventoryCheck::class, 'taskAssetDone')//资产盘点-资产盘点清单-确认无资产
            ->withClassAndMethod('task_asset_update', MaterialInventoryCheck::class, 'taskAssetUpdate')//资产盘点-资产盘点清单-更新盘点
            ->withClassAndMethod('task_check_punch_out', MaterialInventoryCheck::class, 'taskCheckPunchOut')//资产盘点-资产盘点清单-限制打卡

            //新资产-资产退回-相关rpc服务接口
            ->withClassAndMethod('asset_return_search_return_type', AssetReturn::class, 'searchReturnType')//资产退回-退回类型-筛选
            ->withClassAndMethod('asset_return_batch_return', AssetReturn::class, 'batchReturn')//资产退回-批量退回
            ->withClassAndMethod('asset_return_batch_return_cancel', AssetReturn::class, 'batchCancel')//资产退回-批量撤销退回
            ->withClassAndMethod('asset_return_list', AssetReturn::class, 'returnList')//资产管理-资产退回申请-列表
            ->withClassAndMethod('asset_return_detail', AssetReturn::class, 'returnDetail')//资产管理-资产退回申请-查看
            ->withClassAndMethod('asset_return_cancel', AssetReturn::class, 'returnCancel')//资产管理-资产退回申请-撤销
            ->withClassAndMethod('asset_return_add_asset_list', AssetReturn::class, 'returnAddAssetList')//资产管理-资产退回申请-创建-添加资产-列表

            ->withClassAndMethod('approval_callback', ByWorkflow::class, 'approvalCallback')//by终审异步回调
            ->withClassAndMethod('get_position_cost_list', JobService::class, 'getPositionCostListToRpc')//获取职位性质，成本类型枚举

            /** 移动端报销 **/
            ->withClassAndMethod('check_bt_is_reimbursed', BusinessTripCheck::class, 'checkBusinessTripIsReimbursed')//验证出差是否已经报销
            ->withClassAndMethod('get_reimbursement_apply_pending', BusinessTripCheck::class, 'getReimbursementApplyPendingNum')//获取员工报销待办数
            ->withClassAndMethod('get_reimbursement_enums', BusinessTripCheck::class, 'getReimbursementEnums')//获取报销列表筛选项枚举
            ->withClassAndMethod('get_reimbursement_list', BusinessTripCheck::class, 'getReimbursementList')//获取报销列表
            ->withClassAndMethod('reimbursement_signature_reminder', BusinessTripCheck::class, 'signatureReminder')//报销申请-签字提醒
            ->withClassAndMethod('get_reimbursement_confirmed_detail', BusinessTripCheck::class, 'getConfirmedDetail')//报销申请-确认详情
            ->withClassAndMethod('submit_reimbursement', BusinessTripCheck::class, 'submitReimbursement')//报销申请-提交
            ->withClassAndMethod('cancel_reimbursement', BusinessTripCheck::class, 'cancelReimbursement')//报销申请-撤回
            ->withClassAndMethod('download_reimbursement', BusinessTripCheck::class, 'downloadReimbursement')//报销申请-下载
            ->withClassAndMethod('view_reimbursement', BusinessTripCheck::class, 'viewDetail')//报销申请-查看/查看详情
            ->withClassAndMethod('go_confirm_reimbursement_info', BusinessTripCheck::class, 'goConfirmInfo')               //报销申请-去确认
            ->withClassAndMethod('reject_confirm_reimbursement_travel_roommate', BusinessTripCheck::class, 'rejectConfirm')//报销申请-去确认-未共同住宿
            ->withClassAndMethod('agree_confirm_reimbursement_travel_roommate', BusinessTripCheck::class, 'agreeConfirm')  //报销申请-去确认-确认无误
            ->withClassAndMethod('invalid_reimbursement', BusinessTripCheck::class, 'invalidReimbursement')                //报销申请-作废
            ->withClassAndMethod('go_sign_reimbursement_info', BusinessTripCheck::class, 'goSignInfo')//报销申请-去签字
            ->withClassAndMethod('reject_sign_reimbursement', BusinessTripCheck::class, 'rejectSign')//报销申请-去签字-拒绝签字
            ->withClassAndMethod('get_reimbursement_apply_info', BusinessTripCheck::class, 'getApplyIDInfo')//报销申请-去签字-签字提交页-获取申请人身份证信息
            ->withClassAndMethod('submit_sign_info_reimbursement', BusinessTripCheck::class, 'submitSignInfo')//报销申请-去签字-签字提交页-提交签字
            ->withClassAndMethod('check_sign_auth_reimbursement', BusinessTripCheck::class, 'checkSignAuth')//报销申请-二维码签字/去签字鉴权
            ->withClassAndMethod('check_confirm_auth_reimbursement', BusinessTripCheck::class, 'checkConfirmAuth')//报销申请-去确认鉴权


            //e-Invoice数据查询
            ->withClassAndMethod('get_invoice_uuid', InvoiceCheck::class, 'getInvoiceUuidByInternalNo')//获取电子发票的 uuid

            //纸质单据相关rpc服务接口
            ->withClassAndMethod('get_paper_doc_basic_info', PaperDocument::class, 'getPaperDocBasicInfo')//获取纸质单据基本信息
            ->withClassAndMethod('get_paper_doc_info', PaperDocument::class, 'getPaperDocDetailInfo')//获取纸质单据详细信息
            ->withClassAndMethod('batch_confirm_paper_document', PaperDocument::class, 'batchConfirm')//批量确认
            ->withClassAndMethod('confirm_paper_document', PaperDocument::class, 'singleConfirm')//单个提交
            ->withClassAndMethod('submit_paper_document', PaperDocument::class, 'singleSubmit')//批量确认
            ->withClassAndMethod('get_confirm_reason_type_list', PaperDocument::class, 'getConfirmReasonTypeList')//待补全原因
        ;
        echo  $server->execute();
    }
}
