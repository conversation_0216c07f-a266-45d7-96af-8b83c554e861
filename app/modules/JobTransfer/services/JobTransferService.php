<?php

namespace App\Modules\JobTransfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\JobTransferConfirmEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\JobTransferEnums;
use App\Library\OssHelper;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\JobTransferModel;
use App\Models\backyard\JobTransferSpecialModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\HrJobTitleRoleModel;
use App\Modules\AccidentReport\Models\HrStaffInfo;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Hc\Models\VehicleInfoModel;
use App\Modules\JobTransfer\Models\HrHcModel;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobTitleByModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentStoreRelationModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Models\HrStaffItemsModel;
use App\Traits\TokenTrait;
use App\Util\RedisKey;


class JobTransferService extends BaseService
{
    use TokenTrait;
    private static $instance;
    private $jobTransfer;
    private $sysDepartment;

    const NORMAL_TRANSFER_MIN_IMPORT_ROWS = 1; //转岗最少导入行数
    const NORMAL_TRANSFER_MAX_IMPORT_ROWS = 30; //转岗最多导入行数
    const SPECIAL_TRANSFER_MAX_IMPORT_ROWS = 1000; //特殊转岗最大导入行数
    const IMPORT_RESULT_COLUMN_ID = 7; //导入结果列ID

    public function __construct()
    {
        $this->jobTransfer = JobTransferModel::class;
        $this->sysDepartment = SysDepartmentModel::class;
    }

    /**
     * @return JobTransferService
     */
    public static function getInstance(): JobTransferService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 上传员工ID
     * @param $excel_data array 导入数据
     * @param $submitter_id int 提交人
     * @return array
     * @throws ValidationException
     */
    public function uploadApplyStaffIdsV2($excel_data, $submitter_id): array
    {
        //输入行数限制
        if (count($excel_data) < self::NORMAL_TRANSFER_MIN_IMPORT_ROWS) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'msg' => 'Excel error', 'data' => []];
        }

        if (count($excel_data) > self::NORMAL_TRANSFER_MAX_IMPORT_ROWS) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'msg' => static::$t->_('job_transfer_err.7'), 'data' => []]; // excel数据量超过40条，请重新上传
        }

        $successList = [];
        $failList    = [];

        //获取申请人管辖信息
        foreach ($excel_data as $value) {
            $staffId = $value[0];
            $params  = [
                'staff_id'     => $staffId,
            ];

            try {
                $result = DetailService::getInstance()->checkStaffInfo($params, $submitter_id);
                if ($result) {
                    $successList[] = DetailService::getInstance()->getStaffDetailInfo($params);
                }
            } catch (ValidationException $e) {
                $failList[] = [
                    'staff_id' => $staffId,
                    'fail_msg' => $e->getMessage() ?? '',
                ];
            }
        }

        return [
            'code' => ErrCode::$SUCCESS,
            'msg'  => 'success',
            'data' => [
                "success" => $successList,
                "fail"    => $failList,
            ],
        ];
    }

    /**
     * 获取各种数据
     * @param $list
     * @param $type
     * @return array
     */
    public function genBaseData($list, $type): array
    {
        // 获取部门信息
        $old_department  = array_column($list, 'after_department_id');
        $new_department  = array_column($list, 'current_department_id');
        $old_company     = array_column($list, 'after_company_id');
        $new_company     = array_column($list, 'current_company_id');
        $departmentIds   = array_values(array_unique(array_merge($old_department, $new_department, $old_company,
            $new_company)));
        $sys_department  = new SysDepartmentModel();
        $department_info = $sys_department->getDataByIds($departmentIds, 'id, name');

        // 获取职位信息
        $old_job  = array_column($list, 'after_position_id');
        $new_job  = array_column($list, 'current_position_id');
        $jobs     = array_unique(array_merge($old_job, $new_job));
        $job      = new HrJobTitleModel();
        $job_info = $job->getDataByIds($jobs);

        // 获取员工信息
        $staff_ids = array_column($list, 'staff_id');
        // 当前直线上级
        $manager = array_column($list, 'current_manager_id');
        // 当前虚线上级
        $indirect_manager = array_column($list, 'current_indirect_manger_id');
        // 转岗后直线上级
        $after_manager_id = array_column($list, 'after_manager_id');
        //申请人
        $submitter_id = array_values(array_unique(array_column($list, 'submitter_id')));
        $all_staff_ids    = array_values(array_unique(array_merge($staff_ids, $manager, $indirect_manager, $after_manager_id, $submitter_id)));
        $staff            = HrStaffInfoModel::find([
            "conditions" => "staff_info_id IN ({ids:array})",
            "columns"    => "emp_id, staff_info_id, name, name_en, sex, personal_email, identity, mobile, state, sys_store_id,hire_date,sys_department_id,contract_company_id",
            "bind"       => ["ids" => array_values($all_staff_ids)],
        ])->toArray();
        $user_info        = array_column($staff, null, 'staff_info_id');

        if ($type == Enums::FETCH_DATA_LIST) {
            return [$department_info, $job_info, $user_info];
        }

        // 网点信息
        $old_store        = array_column($list, 'current_store_id');
        $new_store        = array_column($list, 'after_store_id');
        $store_ids        = array_values(array_unique(array_merge($old_store, $new_store)));
        $store            = new SysStoreModel();
        $store_info       = $store->getDataByIds($store_ids, 'id, name,manage_region,manage_piece');
        $store_info['-1'] = [
            'id'            => "-1",
            'name'          => 'Head Office',
            'manage_region' => '-1',
            'manage_piece'  => '-1',
        ];


        // 大区
        $regionIds = array_values(array_merge(array_column($list, 'current_region_id'), array_column($list, 'after_region_id')));
        $regions   = SysManageRegionModel::find([
            'columns'    => 'id, name',
            'conditions' => ' id in ({ids:array}) ',
            'bind'       => ['ids' => $regionIds],
        ])->toArray();
        $regions   = array_column($regions, 'name', 'id');

        // 片区
        $pieceIds = array_values(array_merge(array_column($list, 'current_piece_id'), array_column($list, 'after_piece_id')));
        $pieces   = SysManagePieceModel::find([
            'columns'    => 'id, name',
            'conditions' => ' id in ({ids:array}) ',
            'bind'       => ['ids' => $pieceIds],
        ])->toArray();
        $pieces   = array_column($pieces, 'name', 'id');

        //HC信息
        $hcIds = array_column($list, 'hc_id');
        $hcIds = array_unique($hcIds);
        $hc = new HrHcModel();
        $hc_info = $hc->getDataByIds($hcIds, 'hc_id, expirationdate', 'hc_id');

        if ($type == Enums::FETCH_DATA_EXPORT) {
            return [$department_info, $job_info, $user_info, $store_info, $hc_info,$manager,$regions,$pieces];
        }

        // 获取角色信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id, GROUP_CONCAT(position_category) position_category');
        $builder->from(['s' => HrStaffInfoPositionModel::class]);
        $builder->inWhere('staff_info_id', $staff_ids);
        $builder->groupBy('staff_info_id');
        $roles = $builder->getQuery()->execute()->toArray();
        $roles = array_column($roles, 'position_category', 'staff_info_id');

        // 获取到所有已转岗的员工
        $done_ids = array();
        foreach ($list as $item) {
            if ($item['state'] == Enums::JOB_TRANSFER_STATE_SUCCESS) {
                $done_ids[] = $item['staff_id'];
            }
        }

        $manager = array();
        if ($done_ids) {

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('b.staff_info_id staff_id, a.value manager_staff_id, b.name');
            $builder->from(['a' => HrStaffItemsModel::class]);
            $builder->innerJoin(HrStaffInfoModel::class, 'a.value = b.staff_info_id', 'b');
            $builder->inWhere('a.staff_info_id', $done_ids);
            $builder->andWhere('a.item = :item:', ['item' => 'INDIRECT_MANGER']);
            $manager = $builder->getQuery()->execute()->toArray();
            $manager = array_column($manager, null, 'staff_id');
        }

        return [$department_info, $job_info, $user_info, $store_info, $roles, $manager, $regions, $pieces];
    }

    /**
     * 立即转岗
     * @param array $paramIn
     * @param $staff_id
     * @return array|false
     */
    public function doTransfer($paramIn = [], $staff_id)
    {
        //禁止频繁使用立即转岗
        if($this->checkLock(RedisKey::JOB_TRANSFER_DO_TRANSFER_LOCK.$paramIn['id'])){
            return ['code' => ErrCode::$SYSTEM_ERROR,'message' => self::$t->_('job_transfer_err.8')];
        } else {
            $this->setLock(RedisKey::JOB_TRANSFER_DO_TRANSFER_LOCK.$paramIn['id'],1,3);
        }

        $jobTransferId = $paramIn['id'] ?? 0;
        $staffId = $staff_id;
        if (empty($jobTransferId)) {
            return false;
        }

        //立即转岗
        $rmq = new RocketMQ('do-job-transfer');
        $params = [
            'transfer_id' => $jobTransferId,
            'operator_id' => $staffId,
        ];

        return $rmq->sendToMsg($params);
    }

    /**
     * 立即转岗
     * @param array $paramIn
     * @param $staff_id
     * @return bool
     * @throws ValidationException
     */
    public function activate($paramIn = [], $staff_id)
    {
        $jobTransferId = $paramIn['id'] ?? 0;
        if (empty($jobTransferId)) {
            return false;
        }
        $model = JobTransferModel::findFirst($jobTransferId);
        if (empty($model)) {
            throw new ValidationException('data not exist');
        }
        if (!ListService::getInstance()->isShowActivate($model->toArray())) {
            throw new ValidationException(static::$t->_('refresh_and_retry'));
        }

        //校验是否存在新申请(排除本身这条数据)
        $checkIsExistJobTransfer = JobTransferModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and state = 1 and approval_state in(1,2) and id != :job_transfer_id:',
            'bind' => [
                'staff_id'        => $model->staff_id,
                'job_transfer_id' => $jobTransferId,
            ],
        ]);
        if (!empty($checkIsExistJobTransfer)) { // 无需操作激活确认单，被转岗人已经存在新的转岗申请
            throw new ValidationException(static::$t->_('err_msg_exist_new_request'));
        }
        $params = [
            'id'          => $jobTransferId,
            'operator_id' => $staff_id,
        ];
        $this->logger->info('activate----参数：' . json_encode($params));
        $ac = new ApiClient('by', '', 'activate_job_transfer', static::$language);
        $ac->setParams([$params]);
        $res = $ac->execute();
        $this->logger->info('activate----结果' . json_encode($res));

        if ($res['result']['code'] != ErrCode::$SUCCESS) {
            throw new BusinessException($res['result']['msg']);
        }
        return true;

    }

    /**
     * 获取导入结果excel Header头
     * @param $lang
     * @return array
     */
    public function getExportExcelHeaderFields($lang = null): array
    {
        if (empty($lang)) {
            return [
                //转岗员工工号
                static::$t->_('job_transfer.staff_info_id'),
                //转岗后所属部门ID
                static::$t->_('job_transfer.after_department_id'),
                //转岗后职位ID
                static::$t->_('job_transfer.after_job_title_id'),
                //转岗后网点ID
                static::$t->_('job_transfer.after_store_id'),
                //期望转岗日期
                static::$t->_('job_transfer.after_date'),
                //转岗原因
                static::$t->_('job_transfer.transfer_reason'),
                 //转岗后直线上级
                static::$t->_('job_transfer.26'),
            ];
        } else {
            $tmp = BaseService::getTranslation($lang);
            return [
                //转岗员工工号
                $tmp['job_transfer.staff_info_id'],
                //转岗后所属部门ID
                $tmp['job_transfer.after_department_id'],
                //转岗后职位ID
                $tmp['job_transfer.after_job_title_id'],
                //转岗后网点ID
                $tmp['job_transfer.after_store_id'],
                //期望转岗日期
                $tmp['job_transfer.after_date'],
                //转岗原因
                $tmp['job_transfer.transfer_reason'],
                //转岗后直线上级
                $tmp['job_transfer.26'],
            ];
        }
    }

    /**
     * 批量创建特殊转岗
     * @param $excel_data
     * @param $user_info
     * @param $import_task
     * @return array
     * @throws ValidationException
     */
    public function batchCreateSpecialJobTransfer($excel_data, $user_info, $import_task): array
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = $real_trace = '';

        //excel数据转data
        foreach ($excel_data as $index => $value) {
            if (empty($value) || empty($value[0])) {
                unset($excel_data[$index]);
                continue;
            }
            foreach ($value as $key => $v) {
                if ($key == 4) {
                    $excel_data[$index][$key] = is_numeric($v) && $v > *********
                        ? date('Y-m-d', $v)
                        : $v;
                } else if (is_string($v)) {
                    $excel_data[$index][$key] = trim($v);
                }
            }
        }

        //验证条数
        if (count($excel_data) > self::SPECIAL_TRANSFER_MAX_IMPORT_ROWS) {
            throw new ValidationException(self::$t['bank_flow_data_gt_1000']);
        }
        $excel_data = array_values($excel_data);
        $data       = $this->excelDataToArray($excel_data);

        //数据校验
        [$correct_data, $error_data] = $this->validationImportData($data, $user_info, $import_task->language);

        //$correct_data和$error_data的key必须和$result_data对齐
        if (!empty($correct_data) || !empty($error_data)) {
            $t = BaseService::getTranslation($import_task->language);
            foreach ($correct_data as $index => $cv) {
                if (isset($cv['error_message'])) {
                    $excel_data[$index][$this->getMaxColumnNum()] = $cv['error_message'];
                }
            }
            foreach ($error_data as $index => $cv) {
                if (isset($cv['error_message'])) {
                    $excel_data[$index][$this->getMaxColumnNum()] = sprintf('%s,%s', $t->_('import_err'), $cv['error_message']);
                }
            }
        }
        if (count(array_column($error_data, 'error_message')) > 0) { // 存在错误的数据
            return [
                'code'    => $code,
                'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                'data'    => [
                    'excel_data'  => $excel_data,
                    'all_num'     => count($excel_data),
                    'success_num' => 0,
                    'failed_num'  => count($error_data),
                ],
            ];
        }

        try {
            //获取批次码
            $batchCode = $this->getTokenId();

            //组织转岗数据，生成excel
            $transferData = array_column($correct_data, 'data');
            $transferData = $this->perfectTransferData($transferData, $batchCode);
            $staffInfoUrl = $this->generateTransferExcel($transferData);

            //生成审批
            $ac = new ApiClient('by', '', 'create_special_job_transfer', static::$language);
            $requestParams = [
                'submitter_id'           => $user_info['id'],
                'upload_staff_info_file' => $staffInfoUrl,
                'batch_code'             => $batchCode,
                'after_date'             => $transferData[0]['after_date'],
            ];
            $ac->setParams([$requestParams]);
            $res = $ac->execute();
            $this->logger->info(sprintf('create_special_job_transfer params:%s,result:%s', json_encode($requestParams) , json_encode($res)));
            if ($res['result']['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException($res['result']['msg']);
            }

            //批量添加申请
            $rmq = new RocketMQ('create-approval');
            foreach ($transferData as $item) {

                $params = [
                    'apply_timestamp' => time(),
                    'apply_user'      => $user_info['id'],
                    'apply_type'      => Enums\ByWorkflowEnums::BY_BIZ_TYPE_JT_SPECIAL,
                    'apply_data'      => $item,
                    'apply_uuid'      => $this->getTokenId(),
                ];
                $rid = $rmq->sendToMsg($params);
               $this->logger->info(sprintf('batchCreateSpecialJobTransfer:%s, params:%s', $rid, json_encode($params)));
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('batchCreateSpecialJobTransfer error:' . $real_message . json_encode($real_trace));
            return [
                'code'    => $code,
                'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                'data'    => [
                    'excel_data'  => $excel_data,
                    'all_num'     => count($excel_data),
                    'success_num' => 0,
                    'failed_num'  => count($error_data),
                ],
            ];
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [
                'excel_data'  => $excel_data,
                'all_num'     => count($excel_data),
                'success_num' => count(array_column($correct_data, 'data')),
                'failed_num'  => count(array_column($error_data, 'error_message')),
            ],
        ];
    }


    /**
     * 生成批量导入转岗员工信息excel
     * @param $data
     * @return string
     */
    private function generateTransferExcel($data): string
    {
        $tmp = BaseService::getTranslation('en');
        $excelHeader = [
            //转岗员工工号
            $tmp['job_transfer.staff_info_id'],
            //转岗员工姓名
            $tmp['job_transfer.staff_name'],
            //转岗前所属部门
            $tmp['job_transfer.11'],
            //转岗前大区
            $tmp['job_transfer.40'],
            //转岗前片区
            $tmp['job_transfer.41'],
            //转岗前所属网点
            $tmp['job_transfer.14'],
            //转岗前职位
            $tmp['job_transfer.13'],
            //转岗前雇佣类型
            $tmp['job_transfer.before_hire_type'],
            //转岗后所属部门
            $tmp['job_transfer.22'],
            //转岗后所属网点
            $tmp['job_transfer.30'],
            //转岗后职位
            $tmp['job_transfer.28'],
            //转岗后雇佣类型
            $tmp['job_transfer.after_hire_type'],
            //期望转岗日期
            $tmp['job_transfer.after_date'],
            //转岗原因
            $tmp['job_transfer.transfer_reason'],
        ];
        //员工信息
        $staffInfo = ListService::getInstance()->translateColumn($data);

        $excelData = [];
        foreach ($staffInfo as $k => $v) {
            $excelData[] = [
                //转岗员工工号
                $v['staff_id'],
                //转岗员工姓名
                $v['staff_name'],
                //转岗前所属部门
                $v['current_department_name'],
                //转岗前大区
                $v['current_region_name'],
                //转岗前片区
                $v['current_piece_name'],
                //转岗前所属网点
                $v['current_store_name'],
                //转岗前职位
                $v['current_position_name'],
                //转岗前雇佣类型
                $v['current_hire_type_name'],
                //转岗后所属部门
                $v['after_department_name'],
                //转岗后所属网点
                $v['after_store_name'],
                //转岗后职位
                $v['after_position_name'],
                //转岗后雇佣类型（转岗前后一致）
                $v['current_hire_type_name'],
                //期望转岗日期
                $v['after_date'],
                //转岗原因
                $tmp['job_transfer_reason.' . $v['transfer_reason_id']],
            ];
        }

        // 2.7生成Excel
        $excel_extra_config = [
            'file_name'       => date('YmdHis') . '.xlsx',
            'end_column_char' => 'L',
            'column_width'    => 25,
        ];
        $path               = self::customizeExcelToFile($excelHeader, $excelData, $excel_extra_config);
        $file               = OssHelper::uploadFile($path);
        return $file['object_url'];
    }

    /**
     * @description 获取重复的工号
     * @param $data
     * @return int[]|string[]
     */
    private function getRepeatStaffInfoId($data)
    {
        $count      = []; // 存储每个工号的出现次数
        $duplicates = []; // 存储重复的工号

        // 遍历二维数组
        foreach ($data as $subArray) {
            if (!empty($subArray)) {
                $staffInfoId = $subArray;

                // 计数工号出现次数
                if (!isset($count[$staffInfoId])) {
                    $count[$staffInfoId] = 0;
                }
                $count[$staffInfoId]++;

                // 如果一个工号出现超过一次，将其添加到重复工号数组中
                if ($count[$staffInfoId] > 1) {
                    $duplicates[$staffInfoId] = $count[$staffInfoId];
                }
            }
        }

        return array_keys($duplicates); // 返回重复工号的数组
    }

    /**
     * 获取部门、职位关联角色
     * @return array
     */
    private function getDepartmentJobTitleRelateRoles()
    {
        $rolesList =  HrJobTitleRoleModel::find([
            'columns' => 'concat(sys_depeartment_id, "-", job_title_id) as unique_key,group_concat(role_id) as role_ids',
            'group' => 'sys_depeartment_id,job_title_id'
        ])->toArray();
        if (empty($rolesList)) {
            return [];
        }
        foreach ($rolesList as &$item) {
            if (!empty($item['role_ids'])) {
                $item['role_ids'] = explode(',', $item['role_ids']);
            }
        }
        return $rolesList;
    }

    /**
     * 校验导入数据
     * @param $data
     * @param $user_info
     * @return array[]
     * @throws ValidationException
     */
    private function validationImportData($data, $user_info, $language): array
    {
        $departmentIds   = array_column($data, 'after_department_id');
        $jobIds          = array_column($data, 'after_position_id');
        $storeIds        = array_column($data, 'after_store_id');
        $staffInfoIds    = array_column($data, 'staff_id');
        $managerIds      = array_column($data, 'after_manager_id');
        $allStaffInfoIds = array_merge($staffInfoIds, $managerIds);

        if (empty($departmentIds) || empty($jobIds) || empty($storeIds)) {
            throw new ValidationException('no valid data');
        }
        $correct_data = $error_data = $hrJobDepartmentRelateInfo = [];
        $t = BaseService::getTranslation($language);

        $departmentInfo = SysDepartmentModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $departmentIds],
            'columns'    => "id,deleted,company_id,ancestry_v3",
        ])->toArray();
        $departmentInfoArr = array_column($departmentInfo, 'id');
        $departmentCompanyInfo = array_column($departmentInfo, 'company_id','id');
        $departmentLevel = array_column($departmentInfo, 'ancestry_v3','id');
        $departmentInfo = array_column($departmentInfo, 'deleted','id');

        $jobInfo = HrJobTitleByModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $jobIds],
            'columns'    => "id,status",
        ])->toArray();
        $jobInfoArr = array_column($jobInfo, 'id');
        $jobInfo = array_column($jobInfo, 'status','id');

        $storeInfo = SysStoreModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $storeIds],
            'columns'    => "id,state",
        ])->toArray();
        $storeInfoArr = array_column($storeInfo, 'id');
        $storeInfo = array_column($storeInfo, 'state','id');

        $manageStaffInfo = HrStaffInfo::find([
            'conditions' => 'staff_info_id IN ({staff_info_id:array})',
            'bind'       => ['staff_info_id' => $managerIds],
            'columns'    => "staff_info_id",
        ])->toArray();
        $manageStaffInfo = array_column($manageStaffInfo, 'staff_info_id');

        $staffInfo           = HrStaffInfo::find([
            'conditions' => 'staff_info_id IN ({staff_info_id:array})',
            'bind'       => ['staff_info_id' => $allStaffInfoIds],
            'columns'    => "staff_info_id,manger,sys_department_id,job_title,state,wait_leave_state,node_department_id,rest_type,week_working_day,hire_type,sys_store_id",
        ])->toArray();
        $staffInfoJobTitleId = array_column($staffInfo, 'job_title', 'staff_info_id');
        $staffInfoManagerId  = array_column($staffInfo, 'manger', 'staff_info_id');
        $staffInfoDepartment = array_column($staffInfo, 'sys_department_id', 'staff_info_id');
        $staffInfo           = array_column($staffInfo, null, 'staff_info_id');

        //部门、职位关联关系
        if (!empty($departmentInfoArr) && !empty($jobInfoArr)) {
            $hrJobDepartmentRelateInfo = HrJobDepartmentRelationModel::find([
                'conditions' => 'department_id IN ({department_ids:array}) and job_id IN({job_ids:array})',
                'bind'       => ['department_ids' => $departmentIds, 'job_ids' => $jobIds],
                'columns'    => "concat(department_id,'-',job_id) unique_key,department_id,job_id,working_day_rest_type",
            ])->toArray();
            $hrJobDepartmentRelateInfo = array_column($hrJobDepartmentRelateInfo, null,'unique_key');
        }

        //网点对应的大区、片区
        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'store_id'       => $storeIds,
        ];
        $storeRelateInfo =  HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and store_id in({store_id:array})',
            'bind'       => $bind,
        ])->toArray();
        $storeRelateInfo = array_column($storeRelateInfo, null,'store_id');

        //可用转岗原因
        $validJobTransferReason = [
            JobTransferModel::JOB_TRANSFER_REASON_STORE_INTEGRATION,
            JobTransferModel::JOB_TRANSFER_REASON_STORE_NEWLY_OPENED,
            JobTransferModel::JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA,
            JobTransferModel::JOB_TRANSFER_REASON_STORE_UPGRADE,
        ];

        //重复的工号
        $duplicateStaffIds = $this->getRepeatStaffInfoId($staffInfoIds);

        //部门、职位关联角色
        $roles = $this->getDepartmentJobTitleRelateRoles();
        $roles = array_column($roles, 'role_ids', 'unique_key');

        //批量校验转岗申请
        $ac = new ApiClient('by', '', 'check_batch_apply', $language);
        $ac->setParams([
            [
                'data'         => $data,
                'submitter_id' => $user_info['id'],
                'type'         => JobTransferModel::TRANSFER_TYPE_SPECIAL,
            ],
        ]);
        $res = $ac->execute();

        if (!empty($res['result']) ) {

            $errReason = array_column($res['result'], 'reason','staff_id');
            foreach ($data as $key => $value) {

                if (!empty($errReason[$value['staff_id']]) ) {
                    $error_data[$key]['error_message'] = $errReason[$value['staff_id']];
                }
            }
        }
        $countryCode = get_country_code();
        $individualContractorJobTitleConfig = (new SettingEnvModel())->getSetVal('individual_contractor_job_title', ',');

        //校验薪资是否发生变化
        $countryCode = get_country_code();
        $ac_salary   = new ApiClient('hcm_rpc', '', 'get_special_batch_transfer_salary', $language);

        //车类型
        $vehicleInfo = VehicleInfoModel::find([
            'conditions' => 'uid in({staff_info_id:array})',
            'bind' => [
                'staff_info_id' => $allStaffInfoIds
            ],
            'columns' => 'uid,vehicle_type_category',
        ])->toArray();
        $vehicleInfo = array_column($vehicleInfo, 'vehicle_type_category', 'uid');

        foreach ($data as $key => $value) {

            $correct_data[$key]['staff_id'] = $error_data[$key]['staff_id'] = $value['staff_id'];

            //校验转岗后所属部门ID是否存在，不存在则提示：“导入失败，转岗后所属部门ID不存在”
            if (empty($value['after_department_id'])) {
                $error_data[$key]['error_message'] = $t->_('error_message_department_not_exist'); //转岗后所属部门ID不存在
                continue;
            }

            //校验转岗后职位ID是否存在，不存在则提示：“导入失败，转岗后职位ID不存在”
            if (empty($value['after_position_id'])) {
                $error_data[$key]['error_message'] = $t->_('error_message_job_title_not_exist'); //转岗后职位ID不存在
                continue;
            }

            //校验转岗后网点ID是否存在，不存在则提示：“导入失败，转岗后网点ID不存在”
            if (empty($value['after_store_id'])) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_store'); //转岗后网点ID不存在
                continue;
            }

            if (empty($value['after_manager_id'])) {
                $error_data[$key]['error_message'] = $t->_('error_message_manager_not_exist'); //转岗后上级ID不存在
                continue;
            }

            if (!is_numeric($value['after_department_id']) ||
                !is_numeric($value['after_position_id']) ||
                !is_string($value['after_store_id']) ||
                !is_numeric($value['after_manager_id'])
            ) {
                $error_data[$key]['error_message'] = $t->_('error_message_check_value_type'); //请检查输入数据类型
                continue;
            }

            //部门ID是否有效
            if (!in_array($value['after_department_id'], $departmentInfoArr)) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_department'); //不存在的部门ID
                continue;
            } else {

                //存在部门ID，但是部门已删除
                if (isset($departmentInfo[$value['after_department_id']]) &&
                    $departmentInfo[$value['after_department_id']] == GlobalEnums::IS_DELETED) {
                    $error_data[$key]['error_message'] = $t->_('error_message_department_invalid'); //部门ID已删除
                    continue;
                }
            }

            //职位ID是否有效
            if (!in_array($value['after_position_id'], $jobInfoArr)) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_job_title'); //不存在职位ID
                continue;
            } else {

                //存在职位ID，但是职位已失效
                if (isset($jobInfo[$value['after_position_id']]) &&
                    $jobInfo[$value['after_position_id']] == HrJobTitleByModel::STATUS_NOT_VALID) {
                    $error_data[$key]['error_message'] = $t->_('error_message_job_title_invalid'); //职位ID已失效
                    continue;
                }

                if (isset($staffInfoJobTitleId[$value['staff_id']]) &&
                    $staffInfoJobTitleId[$value['staff_id']] != $value['after_position_id']) {
                    $error_data[$key]['error_message'] = $t->_('error_message_job_title_not_same'); //转岗前后职位不一致
                    continue;
                }
            }

            //网点ID是否有效
            if (!in_array($value['after_store_id'], $storeInfoArr)) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_store'); //不存在网点ID
                continue;
            } else {

                //存在网点ID，但是网点已失效
                if (isset($storeInfo[$value['after_store_id']]) &&
                    $storeInfo[$value['after_store_id']] == HrJobTitleByModel::STATUS_NOT_VALID) {
                    $error_data[$key]['error_message'] = $t->_('error_message_store_invalid'); //网点ID已失效
                    continue;
                }
            }

            //转岗后上级
            if (!in_array($value['after_manager_id'], $manageStaffInfo)) {
                $error_data[$key]['error_message'] = $t->_('error_message_manager_not_exist');
                continue;
            } else {

                //存在网点ID，但是网点已失效
                if (isset($staffInfo[$value['after_manager_id']]) &&
                    (
                        in_array($staffInfo[$value['after_manager_id']]['state'], [StaffInfoEnums::STAFF_STATE_LEAVE, StaffInfoEnums::STAFF_STATE_STOP])
                    )) {
                    $error_data[$key]['error_message'] = $t->_('error_message_manager_has_resigned'); //网点ID已失效
                    continue;
                }
            }

            //已经存在同样的申请数据
            if (in_array($value['staff_id'], $duplicateStaffIds)) {
                $error_data[$key]['error_message'] = $t->_('error_message_staff_repeat');
                continue;
            }

            if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {

                //不支持个人代理员工转到所选择的职位
                if (isset($staffInfo[$value['staff_id']]['hire_type']) &&
                    in_array($staffInfo[$value['staff_id']]['hire_type'], [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT]) &&
                    !in_array($value['after_position_id'], $individualContractorJobTitleConfig)
                ) {
                    $error_data[$key]['error_message'] = $t->_('error_message_agent_job_title_not_support');
                    continue;
                }
            }

            //不存在指定部门、职位的关联关系
            //部门与职位关联表唯一健值，格式: 部门-职位
            $departmentJobRelationKey = sprintf("%d-%d", $value['after_department_id'], $value['after_position_id']);
            if (!isset($hrJobDepartmentRelateInfo) || !in_array($departmentJobRelationKey, array_keys($hrJobDepartmentRelateInfo))) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_relate');
                continue;
            }

            if (empty($staffInfo[$value['staff_id']])) {
                continue;
            }
            $currentStaffInfo = $staffInfo[$value['staff_id']];

            //转岗后工作天数与轮休规则可选项与转岗前不匹配
            $currentRelation = HrJobDepartmentRelationModel::findFirst([
                'conditions' => 'department_id = :department_id: and job_id = :job_title_id:',
                'bind'       => ['department_id' => $currentStaffInfo['node_department_id'], 'job_title_id' => $currentStaffInfo['job_title']],
                'columns'    => "working_day_rest_type",
            ]);
            if (empty($currentRelation)) {
                $error_data[$key]['error_message'] = $t->_('error_message_not_exist_relate');
                continue;
            }
            $afterWorkingDayRestType = !empty($hrJobDepartmentRelateInfo[$departmentJobRelationKey]['working_day_rest_type'])
                ? explode(',', $hrJobDepartmentRelateInfo[$departmentJobRelationKey]['working_day_rest_type'])
                : [];
            if (!in_array($currentStaffInfo['week_working_day'] . $currentStaffInfo['rest_type'] , $afterWorkingDayRestType)) {
                $error_data[$key]['error_message'] = $t->_('error_message_job_department_relate_not_match');
                continue;
            }

            //校验转岗后的网点是否在转岗后的所属部门下
            $isStoreBelongDepartment = ManageOrganizationService::getInstance()->isStoreBelongDepartment($value['after_department_id'], $value['after_store_id']);
            if ($isStoreBelongDepartment === false) {
                $error_data[$key]['error_message'] = $t->_('error_message_store_not_belong_department');
                continue;
            }

            //被转岗人转岗前后的部门是否隶属相同的一级部门,仅支持转岗前后隶属同一一级部门的场景
            $afterDepartmentLevel = $departmentLevel[$value['after_department_id']] ?? '';
            $departmentLevelArr = explode('/', $afterDepartmentLevel);

            if (!in_array($staffInfoDepartment[$value['staff_id']], $departmentLevelArr)) {
                $error_data[$key]['error_message'] = $t->_('error_message_level_1_department_not_same');
                continue;
            }

            //校验转岗原因是否是提供的可选项内的原因
            if (!in_array($value['transfer_reason_id'], $validJobTransferReason)) { //转岗原因只能按照模板选择
                $error_data[$key]['error_message'] = $t->_('error_message_transfer_reason_not_exist');
                continue;
            }

            //唯一key
            $unique = sprintf('%s-%s', $value['after_department_id'], $value['after_position_id']);

            //员工角色
            $staffInfoPositions = HrStaffInfoPositionModel::findByStaffInfoId($value['staff_id'])->toArray();
            $staffInfoPositions = array_column($staffInfoPositions, 'position_category');
            if (empty($staffInfoPositions)) {
                continue;
            }
            if (empty($roles[$unique])) { //转岗后部门、职位没有绑定的角色
                $error_data[$key]['error_message'] = $t->_('error_message_roles_not_match');
                continue;
            }

            $diff = array_diff($staffInfoPositions, array_intersect($staffInfoPositions, $roles[$unique]));
            if (!empty($diff)) {
                $error_data[$key]['error_message'] = $t->_('error_message_roles_not_match');
                continue;
            }

            if ($countryCode == GlobalEnums::MY_COUNTRY_CODE) {

                if (isset($staffInfo[$value['staff_id']]['hire_type']) &&
                    in_array($staffInfo[$value['staff_id']]['hire_type'], [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT])
                ) {
                    continue;
                }

                //校验薪资是否变化
                $ac_salary->setParams([
                    [
                        'staff_id'            => $value['staff_id'],
                        'current_position_id' => $staffInfoJobTitleId[$value['staff_id']] ?? '',
                        'after_position_id'   => $value['after_position_id'],
                        'current_store_id'    => $currentStaffInfo['sys_store_id'],
                        'after_store_id'      => $value['after_store_id'],
                        'after_date'          => $value['after_date'],
                        'after_car_type'      => $vehicleInfo[$value['staff_id']] ?? null,
                    ],
                ]);
                $ac_salary_info = $ac_salary->execute();
                if (empty($ac_salary_info['result']) ) {
                    $error_data[$key]['error_message'] = $t->_('error_message_cannot_get_salary_info');
                    continue;
                }
                if (!isset($ac_salary_info['result']['data']['before_base_salary'], $ac_salary_info['result']['data']['after_base_salary'])) {
                    $error_data[$key]['error_message'] = $t->_('error_message_salary_info_not_exists');
                    continue;
                }

                if ($ac_salary_info['result']['data']['before_base_salary'] != $ac_salary_info['result']['data']['after_base_salary']) {
                    $error_data[$key]['error_message'] = $t->_('error_message_salary_has_changed');
                }
            }
        }

        foreach ($data as $key => $value) {
            if (empty($error_data[$key]['error_message'])) {
                $value['submitter_id']     = $user_info['id'];
                $value['after_piece_id']   = $storeRelateInfo[$value['after_store_id']]['piece_id'];
                $value['after_region_id']  = $storeRelateInfo[$value['after_store_id']]['region_id'];
                $value['after_company_id'] = $departmentCompanyInfo[$value['after_department_id']];
                $value['current_manager_id']= $staffInfoManagerId[$value['staff_id']];

                $correct_data[$key]['data'] = $value;
                $correct_data[$key]['error_message'] = $t->_('excel_result_pass');
            }
        }

        return [
            $correct_data,
            $error_data,
        ];
    }

    /**
     * 导入excel数据转换为数组
     * @param $excel_data
     * @return array
     * @throws ValidationException
     */
    public function excelDataToArray($excel_data): array
    {
        //excel转字段
        $data_key = [
            0 => 'staff_id',                    //转岗员工工号
            1 => 'after_department_id',         //转岗后所属部门ID
            2 => 'after_position_id',           //转岗后职位ID
            3 => 'after_store_id',              //转岗后网点ID
            4 => 'after_date',                  //期望转岗日期
            5 => 'transfer_reason',             //转岗原因
            6 => 'after_manager_id',            //转岗后直线上级
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'],
                        ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        $reasonList = SysService::getInstance()->getTransferReasonList();
        foreach ($data as &$value) {
            $value['transfer_reason_id'] = array_search($value['transfer_reason'], $reasonList);
        }
        return $data;
    }

    public function getMaxColumnNum(): int
    {
        return self::IMPORT_RESULT_COLUMN_ID;
    }

    /**
     * 完善转岗信息
     * @param $data
     * @param $batch_code
     * @return array
     */
    private function perfectTransferData($data, $batch_code): array
    {
        $countryCode = get_country_code();
        foreach ($data as &$item) {
            try {
                $staffInfo                     = DetailService::getInstance()->getStaffDetailInfo($item);
                $item['type']                  = JobTransferModel::TRANSFER_TYPE_SPECIAL;
                $item['current_department_id'] = $staffInfo['node_department_id'];
                $item['current_store_id']      = $staffInfo['sys_store_id'];
                $item['current_position_id']   = $staffInfo['job_title'];
                $item['current_piece_id']      = $staffInfo['piece_id'];
                $item['current_region_id']     = $staffInfo['region_id'];
                $item['current_company_id']    = $staffInfo['company_id'];
                $item['current_role_id']       = $staffInfo['current_role_id'];
                $item['current_job_title_grade']= $staffInfo['current_job_title_grade'];
                $item['approval_state']        = Enums\ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT;
                $item['batch_code']            = $batch_code;
                $item['car_owner'] = $item['vehicle_source'] = $staffInfo['vehicle_source_code'];
                $item['project_num']           = $staffInfo['project_num'];
                $item['rental_car_cteated_at'] = $item['current_rental_car_created_at'] = $staffInfo['vehicle_start_date'];
                $item['before_working_day_rest_type'] = $staffInfo['before_working_day_rest_type'];
                $item['state']                 = JobTransferModel::JOB_TRANSFER_STATE_TO_BE_TRANSFERRED;
                $item['confirm_state']         = JobTransferConfirmEnums::CONFIRM_STATE_INVALID; //等转岗完毕才待确认
                if ($countryCode == GlobalEnums::MY_COUNTRY_CODE) {
                    $item['current_car_type']   = $staffInfo['current_vehicle_type_category'] ?? 0;
                }
                $item['current_hire_type']      = $staffInfo['hire_type'];
                $item['current_hire_type_name'] = $staffInfo['hire_type_title'];
                $item['current_hire_times']     = $staffInfo['hire_times'];
            } catch (ValidationException $ve) {
                $this->logger->warning('perfectTransferData err:' . $ve->getMessage() . $ve->getTraceAsString());
            }
        }
        return $data;
    }
}