<?php

namespace App\Modules\JobTransfer\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\JobTransfer\Services\AuditService;
use App\Modules\JobTransfer\Services\DetailService;
use App\Modules\JobTransfer\Services\JobTransferService;
use App\Modules\JobTransfer\Services\ListService;
use App\Modules\JobTransfer\Services\SysService;

class ApplyController extends BaseController
{
    /**
     * @description 转岗管理-转岗申请-查询详情
     * @Token
     * @throws \Exception
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getJobTransferApplyDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * @description 转岗管理-转岗申请-下载一线职位清单
     * @Token
     * @throws \Exception
     */
    public function downloadFrontLineJobListAction()
    {
        $returnArr = SysService::getInstance()->downloadFrontLineJobList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['path' => $returnArr]);
    }

    /**
     * 转岗管理-转岗申请-上传excel批量获取员工信息
     * @Token
     */
    public function uploadApplyAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        if (empty($excel_file)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Excel File Empty', []);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(0)
            ->getSheetData();

        try {
            $res = JobTransferService::getInstance()->uploadApplyStaffIdsV2($excel_data, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['msg'], $res['data']);
    }

    /**
     * 转岗管理-转岗申请-获取员工信息（单个）
     * @Token
     */
    public function searchStaffDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_staff_detail);

            $returnArr = DetailService::getInstance()->searchStaffDetail($params, $this->user['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 转岗管理-转岗申请-批量申请提交
     * @Permission(action='transfer.apply.add')
     */
    public function batchApplyAction()
    {
        $params = $this->request->get();
        try {
            if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                $validateRule = ListService::$validate_batch_add_my;
            } else {
                $validateRule = ListService::$validate_batch_add;
            }
            Validation::validate($params, $validateRule);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        try {
            $res = AuditService::getInstance()->batchApplyJobTransfer($params, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 转岗管理-特殊转岗-提交excel
     * @Permission(action='transfer.apply.add')
     */
    public function batchSpecialApplyAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        if (empty($excel_file)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Excel File Empty', []);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(0)
            ->getSheetData();

        try {
            $res = JobTransferService::getInstance()->uploadApplyStaffIdsV2($excel_data, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['msg'], $res['data']);
    }

    /**
     * @description 获取转岗后直线上级
     * @Token
     */
    public function getAfterManagerAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_do_transfer);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $res = DetailService::getInstance()->getAfterManagerInfo($params, $this->user['id']);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res ?? null);
    }

    /**
     * 搜索员工工号接口
     * @Token
     */
    public function searchStaffListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_search_staff_name);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $res = DetailService::getInstance()->searchStaff($params, $this->user['id']);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res ?? null);
    }

    /**
     * @description 编辑
     * @Permission(action='transfer.search.edit')
     */
    public function editAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_edit);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lockKey = AuditService::getInstance()->getSingleAuditLockKey('edit', $params['id']);
        $res = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->edit($params, $this->user['id']);
        }, $lockKey, 10, true);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @description 立即转岗
     * @Permission(action='transfer.search.transfer')
     */
    public function doTransferAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_do_transfer);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lockKey = AuditService::getInstance()->getSingleAuditLockKey('do_transfer', $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return JobTransferService::getInstance()->doTransfer($params, $this->user['id']);
        }, $lockKey, 10, false);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @description 激活确认单
     * @Permission(action='transfer.search.active')
     */
    public function activateAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_do_transfer);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lockKey = AuditService::getInstance()->getSingleAuditLockKey('do_transfer', $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return JobTransferService::getInstance()->activate($params, $this->user['id']);
        }, $lockKey, 10, false);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取批量添加进度
     * @Token
     */
    public function getBatchAddProgressAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_batch);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getBatchAddProgress($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取立即转岗转岗进度
     * @Token
     */
    public function getDoTransferProgressAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_do_transfer);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getDoTransferProgress($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 上传Excel批量导入转岗
     * @Token
     */
    public function uploadBatchJobTransferAction()
    {
        try {
            $importPath  = $this->request->get('path');
            $tmpDir      = sys_get_temp_dir();                // 获取系统的临时目录路径
            $fileName    = basename($importPath);             // 提取文件名
            $tmpFilePath = $tmpDir . '/' . $fileName;         // 构建临时文件路径
            if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
                throw new BusinessException('System error');
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            $fileInfo = pathinfo(basename($tmpFilePath));
            if (!isset($fileInfo['extension']) || $fileInfo['extension'] !== "xlsx") { //仅支持的文件格式xlsx
                throw new ValidationException($this->t->_('file_format_error'));
            }

            // 读取上传文件数据
            $excel_data = $excel->openFile($tmpFilePath)
                ->openSheet()
                ->setType([
                    4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP
                ])
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }
            array_shift($excel_data);

            $importData = [];
            foreach ($excel_data as $value) {
                if (!empty($value) && $value[0]) {
                    $importData[] = $value;
                }
            }

            //超过最大条数
            if ((count($importData)) > JobTransferService::SPECIAL_TRANSFER_MAX_IMPORT_ROWS) {
                throw new ValidationException($this->t->_('err_msg_over_max_rows', [
                    'limit_rows' => JobTransferService::SPECIAL_TRANSFER_MAX_IMPORT_ROWS,
                ]));
            }
            $expectTransferDate = array_unique(array_filter(array_column($importData, 4)));
            if (count($expectTransferDate) > 1) {
                throw new ValidationException($this->t->_('err_msg_not_same_import_date'));
            }

            $type     = ImportCenterEnums::TYPE_JOB_TRANSFER_BATCH_UPLOAD;
            $lock_key = md5('upload_batch_job_transfer_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($importPath, $type) {
                return ImportCenterService::getInstance()->addImportTask($importPath, $this->user, $type, true);
            }, $lock_key);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('批量导入转岗失败: ' . $res['message']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @description 转岗管理-特殊转岗申请-下载批量导入模版
     * @Token
     * @throws \Exception
     */
    public function downloadAction()
    {
        $returnArr = SysService::getInstance()->downloadSpecialImportTemplate();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['path' => $returnArr]);
    }
}