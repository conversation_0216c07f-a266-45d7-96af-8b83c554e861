<?php

namespace App\Modules\PaperDocument\Services;

use App\Library\CInterface\PaperDocumentModelInterface;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PaperDocumentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\PaperDocumentConfirmStaffModel;
use App\Models\oa\PaperDocumentLogModel;
use App\Models\oa\PaperDocumentModel;
use App\Modules\Budget\Services\BudgetAdjustService;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;

class ConfirmationService extends BaseService
{
    // 批量确认最大条数
    const BATCH_CONFIRM_MAX_ROWS = 200;

    /**
     * 实例
     * @var ConfirmationService
     */
    private static $instance = null;

    /**
     * 单一实例
     * @return ConfirmationService
     */
    public static function getInstance(): ConfirmationService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 实例
     * @var ReimbursementFlowService
     */
    private $service = null;

    /**
     * 参数验证规则 - 我的申请列表
     */
    public static $validate_list = [
        'page'      => 'IntGt:0',      // 页码
        'page_size' => 'IntGt:0',      // 每页条数
        'module_id' => 'StrLenGe:0',   // 模块编码
        'confirm_status' => 'IntGt:0',  // 确认状态
        'start_date' => 'Date',        // 开始日期
        'end_date' => 'Date',          // 结束日期
    ];

    /**
     * 参数验证规则 - 审核列表
     */
    public static $validate_audit_list = [
        'page'          => 'IntGt:0',       // 页码
        'page_size'     => 'IntGt:0',       // 每页条数
        'module_id'     => 'StrLenGe:0',    // 模块编码
        'confirm_status' => 'IntGt:0',       // 确认状态
        'start_date'    => 'Date',          // 开始日期
        'end_date'      => 'Date',          // 结束日期
    ];

    /**
     * 参数验证规则 - 详情
     */
    public static $validate_detail = [
        'serial_no' => 'Required|StrLenGe:0', // 编码
    ];

    /**
     * 参数验证规则 - 提交
     */
    public static $validate_submit = [
        'serial_no' => 'Required|StrLenGe:0',         // 编码
        'parcel_no' => 'StrLenGe:0',                  // 快递单号
        'remark'    => 'StrLenGe:0',                  // 备注
    ];

    /**
     * 参数验证规则 - 确认
     */
    public static $validate_confirm = [
        'serial_no'      => 'Required|StrLenGe:0',         // 编码
        'confirm_status' => 'Required|IntGt:0',            // 确认状态
        'reason_type'    => 'ArrLenGe:0',                  // 待补齐原因
        'remark'         => 'StrLenGe:0',                  // 备注
    ];

    /**
     * 参数验证规则 - 确认
     */
    public static $validate_batch = [
        'serial_no' => 'Required|Arr|ArrLenGe:1', // 编码
    ];

    /**
     * 获取我的申请列表
     * @param array $params 查询参数：包含page(页码)、page_size(每页条数)等
     * @param array $user 当前用户信息
     * @return array 返回包含items和pagination的数组结构
     */
    public function getMyApplyList($params, $user): array
    {
        $params['tab_confirm_state'] = $this->getMyTabDefaultState($params['tab']);
        $params['created_id']        = $user['id'] ?? 0;
        $pageNum                     = $params['pageNum'] ?? 1;
        $pageSize                    = $params['pageSize'] ?? 20;
        $list                        = [];

        // 获取查询 builder
        $builder = $this->getQueryBuilder($params);

        // 查询总数
        $total = (int)$builder
            ->columns('count(1) as total')
            ->getQuery()
            ->getSingleResult()
            ->total;

        if (empty($total)) {
            return [
                'items'      => $list,
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page'     => $pageSize,
                    'total_count'  => 0,
                ],
            ];
        }

        // 查询列表数据
        $builder->columns('*');
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        if ($params['tab'] == PaperDocumentEnums::TAB_FINISHED) {
            $builder->orderBy('apply_date DESC');
        } else {
            $builder->orderBy('apply_date ASC');
        }
        $items = $builder->getQuery()->execute()->toArray();

        // 处理列表数据
        $list = $this->formatList($items);

        return [
            'items'      => $list,
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => $total,
            ],
        ];
    }

    /**
     * 获取纸质审件审核列表
     * @param array $params 查询参数
     * @param array $user 当前用户信息
     * @return array 返回包含items和pagination的数组结构
     * @throws ValidationException
     */
    public function getAuditList($params, $user): array
    {
        $params['tab_confirm_state'] = $this->getAuditTabDefaultState($params['tab']);
        $pageNum                     = $params['pageNum'] ?? 1;
        $pageSize                    = $params['pageSize'] ?? 20;
        $list                        = [];

        // 检查权限：提交人或确认人可以查看
        $canViewModuleId = $this->getSpecModulePermission([], $user['id']);

        if (empty($canViewModuleId)) {
            throw new ValidationException(static::$t->_('permission_denied'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取查询 builder
        $builder = $this->getQueryBuilder($params);

        // 查询总数
        $total = (int)$builder
            ->columns('count(1) as total')
            ->getQuery()
            ->getSingleResult()
            ->total;

        if (empty($total)) {
            return [
                'items'      => $list,
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page'     => $pageSize,
                    'total_count'  => 0,
                ],
            ];
        }

        // 查询列表数据
        $builder->columns('*');
        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        if ($params['tab'] == PaperDocumentEnums::TAB_FINISHED) {
            $builder->orderBy('apply_date DESC');
        } else {
            $builder->orderBy('apply_date ASC');
        }
        $items = $builder->getQuery()->execute()->toArray();

        // 处理列表数据
        $list = $this->formatList($items);

        return [
            'items'      => $list,
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => $total,
            ],
        ];
    }

    /**
     * 获取纸质文件详情
     * @param array $params 查询参数：包含id
     * @param array $user 当前用户信息
     * @return array 返回详情数据
     * @throws ValidationException
     */
    public function getDetail($params, $user): array
    {
        $serialNo = $params['serial_no'];

        // 查询记录
        $record = $this->getDetailInfo($serialNo);

        if (empty($record)) {
            throw new ValidationException(static::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
        }

        // 检查权限：提交人或确认人可以查看
        $canView = $this->checkPermission($record, $user['id']);

        if (!$canView) {
            throw new ValidationException(static::$t->_('permission_denied'), ErrCode::$VALIDATE_ERROR);
        }

        //组织类型
        $costStoreType = BudgetAdjustService::getInstance()->getOrganizationTypeItems();

        // 详情数据
        $data = $record->toArray();

        //费用所属公司
        $companyList = SysService::getInstance()->getCostCompanyId(true);
        $companyList = array_column($companyList, 'label','value');

        //补全原因
        $reasonTypeListMap     = SysService::getInstance()->getConfirmReason();
        $reasonTypeListMap     = array_column($reasonTypeListMap, 'label', 'value');
        $reasonType            = $data['reason_type'] ?? [];
        $reasonType            = !empty($reasonType) ? json_decode($reasonType) : [];
        $data['reason_detail'] = ConfirmationService::getInstance()->formatReason(
            $reasonType,
            $data['reason_text'] ?? '',
            $reasonTypeListMap);

        // 获取操作日志
        $data['confirm_logs']       = $this->getOperationLogs($serialNo);

        $data['module_name']        = SysService::getInstance()->getModuleName($data['module_id']);
        $data['confirm_status_txt'] = $this->getConfirmStateText($data['confirm_status']);
        $data['currency_txt']       = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
        $data['cost_store_type_txt']= isset($costStoreType[$data['cost_store_type']])
            ? static::$t->_($costStoreType[$data['cost_store_type']])
            : '';
        $data['cost_company_name']  = $companyList[$data['cost_company_id']] ?? '';

        // 费用所属项目
        $country_code_text         = \App\Modules\Reimbursement\Services\BaseService::getLangCountryArr();
        $country_code_text         = array_column($country_code_text, 'text_key', 'code');
        $data['country_code_text'] = static::$t->_($country_code_text[$data['country_code']] ?? '');

        //最后确认人
        $data['last_confirm_staff_info'] = '';
        $lastConfirmStaffIds             = !empty($data['last_confirm_staff_ids'])
            ? explode(',', $data['last_confirm_staff_ids']) : [];
        if (!empty($lastConfirmStaffIds)) {
            $staffInfo                       = (new HrStaffInfoService())->getStaffInfoByIds($lastConfirmStaffIds,
                'staff_info_id,name,state');
            $staffInfo                       = array_column($staffInfo, null, 'staff_info_id');
            $data['last_confirm_staff_info'] = $this->formatLastStaffInfo($lastConfirmStaffIds, $staffInfo);
        }

        //金额
        $data['payable_amount_all'] = $this->formatAmount($data['payable_amount_all']);

        return $data;
    }

    /**
     * 获取详情
     * @param $serial_no
     * @return mixed
     */
    public function getDetailInfo($serial_no)
    {
        return PaperDocumentModel::findFirst([
            'conditions' => 'serial_no = :serial_no:',
            'bind' => ['serial_no' => $serial_no],
        ]);
    }

    /**
     * 提交纸质文件确认申请
     * @param array $params 提交参数：包含serial_no(单据编号)、parcel_no(快递单号)、remark(备注)
     * @param array $user 当前用户信息
     * @return bool 返回是否提交成功
     * @throws ValidationException
     * @throws \Exception
     */
    public function submitPaperDocument($params, $user): bool
    {
        $serialNo = $params['serial_no'];
        $parcelNo = trim($params['parcel_no'] ?? '');
        $remark   = trim($params['remark'] ?? '');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 查询纸质文件记录
            $record = PaperDocumentModel::findFirst([
                'conditions' => 'serial_no = :serial_no: AND deleted = 0',
                'bind' => ['serial_no' => $serialNo],
            ]);

            if (empty($record)) {
                throw new ValidationException(static::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            // 校验提交人是否为创建人
            if ($record->created_id != $user['id']) {
                throw new ValidationException(static::$t->_('permission_denied_only_creator_can_submit'), ErrCode::$VALIDATE_ERROR);
            }

            // 校验确认状态：只有待提交(1)或待补全(4)时，才可以提交
            $allowedStates = [
                PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT,  // 待提交
                PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL,    // 待补齐
            ];
            
            if (!in_array($record->confirm_status, $allowedStates)) {
                throw new ValidationException(static::$t->_('paper_document_status_not_allow_submit'), ErrCode::$VALIDATE_ERROR);
            }

            // 是否补交
            if ($record->confirm_status == PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL) {
                $record->is_additional = PaperDocumentEnums::ADDITIONAL_STATE_YES;
            }

            // 补充原因说明
            if ($record->confirm_status == PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL) {
                $remark = PaperDocumentEnums::DEFAULT_SUBMIT_REASON;
            }

            // 更新记录状态为待确认
            $record->confirm_status = PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM;

            if (!$record->save()) {
                throw new \Exception('更新纸质文件确认状态失败: ' . $record->getErrorMessagesJson());
            }
            // 更新业务主表状态
            $this->updateMainConfirmStatus($serialNo, PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM);

            // 记录操作日志
            $this->createOperationLog(
                $record->serial_no,
                $record->module_id,
                $record->confirm_status,
                $user,
                $parcelNo,
                $remark
            );

            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 确认纸质文件状态
     * @param array $params 确认参数
     * @param array $user 当前用户信息
     * @return bool 返回是否成功
     * @throws \Exception
     */
    public function confirmPaperDocument($params, $user): bool
    {
        $serialNo     = $params['serial_no'];
        $confirmStatus = (int)$params['confirm_status'];
        $reason        = $params['reason_type'] ?? [];
        $remark        = trim($params['remark'] ?? '');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 查询记录
            $record = PaperDocumentModel::findFirst([
                'conditions' => 'serial_no = :serial_no:',
                'bind' => ['serial_no' => $serialNo],
            ]);

            if (empty($record)) {
                throw new ValidationException(static::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            // 检查是否是确认人员
            $confirmStaffRecord = PaperDocumentConfirmStaffModel::findFirst([
                'conditions' => 'module_id = :module_id: AND confirm_staff_id = :confirm_staff_id: AND deleted = 0',
                'bind' => [
                    'module_id' => $record->module_id,
                    'confirm_staff_id' => $user['id'],
                ],
            ]);

            if (empty($confirmStaffRecord)) {
                throw new ValidationException(static::$t->_('permission_denied'), ErrCode::$VALIDATE_ERROR);
            }

            if ($record->created_id == $user['id']) {
                throw new ValidationException(static::$t->_('confirm_staff_can_not_be_same_created_staff'), ErrCode::$VALIDATE_ERROR);
            }

            // 检查状态是否可以确认
            if ($record->confirm_status == PaperDocumentEnums::CONFIRM_STATE_COMPLETE) {
                throw new ValidationException(static::$t->_('paper_document_already_confirmed'), ErrCode::$VALIDATE_ERROR);
            }

            // 更新记录
            $record->confirm_status         = $confirmStatus;
            $record->last_confirm_staff_ids = $user['id'];
            if (!empty($reason)) {
                $record->reason_type = json_encode($reason);
            }
            if (!empty($remark)) {
                $record->reason_text = $remark;
            }

            // 更新补交状态
            $lastOperateTime = '';
            if ($confirmStatus == PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL) {

                // 待补齐原因
                $reasonTypeListMap = SysService::getInstance()->getConfirmReason();
                $reasonTypeListMap = array_column($reasonTypeListMap, 'label', 'value');
                $confirmReason     = ConfirmationService::getInstance()->formatReason(
                    $reason,
                    $remark,
                    $reasonTypeListMap);

                // 最后操作时间
                $lastOperateTime              = date('Y-m-d H:i:s');
                $record->last_fill_operate_at = $lastOperateTime;

            } else {
                $confirmReason = PaperDocumentEnums::DEFAULT_COMPLETE_REASON;
            }

            if (!$record->save()) {
                throw new \Exception('更新纸质文件确认状态失败');
            }

            // 更新业务主表状态
            $this->updateMainConfirmStatus($serialNo, $confirmStatus);

            // 记录操作日志
            $this->createOperationLog(
                $record->serial_no,
                $record->module_id,
                $record->confirm_status,
                $user,
                '',
                $confirmReason,
                !empty($lastOperateTime) ? $lastOperateTime: ''
            );

            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 获取确认状态文本
     * @param int $confirmState 确认状态
     * @return string 状态文本
     */
    private function getConfirmStateText(int $confirmState): string
    {
        $stateTexts = [
            PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT  => static::$t->_('confirm_state_1'),
            PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM => static::$t->_('confirm_state_2'),
            PaperDocumentEnums::CONFIRM_STATE_COMPLETE        => static::$t->_('confirm_state_3'),
            PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL    => static::$t->_('confirm_state_4'),
        ];

        return $stateTexts[$confirmState] ?? '';
    }

    /**
     * 创建操作日志
     * @param string $serialNo 纸质文件编号
     * @param int $moduleId 模块ID
     * @param int $status 文件状态
     * @param array $user 操作用户
     * @param string $parcelNo 快递单号
     * @param string $remark 备注
     * @return bool 是否成功
     */
    private function createOperationLog(string $serialNo, int $moduleId, int $status, array $user, string $parcelNo = '', string $remark = '', $operate_time = ''): bool
    {
        $log = new PaperDocumentLogModel();
        $log->module_id = $moduleId;
        $log->serial_no = $serialNo;
        $log->operator_id = $user['id'];
        $log->operate_time = empty($operate_time) ? date('Y-m-d H:i:s') : $operate_time;
        $log->status = $status;
        $log->parcel_no = $parcelNo;
        $log->remark = $remark;

        return $log->save();
    }

    /**
     * 获取操作日志
     * @param string $serial_no 纸质文件记录编号
     * @return array 操作日志列表
     */
    private function getOperationLogs(string $serial_no): array
    {
        $logs = PaperDocumentLogModel::find([
            'conditions' => 'serial_no = :serial_no:',
            'bind' => ['serial_no' => $serial_no],
            'order' => 'id DESC',
        ]);

        $result = [];

        //操作人
        $operatorIds = array_column($logs->toArray(), 'operator_id');
        $staffInfo   = (new HrStaffInfoService())->getStaffInfoByIds($operatorIds,
            'staff_info_id,name,state');
        $staffInfo   = array_column($staffInfo, null, 'staff_info_id');

        foreach ($logs as $log) {
            $logStaffInfo = $staffInfo[$log->operator_id] ?? [];
            $result[]     = [
                'id'            => $log->id,
                'module_name'   => SysService::getInstance()->getModuleName($log->module_id),
                'serial_no'     => $log->serial_no,
                'operator_id'   => $log->operator_id,
                'operator_name' => $logStaffInfo['name'] ?? '',
                'operate_time'  => $log->operate_time,
                'status'        => $this->getConfirmStateText($log->status),
                'parcel_no'     => $log->parcel_no ?? '',
                'remark'        => $log->remark ?? '',
                'created_at'    => $log->created_at,
            ];
        }

        return $result;
    }

    private function getQueryBuilder(array $params)
    {
        $moduleId        = $params['module_id'] ?? 0;
        $defaultModuleId = $params['default_module_id'] ?? 0;
        $confirmState    = $params['confirm_status'] ?? 0;
        $tabConfirmState = $params['tab_confirm_state'] ?? [];
        $startDate       = $params['apply_date_start'] ?? '';
        $endDate         = $params['apply_date_end'] ?? '';
        $createdId       = $params['created_id'] ?? '';
        $serialNo        = $params['serial_no'] ?? '';
        $costCompanyId   = $params['cost_company_id'] ?? '';
        $currency        = $params['currency'] ?? '';
        $isAdditional    = $params['is_additional'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['p' => PaperDocumentModel::class]);

        // 构建查询条件

        if (!empty($createdId)) {
            $builder->andWhere('p.created_id = :created_id: or p.created_name = :created_id:', ['created_id' => $createdId]);
        }

        if (!empty($moduleId)) {
            $builder->andWhere('p.module_id = :module_id:', ['module_id' => $moduleId]);
        }

        if (!empty($defaultModuleId)) {
            $builder->inWhere('p.module_id', $defaultModuleId);
        }

        if (!empty($serialNo)) {
            $builder->andWhere('p.serial_no = :serial_no:', ['serial_no' => $serialNo]);
        }

        if (!empty($costCompanyId)) {
            $builder->andWhere('p.cost_company_id = :cost_company_id:', ['cost_company_id' => $costCompanyId]);
        }

        if (!empty($currency)) {
            $builder->andWhere('p.currency = :currency:', ['currency' => $currency]);
        }

        // 根据时间范围过滤
        if (!empty($startDate)) {
            $builder->andWhere('p.apply_date >= :apply_date_start:', ['apply_date_start' => $startDate]);
        }
        if (!empty($endDate)) {
            $builder->andWhere('p.apply_date <= :apply_date_end:', ['apply_date_end' => $endDate]);
        }

        if (!empty($confirmState)) {
            $builder->andWhere('p.confirm_status = :confirm_status:', ['confirm_status' => $confirmState]);
        }

        if (!empty($tabConfirmState)) {
            $builder->inWhere('p.confirm_status', $tabConfirmState);
        }

        //是否补交
        if (is_numeric($isAdditional) && $isAdditional != PaperDocumentEnums::IS_ADDITIONAL_ALL) {
            $builder->andWhere('p.is_additional = :is_additional:', ['is_additional' => $isAdditional]);
        }

        return $builder;
    }

    /**
     * 组织列表数据
     * @param $items
     * @return array
     */
    private function formatList($items): array
    {
        //确认状态
        $confirmStatusList = SysService::getInstance()->getConfirmStateList();
        $confirmStatusList = array_column($confirmStatusList, 'label', 'value');

        //费用所属公司
        $companyList = SysService::getInstance()->getCostCompanyId(true);
        $companyList = array_column($companyList, 'label','value');

        $reasonTypeListMap = SysService::getInstance()->getConfirmReason();
        $reasonTypeListMap = array_column($reasonTypeListMap, 'label', 'value');

        // 收集所有需要查询的员工ID
        $allStaffIds = [];
        foreach ($items as $item) {
            if (!empty($item['last_confirm_staff_ids'])) {
                $staffIds = explode(',', $item['last_confirm_staff_ids']);
                $allStaffIds = array_merge($allStaffIds, $staffIds);
            }
        }
        
        // 批量查询员工信息
        $staffInfoMap = [];
        if (!empty($allStaffIds)) {
            $allStaffIds   = array_values(array_unique($allStaffIds));
            $staffInfoList = (new HrStaffInfoService())->getStaffInfoByIds($allStaffIds, 'staff_info_id,name,state');
            $staffInfoMap  = array_column($staffInfoList, null, 'staff_info_id');
        }

        foreach ($items as &$item) {
            $item['module_name']        = SysService::getInstance()->getModuleName($item['module_id']);
            $item['confirm_status_txt'] = isset($confirmStatusList[$item['confirm_status']])
                ? static::$t->_($confirmStatusList[$item['confirm_status']])
                : '';
            $item['currency_txt']       = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
            $item['cost_company_name']  = $companyList[$item['cost_company_id']] ?? '';

            $reasonType            = $item['reason_type'] ?? [];
            $reasonType            = !empty($reasonType) ? json_decode($reasonType) : [];
            $item['reason_detail'] = ConfirmationService::getInstance()->formatReason(
                $reasonType,
                $item['reason_text'] ?? '',
                $reasonTypeListMap);

            $item['is_additional_text'] = $this->formatAdditional($item['is_additional']);
            
            // 处理最后确认人信息
            $item['last_confirm_staff_info'] = '';
            if (!empty($item['last_confirm_staff_ids'])) {
                $lastConfirmStaffIds = explode(',', $item['last_confirm_staff_ids']);
                $item['last_confirm_staff_info'] = $this->formatLastStaffInfo($lastConfirmStaffIds, $staffInfoMap);
            }
            $item['payable_amount_all'] = $this->formatAmount($item['payable_amount_all']);
        }
        return $items;
    }

    /**
     * @param $model
     * @return true
     * @throws BusinessException
     */
    public function saveOne($model): bool
    {
        if (!$model instanceof PaperDocumentModelInterface) {
            throw new BusinessException('not implement PayModelInterface', ErrCode::$BUSINESS_ERROR);
        }

        $data = $model->getPaperDocumentData();

        $paperDocument = new PaperDocumentModel();
        $bool = $paperDocument->i_create($data);
        if ($bool === false) {
            throw new BusinessException('纸质文件确认模块创建失败=' . get_data_object_error_msg($paperDocument), ErrCode::$CONTRACT_CREATE_ERROR);
        }
        return true;
    }

    /**
     * @param $record
     * @param $user
     * @return bool
     */
    private function checkPermission($record, $user): bool
    {
        if ($record->created_id == $user) { // 发起人为当期登录人
            $canView = true;
        } else {
            $canView = $this->checkConfirmPermission($record->module_id, $user);
        }
        return $canView;
    }

    /**
     * 我的列表
     * @param $tab
     * @return array
     */
    private function getMyTabDefaultState($tab): array
    {
        if ($tab == PaperDocumentEnums::TAB_PENDING) {
            return [
                PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT,
                PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL,
            ];
        } else {
            return [
                PaperDocumentEnums::CONFIRM_STATE_COMPLETE,
            ];
        }
    }

    /**
     * 审批列表
     * @param $tab
     * @return array
     */
    private function getAuditTabDefaultState($tab): array
    {
        if ($tab == PaperDocumentEnums::TAB_PENDING) {
            return [
                PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM,
            ];
        } else {
            return [
                PaperDocumentEnums::CONFIRM_STATE_COMPLETE,
            ];
        }
    }

    /**
     * 校验权限
     * @param array $module_ids
     * @param $user
     * @return bool
     */
    private function checkConfirmPermission($module_ids = [], $user): bool
    {
        $confirmStaffRecord = $this->getSpecModulePermission($module_ids, $user);
        if (!empty($confirmStaffRecord)) {
            $canView = true;
        }
        return $canView ?? false;
    }

    /**
     * 获取管理的模块的权限
     * @param $module_ids
     * @param $user
     * @return mixed
     */
    private function getSpecModulePermission($module_ids, $user)
    {
        if (empty($module_ids)) {
            $conditions = 'confirm_staff_id = :confirm_staff_id: AND deleted = 0';
            $bind       = [
                'confirm_staff_id' => $user,
            ];
        } else if (is_array($module_ids)) {
            $conditions = 'module_id in({module_id:array}) AND confirm_staff_id = :confirm_staff_id: AND deleted = 0';
            $bind       = [
                'module_id'        => $module_ids,
                'confirm_staff_id' => $user,
            ];
        } else {
            $conditions = 'module_id = :module_id: AND confirm_staff_id = :confirm_staff_id: AND deleted = 0';
            $bind       = [
                'module_id'        => $module_ids,
                'confirm_staff_id' => $user,
            ];
        }

        // 检查是否是确认人员
        $permission = PaperDocumentConfirmStaffModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        return array_column($permission, 'module_id');
    }

    /**
     * 获取字段
     * @param array $serialNoIds
     * @param array $columns
     * @return array
     */
    public function getConfirmList(array $serialNoIds, array $columns = ['serial_no','last_confirm_staff_ids']): array
    {
        if (empty($serialNoIds)) {
            return [];
        }
        return PaperDocumentModel::find([
            'conditions' => 'serial_no in ({serial_no:array}) and deleted = 0',
            'bind' => [
                'serial_no' => $serialNoIds,
            ],
            'columns' => $columns,
        ])->toArray();
    }

    /**
     * 组织待补全原因
     * @param $reason_type
     * @param $reason_text
     * @param $reasonTypeListMap
     * @return string
     */
    public function formatReason($reason_type, $reason_text, $reasonTypeListMap): string
    {
        $countryCode = get_country_code();
        if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            // 如果 reason_type 为空，直接返回 reason_text
            if (empty($reason_type)) {
                return $reason_text ?? '';
            }
            
            // 将 reason_type 按逗号分割成数组
            $reasonTypes = is_string($reason_type) ? explode(',', $reason_type) : $reason_type;
            $formattedReasons = [];
            $hasOtherType = false;
            
            foreach ($reasonTypes as $type) {
                $type = trim($type);
                
                // 如果是99其他类型，标记但不添加到格式化原因中
                if ($type == '99') {
                    $hasOtherType = true;
                    continue;
                }
                
                // 根据映射获取原因类型文本
                if (isset($reasonTypeListMap[$type])) {
                    $formattedReasons[] = $reasonTypeListMap[$type];
                }
            }
            
            // 拼接格式化的原因
            $result = implode(',', $formattedReasons);
            
            // 如果存在99其他类型，拼接 reason_text
            if ($hasOtherType && !empty($reason_text)) {
                if (!empty($result)) {
                    $result .= ',' . $reason_text;
                } else {
                    $result = $reason_text;
                }
            }
            
            return $result;
        } else {
            return $reason_text ?? '';
        }
    }

    /**
     * 批量确认纸质文件已齐全
     * @param array $params 确认参数：包含serial_no(单号数组)
     * @param array $user 当前用户信息
     * @return array 返回处理结果
     * @throws ValidationException
     * @throws \Exception
     */
    public function batchConfirmPaperDocument($params, array $user): array
    {
        $serialNos = $params['serial_no'] ?? []; // 单号数组

        // 检查参数是否为空
        if (empty($serialNos)) {
            throw new ValidationException(static::$t->_('miss_args'), ErrCode::$VALIDATE_ERROR);
        }

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        
        try {
            // 校验
            [$records, $validationErrors] = $this->checkBatchConfirm($serialNos, $user['id']);

            // 如果存在任何校验错误，回滚事务并返回错误信息
            if (!empty($validationErrors)) {
                $db->rollback();
                return $validationErrors;
            }

            // 如果没有可处理的记录，回滚事务并返回
            if (empty($records)) {
                throw new ValidationException(static::$t->_('no valid data'));
            }

            foreach ($records as $record) {
                // 更新记录状态为已齐全
                $record->confirm_status         = PaperDocumentEnums::CONFIRM_STATE_COMPLETE;
                $record->last_confirm_staff_ids = $user['id'];
                $update_res = $record->save();
                if (!$update_res) {
                    throw new \Exception('更新记录失败: ' . $record->serial_no);
                }
                $this->logger->info(['serial_no'=>$record->serial_no,'update_res'=>$update_res,'PaperDocumentModel'=>'PaperDocumentModel']);
                // 更新业务表状态
                $this->updateMainConfirmStatus($record->serial_no, PaperDocumentEnums::CONFIRM_STATE_COMPLETE);

                // 记录操作日志
                $this->createOperationLog(
                    $record->serial_no,
                    $record->module_id,
                    $record->confirm_status,
                    $user,
                    '',
                    PaperDocumentEnums::DEFAULT_COMPLETE_REASON
                );
            }

            $commit_res = $db->commit();

            $this->logger->info(['commit_res'=>$commit_res]);
            
            return [];
            
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('batchConfirmPaperDocument err:' . $e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 批量确认提交
     * @param $serialNos
     * @param $user
     * @return array
     * @throws ValidationException
     */
    private function checkBatchConfirm($serialNos, $user): array
    {
        $records          = [];
        $validationErrors = [];

        //超过最大行数
        if (count($serialNos) > self::BATCH_CONFIRM_MAX_ROWS) {
            throw new ValidationException(static::$t->_('over max raws'), ErrCode::$VALIDATE_ERROR);
        }

        // 一次性查询所有纸质文件记录
        $paperDocuments = PaperDocumentModel::find([
            'conditions' => 'serial_no IN ({serial_nos:array}) AND deleted = 0',
            'bind'       => ['serial_nos' => $serialNos],
        ]);

        // 将查询结果转换为以serial_no为键的数组，便于后续查找
        $paperDocumentMap = [];
        $moduleIds        = [];
        foreach ($paperDocuments as $doc) {
            $paperDocumentMap[$doc->serial_no] = $doc;
            $moduleIds[]                       = $doc->module_id;
        }

        // 一次性查询所有确认人员权限记录
        $confirmStaffRecords = [];
        if (!empty($moduleIds)) {
            $confirmStaffData = PaperDocumentConfirmStaffModel::find([
                'conditions' => 'module_id IN ({module_ids:array}) AND confirm_staff_id = :confirm_staff_id: AND deleted = 0',
                'bind'       => [
                    'module_ids'       => array_values(array_unique($moduleIds)),
                    'confirm_staff_id' => $user,
                ],
            ]);

            // 将确认人员权限记录转换为以module_id为键的数组
            foreach ($confirmStaffData as $confirmStaff) {
                $confirmStaffRecords[$confirmStaff->module_id] = 1;
            }
        }

        foreach ($serialNos as $serialNo) {
            // 从预查询的结果中获取纸质文件记录
            if (!isset($paperDocumentMap[$serialNo])) {
                throw new ValidationException('serial number not exists' . $serialNo);
            }

            $record = $paperDocumentMap[$serialNo];

            // 校验确认人不能是自己（不能是创建人）
            if ($record->created_id == $user) {
                throw new ValidationException(static::$t->_('confirm_staff_can_not_be_same_created_staff'));
            }

            // 从预查询的结果中检查是否是确认人员
            if (!isset($confirmStaffRecords[$record->module_id])) {
                $validationErrors['permission'][] = [
                    'module_id'   => $record->module_id,
                    'module_name' => SysService::getInstance()->getModuleName($record->module_id),
                ];
                continue;
            }

            // 检查状态是否可以确认（只有待确认状态才能确认为已齐全）
            if ($record->confirm_status != PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM) {
                $validationErrors['status_changed'][] = $serialNo;
                continue;
            }

            // 校验通过，保存记录以备后续更新
            $records[] = $record;
        }
        return [$records, $validationErrors];
    }

    /**
     * 获取待提交数
     * @param $staff_id
     * @return int
     */
    public function getWaitSubmitNum($staff_id): int
    {
        $params['tab_confirm_state'] = $this->getMyTabDefaultState(PaperDocumentEnums::TAB_PENDING);
        $params['created_id']        = $staff_id;

        // 获取查询 builder
        $builder = $this->getQueryBuilder($params);

        // 查询总数
        return (int)$builder
            ->columns('count(1) as total')
            ->getQuery()
            ->getSingleResult()
            ->total;
    }

    /**
     * 获取待确认数
     * @param $staff_id
     * @return int
     */
    public function getWaitConfirmNum($staff_id): int
    {
        $params['tab_confirm_state'] = $this->getAuditTabDefaultState(PaperDocumentEnums::TAB_PENDING);

        // 检查权限：提交人或确认人可以查看
        $canViewModuleId = $this->getSpecModulePermission([], $staff_id);

        if (empty($canViewModuleId)) {
            return 0;
        }

        // 获取查询 builder
        $builder = $this->getQueryBuilder($params);

        // 查询总数
        return (int)$builder
            ->columns('count(1) as total')
            ->getQuery()
            ->getSingleResult()
            ->total;
    }

    /**
     * 获取纸质单据基本信息
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getPaperDocBasicInfo($params = []): array
    {
        $staffId = $params['staff_id'] ?? '';
        $sn      = $params['sn'] ?? '';

        if (empty($staffId) || empty($sn)) {
            throw new ValidationException(static::$t->_('miss_args'));
        }

        // 分离国家码和序列号：前两位为国家码，后面为 serial_no
        if (strlen($sn) < 3) {
            throw new ValidationException(static::$t->_('invalid_sn_format'));
        }

        $countryCode  = strtoupper(substr($sn, 0, 2));
        $serialNumber = substr($sn, 2);

        // 验证国家码
        if (get_country_code() != $countryCode) {
            throw new ValidationException(static::$t->_('paper_document_wrong_country'));
        }

        // 查询记录
        $record = PaperDocumentModel::findFirst([
            'conditions' => 'serial_no = :serial_no:',
            'bind' => ['serial_no' => $serialNumber],
        ]);

        if (empty($record)) {
            throw new ValidationException(static::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
        }

        if (!in_array($record->module_id, SysService::getInstance()->getValidModuleIds())) {
            throw new ValidationException(static::$t->_('22111_56eef837'));
        }

        // 检查权限：提交人或确认人可以查看
        $canView = $this->checkPermission($record, $staffId);

        if (!$canView) {
            throw new ValidationException(static::$t->_('permission_denied'), ErrCode::$VALIDATE_ERROR);
        }
        // 待补齐原因
        $reasonTypeListMap = SysService::getInstance()->getConfirmReason();
        $reasonTypeListMap = array_column($reasonTypeListMap, 'label', 'value');

        $data = [
            'serial_number'          => $record->serial_no,
            'created_id'             => $record->created_id,
            'created_name'           => $record->created_name,
            'amount_money'           => $this->formatAmount($record->payable_amount_all),
            'currency'               => $record->currency,
            'currency_text'          => static::$t->_(GlobalEnums::$currency_item[$record->currency]),
            'apply_date'             => $record->apply_date,
            'module_id'              => $record->module_id,
            'module_name'            => SysService::getInstance()->getModuleName($record->module_id),
            'confirm_status'         => $record->confirm_status,
            'confirm_status_text'    => $this->getConfirmStateText($record->confirm_status),
            'last_confirm_staff_ids' => $record->last_confirm_staff_ids,
            'reason'                 => $this->formatReason(json_decode($record->reason_type), $record->reason_text, $reasonTypeListMap),
        ];

        if (!empty($record->last_confirm_staff_ids)) {
            $lastConfirmStaffIds             = explode(',', $record->last_confirm_staff_ids);
            $staffInfo                       = (new HrStaffInfoService())->getStaffInfoByIds($lastConfirmStaffIds,
                'staff_info_id,name,state');
            $staffInfo                       = array_column($staffInfo, null, 'staff_info_id');
            $data['last_confirm_staff_info'] = $this->formatLastStaffInfo($lastConfirmStaffIds, $staffInfo);
        }

        return $data;
    }

    /**
     * 获取详情
     * @return void
     */
    public function getPaperDocInfo($params)
    {

    }

    /**
     * 格式化详情数据
     * @param array $details
     * @return array|array[]
     */
    public function formatDetails(array $details): array
    {
        $result = [
            'basicInfo'  => [],
            'record_arr' => [],
        ];

        if (empty($details)) {
            return $result;
        }

        $country_code_text = \App\Modules\Reimbursement\Services\BaseService::getLangCountryArr();
        $country_code_text = array_column($country_code_text, 'text_key', 'code');
        $countryCodeText = static::$t->_($country_code_text[$details['country_code']] ?? '');

        $basicInfo = [
            'payment_export_module'            => $details['module_name'],             //模块
            'global.number'                    => $details['serial_no'],               //编号
            're_field_created_name'            => $details['created_name'],            //发起人姓名
            're_field_created_id'              => $details['created_id'],              //发起人工号
            're_field_created_company_name'    => $details['created_company_name'],    //发起人所属公司
            're_field_created_department_name' => $details['created_department_name'], //发起人所属部门
            'global.applicant.name'            => $details['apply_name'],              //申请人姓名
            'global.applicant.id'              => $details['apply_id'],                //申请人工号
            're_field_apply_department_name'   => $details['apply_department_name'],   //申请人所属部门
            're_field_apply_company_name'      => $details['apply_company_name'],      //申请人所属公司
            're_field_apply_store_name'        => $details['apply_store_name'],        //申请人所属网点
            're_field_apply_mobile'            => $details['apply_mobile'],            //申请人电话
            're_field_apply_center_code'       => $details['apply_center_code'],       //申请人所属中心
            're_filed_apply_date'              => $details['apply_date'],              //申请日期
            'sample_cost_company_name'         => $details['cost_company_name'],       //费用所属公司
            're_filed_apply_cost_department'   => $details['cost_department_name'],    //费用所属部门
            'country_code_project'             => $countryCodeText,                    //费用所属项目
            're_filed_apply_cost_store'        => $details['cost_store_type_txt'],     //费用所属网点/总部
            're_field_currency_text'           => $details['currency_txt'],            //币种
            'global.amount'                    => $details['payable_amount_all'],      //金额
        ];

        foreach ($basicInfo as $translation_key => $item) {
            $result['basicInfo'][] = [
                'key'   => static::$t->_($translation_key),
                'value' => $item,
            ];
        }
        $result['record_arr'] = $details['confirm_logs'] ?? [];

        // 确认人
        $result['confirm_staff'] = ConfirmStaffService::getInstance()->getConfirmStaffIdsByModuleId($details['module_id']);

        return $result;
    }

    /**
     * 更新业务主表的纸质单据确认状态
     * @param string $serial_no
     * @param int $confirm_status
     * @return void
     * @throws BusinessException
     */
    private function updateMainConfirmStatus(string $serial_no, int $confirm_status)
    {
        //暂时只有一个业务，多个业务时，这里需要获取各个业务模块的model ，然后更新
        $model = Reimbursement::getFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $serial_no],
        ]);
        
        if (empty($model)) {
            // 如果找不到对应的业务记录，记录日志但不抛出异常，避免影响批量操作
            $this->logger->warning("Cannot find Reimbursement record for serial_no: {$serial_no}");
            throw new BusinessException("Cannot find Reimbursement record for serial_no: {$serial_no}", ErrCode::$BUSINESS_ERROR);
        }
        $update_res = $model->i_update(['confirm_status' => $confirm_status]);
        if (!$update_res) {
            throw new BusinessException('更新业务主表确认状态失败: ' . $serial_no, ErrCode::$BUSINESS_ERROR);
        }
        $this->logger->info(['serial_no'=>$serial_no,'confirm_status'=>$confirm_status,'update_res'=>$update_res,'Reimbursement'=>'Reimbursement']);

        if ($confirm_status == PaperDocumentEnums::CONFIRM_STATE_COMPLETE) { // 已补全
            $service = $this->getReimbursementFlowService();
            $workflowRequest = $service->getRequest($model->id);
            $service->syncDataToPayment($model, $workflowRequest);
        }
    }

    /**
     * @return ReimbursementFlowService
     */
    private function getReimbursementFlowService(): ReimbursementFlowService
    {
        if (is_null($this->service)) {
            $this->service = new ReimbursementFlowService();
        }
        return $this->service;
    }

    private function formatAdditional($is_additional)
    {
        if ($is_additional) {
            return static::$t->_('global_bool_status_1');
        }
        return static::$t->_('global_bool_status_0');
    }

    /**
     * 组织最后确认人信息
     * @param array $lastConfirmStaffIds
     * @param array $staffInfo
     * @return string
     */
    public function formatLastStaffInfo(array $lastConfirmStaffIds, array $staffInfo): string
    {
        $result = [];
        foreach ($lastConfirmStaffIds as $staffId) {
            $staffDetail = $staffInfo[$staffId] ?? [];
            $result[]  = sprintf('%s (%s)', $staffDetail['name'] ?? '', $staffId);
        }
        return join(',', $result);
    }

    /**
     * 组织金额
     * @param $amount
     * @return string|null
     */
    private function formatAmount($amount)
    {
        if (empty($amount)) {
            return 0;
        }
        return bcdiv($amount, 1000, 2);
    }
}