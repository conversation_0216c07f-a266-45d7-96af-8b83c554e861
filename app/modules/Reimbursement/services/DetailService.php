<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\oa\ReimbursementDraftModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\BySettingEnvModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\PaperDocument\Services\ConfirmationService;
use App\Modules\PaperDocument\Services\ConfirmStaffService;
use App\Modules\PaperDocument\Services\SysService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Purchase\Services\PayFlowService;
use App\Modules\Reimbursement\Models\DepartmentModel;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementDetailTicketModel;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\StaffExpenseModel;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\backyard\AttendanceDataV2Repository;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\backyard\HrStaffApplySupportStoreRepository;
use App\Repository\oa\BudgetObjectProductRepository;
use App\Repository\oa\SysAttachmentRepository;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Exception\GenerateImageException;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Phalcon\Mvc\Model;

class DetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $id
     * @param $uid
     * @param $if_download
     * @param $type
     * @return mixed
     * @throws BusinessException
     */
    public function getDetail($id, $uid = 0, $if_download = false, $type = 0)
    {
        $item = Reimbursement::getFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($item)) {
            return [];
        }

        $data = $this->getDetailCommonData($item);

        $req = null;
        if (in_array($item->status, ReimbursementEnums::$audit_status_item)) {
            $req = (new ReimbursementFlowService())->getRequest($id);
            if (empty($req->id)) {
                throw new BusinessException("获取工作流批次失败, no={$item->no}, status={$item->status}", ErrCode::$BUSINESS_ERROR);
            }
        }

        //待回复征询ID
        $ask               = (new FYRService())->getRequestToByReplyAsk($req, $uid);
        $data['ask_id']    = $ask ? $ask->id : '';
        $data['auth_logs'] = $this->getAuditLogs($req, $item, $if_download);

        $data['edit_logs'] = (new ReimbursementFlowService())->getEditLog($req);

        //判断是否能更改
        $data['can_edit']        = false;
        $data['can_edit_fields'] = (object)[];
        if ($type == self::LIST_TYPE_AUDIT) {
            $can_edit_fields = (new ReimbursementFlowService())->getCanEditFieldByReq($req, $uid);
            if ($can_edit_fields !== false) {
                $data['can_edit_fields'] = $can_edit_fields;
                $data['can_edit']        = true;
            }
        }

        // 获取当前审批节点node_tag
        $data['node_tag'] = (new ReimbursementFlowService())->getPendingNodeTag($req, $uid);

        return $data;
    }

    /**
     * @param $id
     * @param $uid
     * @param $type
     * @return array
     */
    public function getCommonDetail($id, $uid = 0, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $item = [];

        try {
            // 支付详情: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getReimbursementPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $item = $this->getDetail($id, $uid, false, $type);
            if (empty($item['id'])) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            $setting_data = EnumsService::getInstance()->getMultiEnvByCode(['reimbursement_is_restrict_self_currency', 'sys_module_reimbursement_validation_tax_no', 'sys_module_reimbursement_auto_tax_no']);
            //增加返回字段 是否限制本位币 "1" : 限制  "0" : 不限制
            $item['is_restrict_currency'] = !empty($setting_data['reimbursement_is_restrict_self_currency']) ? $setting_data['reimbursement_is_restrict_self_currency'] : '0';
            //是否启用发票税务号校验(长度和字符内容)
            $item['is_validation_tax_no'] = !empty($setting_data['sys_module_reimbursement_validation_tax_no']) ? $setting_data['sys_module_reimbursement_validation_tax_no'] : '0';
            //是否自动填充发票税务号
            $item['is_auto_tax_no'] = !empty($setting_data['sys_module_reimbursement_auto_tax_no']) ? $setting_data['sys_module_reimbursement_auto_tax_no'] : '0';

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('Reimbursement-get-detail-failed: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('Reimbursement-get-detail-failed: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $item,
        ];
    }


    /**
     * @param $id
     * @return array
     */
    public function getPcCode($id)
    {

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $data = [];
            $data['cost_center_code_is_update'] = '0';
            $data['cost_center_code_list'] = [];
            $data['cost_center_code'] = '';

            $item = Pccode::findFirst(["conditions" => "department_id = :id:", "bind" => ["id" => $id]]);
            if (!empty($item)) {
                $data['cost_center_code'] = $item->pc_code;
            }

            // 获取费用部门的部门信息, 9975需求 1. 根据【费用部门】字段，自动带出部门所属的BU级别的信息。2. 如果无BU或者所属Clevel不是COO\CEO，则默认值为空，且必填；需要选择对应COO\CEO下的BU级别的信息。
            $data['cost_company_id'] = '';
            $data['cost_company_name'] = '';
            $dept_info = DepartmentModel::findFirst($id);
            if (!empty($dept_info) && !empty($dept_info->company_id)) {
                $coo_company_list = (new PurchaseService())->getCooCostCompany();
                if (!empty($coo_company_list)) {
                    foreach ($coo_company_list as $company) {
                        if ($company['cost_company_id'] == $dept_info->company_id) {
                            $data['cost_company_id'] = $company['cost_company_id'];
                            $data['cost_company_name'] = $company['cost_company_name'];
                            break;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('apply-get-pc-code-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 根据部门id,网点id找费用网点类型
     *
     * @param $department_id
     * @param string $store_id
     * @param string $staff_id
     * @return array
     */
    public function costStoreType($department_id, $store_id = '', $staff_id = '')
    {
        $data = [];
        // 可选网点或总部的特殊工号配置
        $privilege_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_all_type_by_staff_ids');
        if (in_array($staff_id, $privilege_staff_ids)) {
            // 申请人工号属于该工号配置的, 可以选总部或网点
            $data['items'][] = [
                'id' => 1,
                'name' => static::$t->_('global.branch'),
            ];
            $data['items'][] = [
                'id' => 2,
                'name' => static::$t->_('payment_cost_store_type_1'),
            ];
        } elseif ($store_id != Enums::HEAD_OFFICE_STORE_FLAG) {
            //为网点
            $data['items'][] = [
                'id' => 1,
                'name' => static::$t->_('global.branch'),
            ];
        } else {
            //为总部
            $first_department = StaffService::getInstance()->getParentDepartment($department_id, 1);
            $first_department_id = $first_department['id'] ?? 0;

            // 可选网点或总部的特殊部门配置
            $privilege_dept_ids = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_cost_all_dep');

            // 申请人一级部门隶属该部门配置的, 可以选总部或网点
            if (in_array($first_department_id, $privilege_dept_ids)) {
                $data['items'][] = [
                    'id' => 1,
                    'name' => static::$t->_('global.branch'),
                ];
                $data['items'][] = [
                    'id' => 2,
                    'name' => static::$t->_('payment_cost_store_type_1'),
                ];
            } else {
                $data['items'][] = [
                    'id' => 2,
                    'name' => static::$t->_('payment_cost_store_type_1'),
                ];
            }
        }
        return $data;
    }

    /**
     *
     * /**
     * 获得可冲销借款金额 ==都成功，失败返回0
     *
     * @param $id
     * @return array
     */
    public function getLoanAmount($id)
    {

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';


        try {
            $item = Reimbursement::getFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($item)) {
                throw new ValidationException("not found item");
            }


            $expense = StaffExpenseModel::findFirst([
                'staff_info_id = :id:',
                'bind' => ["id" => $item->apply_id],
            ]);

            if (empty($expense)) {
                throw new ValidationException("not found expense");
            }

            $data = 0;

            $use = bcsub($expense->loan_amount, $expense->re_amount);
            if (bccomp($use, $item->amount) >= 0) {
                $data = $item->amount;
            } else {
                $data = $use;
            }
            if ($data < 0) {
                $data = 0;
            }
        } catch (ValidationException $e) {
            $data = 0;
            $this->logger->info('Reimbursement-getLoanAmount-info:' . $e->getMessage());
        }

        $bank = EnvModel::getEnvByCode("reimbursement_bank");
        return [
            'code' => $code,
            'message' => $message,
            'data' => ["loan_amount" => bcdiv($data, 1000, 2), "bank" => json_decode($bank, 1)],
        ];
    }


    /**
     * @param $data
     * @return array
     */
    public function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['amount'] = bcdiv($data['amount'], 1000, 2);
        $data['travel_amount'] = bcdiv($data['travel_amount'], 1000, 2);
        $data['local_amount'] = bcdiv($data['local_amount'], 1000, 2);
        $data['payable_amount_all'] = bcdiv($data['payable_amount_all'], 1000, 2);
        $data['loan_amount'] = bcdiv($data['loan_amount'], 1000, 2);

        // 当冲减借款金额大于报销金额时，冲减借款金额取应付金额总计（含VAT不含WHT）字段
        $data['loan_amount'] = bccomp($data['loan_amount'], $data['amount'], 2) > 0 ? $data['payable_amount_all'] : $data['loan_amount'];
        $data['other_amount'] = bcdiv($data['other_amount'], 1000, 2);
        // 实付金额总计小于0时，取0
        $data['real_amount'] = $data['real_amount'] < 0 ? 0 : bcdiv($data['real_amount'], 1000, 2);

        if ($data['cost_store_type']) {
            if ($data['cost_store_type'] == 1) {
                $data['cost_store_type_text'] = static::$t->_('global.branch');
            } else {
                $data['cost_store_type_text'] = static::$t->_('payment_cost_store_type_1');
            }
        }

        $expenseIdArr = array_values(array_unique(array_column($data['expense'], "category_b")));

        $expenseMap = CategoryService::getInstance()->getListByIds($expenseIdArr);

        $budgetIds = array_values(array_filter(array_column($data['expense'], 'budget_id')));
        if ($budgetIds) {
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
        }

        $productIds = array_values(array_filter(array_column($data['expense'], 'product_id')));
        if ($productIds) {
            $products = BudgetObjectProduct::find([
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $productIds],
            ])->toArray();
            $products = array_column($products, null, 'id');
        }

        $ledger_id_to_name = [] ;
        $accounting_subjects_to_name = [];
        //17764需求，需要按照科目类型区分1快递公司科目、2子公司科目来查询不同的科目表
        $ledger_account_ids = array_values(array_filter(array_column($data['expense'],'ledger_account_id')));
        //核算科目名字
        if (!empty($ledger_account_ids)) {
            //快递公司科目
            if ($data['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledger_id_to_name = array_column($res['data'],'name','id');
                }
            } else {
                //子公司科目
                $accounting_subjects_to_name = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds($ledger_account_ids);
            }
        }

        //备用金信息
        $data['rfano'] = '';
        $data['petty_used'] = ReimbursementEnums::PETTY_USED_NO;
        $rfrei = ReserveFundReimburse::findFirst([
            'conditions' => 'rei_id = :rei_id:',
            'bind' => ['rei_id' => $data['id'] ?? 0],
        ]);
        if ($rfrei) {
            $data['rfano'] = $rfrei->rfano;
            $data['petty_used'] = ReimbursementEnums::PETTY_USED_YES;
        }

        $wht_type_arr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        $budget_name_lang_suffix = strtolower(substr(static::$language, -2));

        foreach ($data['expense'] as &$item) {
            $item['tax'] = bcdiv($item['tax'], 1000, 2);
            $item['tax_not'] = bcdiv($item['tax_not'], 1000, 2);
            $item['amount'] = bcdiv($item['amount'], 1000, 2);
            $item['rate'] = $this->getViewRate($item['rate']);
            //$item['wht_tax'] = bcdiv($item['wht_tax'], 1000, 2);
            $item['wht_tax'] = (string)$item['wht_tax'] ?? '';
            $item['wht_tax_amount'] = bcdiv($item['wht_tax_amount'], 1000, 2);
            $item['deductible_vat_tax'] = (string)$item['deductible_vat_tax'] ?? '';
            $item['deductible_tax_amount'] = bcdiv($item['deductible_tax_amount'], 1000, 2);
            $item['payable_amount'] = bcdiv($item['payable_amount'], 1000, 2);

            $item['fuel_mileage']       = !empty($item['fuel_mileage']) ? bcdiv($item['fuel_mileage'], 1000, 2) : '';
            $item['fuel_start_mileage'] = !empty($item['fuel_start_mileage']) ? bcdiv($item['fuel_start_mileage'], 1000, 2) : '';
            $item['fuel_end_mileage']   = !empty($item['fuel_end_mileage']) ? bcdiv($item['fuel_end_mileage'], 1000, 2) : '';
            $item['fuel_use_date']      = $item['fuel_use_date'] ?? '';
            $item['fuel_oil_type']      = !empty($item['fuel_oil_type']) ? $item['fuel_oil_type'] : '';

            $item['category_a_text'] = $item['category_a'] ? static::$t->_(Enums::$reimbursement_expense_type[$item['category_a']]) : "";
            $item['category_b_text'] = $item['category_b'] ? static::$t->_($expenseMap[$item['category_b']]) : "";

            $budget_info = $budgets[$item['budget_id']] ?? [];
            if (!empty($budget_info)) {
                $item['budget_text'] = $budget_info['name_' . $budget_name_lang_suffix] ?? '';
                // 科目英文翻译
                $item['budget_text_en'] = $budget_info['name_en'];
                $item['budget_text_th'] = $budget_info['name_th'];
            } else {
                $item['budget_text']    = '';
                $item['budget_text_en'] = '';
                $item['budget_text_th'] = '';
            }

            $product_info = $products[$item['product_id']] ?? [];
            if ($product_info) {
                $item['template_type']   = $product_info['template_type'];
                $item['product_name']    = $product_info['name_' . $budget_name_lang_suffix];
                $item['product_name_en'] = $product_info['name_en'];
                $item['product_name_th'] = $product_info['name_th'];
            } else {
                $item['product_name_en'] = $item['product_name'];
                $item['product_name_th'] = '';
                $item['template_type']   = 0;
            }

            $item['start_at'] = empty($item['start_at']) ? '' : $item['start_at'];
            $item['end_at'] = empty($item['end_at']) ? '' : $item['end_at'];
            $item['cost_store_n_id'] = empty($item['cost_store_n_id']) ? '' : $item['cost_store_n_id'];
            $item['cost_store_n_name'] = empty($item['cost_store_n_name']) ? '' : $item['cost_store_n_name'];
            $item['cost_center_code'] = empty($item['cost_center_code']) ? '' : $item['cost_center_code'];
            $item['wht_type'] = is_numeric($item['wht_type']) ? $item['wht_type'] : '';
            $item['wht_type_name'] = $wht_type_arr[$item['wht_type']] ?? '';

            //核算科目
            $item['ledger_account_name'] = $ledger_id_to_name[$item['ledger_account_id']] ?? ($accounting_subjects_to_name[$item['ledger_account_id']]['subjects_name'] ?? '');

            //重新提交的时候，前端用这个字段
            $item['cost_store_n_pc_code'] = '';

            //如果是网点类型
            if ($data['cost_store_type'] == 1) {
                $item['cost_store_n_pc_code'] = $item['cost_center_code'];
            }

            //19706 出差类型文本
            $item['travel_business_trip_type_text'] = static::$t->_(ReimbursementEnums::$travel_business_trip_type[$item['travel_business_trip_type']] ?? '');
        }

        $data['pay_at'] = $data['pay_at'] ?? '';
        $data['remark'] = $data['remark'] ?? '';
        $data['cost_company_id'] = $data['cost_company_id'] ?? '';
        unset($data['travel_is_have_roommate']);

        //查询公司名称
        $data['cost_company_name'] = '';
        if ($data['cost_company_id']) {
            $company_info = (new DepartmentService())->getCostCompanyByDepartmentId($data['cost_company_id']);
            $data['cost_company_name'] = $company_info['name'] ?? '';
        }

        $data['extra_message'] = $data['extra_message'] ?? '';
        $data['voucher_abstract'] = $data['voucher_abstract'] ?? '';
        $data['is_supplement_invoice'] = isset($data['is_supplement_invoice']) ? intval($data['is_supplement_invoice']) : 0;
        $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
        $data['pay_status_text'] = static::$t->_(Enums::$loan_pay_status[$data['pay_status']]);
        $data['business_type_text'] = $data['business_type'] ? static::$t->_(GlobalEnums::$business_type_item[$data['business_type']]) : '';

        if ($budgetIds) {
            $data['expense_v1'] = $data['expense'];
            unset($data['expense']);
        }

        return $data;
    }


    private function getAuditLogs($req, $item, $if_download = false)
    {
        if (empty($req)) {
            return [];
        }

        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        $pay_staff_id = $this->getReimbursementPayStaffIds();


        //查询支付模块的审批流
        if ($item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT, $item->no);
            if (!empty($payment_data)) {
                $pay_flow_service = new \App\Modules\Pay\Services\PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                if ($if_download) {
                    $auth_logs = array_reverse($auth_logs);
                    return $auth_logs;
                }
                return $auth_logs;
            }
        }
        $us = new UserService();
        //审核通过，待支付，下载不要待支付的,不要开启支付模块
        if ($item->status == Enums::CONTRACT_STATUS_APPROVAL && $item->pay_status == Enums::LOAN_PAY_STATUS_PENDING && !$if_download) {
            $payPendingLogs = [
                'action_name' => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                'audit_at' => $item->approved_at,
                'audit_at_datetime' => $item->approved_at,
                'action' => 5,
                "info" => '',
            ];
            foreach ($pay_staff_id as $staff_id) {
                $current = $us->getUserById($staff_id);
                if (!empty($current)) {
                    //待支付
                    $payPendingLogs['list'][] = [
                        'staff_id' => $staff_id,
                        'staff_name' => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title' => $current->getJobTitle()->name ?? '',
                    ];

                }
            }
            array_unshift($auth_logs, $payPendingLogs);
        }
        //审核通过，已支付或者未支付
        if ($item->status == Enums::CONTRACT_STATUS_APPROVAL && ($item->pay_status == Enums::LOAN_PAY_STATUS_PAY || $item->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY)) {
            if (!empty($item->payer_id)) {
                $current = $us->getUserById($item->payer_id);
                //放入付款
                if ($current && !is_string($current)) {
                    $payLogs = [
                        'staff_id' => $item->payer_id,
                        'staff_name' => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title' => $current->getJobTitle()->name ?? '',
                        'action_name' => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                        'audit_at' => !empty($item->pay_operate_date) ? $item->pay_operate_date : $item->updated_at,
                        'audit_at_datetime' => !empty($item->pay_operate_date) ? $item->pay_operate_date : $item->updated_at,
                        'action' => 5,
                        "info" => '',
                    ];
                    array_unshift($auth_logs, $payLogs);
                }
            }
        }
        //下载要正序
        if ($if_download) {
            $auth_logs = array_reverse($auth_logs);
        }
        return $auth_logs;
    }


    /**
     * @param $id
     * @param int $uid
     * @param int $type
     * @return array
     * @throws GuzzleException
     */
    public function download($id, $uid = 0, $type = 0)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = static::$t->_('success');
        $real_message = '';

        $lang = $this->getLang();

        $url_pdf = '';

        try {
            // 借款支付详情: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getReimbursementPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $data = $this->getDetail($id, $uid, true);
            if (empty($data['id'])) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            if (!$this->isCanDownload($data, $uid)) {
                throw new ValidationException(static::$t->_('no_download_permission'), ErrCode::$VALIDATE_ERROR);
            }

            //表头
            $head = [
                //差旅
                Enums::REIMBURSEMENT_EXPENSE_TRAVEL => [
                    'start_at',
                    'travel_start',
                    'travel_end',
                ],
                //油费
                Enums::REIMBURSEMENT_EXPENSE_FUEL => [
                    'fuel_start',
                    'fuel_end',
                    'fuel_mileage',
                ],
                //水电费，饮水费算一个
                Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER => [
                    'cost_store_name',
                ],
            ];

            $countA = [];
            $countB = [];

            //如果费用明细，或者审批流程有为null的，显示-
            if (isset($data['expense'])) {
                foreach ($data['expense'] as $kk => $item) {
                    foreach ($item as $k => $v) {
                        if ($k == 'travel_start_at' || $k == 'travel_end_at') {
                            continue;
                        }

                        if ($this->isEmpty($v)) {
                            $data['expense'][$kk][$k] = '-';
                        }
                    }

                    if (empty($countA[$item['category_a']])) {
                        $countA[$item['category_a']] = 0;
                    }

                    $countA[$item['category_a']]++;

                    //统计
                    if ($item['category_b'] == Enums::REIMBURSEMENT_EXPENSE_WATER_AND_ELE) {
                        $item['category_b'] = Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER;
                    }

                    if (empty($countB[$item['category_b']])) {
                        $countB[$item['category_b']] = 0;
                    }

                    $countB[$item['category_b']]++;
                }
            }

            //是否补交
            $data['is_additional'] = false;
            $paperDocumentDetail = ConfirmationService::getInstance()->getDetailInfo($data['no']);
            if (!empty($paperDocumentDetail)) {
                $data['is_additional'] = $paperDocumentDetail->is_additional == Enums\PaperDocumentEnums::IS_ADDITIONAL_YES &&
                    $paperDocumentDetail->confirm_status = Enums\PaperDocumentEnums::CONFIRM_STATE_COMPLETE;
            }

            //获取二维码
            $data['qr_code'] = $this->getQrCode($data['no']);

            //主要用来算合并多少个格
            $data['head'] = [];
            $data['head'][] = 'detail_id';
            $data['head'][] = 'category_a';
            $data['head'][] = 'category_b';
            $data['head'][] = 'info';
            $data['field_flag']['travel_flag'] = 0;
            $data['field_flag']['fuel_flag'] = 0;
            $data['field_flag']['water_flag'] = 0;

            if (!empty($countA[Enums::REIMBURSEMENT_EXPENSE_TRAVEL])) {
                $data['head'] = array_merge($data['head'], $head[Enums::REIMBURSEMENT_EXPENSE_TRAVEL]);
                $data['field_flag']['travel_flag'] = 1;
            }

            if (!empty($countB[Enums::REIMBURSEMENT_EXPENSE_FUEL])) {
                $data['head'] = array_merge($data['head'], $head[Enums::REIMBURSEMENT_EXPENSE_FUEL]);
                $data['field_flag']['fuel_flag'] = 1;
            }

            if (!empty($countB[Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER])) {
                $data['head'] = array_merge($data['head'], $head[Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER]);
                $data['field_flag']['water_flag'] = 1;
            }

            $data['head'][] = 'tax_not';
            $data['head'][] = 'wht_tax_amount';
            $data['head'][] = 'tax';
            $data['head'][] = 'payable_amount';
            foreach ($data['auth_logs'] as $kk => $item) {
                foreach ($item as $k => $v) {
                    if ($this->isEmpty($v)) {
                        $data['auth_logs'][$kk][$k] = '-';
                    }
                }
            }


            $fieldArr = $this->getLangKeyArr();
            foreach ($fieldArr as $val) {
                $data['field'][$val] = static::$t->_('re_field_' . $val);
            }

            //付款项列名
            $data['field']['country_code_project'] = static::$t->_('country_code_project');
            $data['field']['cost_company_name'] = static::$t->_('re_field_cost_company_name');

            //是否补交
            $data['field']['additional'] = static::$t->_('global.additional');
            $ccArr = static::getLangCountryArr();
            foreach ($ccArr as $v) {
                $data['field'][$v['code']] = static::$t->_($v['text_key']);
            }

            $file_path = $file_dir = sys_get_temp_dir() . '/';
            $file_name = 'reimbursement_' . md5($id) . '_' . $lang . '_' . date('ymdHis') . '.pdf';
            $file_full_path = $file_path . $file_name;

            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            $view->setVars($data);
            $view->start();
            $view->disableLevel([
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]);

            if (isset($data['expense_v1']) && $data['expense_v1']) {
                $view->render('reimbursement', 'apply_v1');
            } else {
                $view->render('reimbursement', 'apply');
            }

            $view->finish();
            $content = $view->getContent();
            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode' => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader('');
            $mpdf->SetHTMLFooter('');
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_full_path, 'f');

            // 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_full_path, $file_full_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_full_path);
            if (!empty($upload_res['object_url'])) {
                $url_pdf = $upload_res['object_url'];
            } else {
                throw new ValidationException(static::$t->_('rent_contract_download_fail_error'), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException | MpdfException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('reimbursement-detail-download-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => ['url' => $url_pdf],
        ];

    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }

    private function isEmpty($val)
    {
        if (!isset($val)) {
            return true;
        }

        if (is_string($val) && strlen($val) == 0) {
            return true;
        }
        return false;
    }

    /**
     * DESC: 预算科目表 模板
     *
     * @param $budget_ids
     * @return mixed
     */
    public function getBudgetObjectTemplateType($budget_ids)
    {
        $budget_ids = array_values(array_filter($budget_ids));
        if (empty($budget_ids)) {
            return [];
        }

        $list = BudgetObject::find([
            'conditions' => ' id in ({ids:array})',
            'bind'       => ['ids' => $budget_ids],
            'columns'    => 'id, template_type',
        ])->toArray();
        return array_column($list, 'template_type', 'id');
    }

    /**
     * DESC: 报销关联的发票单号
     *
     * @param $detail_ids array
     * @return mixed
     */
    public function getTicketList($detail_ids = [])
    {
        return ReimbursementDetailTicketModel::find([
            'conditions' => ' detail_id in ({ids:array})',
            'bind' => ['ids' => $detail_ids],
            'group' => 'detail_id',
            'columns' => 'detail_id,group_concat(invoice_no) invoices_no',
        ])->toArray();
    }

    /**
     * 获取前端展示的vat税率
     *
     * @param number $rate vat税率,表中的原值
     * @return string
     */
    public function getViewRate($rate)
    {
        //先除1000,转为0.06, 兼容以前的逻辑(除1000可以直接用于计算),再乘100转为前端的枚举
        $rate = (string)bcdiv($rate, 1000, 4);
        $rate = (string)bcmul($rate, 100, 2);
        $rate = rtrim(rtrim($rate, '0'), '.');

        // 非数值的展示为空: vat配置有删减, 旧值在回显时, 直接拼接%即可
        if (!is_numeric($rate)) {
            $rate = '';
        }
        return $rate;
    }

    /**
     * 获取当前用户的草稿
     *
     * @param array $user
     * @return mixed
     * @throws ValidationException
     */
    public function getUserDraft(array $user)
    {
        $draft_model = ReimbursementDraftModel::findFirst([
            'conditions' => 'created_id = :created_id:',
            'bind' => ['created_id' => $user['id']],
        ]);
        if (empty($draft_model)) {
            throw new ValidationException(static::$t->_('reimbursement_draft_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        $item = json_decode($draft_model->content, true);
        $item['lno'] = '';

        //获取报销发票类型配置
        $reimbursement_invoice_type_enums = EnumsService::getInstance()->getSettingEnvValueMap('sys_module_reimbursement_invoice_type_enums');
        foreach ($item['expense'] as &$detail) {
            // 发票类型
            if (!empty($detail['invoice_type'])) {
                $detail['invoice_type'] = $detail['invoice_type'];
                $detail['invoice_type_label'] = isset($reimbursement_invoice_type_enums[$detail['invoice_type']]) ? static::$t->_($reimbursement_invoice_type_enums[$detail['invoice_type']]) : '';
            } else {
                $detail['invoice_type'] = '';
                $detail['invoice_type_label'] = '';
            }

            $invoices_ids = [];
            if (!empty($detail['invoices_ids'])) {
                foreach ($detail['invoices_ids'] as $invoices_id) {
                    $invoices_ids[] = [
                        'detail_id' => '',
                        'invoice_no' => $invoices_id,
                    ];
                }
            }
            $detail['invoices_ids'] = $invoices_ids;

            // 是否有增值税票
            $detail['is_with_vat_invoice'] = $detail['is_with_vat_invoice'] ? $detail['is_with_vat_invoice'] : '';

            // 支付方式
            $detail['payment_method'] = $detail['payment_method'] ? $detail['payment_method'] : '';
        }

        $country_code_text = $this->getLangCountryArr();
        $country_code_text = array_column($country_code_text, 'text_key', 'code');
        $item['country_code_text'] = static::$t->_($country_code_text[$item['country_code']] ?? '');

        // 处理公共格式
        $item = $this->handleData($item);

        $setting_data = EnumsService::getInstance()->getMultiEnvByCode(['reimbursement_is_restrict_self_currency', 'sys_module_reimbursement_validation_tax_no', 'sys_module_reimbursement_auto_tax_no']);

        // 增加返回字段 是否限制本位币 "1" : 限制  "0" : 不限制
        $item['is_restrict_currency'] = '0';
        if (!empty($setting_data['reimbursement_is_restrict_self_currency'])) {
            $item['is_restrict_currency'] = $setting_data['reimbursement_is_restrict_self_currency'];
        }

        // 是否启用发票税务号校验(长度和字符内容)
        $item['is_validation_tax_no'] = '0';
        if (!empty($setting_data['sys_module_reimbursement_validation_tax_no'])) {
            $item['is_validation_tax_no'] = $setting_data['sys_module_reimbursement_validation_tax_no'];
        }

        // 是否自动填充发票税务号
        $item['is_auto_tax_no'] = '0';
        if (!empty($setting_data['sys_module_reimbursement_auto_tax_no'])) {
            $item['is_auto_tax_no'] = $setting_data['sys_module_reimbursement_auto_tax_no'];
        }

        return $item;
    }

    /**
     * 获取当前明细行网点支援列表
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getDetailStoreSupportList($params)
    {
        // 获取报销详情数据
        $detail_model = Detail::getFirst([
            'conditions' => 'id = :id: AND re_id = :re_id:',
            'bind'       => ['id' => $params['detail_id'], 're_id' => $params['reimbursement_id']],
        ]);
        if (empty($detail_model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['reimbursement_id'] . '-' . $params['detail_id']]), ErrCode::$VALIDATE_ERROR);
        }

        $support_serial_no = $detail_model->getSupportSerialNo()->toArray();
        $support_serial_no = array_column($support_serial_no, 'support_serial_no');
        $items             = HrStaffApplySupportStoreRepository::getInstance()->getListBySupportNoList($support_serial_no);
        return $this->handleStoreSupportList($items);
    }

    /**
     * 获取指定支援单号的详情
     * @param $support_serial_no_item
     * @return mixed
     */
    public function getStoreSupportNoDetail($support_serial_no_item)
    {
        $support_serial_no_item = array_unique(array_filter($support_serial_no_item));
        $items                  = HrStaffApplySupportStoreRepository::getInstance()->getListBySupportNoList($support_serial_no_item);
        return $this->handleStoreSupportList($items);
    }

    /**
     * 获取当前明细行网点支援列表
     * @param $params
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function exportDetailStoreSupportList($params)
    {
        $items = $this->getDetailStoreSupportList($params);

        $execl_data = [];
        foreach ($items as $val) {
            $execl_data[] = [
                $val['support_serial_no'],
                $val['staff_info_id'],
                $val['staff_info_name'],
                $val['staff_store_name'],
                $val['support_store_name'],
                $val['employment_begin_date'],
                $val['employment_end_date'],
                $val['employment_days'],
                $val['status_text'],
            ];
        }

        $header = [
            static::$t->_('reimbursement_support_export_001'),
            static::$t->_('reimbursement_support_export_002'),
            static::$t->_('reimbursement_support_export_003'),
            static::$t->_('reimbursement_support_export_004'),
            static::$t->_('reimbursement_support_export_005'),
            static::$t->_('reimbursement_support_export_006'),
            static::$t->_('reimbursement_support_export_007'),
            static::$t->_('reimbursement_support_export_008'),
            static::$t->_('reimbursement_support_export_009'),
        ];

        $file_name = 'Reimbursement branch support details - ' . date('YmdHis') . '.xlsx';
        $export_res = $this->exportExcel($header, $execl_data, $file_name);
        if (empty($export_res['data'])) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }

        return $export_res['data'];
    }

    /**
     * 获取当前明细行出差共同住宿单列表
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getDetailTravelRoommateList($params)
    {
        // 获取报销详情数据
        $detail_model = Detail::getFirst([
            'conditions' => 'id = :id: AND re_id = :re_id:',
            'bind'       => ['id' => $params['detail_id'], 're_id' => $params['reimbursement_id']],
        ]);
        if (empty($detail_model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null',
                ['biz_id' => $params['reimbursement_id'] . '-' . $params['detail_id']]), ErrCode::$VALIDATE_ERROR);
        }

        $list           = $detail_model->getTravelRoommateRelList()->toArray();
        $serial_no_item = array_column($list, 'serial_no');
        $business_trip_list = $this->getBusinessTripListFromBy($serial_no_item);
        $business_trip_list = array_column($business_trip_list, null, 'serial_no');

        foreach ($list as &$v) {
            $trip_info = $business_trip_list[$v['serial_no']] ?? [];

            $v['destination'] = '';
            $v['start_time']  = '';
            $v['end_time']    = '';
            if (!empty($trip_info)) {
                $v['business_trip_type'] = $trip_info['business_trip_type_label'];
                $v['destination']        = $trip_info['destination_label'];
                $v['start_time']         = $trip_info['start_time'];
                $v['end_time']           = $trip_info['end_time'];
            }

            $v['confirm_status'] = static::$t->_(ReimbursementEnums::$travel_roommate_confirm_status_item[$v['confirm_status']]);
            $v['confirm_at']     = $v['confirm_at'] ?? '';
        }

        return $list;
    }

    /**
     * 获取共同住宿出差编号详情
     */
    protected function getBusinessTripListFromBy($serial_no_item)
    {
        if (empty($serial_no_item)) {
            return [];
        }

        $ac = new ApiClient('by', '', 'getBusinessTripList', static::$language);
        $ac->setParams([
            ['serial_no_item' => $serial_no_item],
        ]);
        $res = $ac->execute();

        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::$SUCCESS) {
            $this->logger->notice('报销-获取共同住宿出差单列表异常[rpc], ' . ($res['result']['message']) ?? '');
        }

        return $res['result']['data'] ?? [];
    }

    /**
     * 获取明细行的BY出差单信息
     * @param $params
     * @param $user
     * @return mixed
     * @throws ValidationException
     */
    public function getDetailTravelNoInfo($params, $user)
    {
        // 获取报销详情数据
        $detail_model = Detail::getFirst([
            'conditions' => 'id = :id: AND re_id = :re_id:',
            'bind'       => ['id' => $params['detail_id'], 're_id' => $params['reimbursement_id']],
        ]);
        if (empty($detail_model->travel_serial_no)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null',
                ['biz_id' => $params['reimbursement_id'] . '-' . $params['detail_id']]), ErrCode::$VALIDATE_ERROR);
        }

        $ac = new ApiClient('hcm_rpc', '', 'get_business_trip_info', static::$language);
        $ac->setParams([
            [
                'serial_no' => $detail_model->travel_serial_no,
                'staff_id'  => $user['id'],
            ],
        ]);
        $res = $ac->execute();

        if (!isset($res['result']['code']) || $res['result']['code'] != ErrCode::$SUCCESS) {
            $this->logger->notice('报销-获取BY出差申请单详情异常[rpc], ' . ($res['result']['message']) ?? '');
        }

        return $res['result']['data'] ?? [];
    }

    /**
     * 获取共同住宿确认详情
     *
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getConfirmedDetail($params)
    {
        $order_no = $params['order_no'];
        $user_id  = $params['user_id'];

        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->created_id != $user_id) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
        }

        // 获取报销所有行关联的共同住宿出差单号
        $travel_roommate_list = $this->formatTravelRoommateRel($model->getDetailTravelRoommateRel()->toArray(), false);
        $serial_no_item       = array_column($travel_roommate_list, 'serial_no');
        $business_trip_list   = $this->getBusinessTripListFromBy($serial_no_item);
        $business_trip_list   = array_column($business_trip_list, null, 'serial_no');

        foreach ($travel_roommate_list as &$v) {
            $trip_info = $business_trip_list[$v['serial_no']] ?? [];

            $v['destination'] = '';
            $v['start_time']  = '';
            $v['end_time']    = '';
            if (!empty($trip_info)) {
                $v['destination'] = $trip_info['destination_label'];
                $v['start_time']  = $trip_info['start_time'];
                $v['end_time']    = $trip_info['end_time'];
            }

            $v['confirm_status_label'] = static::$t->_(ReimbursementEnums::$travel_roommate_confirm_status_item[$v['confirm_status']]);
            $v['confirm_at']           = $v['confirm_at'] ?? '';
        }

        return array_sort($travel_roommate_list, 'serial_no', SORT_ASC);
    }

    /**
     * 共同住宿人去确认的信息
     *
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function goConfirmInfo($params)
    {
        $order_no = $params['order_no'];
        $user_id  = $params['user_id'];

        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
        }

        $base_info = [
            'no'           => $model->no,
            'created_id'   => $model->created_id,
            'created_name' => $model->created_name,
            'apply_id'     => $model->apply_id,
            'apply_name'   => $model->apply_name,
        ];

        $columns = [
            'detail.travel_serial_no',
            'detail.amount',
            'roommate_rel.serial_no',
            'roommate_rel.detail_id',
            'roommate_rel.apply_staff_id AS roommate_staff_id',
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['roommate_rel' => ReimbursementDetailTravelRoommateRelModel::class]);
        $builder->leftjoin(Reimbursement::class, 'roommate_rel.re_id = main.id', 'main');
        $builder->leftjoin(Detail::class, 'detail.id = roommate_rel.detail_id', 'detail');
        $builder->where('main.no = :no: AND main.status = :status:', [
            'no'     => $order_no,
            'status' => ReimbursementEnums::STATUS_WAITING_CONFIRMED,
        ]);

        $builder->andWhere('roommate_rel.apply_staff_id = :apply_staff_id: AND roommate_rel.confirm_status = :confirm_status:', [
            'apply_staff_id' => $user_id,
            'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
        ]);

        $item = $builder->getQuery()->execute()->toArray();
        if (empty($item)) {
            return [];
        }

        $travel_serial_no_item = array_column($item, 'travel_serial_no');
        $serial_no_item        = array_column($item, 'serial_no');
        $serial_no_item        = array_merge($travel_serial_no_item, $serial_no_item);

        $business_trip_list = $this->getBusinessTripListFromBy($serial_no_item);
        $business_trip_list = array_column($business_trip_list, null, 'serial_no');

        GlobalEnums::getSysDefaultCurrency();
        $currency_symbol_simple = GlobalEnums::$sys_default_currency_symbol_simple;

        foreach ($item as &$v) {
            // 申请人出差信息
            $apply_trip_info        = $business_trip_list[$v['travel_serial_no']] ?? [];
            $v['apply_destination'] = '';
            $v['apply_start_time']  = '';
            $v['apply_end_time']    = '';
            if (!empty($apply_trip_info)) {
                $v['apply_destination'] = $apply_trip_info['destination_label'];
                $v['apply_start_time']  = $apply_trip_info['start_time'];
                $v['apply_end_time']    = $apply_trip_info['end_time'];
            }

            // 共同住宿人出差信息
            $roommate_trip_info        = $business_trip_list[$v['serial_no']] ?? [];
            $v['roommate_destination'] = '';
            $v['roommate_start_time']  = '';
            $v['roommate_end_time']    = '';
            if (!empty($roommate_trip_info)) {
                $v['roommate_destination'] = $roommate_trip_info['destination_label'];
                $v['roommate_start_time']  = $roommate_trip_info['start_time'];
                $v['roommate_end_time']    = $roommate_trip_info['end_time'];
            }

            $v['amount'] = $currency_symbol_simple . bcdiv($v['amount'], 1000, 2);
        }

        $item = array_sort($item, 'detail_id', SORT_ASC);

        return [
            'base_info' => $base_info,
            'item'      => $item,
        ];
    }

    /**
     * 获取详情数据公共处理方法
     */
    public function getDetailCommonData($main_model)
    {
        $data = $main_model->toArray();

        $country_code = get_country_code();

        // 获取报销所有行关联的支援单号
        $support_serial_no_list = $this->formatDetailSupportRel($main_model->getDetailSupportRelSerialNo()->toArray());

        // 获取报销所有行关联的共同住宿出差单号
        $travel_roommate_list = $this->formatTravelRoommateRel($main_model->getDetailTravelRoommateRel()->toArray());

        // 明细行
        $detail_list = $main_model->getDetails()->toArray();
        $detail_ids = array_column($detail_list, 'id');

        // 获取明细行附件
        $detail_files = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, Enums::OSS_BUCKET_TYPE_REIMBURSEMENT, 0, false);

        // 预算科目模板类型
        $budget_ids               = array_column($detail_list, 'budget_id');
        $budget_template_type_map = $this->getBudgetObjectTemplateType($budget_ids);

        // 获取明细行发票
        $detail_tickets_map = [];
        if (!empty($detail_ids)) {
            $detail_tickets_map = ReimbursementDetailTicketModel::find([
                'conditions' => 'detail_id IN ({detail_ids:array})',
                'bind'       => ['detail_ids' => $detail_ids],
                'columns'    => ['detail_id', 'invoice_no'],
            ])->toArray();
            $detail_tickets_map = array_group_by_column($detail_tickets_map, 'detail_id');
        }

        // 明细行补充附件
        $attach_supplement_list = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE);

        // 明细行支付凭证
        $detail_payment_voucher = [];
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            $detail_payment_voucher = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_PAYMENT_VOUCHER, 0, false);
        }

        $exceeds_standard_amount_email_file = []; // 报销超出标准金额附件
        $fuel_start_mileage_file            = []; // 油费里程照片(开始里程)
        $fuel_end_mileage_file              = []; // 油费里程照片(结束里程)
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $exceeds_standard_amount_email_file = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, ReimbursementEnums::OSS_BUCKET_TYPE_EXCEEDS_STANDARD_AMOUNT_EMAIL_FILE, 0, false);

            $fuel_start_mileage_file = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_START_MILEAGE_FILE, 0, false);

            $fuel_end_mileage_file = SysAttachmentRepository::getInstance()->getAttachmentsListById($detail_ids, ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_END_MILEAGE_FILE, 0, false);
        }

        //获取报销发票类型配置
        $reimbursement_invoice_type_enums = EnumsService::getInstance()->getSettingEnvValueMap('sys_module_reimbursement_invoice_type_enums');

        $data['expense'] = [];
        foreach ($detail_list as $detail_info) {
            // 附件
            $detail_info['attachments'] = $detail_files[$detail_info['id']] ?? [];

            // 补充附件
            $detail_info['required_supplement_file'] = $attach_supplement_list[$detail_info['id']] ?? [];

            // 当时 劳务成本-外包， 劳务成本-外协  前端模板显示会变化
            $detail_info['budget_template_type'] = (int)($budget_template_type_map[$detail_info['budget_id']] ?? 0);

            // 发票编号
            $detail_info['invoices_ids'] = $detail_tickets_map[$detail_info['id']] ?? [];

            // 发票类型
            if (!empty($detail_info['invoice_type'])) {
                $detail_info['invoice_type']       = $detail_info['invoice_type'];
                $detail_info['invoice_type_label'] = isset($reimbursement_invoice_type_enums[$detail_info['invoice_type']]) ? static::$t->_($reimbursement_invoice_type_enums[$detail_info['invoice_type']]) : '';
            } else {
                $detail_info['invoice_type']       = '';
                $detail_info['invoice_type_label'] = '';
            }

            // 是否有增值税票
            $detail_info['is_with_vat_invoice'] = !empty($detail_info['is_with_vat_invoice']) ? $detail_info['is_with_vat_invoice'] : '';

            // 支付方式
            $detail_info['payment_method'] = !empty($detail_info['payment_method']) ? $detail_info['payment_method'] : '';

            // 支付凭证
            $detail_info['payment_voucher'] = [];
            if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                $detail_info['payment_voucher'] = $detail_payment_voucher[$detail_info['id']] ?? [];
            }

            // 支援单号
            $detail_info['support_serial_no']          = implode(',', $support_serial_no_list[$detail_info['id']] ?? []);
            $detail_info['support_is_followed_policy'] = !empty($detail_info['support_is_followed_policy']) ? $detail_info['support_is_followed_policy'] : '';

            // 共同住宿出差单
            $detail_info['travel_roommate_item']    = $travel_roommate_list[$detail_info['id']] ?? [];
            $detail_info['travel_is_have_roommate'] = !empty($detail_info['travel_is_have_roommate']) ? $detail_info['travel_is_have_roommate'] : '';

            $detail_info['exceeds_standard_amount_email_file'] = [];
            $detail_info['fuel_start_mileage_file']            = [];
            $detail_info['fuel_end_mileage_file']              = [];
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $detail_info['exceeds_standard_amount_email_file'] = $exceeds_standard_amount_email_file[$detail_info['id']] ?? [];
                $detail_info['fuel_start_mileage_file']            = $fuel_start_mileage_file[$detail_info['id']] ?? [];
                $detail_info['fuel_end_mileage_file']              = $fuel_end_mileage_file[$detail_info['id']] ?? [];
            }

            $data['expense'][] = $detail_info;
        }

        $country_code_text         = $this->getLangCountryArr();
        $country_code_text         = array_column($country_code_text, 'text_key', 'code');
        $data['country_code_text'] = static::$t->_($country_code_text[$data['country_code']] ?? '');

        // 查询是否有冲减借款 根据不同状态查询不同状态借款信息
        $data['lno']     = '';
        $is_deleted  = GlobalEnums::IS_NO_DELETED;
        if ($data['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY || in_array($data['status'], [Enums::CONTRACT_STATUS_REJECTED, Enums::CONTRACT_STATUS_CANCEL])) {
            $is_deleted = GlobalEnums::IS_DELETED;//删除状态
        }

        $rel_loan = ReimbursementRelLoan::getFirst([
            'conditions' => 're_id = :re_id: and is_deleted = :is_deleted:',
            'bind'       => ['re_id' => $data['id'], 'is_deleted' => $is_deleted],
            'columns'    => 'loan_id',
        ]);
        if (!empty($rel_loan)) {
            $data['lno']     = Loan::getFirst([
                    'conditions' => 'id = :id:',
                    'columns'    => 'lno',
                    'bind'       => ['id' => $rel_loan->loan_id],
                ])->lno ?? '';
        }

        //纸质文件确认状态
        $data['confirm_status_text'] = !empty($data['confirm_status'])
            ? static::$t->_('confirm_state_'. $data['confirm_status'])
            : '';

        $confirmInfo = ConfirmationService::getInstance()->getConfirmList([$data['no']], ['serial_no','last_confirm_staff_ids', 'reason_type', 'reason_text']);
        $confirmInfo = array_column($confirmInfo, null,'serial_no');
        $confirmDetailInfo = $confirmInfo[$data['no']] ?? [];

        //最后确认人
        $data['last_confirm_staff_ids']  = $confirmDetailInfo['last_confirm_staff_ids'] ?? '';
        $data['last_confirm_staff_info'] = '';
        $lastConfirmStaffIds             = !empty($confirmDetailInfo['last_confirm_staff_ids'])
            ? explode(',', $confirmDetailInfo['last_confirm_staff_ids'])
            : '';
        if (!empty($lastConfirmStaffIds)) {
            $staffInfo                       = (new HrStaffInfoService())->getStaffInfoByIds($lastConfirmStaffIds,
                'staff_info_id,name,state');
            $staffInfo                       = array_column($staffInfo, null, 'staff_info_id');

            $data['last_confirm_staff_info'] = ConfirmationService::getInstance()->formatLastStaffInfo($lastConfirmStaffIds,
                $staffInfo);
        }

        $reasonTypeListMap = SysService::getInstance()->getConfirmReason();
        $reasonTypeListMap = array_column($reasonTypeListMap, 'label', 'value');

        $reasonType = $confirmDetailInfo['reason_type'] ?? [];
        $reasonType = !empty($reasonType) ? json_decode($reasonType) : [];
        $data['confirm_reason'] = ConfirmationService::getInstance()->formatReason(
            $reasonType,
            $confirmDetailInfo['reason_text'] ?? '',
            $reasonTypeListMap);

        return $this->handleData($data);
    }

    /**
     * 获取报销详情(移动端)
     *
     * @param $order_no
     * @param $user
     * @param $view_type
     * @return mixed
     */
    public function getDetailFromMobile($order_no, $user, $view_type)
    {
        $main_model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);

        if (empty($main_model)) {
            return [];
        }

        // 是否是 [一线操作]的员工 在[去签字]取数
        $is_go_sign_frontline_operator = $view_type == ReimbursementEnums::MOBILE_DETAIL_PAGE_TYPE_GO_SIGN && $user['job_position_type'] == ReimbursementEnums::POSITION_TYPE_1;

        // 获取详情页主数据之外的公共数据
        $data    = $this->getDetailCommonData($main_model);
        $expense = $data['expense_v1'] ?? $data['expense'];

        $date_connector = static::$t->_('global_date_connector');
        foreach ($expense as &$v) {
            $v['cost_period']          = $v['start_at'] . $date_connector . $v['end_at'];
            $v['invoices_ids']         = implode(',', array_column($v['invoices_ids'], 'invoice_no'));
            $v['travel_roommate_item'] = implode(',', array_column($v['travel_roommate_item'], 'serial_no'));
            $v['rate']                 = $v['rate'] != '' ? $v['rate'] . '%' : '';
            $v['wht_tax']              = $v['wht_tax'] != '' ? $v['wht_tax'] . '%' : '';
            $v['deductible_vat_tax']   = $v['deductible_vat_tax'] != '' ? $v['deductible_vat_tax'] . '%' : '';

            // 发票种类
            $v['ticket_type_label'] = static::$t->_(GlobalEnums::$reimbursement_ticket_type[$v['ticket_type']]);

            // 是否有发票号
            $v['is_ticket_no_exist_label'] = static::$t->_('global_bool_status_' . $v['is_ticket_no_exist']);

            // 燃油类型
            $v['fuel_oil_type_label'] = !empty($v['fuel_oil_type']) ? static::$t->_(ReimbursementEnums::$oil_type_item[$v['fuel_oil_type']]) : '';

            // 是否有共同住宿人
            $v['travel_is_have_roommate_label'] = '';
            if (!empty($v['travel_is_have_roommate'])) {
                $v['travel_is_have_roommate_label'] = static::$t->_('global_bool_status_' . ($v['travel_is_have_roommate'] == 1 ? 1 : 0));
            }

            $value_fileds = [];
            foreach ($v as $field_k => $field_v) {
                // 跳过无需前端回显的字段
                if (!in_array($field_k, ReimbursementEnums::$mobile_detail_page_show_fields)) {
                    continue;
                }

                // 去签字页面, 一线操作员工 展示的字段
                if ($is_go_sign_frontline_operator && !in_array($field_k, ReimbursementEnums::$mobile_sign_page_frontline_operator_expense_fields)) {
                    continue;
                }

                // 附件类型处理
                if (in_array($field_k, ReimbursementEnums::$mobile_detail_page_attachment_fields)) {
                    $file_item = [];
                    if (!empty($field_v)) {
                        foreach ($field_v as $file_info) {
                            $file_item[] = [
                                'file_name'  => $file_info['file_name'] ?? '',
                                'object_url' => gen_file_url($file_info),
                            ];
                        }

                        $field_v = $file_item;
                    } else {
                        $field_v = null;
                    }

                    $value_fileds[$field_k] = $field_v;
                    continue;
                }

                // 0 值字段重置为空
                if (in_array($field_k, ReimbursementEnums::$mobile_datail_page_zero_value_reset_fields)) {
                    $field_v = $field_v == 0 ? '' : $field_v;
                }

                $value_fileds[$field_k] = $field_v ?? '';
            }

            $v = $value_fileds;
        }

        $expense_info = [
            'invoice_header_name' => $data['invoice_header_name'] ?? '',           // 发票抬头
            'expense'             => $expense,                                     // 报销实质
        ];

        $base_info = [
            'no'                      => $data['no'],
            'created_id'              => $data['created_id'],
            'created_name'            => $data['created_name'],
            'created_department_name' => $data['created_department_name'],
            'apply_id'                => $data['apply_id'],
            'apply_name'              => $data['apply_name'],
            'apply_company_name'      => $data['apply_company_name'],
            'apply_department_name'   => $data['apply_department_name'],
            'apply_store_name'        => $data['apply_store_name'],
            'apply_mobile'            => $data['apply_mobile'],
            'cost_company_name'       => $data['cost_company_name'],
            'cost_department_name'    => $data['cost_department_name'],
            'country_code_text'       => $data['country_code_text'],
            'cost_store_type_text'    => $data['cost_store_type_text'],
            'apply_date'              => $data['apply_date'],
            'business_type_text'      => $data['business_type_text'],
            'extra_message'           => $data['extra_message'],
            'voucher_abstract'        => $data['voucher_abstract'],
            'status'                  => $data['status'],
            'pay_status'              => $data['pay_status'],
        ];

        $bank_info = $reserve_fund_info = $amount_info = $supplement_invoice_info = $payment_info = $workflow_log = [];
        if (!$is_go_sign_frontline_operator) {
            // 银行信息
            $bank_info = [
                'currency_text' => $data['currency_text'],                                                                  // 币种
                'bank_name'     => $data['bank_name'] ?? '',                                                                // 收款人户名
                'bank_account'  => !empty($data['bank_account']) ? '******' . mb_substr($data['bank_account'], -4, 4) : '', // 收款人账号
                'bank_type'     => $data['bank_type'] ?? '',                                                                // 收款人开户银行
            ];

            // 是否使用备用金
            $reserve_fund_info = [
                'petty_used_label' => static::$t->_('global_bool_status_' . $data['petty_used']), // 是否使用备用金
                'rfano'            => $data['rfano'],                                             // 使用备用金单号
            ];

            // 金额总计
            $amount_info = [
                'amount'             => $data['amount'],              // 发票金额总计（含VAT含WHT
                'payable_amount_all' => $data['payable_amount_all'],  // 应付金额总计（含VAT不含WHT）
                'real_amount'        => $data['real_amount'],         // 实付金额总计
                'loan_amount'        => $data['loan_amount'],         // 冲减借款金额
                'lno'                => '',                           // 冲减借款单号
            ];

            // 是否需要补充发票
            if ($data['is_supplement_invoice']) {
                // 是否需要补充发票
                $is_supplement_invoice_key = ReimbursementEnums::$supplement_invoice_status[$data['is_supplement_invoice']] ?? '';
                $supplement_invoice_info   = [
                    'is_supplement_invoice_text' => !empty($is_supplement_invoice_key) ? static::$t->_($is_supplement_invoice_key) : '',
                ];
            }

            // 支付信息
            if (in_array($data['pay_status'], [ReimbursementEnums::PAY_STATUS_PAY, ReimbursementEnums::PAY_STATUS_UN_PAY])) {
                $is_pay_status_enums                = $data['pay_status'] == ReimbursementEnums::PAY_STATUS_PAY ? 1 : 0;
                $payment_info['is_pay_status_text'] = static::$t->_('global_bool_status_' . $is_pay_status_enums); // 是否已付款
                $payment_info['pay_bank_name']      = '';                                                          // 付款银行
                $payment_info['pay_bank_account']   = '';                                                          // 付款账号
                $payment_info['apply_name']         = '';                                                          // 签收人
                $payment_info['bank_flow_date']     = '';                                                          // 银行流水日期
                $payment_info['remark']             = $data['remark'];                                             // 备注

                if ($data['pay_status'] == ReimbursementEnums::PAY_STATUS_PAY) {
                    $payment_info['pay_bank_name']    = $data['pay_bank_name'];                                            // 付款银行
                    $payment_info['pay_bank_account'] = $data['pay_bank_account'];                                         // 付款账号
                    $payment_info['apply_name']       = $data['apply_name'];                                               // 签收人
                    $payment_info['bank_flow_date']   = $data['pay_at'] ? mb_substr($data['pay_at'], 0, 10) : '';          // 银行流水日期
                }
            }

            // 审批流日志
            if (in_array($main_model->status, ReimbursementEnums::$audit_status_item)) {
                $req          = (new ReimbursementFlowService())->getRequest($main_model->id);
                $workflow_log = $this->getAuditLogs($req, $main_model);
            }
        }

        unset($data);

        return [
            'base_info'               => $base_info,                                                                            // 基本信息
            'bank_info'               => !empty($bank_info) ? $bank_info : null,                                                // 银行信息
            'expense_info'            => $expense_info,                                                                         // 报销明细
            'reserve_fund_info'       => !empty($reserve_fund_info) ? $reserve_fund_info : null,                                // 是否使用备用金
            'amount_info'             => !empty($amount_info) ? $amount_info : null,                                            // 金额总计
            'supplement_invoice_info' => !empty($supplement_invoice_info) ? $supplement_invoice_info : null,                    // 是否需要补充发票
            'workflow_log'            => !empty($workflow_log) ? $workflow_log : null,                                          // 审批流程日志
            'payment_info'            => !empty($payment_info) ? $payment_info : null,                                          // 支付信息
        ];
    }

    /**
     * 获取当前明细行网点支援列表V2
     * @param $params
     * @return mixed
     */
    public function getDetailStoreSupportListV2($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 获取报销详情数据
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['rel' => ReimbursementDetailSupportRelModel::class]);
            $builder->leftjoin(Detail::class, 'rel.detail_id = detail.id', 'detail');
            $builder->where('rel.re_id = :re_id: AND rel.detail_id = :detail_id:', [
                're_id'     => $params['reimbursement_id'],
                'detail_id' => $params['detail_id'],
            ]);
            $count = (int) $builder->columns('COUNT(*) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns(['rel.support_serial_no']);
                $builder->orderBy('rel.support_serial_no ASC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();

                $serial_no_list = array_column($items, 'support_serial_no');
                $items          = HrStaffApplySupportStoreRepository::getInstance()->getListBySupportNoList($serial_no_list);

                // 获取报销次数
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(['rel.support_serial_no', 'COUNT(rel.detail_id) AS detail_count']);
                $builder->from(['rel' => ReimbursementDetailSupportRelModel::class]);
                $builder->leftjoin(Reimbursement::class, 'rel.re_id = main.id', 'main');
                $builder->where('main.status IN ({status:array}) AND main.pay_status IN ({pay_status:array})', [
                    'status'     => [
                        Enums::WF_STATE_PENDING,
                        Enums::WF_STATE_APPROVED,
                    ],
                    'pay_status' => [
                        Enums::PAYMENT_PAY_STATUS_PENDING,
                        Enums::PAYMENT_PAY_STATUS_PAY,
                    ],
                ]);
                $builder->inWhere('rel.support_serial_no', $serial_no_list);
                $builder->groupBy('rel.support_serial_no');
                $detail_count_item = $builder->getQuery()->execute()->toArray();
                $detail_count_item = array_column($detail_count_item, 'detail_count', 'support_serial_no');

                foreach ($items as &$v) {
                    // 报销次数
                    $v['reimbursement_num'] = $detail_count_item[$v['support_serial_no']] ?? '0';

                    // 支援期间打卡天数
                    $total_attendance_time = AttendanceDataV2Repository::getInstance()->getDaysByStaffRangeDate($v['staff_info_id'], $v['employment_begin_date'], $v['employment_end_date']);
                    $v['punch_in_days_num'] = (string)$total_attendance_time;
                }

                $items             = $this->handleStoreSupportList($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ],
            ];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('reimbursement-getDetailStoreSupportListV2-failed: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出明细行网点支援列表V2
     * @param $params
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportDetailStoreSupportListV2($params)
    {
        $list = $this->getDetailStoreSupportListV2($params);
        if ($list['code'] != ErrCode::$SUCCESS) {
            return $list;
        }

        $list = $list['data']['items'] ?? [];

        $excel_data = [];
        foreach ($list as $value) {
            $excel_data[] = [
                $value['support_serial_no'],
                $value['reimbursement_num'],
                $value['staff_info_id'],
                $value['staff_info_name'],
                $value['staff_store_name'],
                $value['support_store_name'],
                $value['transportation_mode_text'],
                $value['is_stay_text'],
                $value['employment_begin_date'],
                $value['employment_end_date'],
                $value['employment_days'],
                $value['punch_in_days_num'],
                $value['status_text'],
            ];
        }

        $excel_header = [
            static::$t->_('export_detail_store_support_001'),
            static::$t->_('export_detail_store_support_002'),
            static::$t->_('export_detail_store_support_003'),
            static::$t->_('export_detail_store_support_004'),
            static::$t->_('export_detail_store_support_005'),
            static::$t->_('export_detail_store_support_006'),
            static::$t->_('export_detail_store_support_007'),
            static::$t->_('export_detail_store_support_008'),
            static::$t->_('export_detail_store_support_009'),
            static::$t->_('export_detail_store_support_010'),
            static::$t->_('export_detail_store_support_011'),
            static::$t->_('export_detail_store_support_012'),
            static::$t->_('export_detail_store_support_013'),
        ];

        return $this->exportExcel($excel_header, $excel_data, $params['export_file_name']);
    }

    /**
     * 获取支援单关联的报销实质列表
     * @param string $support_serial_no
     * @return mixed
     */
    public function getSupportRelatedDetailList(string $support_serial_no)
    {
        $columns = [
            'rel.support_serial_no',
            'main.no',
            'main.apply_date',
            'main.apply_id',
            'main.apply_name',
            'main.status',
            'main.pay_status',
            'detail.budget_id',
            'detail.product_id',
            'detail.amount',
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['rel' => ReimbursementDetailSupportRelModel::class]);
        $builder->leftjoin(Detail::class, 'rel.detail_id = detail.id', 'detail');
        $builder->leftjoin(Reimbursement::class, 'detail.re_id = main.id', 'main');
        $builder->where('rel.support_serial_no = :support_serial_no: ', ['support_serial_no' => $support_serial_no,]);
        $builder->andWhere('main.status IN ({status:array}) AND main.pay_status IN ({pay_status:array})', [
            'status'     => [
                Enums::WF_STATE_PENDING,
                Enums::WF_STATE_APPROVED,
            ],
            'pay_status' => [
                Enums::PAYMENT_PAY_STATUS_PENDING,
                Enums::PAYMENT_PAY_STATUS_PAY,
            ],
        ]);
        $builder->orderBy('main.no ASC');
        $items = $builder->getQuery()->execute()->toArray();

        $budget_ids  = array_column($items, 'budget_id');
        $product_ids = array_column($items, 'product_id');

        $budget_list_map  = (new BudgetService())->getBudgetByIds($budget_ids);
        $product_list_map = BudgetObjectProductRepository::getInstance(static::$language)->getListByIds($product_ids);
        $product_list_map = array_column($product_list_map, 'product_name', 'id');

        foreach ($items as &$v) {
            $v['status_text']     = static::$t->_('reimbursement_apply_status_' . $v['status']);
            $v['amount']          = bcdiv($v['amount'], 1000, 2);
            $v['budget_name']     = $budget_list_map[$v['budget_id']] ?? '';
            $v['product_name']    = $product_list_map[$v['product_id']] ?? '';
            $v['pay_status_text'] = !empty($v['pay_status']) ? static::$t->_(Enums::$loan_pay_status[$v['pay_status']]) : '';
        }

        return $items;
    }

    /**
     * 导出支援单关联的报销实质列表
     *
     * @param string $support_serial_no
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function exportSupportRelatedDetailList(string $support_serial_no)
    {
        $list = $this->getSupportRelatedDetailList($support_serial_no);

        $excel_data = [];
        foreach ($list as $value) {
            $excel_data[] = [
                $value['support_serial_no'],
                $value['no'],
                $value['apply_date'],
                $value['apply_id'],
                $value['apply_name'],
                $value['budget_name'],
                $value['product_name'],
                $value['amount'],
                $value['status_text'],
                $value['pay_status_text'],
            ];
        }

        $excel_header = [
            static::$t->_('export_support_related_detail_001'),
            static::$t->_('export_support_related_detail_002'),
            static::$t->_('export_support_related_detail_003'),
            static::$t->_('export_support_related_detail_004'),
            static::$t->_('export_support_related_detail_005'),
            static::$t->_('export_support_related_detail_006'),
            static::$t->_('export_support_related_detail_007'),
            static::$t->_('export_support_related_detail_008'),
            static::$t->_('export_support_related_detail_009'),
            static::$t->_('export_support_related_detail_011'),
        ];

        $file_name      = 'Export support related detail ' . date('YmdHis') . '.xlsx';
        $excel_file_res = $this->exportExcel($excel_header, $excel_data, $file_name);
        if (empty($excel_file_res['data'])) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }

        return [
            'file_url' => $excel_file_res['data'],
        ];
    }



    /**
     * 根据传入的单号生成二维码并保存图片
     *
     * @param string $no 报销单号
     * @return string 返回二维码相关信息
     * @throws ValidationException 当二维码生成失败时抛出异常
     */
    private function getQrCode($no): string
    {
        try {
            // 创建二维码对象，设置二维码内容为报销单号
            $qrCode = new QrCode($this->getConfirmationLink($no));

            // 设置二维码像素大小
            $qrCode->setSize(120);

            // 设置二维码边距为10像素
            $qrCode->setMargin(1);

            $qrCode->setEncoding('UTF-8');

            $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::LOW());

            // 创建PNG写入器，用于生成PNG格式的二维码图片
            $writer = new PngWriter();

            // 生成二维码图片内容
            $imageData = $writer->writeString($qrCode);

            // 生成base64编码的图片数据，用于直接在HTML中显示
            // 返回二维码相关信息
            return 'data:image/png;base64,' . base64_encode($imageData);

        } catch (GenerateImageException $e) {
            // 记录二维码图片生成异常
            $this->logger->error('QR code image generation failed: ' . $e->getMessage());
            throw new ValidationException('二维码图片生成失败', ErrCode::$SYSTEM_ERROR);

        } catch (\Exception $e) {
            // 记录其他异常
            $this->logger->error('QR code generation failed: ' . $e->getMessage());
            throw new ValidationException('二维码生成失败', ErrCode::$SYSTEM_ERROR);
        }
    }

    /**
     * 获取确认链接
     * @description 二维码格式
     * flashbackyard-国家code:flashbackyard-th://fe/html?url=urlencode(https://by-ui-host?sn=abc)
     *
     * @param string $no
     * @return string
     */
    private function getConfirmationLink(string $no): string
    {
        $url = $this->getLinkUrl($no);
        return sprintf('flashbackyard-%s://fe/html?url=%s', strtolower(get_country_code()), urlencode($url));
    }

    /**
     * @return void
     */
    private function getHost()
    {
        return (new SettingEnvModel())->getValByCode('h5_endpoint');
    }

    private function getLinkUrl(string $no): string
    {
        return $this->getHost() . sprintf('paper?sn=%s', strtoupper(get_country_code()) . $no);
    }
}
