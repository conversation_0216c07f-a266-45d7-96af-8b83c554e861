<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\Enums;
use App\Library\Enums\DepositEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\LoanEnums;
use App\Library\Enums\PaperDocumentEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Deposit\Models\DepositLossModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\PaperDocument\Services\ConfirmationService;
use App\Modules\PaperDocument\Services\SysService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Reimbursement\Models\BudgetObject;
use App\Modules\Reimbursement\Models\BudgetObjectOrder;
use App\Modules\Reimbursement\Models\BudgetObjectProduct;
use App\Modules\Reimbursement\Models\DepartmentModel;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Contract\Models\SysDepartmentModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\oa\AccountingSubjectsRepository;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    public static $not_must_params = [
        'no',
        'status',
        'apply_date_start',
        'apply_date_end',
        'pay_status',
        'end_at_start',
        'end_at_end',
        'pageSize',
        'pageNum'
    ];

    public static $validate_list_search = [

    ];



    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $condition
     * @param $user
     * @param int $type
     * @return array
     */
    public function getList($condition, $user = [], $type = 0)
    {
        $uid = $user['id'] ?? 0;
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 支付列表
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getReimbursementPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['r' => Reimbursement::class]);

            // 需要连报销明细表的情况, 当有明细行搜索项时
            if (in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_PAY, self::LIST_TYPE_QUERY])) {
                if (!empty($condition['travel_id']) || !empty($condition['serial_no']) || !empty($condition['budget_id']) || !empty($condition['product_id'])) {
                    $builder->leftjoin(Detail::class, 'rd.re_id = r.id', 'rd');
                }
            }

            if ($type == self::LIST_TYPE_QUERY) {
                $builder->leftjoin(ReimbursementRelLoan::class, 'rrl.re_id = r.id and rrl.is_deleted = ' . GlobalEnums::IS_NO_DELETED, 'rrl');
                $builder->leftjoin(Loan::class, 'lo.id = rrl.loan_id', 'lo');
            }

            $builder = $this->getCondition($builder, $condition, $type, $user);

            // 总条数
            $count = (int) $builder->columns('COUNT(DISTINCT(r.id)) AS total')->getQuery()->getSingleResult()->total;

            // 取列表
            $items = [];
            if ($count) {
                $column_str = '                
                    r.id,
                    r.no,
                    r.apply_id,
                    r.created_id,
                    r.apply_name,
                    r.apply_department_name,
                    r.apply_date,
                    r.start_at,
                    r.end_at,
                    r.amount,
                    r.source_type,
                    r.status,
                    r.pay_status,
                    r.cost_department,
                    r.is_after_ap_th,
                    r.created_at,
                    r.currency,
                    r.cost_company_id,
                    r.pay_operate_date,
                    r.is_pay_module,
                    r.confirm_status
                ';

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $column_str .= ',log.audit_at';
                }

                $builder->columns($column_str);

                if ($type == self::LIST_TYPE_PAY) {
                    if (isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
                        $builder->orderBy('r.approved_at ASC');
                    } else {
                        $builder->orderBy('r.pay_operate_date DESC');
                    }
                } elseif ($type == self::LIST_TYPE_APPLY) {
                    $builder->orderBy('r.no DESC');
                } elseif ($type != self::LIST_TYPE_AUDIT) {
                    $builder->orderBy('r.id desc');
                }

                $builder->groupBy('r.id');
                $builder->limit($page_size, $offset);

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $uid, $type);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('reimbursement-list-failed: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * 获得下载的列表，主要是详细
     *
     * @param $condition
     * @param $user
     * @param bool $is_batch
     * @param int $type
     * @return array
     */
    public function getDownloadList($condition, $user, $is_batch = false, $type = self::LIST_TYPE_QUERY_EXPORT)
    {
        $condition['uid'] = $user['id'] ?? 0;
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $column_str = '
                r.id,
                r.no,
                r.apply_id,
                r.apply_name,
                r.apply_date,
                r.start_at,
                r.end_at,
                r.amount,
                r.status,
                r.pay_status,
                r.currency,
                r.account_type,
                rd.budget_id,
                rd.product_id,
                rd.product_name,
                rd.category_a,
                rd.category_b,
                rd.rate,
                rd.tax,
                rd.info,
                rd.tax_not,
                rd.wht_type,
                rd.wht_tax,
                rd.wht_tax_amount,
                rd.deductible_vat_tax,
                rd.deductible_tax_amount,
                rd.payable_amount,
                rd.amount as detail_amount,
                rd.start_at as d_start_at,
                rd.end_at as d_end_at,
                rd.cost_store_n_name as d_cost_store,
                rd.cost_center_code as d_cost_center_code,
                rd.serial_no,
                rd.travel_serial_no,
                rd.invoice_tax_no,
                rd.enterprise_name,
                rd.ledger_account_id,
                rd.voucher_description,
                r.apply_department_name,
                r.apply_company_name,
                r.apply_center_code,
                r.sys_department_id,
                r.cost_department_name,
                r.cost_sys_department,
                r.cost_store_name,
                r.cost_center_code,
                r.cost_company_id,
                r.extra_message,
                r.voucher_abstract,
                r.apply_store_name,
                r.currency,
                r.created_name,
                r.created_id,
                r.created_at,
                r.loan_amount,
                r.other_amount,
                r.real_amount,
                r.pay_operate_date,
                r.payable_amount_all,
                r.pay_bank_name,
                r.pay_at,
                r.pay_bank_account,
                r.remark,
                r.cost_department,
                r.country_code,
                r.bank_name,
                r.bank_account,
                r.bank_type,
                r.cost_store_type,
                rd.invoices_ids,
                lo.lno,
                r.approved_at,
                rd.id detail_id,
                rd.ticket_type,
                rd.invoice_no,
                r.is_supplement_invoice,
                rd.company_addr,
                rd.support_store_name
            ';
            $builder->columns($column_str);
            $builder->from(['rd' => Detail::class]);
            $builder->leftJoin(Reimbursement::class, 'r.id = rd.re_id', 'r');
            $builder->leftjoin(ReimbursementRelLoan::class, 'rrl.re_id = r.id and rrl.is_deleted = ' . GlobalEnums::IS_NO_DELETED, 'rrl');
            $builder->leftjoin(Loan::class, 'lo.id = rrl.loan_id', 'lo');
            $builder = $this->getCondition($builder, $condition, $type, $user);

            // 分批取数 一般来自task场景
            if ($is_batch && !empty($condition['pageSize']) && !empty($condition['pageNum'])) {
                $offset = $condition['pageSize'] * ($condition['pageNum'] - 1);
                $builder->limit($condition['pageSize'], $offset);
            }

            $builder->orderBy('r.id desc');
            $builder->groupBy('rd.id');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleItems($items);
            $data = ['items' => $items];

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('reimbursement-download-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获得下载数据的总数
     *
     * @param $condition
     * @param $user
     * @param $type
     * @return mixed
     */
    public function getDownloadDataTotal($condition, $user, $type)
    {
        $condition['uid'] = $user['id'] ?? 0;
        $total_count = 0;

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['rd' => Detail::class]);
            $builder->leftJoin(Reimbursement::class, 'r.id = rd.re_id', 'r');
            $builder->leftjoin(ReimbursementRelLoan::class, 'rrl.re_id = r.id and rrl.is_deleted = ' . GlobalEnums::IS_NO_DELETED, 'rrl');
            $builder->leftjoin(Loan::class, 'lo.id = rrl.loan_id', 'lo');
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $total_count = (int)$builder->columns('COUNT(DISTINCT(rd.id)) AS total')->getQuery()->getSingleResult()->total;

        } catch (\Exception $e) {
            $this->logger->error('get-reimbursement-download-data-total-failed:' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 待支付统计
     * @param int $user_id
     *
     * @return mixed
     */
    public function getPayPendingCount(int $user_id)
    {
        $pay_pending_count = 0;

        if (empty($user_id)) {
            return $pay_pending_count;
        }

        // 是付款人: 终审通过且待支付的
        $pay_staff_ids = $this->getReimbursementPayStaffIds();
        if (in_array($user_id, $pay_staff_ids)) {
            // 数据权限对接: 支付待办 仅可取 费用所属公司 是 支付人 管辖范围内的
            $dept_ids = DataPermissionModuleConfigService::getInstance()->getDataConfigPermission(SysConfigEnums::SYS_MODULE_REIMBURSEMENT, $user_id);
            if (empty($dept_ids)) {
                return $pay_pending_count;
            }

            if (in_array(get_country_code(),
                [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE])) {
                $conditions = 'cost_company_id IN ({dept_ids:array}) AND status = :audit_status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module: AND confirm_status = :confirm_status:';
                $bind       = [
                    'dept_ids'       => $dept_ids,
                    'audit_status'   => Enums::WF_STATE_APPROVED,
                    'pay_status'     => Enums::PAYMENT_PAY_STATUS_PENDING,
                    'confirm_status' => PaperDocumentEnums::CONFIRM_STATE_COMPLETE,
                    'is_pay_module'  => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO,
                ];
            } else {
                $conditions = 'cost_company_id IN ({dept_ids:array}) AND status = :audit_status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:';
                $bind       = [
                    'dept_ids'      => $dept_ids,
                    'audit_status'  => Enums::WF_STATE_APPROVED,
                    'pay_status'    => Enums::PAYMENT_PAY_STATUS_PENDING,
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO,
                ];
            }

            $pay_pending_count = Reimbursement::count([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);
        }

        return $pay_pending_count;
    }

    /**
     * @param $builder
     * @param $condition
     * @param int $type
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type = 0, $user = [])
    {
        $no = $condition['no'] ?? '';
        $status = !empty($condition['status']) ? $condition['status'] : [];
        $pay_status = !empty($condition['pay_status'] )? $condition['pay_status']  : [];

        // 兼容多状态取值
        $status = is_array($status) ? $status : [$status];
        $pay_status = is_array($pay_status) ? $pay_status : [$pay_status];

        $apply_date_start = !empty($condition['apply_date_start']) ? date('Y-m-d', strtotime($condition['apply_date_start'])) : '';
        $apply_date_end = !empty($condition['apply_date_end']) ? date('Y-m-d', strtotime($condition['apply_date_end'])) : '';
        $cost_company_id = $condition['cost_company_id'] ?? [];
        $end_at_start = !empty($condition['end_at_start']) ? date('Y-m-d', strtotime($condition['end_at_start'])) : '';
        $end_at_end = !empty($condition['end_at_end']) ? date('Y-m-d', strtotime($condition['end_at_end'])) : '';
        $apply_id = $condition['apply_id'] ?? '';
        $cost_department_id = $condition['cost_department_id'] ?? 0;
        $apply_store_id = $condition['apply_store_id'] ?? 0;

        $support_serial_no = '';
        if (in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_QUERY, self::LIST_TYPE_QUERY_EXPORT])) {
            $support_serial_no = $condition['support_serial_no'] ?? '';
        }

        // 增加: 提交人所属部门的取数
        $created_department_ids = $condition['created_department_ids'] ?? [];

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $source_type = $condition['source_type'] ?? 0;
        $time_limit = $condition['created_at'] ?? '';

        $cost_store_type = $condition['cost_store_type'] ?? 0; //费用所属网点 1总部2网点

        $travel_id = $condition['travel_id'] ?? ''; // 出差申请审批编号
        $serial_no = $condition['serial_no'] ?? ''; // 外协申请审批编号
        $lno = $condition['lno'] ?? ''; // 关联借款单号

        // 审批通过/结束时间
        $approve_start_at = $condition['approve_start_at'] ?? '';
        $approve_end_at = $condition['approve_end_at'] ?? '';

        $ids = $condition['ids'] ?? null;

        // 我的申请页签类型: 0-全部; 1-待处理
        $tab_type = isset($condition['tab_type']) && $condition['tab_type'] == 1 ? $condition['tab_type'] : 0;

        $uid = $condition['uid'] ?? 0;

        // 审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_REIMBURSEMENT_TYPE], $condition['uid'], 'r');

        } elseif ($type == self::LIST_TYPE_APPLY) {
            // 我的申请
            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                if ($tab_type == ReimbursementEnums::LIST_TAB_TYPE_PENDING) {
                    $builder->andWhere('(r.created_id = :uid: AND r.status = :waiting_submitted:) OR (r.apply_id = :uid: AND r.status = :waiting_signed:)',
                        [
                            'uid'               => $uid,
                            'waiting_submitted' => ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                            'waiting_signed'    => ReimbursementEnums::STATUS_WAITING_SIGNED,

                        ]);
                } else {
                    $builder->andWhere('r.created_id = :uid: OR r.apply_id = :uid:', ['uid' => $uid]);
                }
            } else {
                if (!empty($uid)) {
                    $builder->andWhere('r.created_id = :uid:', ['uid' => $uid]);
                }
            }
            
            if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) &&
                !empty($condition['confirm_status'])) {
                $builder->andWhere('r.confirm_status = :confirm_status:', ['confirm_status' => $condition['confirm_status']]);
            }

        } elseif ($type == self::LIST_TYPE_PAY) {
            // 支付列表
            $builder->andWhere('r.status = :main_status: AND r.is_pay_module = :is_pay_module:', [
                'main_status' => Enums::WF_STATE_APPROVED,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
            ]);

            // 待处理
            if ($flag == GlobalEnums::AUDIT_TAB_PENDING) {
                $builder->andWhere('r.pay_status = :pay_status_pending:', ['pay_status_pending' => Enums::PAYMENT_PAY_STATUS_PENDING]);
            } elseif ($flag == GlobalEnums::AUDIT_TAB_PROCESSED) {
                // 已处理
                $builder->inWhere('r.pay_status', [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_NOTPAY]);
            }

            // 仅已齐全的数据才显示
            if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) &&
                !empty($condition['confirm_status'])) {
                $builder->andWhere('r.confirm_status = :confirm_status:', ['confirm_status' => PaperDocumentEnums::CONFIRM_STATE_COMPLETE]);
            }

            // 数据权限对接: 支付人仅可取 费用所属公司 是其管辖范围内的
            $permission_dept_ids = DataPermissionModuleConfigService::getInstance()->getDataConfigPermission(SysConfigEnums::SYS_MODULE_REIMBURSEMENT, $user['id']);
            $builder->inWhere('r.cost_company_id', $permission_dept_ids);

        } elseif ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
            $biz_table_info = ['table_alias' => 'r', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_REIMBURSEMENT_TYPE], $condition['uid'], $biz_table_info);
        }

        // 数据查询: 页面列表 和 导出列表
        if (in_array($type, [self::LIST_TYPE_QUERY, self::LIST_TYPE_QUERY_EXPORT])) {
            $builder->inWhere('r.status', ReimbursementEnums::$audit_status_item);

            // 关联的借款单号
            if (!empty($lno)) {
                $builder->andWhere('lo.lno = :lno:', ['lno' => $lno]);
            }

            // 支付操作开始/结束日期搜索
            if (!empty($condition['pay_operate_start_date']) && !empty($condition['pay_operate_end_date'])) {
                $builder->betweenWhere('r.pay_operate_date', $condition['pay_operate_start_date'] . ' 00:00:00', $condition['pay_operate_end_date'] . ' 23:59:59');
            }

            // 数据查询, 对接 通用数据权限
            if (!empty($user)) {
                // 业务表参数
                $table_params = [
                    'table_alias_name' => 'r',
                    'create_id_field' => 'apply_id',
                    'create_node_department_id_filed' => 'cost_department',
                ];

                $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_REIMBURSEMENT, $table_params);
            }
        }

        // 数据查询: 页面列表 和 导出列表 / 审核列表 / 支付列表
        if (in_array($type, [self::LIST_TYPE_QUERY, self::LIST_TYPE_QUERY_EXPORT, self::LIST_TYPE_AUDIT, self::LIST_TYPE_PAY])) {
            if (!empty($travel_id)) {
                $builder->andWhere('rd.travel_serial_no = :travel_serial_no:', ['travel_serial_no' => $travel_id]);
            }

            if (!empty($serial_no)) {
                $builder->andWhere('rd.serial_no = :serial_no:', ['serial_no' => $serial_no]);
            }

            if (!empty($approve_start_at) && !empty($approve_end_at)) {
                $builder->betweenWhere('r.approved_at', $approve_start_at, $approve_end_at);
            }

            // 付款日期: 银行流水日期
            if (!empty($condition['pay_date_start']) && !empty($condition['pay_date_end'])) {
                $builder->betweenWhere('r.pay_at', $condition['pay_date_start'], $condition['pay_date_end']);
            }

            // 报销实质: 兼容多个科目查询
            if (!empty($condition['budget_id'])) {
                $builder->inWhere('rd.budget_id', is_array($condition['budget_id']) ? $condition['budget_id'] : [$condition['budget_id']]);
            }

            // 费用明细
            if (!empty($condition['product_id'])) {
                $builder->andWhere('rd.product_id = :detail_product_id:', ['detail_product_id' => $condition['product_id']]);
            }
        }

        if (!empty($no)) {
            $builder->andWhere('r.no = :no:', ['no' => $no]);
        }

        // 工号或者姓名
        if (!empty($apply_id) && ($type != self::LIST_TYPE_APPLY || empty($condition['uid']))) {
            $builder->andWhere('r.apply_id = :apply_id: or r.apply_name=:apply_id:', ['apply_id' => $apply_id]);
        }

        if (!empty($status)) {
            $builder->inWhere('r.status', $status);
        }

        if (!empty($pay_status)) {
            $builder->inWhere('r.pay_status', $pay_status);
        }

        if (!empty($created_department_ids)) {
            $builder->inWhere('r.created_department_id', $created_department_ids);
        }

        if (!empty($apply_date_start) && !empty($apply_date_end)) {
            $builder->betweenWhere('r.apply_date', $apply_date_start, $apply_date_end);
        }

        if (!empty($end_at_start) && !empty($end_at_end)) {
            $builder->betweenWhere('r.end_at', $end_at_start, $end_at_end);
        }

        if (!empty($ids)) {
            $builder->inWhere('r.id ', $ids);
        }

        if (!empty($source_type)) {
            $builder->andWhere('r.source_type = :source_type:', ['source_type' => $source_type]);
        }

        if (!empty($time_limit)) {
            $builder->andWhere('r.created_at >= :created_at:', ['created_at' => $time_limit]);
        }

        if (!empty($cost_store_type)) {
            $builder->andWhere('r.cost_store_type = :cost_store_type:', ['cost_store_type' => $cost_store_type]);
        }

        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('r.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('r.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        //费用所属部门
        if (!empty($cost_department_id)) {
            $builder->andWhere('r.cost_department = :cost_department:', ['cost_department' => $cost_department_id]);
        }
        //申请人所属网点
        if (!empty($apply_store_id)) {
            $builder->andWhere('r.apply_store_id = :apply_store_id:', ['apply_store_id' => $apply_store_id]);
        }

        if (!empty($support_serial_no)) {
            $builder->leftjoin(ReimbursementDetailSupportRelModel::class, 'r.id = support_rel.re_id', 'support_rel');
            $builder->andWhere('support_rel.support_serial_no = :support_serial_no:', ['support_serial_no' => $support_serial_no]);
        }

        return $builder;
    }

    /**
     * 列表数据处理
     *
     * @param $items
     * @param int $uid
     * @param int $type
     * @return array
     */
    private function handleItems($items, $uid = 0, $type = 0)
    {
        if (empty($items)) {
            return [];
        }

        // 公司列表
        static $company_list_map = [];
        if (empty($company_list_map)) {
            $company_list     = (new DepartmentService())->getCompanyList();
            $company_list_map = array_column($company_list, 'name', 'id');
        }

        //发票信息关联
        $detail_ids = array_values(array_unique(array_column($items, 'detail_id')));
        if (!empty($detail_ids)) {
            $tickets_list = DetailService::getInstance()->getTicketList($detail_ids);
            $tickets_list = array_column($tickets_list, null, 'detail_id');
        }

        // 对于支付员工
        $pay_staff_id = $this->getReimbursementPayStaffIds();
        $is_payer     = !empty($uid) && in_array($uid, $pay_staff_id);

        // 明细行关联的支援单号(导出场景)
        $detail_support_rel_list = [];
        if (!$type) {
            $detail_ids              = array_column($items, 'detail_id');
            $detail_support_rel_list = ReimbursementDetailSupportRelModel::find([
                'conditions' => 'detail_id IN ({detail_ids:array})',
                'bind'       => ['detail_ids' => $detail_ids],
                'columns'    => ['detail_id', 'support_serial_no'],
            ])->toArray();

            $detail_support_rel_list = $this->formatDetailSupportRel($detail_support_rel_list);
        }

        //纸质文件确认状态
        $confirmStatusList = SysService::getInstance()->getConfirmStateList();
        $confirmStatusList = array_column($confirmStatusList, 'label', 'value');

        //最后确认人
        $serialNoId = array_column($items, 'no');
        $lastConfirmStaff = ConfirmationService::getInstance()->getConfirmList($serialNoId);
        $lastConfirmStaff = array_column($lastConfirmStaff, 'last_confirm_staff_ids', 'serial_no');

        $lang_setting = static::$t;

        // 报销申请状态
        $status_item = $this->getStatusItem(self::LIST_TYPE_APPLY);
        $status_item = array_column($status_item, 'label', 'code');

        foreach ($items as &$item) {
            // 币种
            $item['currency_text'] = $item['currency'] ? $lang_setting[GlobalEnums::$currency_item[$item['currency']]] : '';

            $pay_status                = Enums::$loan_pay_status[$item['pay_status']] ?? '';
            $item['amount']            = bcdiv($item['amount'], 1000, 2);
            $item['status_text']       = $status_item[$item['status']] ?? '';
            $item['pay_status_text']   = $lang_setting[$pay_status] ?? '';
            $confirm_status            = $confirmStatusList[$item['confirm_status']] ?? '';
            $item['cost_company_name'] = $company_list_map[$item['cost_company_id']] ?? '';
            $item['is_can_download']   = $this->isCanDownload($item, $uid);
            $item['invoices_ids']      = (isset($item['detail_id']) && isset($tickets_list[$item['detail_id']]['invoices_no']))
                ? $tickets_list[$item['detail_id']]['invoices_no'] : ($item['invoices_ids'] ?? '');

            // 审批已处理列表: 已终审通过 且 已支付/未支付, 且 未走支付模块的 且 是支付人, 则已处理时间 取支付时间
            if ($is_payer && isset($item['audit_at']) && $item['status'] == Enums::WF_STATE_APPROVED && !$item['is_pay_module'] && in_array($item['pay_status'],
                    [Enums::LOAN_PAY_STATUS_PAY, Enums::LOAN_PAY_STATUS_NOTPAY])) {
                $item['audit_at'] = $item['pay_operate_date'];
            }

            // 申请列表: 是否能重新提交: 结合该标识来判断是否要放开重新提交入口
            $item['is_can_recommit'] = $type == self::LIST_TYPE_APPLY && $item['created_at'] >= GlobalEnums::BIZ_MODULE_CAN_RECOMMIT_DATETIME;

            $support_serial_no = isset($item['detail_id']) ? implode(',', $detail_support_rel_list[$item['detail_id']] ?? []) : '';
            if (mb_strlen($support_serial_no) > GlobalEnums::EXCEL_CELLS_CHARACTERS_MAX) {
                $support_serial_no = mb_substr($support_serial_no, 0, GlobalEnums::EXCEL_CELLS_CHARACTERS_MAX);
            }
            $item['support_serial_no'] = $support_serial_no;

            $item['confirm_status_text'] = !empty($confirmStatusList[$item['confirm_status']])
                ? $lang_setting[$confirm_status]
                : '';
            $item['last_confirm_staff_ids'] = $lastConfirmStaff[$item['no']] ?? '';
        }

        return $items;
    }

    /**
     * 导出列表
     *
     * @param $condition
     * @param array $user
     * @param bool $is_batch task任务取全量数据场景使用
     * @param int $type
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function export($condition, array $user = [], bool $is_batch = false, int $type = self::LIST_TYPE_QUERY_EXPORT)
    {
        if (!$is_batch) {
            ini_set('memory_limit', '2048M');
        }

        $condition['pageNum'] = $condition['pageNum'] ?? GlobalEnums::DEFAULT_PAGE_NUM;
        $this->logger->info("是否分批: {$is_batch}; " . '当前内存: ' . memory_usage() . '; ----- 取数批次: ' . $condition['pageNum'] . '-----');

        // 获取列表数据
        $download_data = $this->getDownloadList($condition, $user, $is_batch, $type);
        $this->logger->info('本批数量: ' . count($download_data['data']['items']));

        if ($download_data['code'] != ErrCode::$SUCCESS) {
            return $download_data;
        }

        if ($is_batch && isset($condition['pageNum'])) {
            $condition['pageNum']++;
        }

        $data = $download_data['data']['items'];
        unset($download_data);

        $this->logger->info('当前内存: ' . memory_usage());

        $category_bArr = array_values(array_filter(array_unique(array_column($data, "category_b"))));
        $sysDepartmentIds = array_values(array_filter(array_unique(array_column($data, "sys_department_id"))));
        $sysDepartmentIds = array_values(array_filter(array_unique(array_merge($sysDepartmentIds, array_column($data, "cost_sys_department")))));

        $bList = [];
        if (!empty($category_bArr)) {
            $bList = CategoryService::getInstance()->getListByIds($category_bArr);
        }

        $sysDepartments = [];
        if (!empty($sysDepartmentIds)) {
            $sysDepartments = SysDepartmentModel::find([
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $sysDepartmentIds]
            ])->toArray();
            $sysDepartments = array_column($sysDepartments, null, 'id');
        }

        $budgetIds = array_values(array_filter(array_unique(array_column($data, 'budget_id'))));
        $productIds = array_values(array_filter(array_filter(array_unique(array_column($data,'product_id')))));

        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budgetIds);
        $productIdToName = $budgetService->budgetObjectProductList($productIds);

        $country_code_text = $this->getLangCountryArr();
        $country_code_text = array_column($country_code_text,'text_key','code');

        // 核算科目
        $ledger_account_list = LedgerAccountService::getInstance()->getList();
        $ledger_account_list = !empty($ledger_account_list['data']) ? array_column($ledger_account_list['data'], 'name', 'id') : [];

        // 会计科目
        $account_subjects_list = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds();

        $lang_key = strtolower(substr(self::$language, -2));
        $lang_key = in_array($lang_key, ['th', 'en']) ? $lang_key : 'cn'; //budgets & products，中文名字都是用cn

        $wht_type_arr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        // 取在最后一个可以修改的财务节点确认需要上传发票开始，之后是否上传附件
        $pay_at = date('Y-m-d H:m:s', strtotime(date('Y-m-d H:i:s') . ' -7 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['distinct rd.id']);
        $builder->from(['attach' => AttachModel::class]);
        $builder->leftjoin(Detail::class, 'rd.id = attach.oss_bucket_key', 'rd');
        $builder->leftjoin(Reimbursement::class, 'r.id = rd.re_id', 'r');
        $builder->andWhere('attach.oss_bucket_type = :type: AND attach.deleted = :deleted:', ['type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE, 'deleted' => 0]);
        $builder->andWhere('r.is_supplement_invoice = :is_sup:', ['is_sup' => ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES]);
        $builder->andWhere('attach.created_at >= r.supplement_file_change_date');
        $sup_list = $builder->getQuery()->execute()->toArray();
        $sup_list = !empty($sup_list) ? array_unique(array_column($sup_list,'id')) : [];

        // 相关金额已经算过的报销单号
        $no_array = [];

        // 处理好后的总数据
        $new_data = [];
        // 脱敏: 是否可以查看银行账号
        $view_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_view_bank_account_staff_ids');
        $can_view_bank_account = !empty($view_staff_ids) && isset($user['id']) && in_array($user['id'], $view_staff_ids) ? true : false;
        // 分段处理
        $data = array_chunk($data, 5000);
        foreach ($data as $chunk_data) {
            // 报销单据在支付模块的支付状态
            $pay_status_map_list = PayService::getInstance()->batchGetOrdersPayStatusMap(array_column($chunk_data, 'no'), BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT);

            $new_chunk_data = [];
            foreach ($chunk_data as $key => $val) {
                $new_chunk_data[$key][] = $val['no'];
                $new_chunk_data[$key][] = date('Y-m-d', strtotime($val['created_at'])); // 申请日期
                //$new_chunk_data[$key][] = $val['start_at'] . '_' . $val['end_at'];
                $new_chunk_data[$key][] = $val['created_name'];
                $new_chunk_data[$key][] = $val['created_id'];
                $new_chunk_data[$key][] = $val['apply_name'];
                $new_chunk_data[$key][] = $val['apply_id'];

                // 新增导出字段
                $new_chunk_data[$key][] = $val['bank_name'];
                $new_chunk_data[$key][] = $can_view_bank_account ? $val['bank_account'] : '';
                $new_chunk_data[$key][] = $val['bank_type'];
                $new_chunk_data[$key][] = $val['apply_company_name'];
                $new_chunk_data[$key][] = $val['apply_department_name'];

                // 申请人所属第一级部门
                $new_chunk_data[$key][] = $sysDepartments && isset($sysDepartments[$val['sys_department_id']]) ? $sysDepartments[$val['sys_department_id']]['name'] : '';

                // 申请人所属网点
                $new_chunk_data[$key][] = $val['apply_store_name'];
                $new_chunk_data[$key][] = $val['apply_center_code'];

                // 费用所属一级部门
                $new_chunk_data[$key][] = $sysDepartments && isset($sysDepartments[$val['cost_sys_department']]) ? $sysDepartments[$val['cost_sys_department']]['name'] : '';

                // 费用所属部门
                $new_chunk_data[$key][] = $val['cost_department_name'];

                // 费用所属网点
                if ($val['cost_store_type'] == 1) {
                    $new_chunk_data[$key][] = static::$t->_('global.branch');
                } else {
                    $new_chunk_data[$key][] = static::$t->_('payment_cost_store_type_1');
                }

                //$new_chunk_data[$key][] = $val['cost_center_code']; // 费用所属中心
                $new_chunk_data[$key][] = static::$t->_($country_code_text[$val['country_code']] ?? '');

                //添加 费用所属公司 额外参考信息 凭证摘要
                $new_chunk_data[$key][] = $val['cost_company_name'];
                $new_chunk_data[$key][] = $val['extra_message'];
                $new_chunk_data[$key][] = $val['voucher_abstract'];

                if ($val['budget_id']) {
                    $new_chunk_data[$key][] = isset($budgets[$val['budget_id']]) ? $budgets[$val['budget_id']]['name_' . $lang_key] : '';
                    if (empty($val['product_id'])) {
                        $new_chunk_data[$key][] = $val['product_name'];
                    } else {
                        $new_chunk_data[$key][] = isset($productIdToName[$val['product_id']]) ? $productIdToName[$val['product_id']]['name_' . $lang_key] : $val['product_name'];
                    }
                } else {
                    $new_chunk_data[$key][] = static::$t->_(Enums::$reimbursement_expense_type[$val['category_a']]);
                    $new_chunk_data[$key][] = $bList[$val['category_b']] ? static::$t->_($bList[$val['category_b']]) : '';
                }

                $new_chunk_data[$key][] = $val['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY ? ($ledger_account_list[$val['ledger_account_id']] ?? '') : ($account_subjects_list[$val['ledger_account_id']]['subjects_name'] ?? ''); // 核算科目/会计科目
                $new_chunk_data[$key][] = $val['d_start_at'] ?? '-';
                $new_chunk_data[$key][] = $val['d_end_at'] ?? '-';
                $new_chunk_data[$key][] = $val['d_cost_store'];
                $new_chunk_data[$key][] = $val['d_cost_center_code'] ?? '-';

                $new_chunk_data[$key][] = $val['info'];
                $new_chunk_data[$key][] = $val['travel_serial_no']; // 关联出差单号
                $new_chunk_data[$key][] = $val['serial_no']; // 外协申请审批编号
                $new_chunk_data[$key][] = $val['lno'];        // 关联借款单号
                $new_chunk_data[$key][] = $val['support_serial_no'];//支援费用-申请编号
                $new_chunk_data[$key][] = $val['support_store_name'];//支援费用-申请网点名称
                $new_chunk_data[$key][] = $val['invoice_tax_no'];//发票税务号
                $new_chunk_data[$key][] = $val['company_addr']; //公司地址
                $new_chunk_data[$key][] = $val['enterprise_name'];//企业名称
                $new_chunk_data[$key][] = $val['voucher_description'];//凭证描述

                $new_chunk_data[$key][] = $wht_type_arr[$val['wht_type']] ?? '';//WHT类别

                $export_wht_tax = bcdiv((string)$val['wht_tax'], 100, 4);
                $new_chunk_data[$key][] = rtrim(rtrim($export_wht_tax, '0'), '.');
                $new_chunk_data[$key][] = bcdiv($val['wht_tax_amount'], 1000, 2);

                //vat税率 先除1000,转为0.06
                $export_rate = (string)bcdiv($val['rate'], 1000, 4);

                //乘100变成百分比用来判断枚举
                $export_rate_percent = (string)bcmul($export_rate, 100, 2);
                $export_rate_percent = rtrim(rtrim($export_rate_percent, '0'), '.');

                //匹配不到枚举的返回空
                if (!is_numeric($export_rate_percent)) {
                    $new_chunk_data[$key][] = '';
                } else {
                    $new_chunk_data[$key][] = rtrim(rtrim($export_rate, '0'), '.');
                }

                $new_chunk_data[$key][] = bcdiv($val['tax'], 1000, 2);
                $new_chunk_data[$key][] = bcdiv($val['tax_not'], 1000, 2);
                $new_chunk_data[$key][] = bcdiv($val['detail_amount'], 1000, 2);
                $new_chunk_data[$key][] = bcdiv($val['payable_amount'], 1000, 2);

                //getDownloadList方法中已经除以1000
                $new_chunk_data[$key][] = $val['amount'];
                $new_chunk_data[$key][] = bcdiv($val['payable_amount_all'], 1000, 2);
                $new_chunk_data[$key][] = $val['currency_text'];//币种
                $new_chunk_data[$key][] = bcdiv($val['deductible_tax_amount'], 1000, 2);
                $export_deductible_vat_tax = bcdiv((string)$val['deductible_vat_tax'], 100, 4);
                $new_chunk_data[$key][] = rtrim(rtrim($export_deductible_vat_tax, '0'), '.');
                //amount上面除以1000了
                //$new_chunk_data[$key][] = $val['amount'];

                //如果已经算过，其他为0
                if (in_array($val['no'], $no_array)) {
                    $new_chunk_data[$key][] = 0;
                    $new_chunk_data[$key][] = 0;
                    $new_chunk_data[$key][] = 0;
                } else {
                    $new_chunk_data[$key][] = bcdiv($val['loan_amount'], 1000, 2);
                    $new_chunk_data[$key][] = bcdiv($val['other_amount'], 1000, 2);
                    $new_chunk_data[$key][] = bcdiv($val['real_amount'], 1000, 2);
                    $no_array[] = $val['no'];
                }

                $status = Enums::$loan_status[$val['status']] ?? '';
                $new_chunk_data[$key][] = static::$t->_($status);
                $new_chunk_data[$key][] = $val['pay_status_text'];
                $new_chunk_data[$key][] = $val['pay_operate_date'];
                if ($val['pay_status'] == Enums::LOAN_PAY_STATUS_PAY) {
                    $new_chunk_data[$key][] = $val['pay_bank_name'];
                    $new_chunk_data[$key][] = $val['pay_at'];
                    $new_chunk_data[$key][] = $val['pay_bank_account'];
                    $new_chunk_data[$key][] = $val['apply_name'];
                } else {
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                }

                $new_chunk_data[$key][] = $val['approved_at'];
                $new_chunk_data[$key][] = $val['remark'];
                // 发票编号
                $new_chunk_data[$key][] = $val['invoices_ids'];
                $new_chunk_data[$key][] = static::$t->_(GlobalEnums::$reimbursement_ticket_type[$val['ticket_type']] ?? '');

                $new_chunk_data[$key][] = $val['invoice_no'];

                // 是否上传补充附件
                $new_chunk_data[$key][] = empty($val['is_supplement_invoice']) ? '' : (ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES == $val['is_supplement_invoice'] ? static::$t->_('view_yes') : static::$t->_('view_no'));

                // 是否上传补充附件
                if ($val['is_supplement_invoice'] == ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES) {
                    if (isset($val['detail_id']) && in_array($val['detail_id'], $sup_list)) {
                        $sup_status = ReimbursementEnums::$supplement_invoice_status[ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES];
                    } else {
                        $sup_status = ReimbursementEnums::$supplement_invoice_status[ReimbursementEnums::IS_SUPPLEMENT_INVOICE_NO];
                    }
                } else {
                    $sup_status = '';
                }
                $new_chunk_data[$key][] = !empty($sup_status) ? static::$t->_($sup_status) : '';

                // 支付模块支付状态
                $new_chunk_data[$key][] = $pay_status_map_list[$val['no']] ?? '';
            }

            $new_data = array_merge($new_data, $new_chunk_data);
            unset($new_chunk_data);
        }

        // 需要批量导出的数据
        static $excel_data = [];
        if (!empty($new_data)) {
            $excel_data = array_merge($excel_data, $new_data);
            $new_data = $data = [];
            if ($is_batch) {
                sleep(5);
                $this->export($condition, $user, $is_batch, $type);
            }
        }

        $this->logger->info('实际总量: ' . count($excel_data) . '; 当前内存: ' . memory_usage());
        $file_name = $condition['export_file_name'] ?? 'reimbursement_' . date('YmdHis');
        $header = $this->getHeaderFields();

        $excel_result =  $this->exportExcel($header, $excel_data, $file_name);
        $this->logger->info('Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE));
        return $excel_result;
    }

    /**
     * 获取导出文件头部字段
     * @return array
     */
    public function getHeaderFields(){
        return [
            static::$t->_('re_field_id'),//编号
            static::$t->_('global.apply.date'),//       申请日期
            //static::$t->_('re_field_date'),//报销日期
            static::$t->_('re_field_created_name'),//发起人名字
            static::$t->_('re_field_created_id'),//发起人工号
            static::$t->_('re_field_apply_name'), //申请人
            static::$t->_('re_field_apply_id'),//申请人工号
            //新增导出字段
            static::$t->_('beneficiaryaccountname'),//收款人户名
            static::$t->_('beneficiaryaccount'),//收款人账号
            static::$t->_('beneficiarybankname'),//收款人开户银行

            static::$t->_('re_field_apply_company_name'),//申请人所属公司
            static::$t->_('re_field_apply_department_name'),//申请人所属部门
            static::$t->_('re_field_apply_first_department_name'),//        申请人所属一级部门
            static::$t->_('re_field_apply_store_name'),//         申请人所属网点
            static::$t->_('re_field_apply_center_code'),//费用所属中心
            static::$t->_('re_filed_apply_cost_first_department'),//        费用所属一级部门
            static::$t->_('re_filed_apply_cost_department'),//        费用所属部门
            static::$t->_('re_filed_apply_cost_store'),//        费用所属网点
            static::$t->_('country_code_project'), //费用项目
            static::$t->_('re_field_cost_company_name'),//费用所属公司
            static::$t->_('re_field_extra_message'),//额外参考信息
            static::$t->_('re_field_voucher_abstract'),//凭证摘要
            static::$t->_('re_field_category_a'),//报销分类
            static::$t->_('re_field_category_b'),//费用明细
            static::$t->_('purchase_apply_ledger_account'),//核算科目
            static::$t->_('re_filed_start_at'), //发生开始时间
            static::$t->_('re_filed_end_at'),   //发生结束时间
            static::$t->_('re_filed_apply_cost_store'),     //费用所属网点
            static::$t->_('re_filed_apply_cost_center'),    //费用所属中心
            static::$t->_('re_field_info'),//        报销说明
            static::$t->_('re_travel_serial_no'),//        关联出差单号
            static::$t->_('re_serial_no'),//        外协申请审批编号
            static::$t->_('re_field_lno'),        // 关联借款单号
            static::$t->_('re_filed_support_serial_no'),//关联支援网点审批编号
            static::$t->_('re_filed_support_store_name'),//被支援网点
            static::$t->_('re_invoice_tax_no'),//  发票税务号
            static::$t->_('re_filed_company_addr'),//公司地址
            static::$t->_('re_enterprise_name'),// 企业名称

            static::$t->_('re_voucher_description'),// 凭证描述

            static::$t->_('re_field_wht_type'),//WHT类别
            static::$t->_('re_field_wht_tax'),//WHT税率
            static::$t->_('re_field_wht_tax_amount'),//WHT税额
            static::$t->_('re_field_rate'),//VAT税率
            static::$t->_('re_field_tax'),//VAT税额

            static::$t->_('re_field_tax_not'),//不含税金额 (发票金额（不含VAT含WHT）)
            static::$t->_('re_field_detail_amount'),//含税金额 (发票金额（含VAT含WHT）)

            static::$t->_('re_field_payable_amount'),//应付金额（含VAT不含WHT）
            static::$t->_('re_field_amount'),//含税金额总计(发票金额总计（含VAT含WHT）)
            static::$t->_('re_field_payable_amount_all'),//应付金额总计（含VAT不含WHT）
            static::$t->_('re_field_currency_text'),//币种
            static::$t->_('re_field_deductible_tax_amount'),//可抵扣税额
            static::$t->_('re_field_deductible_vat_tax'),//可抵扣VAT税率

            static::$t->_('re_field_loan_amount'),//冲减借款金额
            static::$t->_('re_field_other_amount'),//其他冲减金额
            static::$t->_('re_field_real_amount'),//实付金额
            static::$t->_('global.apply.status.text'),//        申请状态
            static::$t->_('global_pay_status'), //支付状态
            static::$t->_('re_field_pay_operate_date'), //支付操作日期
            static::$t->_('re_field_pay_bank_name'), //付款银行
            static::$t->_('pay_date'), //      付款日期
            static::$t->_('re_field_pay_bank_account'), //付款账号
            static::$t->_('re_field_sign_name'), //签收人
            static::$t->_('re_field_approve_at'), //审批通过时间
            static::$t->_('re_field_remark'), //备注
            static::$t->_('re_field_invoice_no'), //发票编号
            static::$t->_('re_field_ticket_type'), //发票种类

            static::$t->_('ordinary_payment_invoice_no'), // 增值税发票
            static::$t->_('ordinary_payment_is_supplement_invoice'), // 是否需要补充发票
            static::$t->_('ordinary_payment_supplement_invoice_status'), // 补充附件状态
            static::$t->_('pay_module_pay_status'), // 支付模块支付状态
        ];
    }

    /**
     *
     */
    public function getMyDeptList($user) {

        return DepartmentModel::find([
            'columns' => 'id, name',
            'conditions' => 'level = 1 and type in (2, 3) and deleted = 0 and manager_id = ?1 or id = ?2',
            'bind'       => [
                1 => $user['id'],
                2 => $user['department_id']
            ]
        ])->toArray();
    }

    /**
     * 获取指定时间段上传过附件的单号
     * @param array $condition
     * @return array
     */
    public function getAttachmentNoList($condition = []){
        $start = $condition['start'] ?? '';
        $end = $condition['end'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct r.no'
        ]);
        $builder->from(['attach' => AttachModel::class]);
        $builder->leftjoin(Detail::class, 'd.id = attach.oss_bucket_key', 'd');
        $builder->leftjoin(Reimbursement::class, 'r.id = d.re_id', 'r');

        //组合搜索条件
        $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:',
            ['type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,'deleted' => 0]);
        $builder->andWhere('r.status in({status:array}) and r.pay_status in({pay_status:array})',
            ['status' => [Enums::CONTRACT_STATUS_PENDING,Enums::CONTRACT_STATUS_APPROVAL],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING,Enums::PAYMENT_PAY_STATUS_PAY]]);
        if (!empty($start)) {
            $builder->andWhere('attach.created_at >= :start:',
                ['start' => $start]);
        }
        if (!empty($end)) {
            $builder->andWhere('attach.created_at <= :end:',
                ['end' => $end]);
        }

        $list = $builder->getQuery()->execute()->toArray();

        return !empty($list) ? array_unique(array_filter(array_column($list,'no'))) : [];
    }

    /**
     * 获取支付截止时间超过7天没有补充附件的单号
     * @param array $condition
     * @return array
     */
    public function getSupplementAttachments($condition = []){
        $pay_at = $condition['pay_at'] ?? null;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct r.no, r.created_id, r.supplement_file_change_date, attach.created_at'
        ]);
        $builder->from(['d' => Detail::class]);
        $builder->leftjoin(Reimbursement::class, 'r.id = d.re_id', 'r');
        $builder->leftjoin(AttachModel::class, 'd.id = attach.oss_bucket_key', 'attach');

        // 组合搜索条件
        $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:',
            ['type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,'deleted' => 0]);
        $builder->andWhere('r.status = :status: and r.pay_status = :pay_status: and r.is_supplement_invoice = :is_sup:',
            ['status' => Enums::CONTRACT_STATUS_APPROVAL,'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY,
                'is_sup' => ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES]);

        // 支付时间超过7天
        if (!empty($pay_at)) {
            $builder->andWhere('r.pay_at < :pat_at:', ['pat_at' => $pay_at]);
        }

        $list = $builder->getQuery()->execute()->toArray();
        // 筛选出先款后票未上传附件的单
        foreach ($list as $k => $item) {
            if ($item['created_at'] > $item['supplement_file_change_date']) {
                unset($list[$k]);
                continue;
            }
        }

        $res = $emails = [];
        $id_list = !empty($list) ? array_values(array_unique(array_filter(array_column($list,'created_id')))) : [];
        // 申请邮箱
        if (!empty($id_list)) {
            $emails = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, email',
                'conditions' => 'staff_info_id in({ids:array})',
                'bind'       => [
                    'ids' => $id_list,
                ]
            ])->toArray();
        }

        foreach ($list as $item) {
            $res[$item['created_id']][] = $item['no'];
        }

        return [
            'no_list' => $res,
            'email_list' => !empty($emails) ? array_column($emails,'email','staff_info_id') : []
        ];
    }

    /**
     * 获取需要批量导出的报销单id
     * @return array
     */
    public function getReimbursementIds(){
        $reim_ids = Reimbursement::find([
            'columns' => 'id',
            'conditions' => 'status in({status:array}) and pay_status in({pay_status:array}) and created_at > :created_at:',
            'bind'       => [
                'status' => [Enums::CONTRACT_STATUS_PENDING,Enums::CONTRACT_STATUS_APPROVAL],
                'pay_status' => [Enums::LOAN_PAY_STATUS_PENDING,Enums::LOAN_PAY_STATUS_PAY],
                'created_at' => '2021-01-01 00:00:00' // 产品只需要2021-01-01开始的数据
            ]
        ])->toArray();

        return empty($reim_ids) ? [] : array_values(array_column($reim_ids,'id'));
    }

    /**
     * 导出列表
     *
     * @param $condition
     * @param array $user
     * @param bool $is_batch task任务取全量数据场景使用
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportByTask($condition, $user = [], $is_batch = false)
    {
        ini_set('memory_limit', '2048M');

        parent::setLanguage($params['lang'] ?? 'zh-CN');

        $download_data = $this->getDownloadList($condition, $user, $is_batch, self::LIST_TYPE_QUERY_EXPORT);

        if ($is_batch && isset($condition['pageNum'])) {
            $condition['pageNum']++;
        }

        if ($download_data['code'] != ErrCode::$SUCCESS) {
            return $download_data;
        }

        $data = $download_data['data']['items'];
        $download_data = null;
        $category_bArr = array_values(array_unique(array_column($data, "category_b")));
        $sysDepartmentIds = array_values(array_unique(array_column($data, "sys_department_id")));
        $sysDepartmentIds = array_values(array_unique(array_merge($sysDepartmentIds, array_column($data, "cost_sys_department"))));

        $bList = [];

        if (!empty($category_bArr)) {
            $bList = CategoryService::getInstance()->getListByIds($category_bArr);
        }
        $sysDepartments = [];
        if (!empty($sysDepartmentIds)) {
            $sysDepartments = SysDepartmentModel::find([
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $sysDepartmentIds]
            ])->toArray();
            $sysDepartments = array_column($sysDepartments, null, 'id');
        }

        $budgetIds = array_values(array_unique(array_column($data, 'budget_id')));
        $productIds = array_values(array_filter(array_unique(array_column($data,'product_id'))));

        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budgetIds);
        $productIdToName = $budgetService->budgetObjectProductList($productIds);

        $country_code_text = $this->getLangCountryArr();
        $country_code_text = array_column($country_code_text,'text_key','code');

        // 核算科目
        $ledger_account_list = LedgerAccountService::getInstance()->getList();
        $ledger_account_list = !empty($ledger_account_list['data']) ? array_column($ledger_account_list['data'], 'name', 'id') : [];

        $lang_key = strtolower(substr(self::$language, -2));
        $lang_key = in_array($lang_key, ['th', 'en']) ? $lang_key : 'cn'; //budgets & products，中文名字都是用cn

        $wht_type_arr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        // 取在最后一个可以修改的财务节点确认需要上传发票开始，之后是否上传附件
        $pay_at = date('Y-m-d H:m:s', strtotime(date('Y-m-d H:i:s') . ' -7 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct rd.id'
        ]);
        $builder->from(['attach' => AttachModel::class]);
        $builder->leftjoin(Detail::class, 'rd.id = attach.oss_bucket_key', 'rd');
        $builder->leftjoin(Reimbursement::class, 'r.id = rd.re_id', 'r');

        $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:',
            ['type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,'deleted' => 0]);
        $builder->andWhere('r.is_supplement_invoice = :is_sup:',
            ['is_sup' => ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES]);
        $builder->andWhere('attach.created_at >= r.supplement_file_change_date');
        $sup_list = $builder->getQuery()->execute()->toArray();
        $sup_list = !empty($sup_list) ? array_unique(array_column($sup_list,'id')) : [];

        // 附件
        // 明细相关附件
        $detail_ids = array_values(array_unique(array_filter(array_column($data, 'detail_id'))));
        $append_attachment = $upload_attachment = [];
        if (!empty($detail_ids)) {
            // 补充附件
            $append_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                'bind' => [
                    'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,
                    'keys' => $detail_ids
                ]
            ])->toArray();
            $append_attachment = merge_attachments($append_attachment, false);

            // 明细上传附件
            $upload_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                    'keys' => $detail_ids
                ]
            ])->toArray();
            $upload_attachment = merge_attachments($upload_attachment, false);
        }

        // 意见征询/回复附件
        $main_ids = array_values(array_unique(array_filter(array_column($data, 'id'))));
        $request_ids = [];
        $biz_id_request_id_map = [];
        if (!empty($main_ids)) {
            $workflow_request = WorkflowRequestModel::find([
                'conditions' => 'biz_type = :biz_type: AND biz_value IN ({biz_values:array}) AND is_abandon = 0',
                'bind' => ['biz_type' => Enums::WF_REIMBURSEMENT_TYPE, 'biz_values' => $main_ids],
                'columns' => ['id', 'biz_value'],
            ])->toArray();

            $request_ids = array_column($workflow_request, 'id');
            $biz_id_request_id_map = array_column($workflow_request, 'id', 'biz_value');
        }

        $ask_reply_attachment = [];
        if (!empty($request_ids)) {
            $fyr_ids = WorkflowRequestNodeFYR::find([
                'conditions' => 'request_id IN ({request_ids:array})',
                'bind' => ['request_ids' => $request_ids],
                'columns' => ['id', 'request_id', 'action_type'],
                'order' => 'id ASC'
            ])->toArray();
            $fyr_ids = array_column($fyr_ids, 'request_id', 'id');

            if (!empty($fyr_ids)) {
                $consult_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_FRY_ATTACH,
                        'keys' => array_keys($fyr_ids)
                    ]
                ])->toArray();
                $consult_attachment = merge_attachments($consult_attachment, false);

                // 归并同一个工单的意见征询/回复附件
                foreach ($fyr_ids as $fyr_id => $request_id) {
                    if (empty($consult_attachment[$fyr_id])) {
                        continue;
                    }

                    if (isset($ask_reply_attachment[$request_id])) {
                        $ask_reply_attachment[$request_id] .= " ;\r\n" . $consult_attachment[$fyr_id];
                    } else {
                        $ask_reply_attachment[$request_id] = $consult_attachment[$fyr_id];
                    }
                }

                $consult_attachment = null;
            }
        }

        // 审批日志
        $workflow_service_v2_entity = new WorkflowServiceV2();

        // 报销单号有无算过
        $no_array = [];

        // 处理好的总数据
        $new_data = [];

        // 分段处理
        $data = array_chunk($data, 5000);
        foreach ($data as $chunk_data) {
            // 报销单据在支付模块的支付状态
            $pay_status_map_list = PayService::getInstance()->batchGetOrdersPayStatusMap(array_column($chunk_data, 'no'), BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT);

            $new_chunk_data = [];
            foreach ($chunk_data as $key => $val) {
                $new_chunk_data[$key][] = $val['no'];
                $new_chunk_data[$key][] = date("Y-m-d", strtotime($val['created_at'])); // 申请日期
                //$new_chunk_data[$key][] = $val['start_at'] . "_" . $val['end_at'];
                $new_chunk_data[$key][] = $val['created_name'];
                $new_chunk_data[$key][] = $val['created_id'];
                $new_chunk_data[$key][] = $val['apply_name'];
                $new_chunk_data[$key][] = $val['apply_id'];
                //新增导出字段
                $new_chunk_data[$key][] = $val['bank_name'];
                $new_chunk_data[$key][] = $val['bank_account'];
                $new_chunk_data[$key][] = $val['bank_type'];
                $new_chunk_data[$key][] = $val['apply_company_name'];
                $new_chunk_data[$key][] = $val['apply_department_name'];
                $new_chunk_data[$key][] = $sysDepartments && isset($sysDepartments[$val['sys_department_id']]) ? $sysDepartments[$val['sys_department_id']]['name'] : ''; // 申请人所属第一级部门
                $new_chunk_data[$key][] = $val['apply_store_name']; // 申请人所属网点
                $new_chunk_data[$key][] = $val['apply_center_code'];
                $new_chunk_data[$key][] = $sysDepartments && isset($sysDepartments[$val['cost_sys_department']]) ? $sysDepartments[$val['cost_sys_department']]['name'] : ''; // 费用所属一级部门
                $new_chunk_data[$key][] = $val['cost_department_name']; // 费用所属部门
                // 费用所属网点
                if ($val['cost_store_type'] == 1) {
                    $new_chunk_data[$key][] = static::$t->_('global.branch');
                } else {
                    $new_chunk_data[$key][] = static::$t->_('payment_cost_store_type_1');
                }
                //$new_chunk_data[$key][] = $val['cost_center_code']; // 费用所属中心
                $new_chunk_data[$key][] = static::$t->_($country_code_text[$val['country_code']] ?? '');
                //添加 费用所属公司 额外参考信息 凭证摘要
                $new_chunk_data[$key][] = $val['cost_company_name'];
                $new_chunk_data[$key][] = $val['extra_message'];
                $new_chunk_data[$key][] = $val['voucher_abstract'];

                if ($val['budget_id']) {
                    $new_chunk_data[$key][] = isset($budgets[$val['budget_id']]) ? $budgets[$val['budget_id']]['name_' .$lang_key] : '';
                    if (empty($val['product_id'])) {
                        $new_chunk_data[$key][] = $val['product_name'];
                    } else {
                        $new_chunk_data[$key][] = isset($productIdToName[$val['product_id']]) ? $productIdToName[$val['product_id']]['name_' . $lang_key] : $val['product_name'];
                    }
                } else {
                    $new_chunk_data[$key][] = static::$t->_(Enums::$reimbursement_expense_type[$val['category_a']]);
                    $new_chunk_data[$key][] = $bList[$val['category_b']] ? static::$t->_($bList[$val['category_b']]) : '';
                }

                $new_chunk_data[$key][] = $ledger_account_list[$val['ledger_account_id']] ?? ''; // 核算科目
                $new_chunk_data[$key][] = $val['d_start_at'] ?? '-';
                $new_chunk_data[$key][] = $val['d_end_at'] ?? '-';
                $new_chunk_data[$key][] = $val['d_cost_store'];
                $new_chunk_data[$key][] = $val['d_cost_center_code'] ?? '-';

                $new_chunk_data[$key][] = $val['info'];
                $new_chunk_data[$key][] = $val['travel_serial_no']; // 关联出差单号
                $new_chunk_data[$key][] = $val['serial_no']; // 外协申请审批编号
                $new_chunk_data[$key][] = $val['lno'];        // 关联借款单号
                $new_chunk_data[$key][] = $val['support_serial_no'];//支援费用-申请编号
                $new_chunk_data[$key][] = $val['support_store_name'];//支援费用-申请网点名称
                $new_chunk_data[$key][] = $val['invoice_tax_no'];//发票税务号
                $new_chunk_data[$key][] = $val['company_addr']; //公司地址
                $new_chunk_data[$key][] = $val['enterprise_name'];//企业名称
                $new_chunk_data[$key][] = $val['voucher_description'];//凭证描述

                $new_chunk_data[$key][] = $wht_type_arr[$val['wht_type']] ?? '';//WHT类别

                /**
                 * static::$t->_('re_field_wht_type'),//WHT类别
                static::$t->_('re_field_wht_tax'),//WHT税率
                static::$t->_('re_field_wht_tax_amount'),//WHT税额
                static::$t->_('re_field_rate'),//VAT税率
                static::$t->_('re_field_tax'),//VAT税额

                static::$t->_('re_field_tax_not'),//不含税金额 (发票金额（不含VAT含WHT）)
                static::$t->_('re_field_detail_amount'),//含税金额 (发票金额（含VAT含WHT）)

                static::$t->_('re_field_payable_amount'),//应付金额（含VAT不含WHT）
                static::$t->_('re_field_payable_amount_all'),//含税金额 (发票金额（含VAT含WHT）)
                static::$t->_('re_field_deductible_tax_amount'),//可抵扣税额
                static::$t->_('re_field_deductible_vat_tax'),//可抵扣VAT税率
                 */
                $export_wht_tax = bcdiv((string)$val['wht_tax'], 100, 4);
                $new_chunk_data[$key][] = rtrim(rtrim($export_wht_tax, '0'), '.');

                $new_chunk_data[$key][] = bcdiv($val['wht_tax_amount'], 1000, 2);
                //vat税率 先除1000,转为0.06
                $export_rate = (string)bcdiv($val['rate'], 1000, 4);
                //乘100变成百分比用来判断枚举
                $export_rate_percent = (string)bcmul($export_rate, 100, 2);
                $export_rate_percent = rtrim(rtrim($export_rate_percent, '0'), '.');

                //匹配不到枚举的返回空
                if (!is_numeric($export_rate_percent)) {
                    $new_chunk_data[$key][] = '';
                } else {
                    $new_chunk_data[$key][] = rtrim(rtrim($export_rate, '0'), '.');
                }
                $new_chunk_data[$key][] = bcdiv($val['tax'], 1000, 2);
                $new_chunk_data[$key][] = bcdiv($val['tax_not'], 1000, 2);
                $new_chunk_data[$key][] = bcdiv($val['detail_amount'], 1000, 2);

                $new_chunk_data[$key][] = bcdiv($val['payable_amount'], 1000, 2);
                //getDownloadList方法中已经除以1000
                $new_chunk_data[$key][] = $val['amount'];
                $new_chunk_data[$key][] = bcdiv($val['payable_amount_all'], 1000, 2);
                $new_chunk_data[$key][] = $val['currency_text'];//币种
                $new_chunk_data[$key][] = bcdiv($val['deductible_tax_amount'], 1000, 2);
                $export_deductible_vat_tax = bcdiv((string)$val['deductible_vat_tax'], 100, 4);
                $new_chunk_data[$key][] = rtrim(rtrim($export_deductible_vat_tax, '0'), '.');
                //amount上面除以1000了
                //$new_chunk_data[$key][] = $val['amount'];

                //如果已经算过，其他为0
                if (in_array($val['no'], $no_array)) {
                    $new_chunk_data[$key][] = 0;
                    $new_chunk_data[$key][] = 0;
                    $new_chunk_data[$key][] = 0;
                } else {
                    $new_chunk_data[$key][] = bcdiv($val['loan_amount'], 1000, 2);
                    $new_chunk_data[$key][] = bcdiv($val['other_amount'], 1000, 2);
                    $new_chunk_data[$key][] = bcdiv($val['real_amount'], 1000, 2);
                    $no_array[] = $val['no'];
                }

                $status = Enums::$loan_status[$val['status']] ?? '';
                $new_chunk_data[$key][] = static::$t->_($status);
                $new_chunk_data[$key][] = $val['pay_status_text'];
                $new_chunk_data[$key][] = $val['pay_operate_date'];
                if ($val['pay_status'] == Enums::LOAN_PAY_STATUS_PAY) {
                    $new_chunk_data[$key][] = $val['pay_bank_name'];
                    $new_chunk_data[$key][] = $val['pay_at'];
                    $new_chunk_data[$key][] = $val['pay_bank_account'];
                    $new_chunk_data[$key][] = $val['apply_name'];
                } else {
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                }
                $new_chunk_data[$key][] = $val['approved_at'];
                $new_chunk_data[$key][] = $val['remark'];

                // 发票编号
                $new_chunk_data[$key][] = $val['invoices_ids'];
                $new_chunk_data[$key][] = static::$t->_(GlobalEnums::$reimbursement_ticket_type[$val['ticket_type']] ?? '');

                $new_chunk_data[$key][] = $val['invoice_no'];
                // 是否上传补充附件
                $new_chunk_data[$key][] = empty($val['is_supplement_invoice']) ? '' : (ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES == $val['is_supplement_invoice'] ? static::$t->_('view_yes') : static::$t->_('view_no'));
                // 是否上传补充附件
                if ($val['is_supplement_invoice'] == ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES) {
                    if (isset($val['detail_id']) && in_array($val['detail_id'], $sup_list)) {
                        $sup_status = ReimbursementEnums::$supplement_invoice_status[ReimbursementEnums::IS_SUPPLEMENT_INVOICE_YES];
                    } else {
                        $sup_status = ReimbursementEnums::$supplement_invoice_status[ReimbursementEnums::IS_SUPPLEMENT_INVOICE_NO];
                    }
                } else {
                    $sup_status = '';
                }
                $new_chunk_data[$key][] = !empty($sup_status) ? static::$t->_($sup_status) : '';

                // 支付模块支付状态
                $new_chunk_data[$key][] = $pay_status_map_list[$val['no']] ?? '';

                // 报销实质-上传附件
                $new_chunk_data[$key][] = $upload_attachment[$val['detail_id']] ?? '';

                // 报销实质-补充附件
                $new_chunk_data[$key][] = $append_attachment[$val['detail_id']] ?? '';

                // 意见征询/回复附件
                $wid = $biz_id_request_id_map[$val['id']] ?? '';
                if (!empty($wid)) {
                    $new_chunk_data[$key][] = $ask_reply_attachment[$wid] ?? ''; // 意见征询/回复附件

                    // 审批日志明细
                    // 合同审批日志
                    $request_model = WorkflowRequestModel::findFirst($wid);
                    $flow_logs = $workflow_service_v2_entity->getAuditLogs($request_model);

                    // 格式: 操作行为 - 操作人 - 姓名 - 操作时间(审批时间或等待时长待审批时有)
                    // 暂不处理子节点会签审批 与 审批人会签审批的展示情况
                    $_wk_log = '';
                    foreach ($flow_logs as $log) {
                        $_staff_info = '';
                        $_curr_log = $log['action_name'] . ' - ' . 'AUDIT_STAFF_INFO' . ' - ' . $log['audit_at'];

                        // 待审批人为多个的情况
                        if (!empty($log['list'])) {
                            foreach ($log['list'] as $sub_log) {
                                $_staff_info .= $sub_log['staff_id'] . '[' . $sub_log['staff_name'] . ']/';
                            }

                            $_staff_info = trim($_staff_info, ' /');

                        } else {
                            // 审批人为一
                            $_staff_info = $log['staff_id'] . '[' . $log['staff_name'] . ']';

                        }

                        // 意见征询日志, 预留逻辑
//                        if (!empty($log['fyr_list'])) {
//
//                        }

                        $_wk_log .= str_replace('AUDIT_STAFF_INFO', $_staff_info, $_curr_log) . "\r\n";
                    }

                    $new_chunk_data[$key][] = $_wk_log;

                } else {
                    $new_chunk_data[$key][] = '';
                    $new_chunk_data[$key][] = '';
                }

                $new_chunk_data[$key][] = $val['detail_id'];
                $new_chunk_data[$key][] = $val['id'];
            }

            $new_data = array_merge($new_data, $new_chunk_data);
            unset($new_chunk_data);
        }

        // 需要批量导出的数据
        static $excel_data = [];
        if (!empty($new_data)) {
            $excel_data = array_merge($excel_data, $new_data);
            $new_data = $data = [];
            if ($is_batch) {
                sleep(5);
                $this->exportByTask($condition, $user, $is_batch);
            }
        }

        $file_name = "reimbursement_" . date("YmdHis");
        $header = $this->getHeaderFields();

        // 补充的列
        $extend_columns = [
            '报销实质-上传附件', // 明细上传附件
            '报销实质-补充附件', // 明细补充附件
            '意见征询/回复附件',
            '审批日志明细',
            '附1: 明细ID', // 明细ID
            '附2: 工单ID', // 工单ID
        ];

        $header = array_merge($header, $extend_columns);
        return $this->exportExcel($header, $excel_data, $file_name);
    }


    /**
     * 押金单条数据详情处理
     * @Date: 9/27/22 3:24 PM
     * @param array $params
     * @return array
     * @author: peak pan
     **/
    public function depositDetail(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        //获取付款申请主表信息
        $data =[];
        try {
            $main_model = Detail::findFirst([
                'id = :id:',
                'bind'    => ['id' => $params['id']],
                'columns' => [
                    'id',
                    're_id',
                    'ledger_account_id',
                    'cost_store_n_name as cost_store_name',//详情的网点和总部
                    'cost_center_code as cost_center_name',
                    'start_at as cost_start_date',
                    'end_at as cost_end_date',
                    'tax_not as amount_no_tax',
                    'rate as vat_rate',
                    'tax as amount_vat',
                    'amount as amount_have_tax',//含税金额
                    'wht_type as wht_category',
                    'wht_tax as wht_rate',
                    'wht_tax_amount as amount_wht',
                    'budget_id',
                    'product_id'
                ]
            ]);
            if (empty($main_model)) {

                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }

            $main_obj = Reimbursement::findFirst([
                'id = :id:',
                'bind' => ['id' => $main_model->re_id]
            ]);

            if (empty($main_obj)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }
            $data = DepositService::getInstance()->getDepositInfo($main_model, $main_obj, $params);

            $cost_company_name = '';
            if ($main_obj->cost_company_id) {
                $company_info      = (new DepartmentService())->getCostCompanyByDepartmentId($main_obj->cost_company_id);
                $cost_company_name = $company_info['name'] ?? '';
            }
            $data['head'] = [
                'type'                       => static::$t[DepositEnums::$deposit_modules[$params['type']]],
                'id'                         => $main_model->id,
                'apply_no'                   => $main_obj->no,
                'apply_id'                   => $main_obj->apply_id,
                'apply_name'                 => $main_obj->apply_name,
                'apply_email'                => '',
                'cost_department_name'       => $main_obj->cost_department_name,
                'apply_node_department_name' => $main_obj->apply_department_name,
                'create_company_name'        => $cost_company_name,
                'cost_department_id'         => $main_obj->apply_department_id,
                'cost_store_type'            => static::$t[Enums::$payment_cost_store_type_try[$main_obj->cost_store_type]],
                'currency'                   => static::$t[GlobalEnums::$currency_item[$main_obj->currency]],//币种
            ];

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-报销获取数据详情信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];

    }


    /**
     * 押金列表分页列表 数据
     *
     * @Date: 8/6/22 3:28 PM
     * @param array $condition 查询条件
     * @param array $user 用户
     * @param int $type 类型
     * @return array
     */
    public function getDepositList(array $condition, array $user, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? DepositEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? DepositEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - DepositEnums::PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['sta_date']) && isset($condition['end_date']) && $condition['sta_date'] > $condition['end_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['sta_return_date']) && isset($condition['end_return_date']) && $condition['sta_return_date'] > $condition['end_return_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            $condition['uid'] = $user['id'];
            $builder          = $this->modelsManager->createBuilder();
            if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
                $columns = [
                    'op.no as apply_no',//申请单号
                    'op.apply_id',//申请人工号
                    'op.apply_name',//申请人姓名 押金负责人
                    'op.apply_date as created_at',//申请日期
                    'op.cost_company_id',//费用所属公司
                    'op.cost_department_name',//费用所属部门
                    'op.created_name as create_name',
                    'op.cost_store_type', '"" as contract_no', '"" as contract_status',
                    'de.contract_no as contract_no_b', 'op.currency',//币种
                    'opd.id AS detail_id',// 明细ID
                    'opd.budget_id',//预算分类
                    'opd.product_id',//明细分类
                    'opd.cost_store_n_name as  cost_store_name',//费用所属网点/总部
                    'opd.cost_center_code as cost_center_name',//费用所属中心
                    'opd.start_at as cost_start_date',//费用发生期间
                    'opd.end_at as cost_end_date',//费用结束期间
                    'opd.tax_not as amount_no_tax',//不含税金额（含WHT）
                    'opd.rate as vat_rate',//SST税率
                    'opd.tax as amount_vat',//SST税额
                    'opd.amount as amount_have_tax',//含税金额
                    'opd.wht_type as wht_category',//WHT类
                    'opd.wht_tax as wht_rate',//WHT税率
                    'opd.wht_tax_amount as amount_wht',//WHT税额
                    'de.deposit_money',//押金总金额
                    'dr.status',//押金归还状态
                    'dr.return_money',//归还金额
                    'dr.other_return_money',// 其他退款金额
                    'dr.other_return_info',// 其他退款说明
                    'dr.loss_money as loss_money_return',//损失总金额
                    'dr.bank_flow_date',//银行流水日期
                    'dr.return_info', '"" as return_attachment',//归还详情附加
                    'dl.loss_bear_id',//损失承担方
                    'dl.loss_budget_id',//损失类型
                    'dl.loss_department_id',//损失部门名称 网点/总部
                    'dl.loss_money', '"" as loss_attachment',//损失附加
                    'de.return_status', 'de.id as deposit_id',
                    'dr.id as deposit_return_id', 'dl.id as deposit_loss_id',
                    'de.apply_id as deposit_create_name', 'dl.loss_organization_id',
                    'op.apply_department_name AS biz_apply_department_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            } else {
                $columns = [
                    'op.no as apply_no',
                    'opd.id',
                    'op.apply_id',
                    'op.apply_name',
                    'op.apply_date as created_at',
                    'op.cost_company_id',//费用所属公司
                    'op.cost_department_name',
                    'op.cost_store_type',
                    'op.created_id as create_id',
                    'op.created_name as create_name',
                    'op.cost_company_id',
                    'op.apply_department_name AS biz_apply_department_name',
                    'opd.tax_not as amount_no_tax',//不含税金额（含WHT）
                    'opd.tax as amount_vat', //vat金额
                    'opd.wht_tax_amount as amount_wht',//WHT税额
                    'opd.cost_store_n_name',//详情的网点和总部
                    'op.currency',
                    'de.return_money',
                    'de.deposit_money ',//押金总金额
                    'de.loss_money',
                    ' "" as status',
                    ' "" as contract_no',
                    ' "" as expiry_date',//合同到期
                    'de.contract_no as contract_no_b',
                    'de.return_status',
                    'de.apply_id as deposit_create_id',
                    'de.apply_name as deposit_create_name',
                    'op.created_department_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            }
            $builder->from(['opd' => Detail::class]);
            //组合搜索条件
            $builder = $this->getDepositCondition($builder, $condition, $type, $user);
            $count   = (int)$builder->columns('COUNT(DISTINCT opd.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                if ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
                    $builder->groupBy('dl.id,opd.id');
                } else {
                    $builder->groupBy('opd.id');
                }
                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_CONSULTED_REPLY])) {
                    $builder->orderBy('opd.id DESC');
                }
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDepositItems($items, $condition);
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-报销-列表数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 押金列表数据处理
     *
     * @Date: 9/27/22 3:27 PM
     * @param array $items 数据
     * @param array $condition 条件
     * @return  array
     */
    private function handleDepositItems(array $items, array $condition = [])
    {
        if (empty($items)) {
            return [];
        }
        $wht_category_arr   = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');
        $vat7_rate_list_arr = array_column(EnumsService::getInstance()->getFormatVatRateConfig(), 'label', 'value');
        //处理费用所属公司

        $cost_company_list = [];
        $cost_company_ids  = array_values(array_unique(array_filter(array_column($items, 'cost_company_id'))));
        if ($cost_company_ids) {
            $cost_company_arr  = (new DepartmentService())->getDepartmentInfoByIds($cost_company_ids);
            $cost_company_list = array_column($cost_company_arr, 'name', 'id');
        }

        if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
            $budget_ids            = array_values(array_unique(array_filter(array_column($items, 'budget_id'))));
            $budget_id_arr         = DepositService::getInstance()->getBudgetObjectIdByName($budget_ids);
            $product_ids           = array_values(array_unique(array_filter(array_column($items, 'product_id'))));
            $product_id_arr        = empty($product_ids) ? [] : DepositService::getInstance()->getBudgetObjectProductIdByName($product_ids);
            $deposit_return_ids    = array_values(array_unique(array_filter(array_column($items, 'deposit_id'))));
            $return_attachment     = empty($deposit_return_ids) ? [] : DepositService::getInstance()->getIdByUrlAttach($deposit_return_ids, Enums::OSS_REIMBURSEMENT_TYPE_DEPOSIT_ADD);
            $loss_bear_name_arr    = array_column(array_merge(DepositEnums::$vendor_arr, (new PurchaseService())->getCooCostCompany()), 'cost_company_name', 'cost_company_id');
            $loss_budget_ids       = array_values(array_unique(array_filter(array_column($items, 'loss_budget_id'))));
            $loss_budget_arr       = empty($loss_budget_ids) ? [] : DepositService::getInstance()->getBudgetIdsByname($loss_budget_ids); //损失类型
            $loss_organization_arr = DepositEnums::$organization_type;

            foreach ($items as &$item) {
                $item['contract_no']     = '';
                $item['contract_status'] = '';
                $item['currency']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];//币种
                $item['sum_money']       = bcdiv(bcsub(bcadd($item['amount_no_tax'], $item['amount_vat']), $item['amount_wht']), 1000, 2);//实付金额
                $item['sum_money']       = !empty($item['sum_money']) ? $item['sum_money'] : '';
                $item['deposit_money']   = $item['sum_money'];//押金总金额

                if ($item['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                    //总部
                    $item['cost_store_name'] = $item['cost_department_name'];
                }
                $item['cost_store_type'] = !empty($item['cost_store_type']) ? static::$t[Enums::$payment_cost_store_type_try[$item['cost_store_type']]] : '';
                $item['wht_category']    = $wht_category_arr[$item['wht_category']] ?? '';

                if ($vat7_rate_list_arr[$item['vat_rate'] / 10]) {
                    $item['vat_rate'] = !empty($item['vat_rate']) ? (string)$vat7_rate_list_arr[$item['vat_rate'] / 10] : '0%';
                } else {
                    $item['vat_rate'] = !empty($item['vat_rate']) ? (string)floatval($item['vat_rate'] / 10) . '%' : '0%';
                }
                $item['wht_rate']            = $item['wht_rate'] . '%' ?? '0%';
                $item['budget_id']           = $budget_id_arr[$item['budget_id']] ?? '';//预算分类
                $item['product_id']          = $product_id_arr[$item['product_id']] ?? '';//明细分类
                $item['status']              = empty($item['return_status']) ? DepositEnums::DEPOSIT_RETURN_STATUS_NOT : $item['return_status'];
                $item['return_status_id']    = $item['return_status'] ?? '1';
                $item['return_status']       = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['deposit_apply_name']  = !empty($item['deposit_create_name']) ? $item['deposit_create_name'] : $item['apply_id'];//押金负责人
                $item['create_company_name'] = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';//费用所属公司
                $item['amount_no_tax']       = bcdiv($item['amount_no_tax'], 1000, 2);//不含税金额（含WHT）
                $item['amount_vat']          = bcdiv($item['amount_vat'], 1000, 2);//SST税额
                $item['amount_have_tax']     = bcdiv($item['amount_have_tax'], 1000, 2);//含税金额
                $item['amount_wht']          = bcdiv($item['amount_wht'], 1000, 2);//WHT税额
                $item['return_money']        = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';//归还金额
                $item['other_return_money']  = bcdiv($item['other_return_money'], 1000, 2); // 其他退款金额
                $item['return_attachment']   = !empty($return_attachment[$item['deposit_id']]) ?
                    implode(',', $return_attachment[$item['deposit_id']]) : '';//归还详情附加
                $item['loss_bear_id']        = !empty($item['loss_bear_id']) ? $loss_bear_name_arr[$item['loss_bear_id']] : '';//损失承担方
                $item['loss_budget_id']      = !empty($item['loss_budget_id']) ? $loss_budget_arr[$item['loss_budget_id']] : '';//损失类型
                $item['loss_department_id']  = !empty($item['loss_organization_id']) ? static::$t[$loss_organization_arr[$item['loss_organization_id']]] : '';//损失部门名称 网点/总部
                $item['loss_money']          = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';//损失金额
                $item['loss_money_return']   = $item['loss_money_return'] != '' ? bcdiv($item['loss_money_return'], 1000, 2) : ''; //损失总金额
                $item['cost_start_date']     = $item['cost_start_date'] . ' - ' . $item['cost_end_date'];//费用发生日期
                $item['created_at']          = show_time_zone($item['created_at'], 'Y-m-d');
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }

        } else {
            foreach ($items as &$item) {
                $item['type']                 = DepositEnums::DEPOSIT_REIMBURSEMENT;
                $item['return_money']         = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';
                $item['deposit_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';
                $item['sum_deposit_money']    = bcdiv(bcsub(bcadd($item['amount_no_tax'], $item['amount_vat']), $item['amount_wht']), 1000, 2);
                $item['contract_no']          = '';
                $item['contract_status_name'] = '';
                $item['contract_no_b']        = empty($item['contract_no_b']) ? '' : $item['contract_no_b'];
                $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];
                $item['cost_store_type_text'] = $item['cost_store_n_name'];
                if ($item['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                    //总部
                    $item['cost_store_type_text'] = $item['cost_department_name'];
                }
                $item['return_status_id']    = $item['return_status'] ?? '1';
                $item['return_status']       = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['created_at']          = show_time_zone($item['created_at'], 'Y-m-d');
                $item['create_id']           = empty($item['deposit_create_id']) ? $item['apply_id'] : $item['deposit_create_id'];
                $item['create_name']         = empty($item['deposit_create_id']) ? $item['apply_name'] : $item['deposit_create_name'];
                $item['create_company_name'] = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
                unset($item['cost_store_n_name'], $item['cost_company_id']);
            }
        }

        return $items;
    }


    /**
     *  列表复合搜索条件  押金
     *
     * @Date: 9/27/22 3:27 PM
     * @param object $builder 对象
     * @param array $condition 条件
     * @param int $type 模块类型
     * @param array $user
     * @return object
     * @throws BusinessException
     * @author: peak pan
     */
    private function getDepositCondition(object $builder, array $condition, int $type, array $user = [])
    {
        $apply_no          = $condition['apply_no'] ?? '';
        $create_id         = $condition['create_name'] ?? '';
        $create_company_id = $condition['create_company_id'] ?? '';
        $cost_store_type   = $condition['cost_store_type'] ?? '';
        $sta_date          = $condition['sta_date'] ?? '';
        $end_date          = $condition['end_date'] ?? '';
        $sta_return_date   = $condition['sta_return_date'] ?? '';
        $end_return_date   = $condition['end_return_date'] ?? '';
        $return_status     = $condition['return_status'] ?? '';

        // 是否接入通用数据权限
        $is_access_common_data_permission = $condition['is_access_common_data_permission'] ?? false;

        //付款申请列表当前登录用户提交的所有申请数据
        $builder->leftjoin(Reimbursement::class, 'op.id = opd.re_id', 'op');
        $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
        $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');

        $builder->andWhere('de.id IS NULL OR (de.id IS NOT NULL AND de.deposit_type = :deposit_type:)', ['deposit_type' => DepositEnums::DEPOSIT_REIMBURSEMENT]);

        if (in_array($type, [self::LIST_TYPE_APPLY, self::LIST_TYPE_QUERY, DepositEnums::LIST_TYPE_DATA_EXPORT]) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $builder->andWhere('op.pay_at >= :pay_at:', ['pay_at' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
        }

        if ($type == self::LIST_TYPE_APPLY) {
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
            $builder->andWhere('op.apply_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);

        } elseif ($type == self::LIST_TYPE_QUERY) {
            //数据查询
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);

        } elseif ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {

            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            if (!empty($condition['export_type'])) {
                $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
                $builder->andWhere('op.apply_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
            } else {
                $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
            }
        }

        // 对接通用数据权限
        if ($is_access_common_data_permission === true) {
            // 业务表参数
            $table_params = [
                'type' => SettingEnums::DATA_PERMISSION_TYPE_MULTI_ENTIEY,
                'entity_item' => [
                    'biz' => [
                        'table_alias_name' => 'op',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_department_id',
                        'extra_condtions' => 'de.apply_id IS NULL', // 预留, 暂时不用
                    ],
                    'deposit' => [
                        'table_alias_name' => 'de',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NOT NULL', // 预留, 暂时不用
                    ],
                ]
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_DEPOSIT_REIMBURSEMENT, $table_params);
        }

        //申请编号
        if (is_array($apply_no)) {
            $builder->inWhere('op.no', $apply_no);
        } elseif (!is_array($apply_no) && !empty($apply_no)) {
            $builder->andWhere('op.no = :no:', ['no' => $apply_no]);
        }

        //申请时间-截止日期
        if (!empty($end_date) && !empty($sta_date)) {
            $sta_date .= ' 00:00:00';
            $end_date .= ' 23:59:59';
            $builder->betweenWhere('op.apply_date', $sta_date, $end_date);
        }

        //归还-截止日期
        if (!empty($end_return_date) && !empty($sta_return_date)) {
            $sta_return_date .= ' 00:00:00';
            $end_return_date .= ' 23:59:59';
            $builder->betweenWhere('dr.return_date', $sta_return_date, $end_return_date);

            //如果有归还时间 且没有归还状态的时候 默认为已归还
            if (empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE]);
            } elseif (in_array($return_status, [DepositEnums::DEPOSIT_RETURN_STATUS_NOT, DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION, DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE])) {
                //如果有归还时间 且归还状态为 未归还、法务介入中、法务已确定的时候 为默认状态0
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_DEFAULT]);
            } else {
                //如果有归还时间 且归还状态不为未归还、法务介入中、法务已确定，空的时候 状态为当前选择的状态
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
            }
        } else {
            //如果有归还时间为空 且归还状态不为空按照选择的归还状态查询
            if (!empty($return_status)) {
                if ($return_status == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                    $builder->andWhere('(de.return_status = :return_status:) or (de.return_status is null)', ['return_status' => $return_status]);
                } else {
                    $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
                }
            }
        }

        //押金负责人
        if (!empty($create_id)) {
            $builder->andWhere('((((de.apply_id ="" or de.apply_id is null ) and (op.apply_id = :created_id: or op.apply_name = :created_id:)) or ((de.apply_id = :created_id: or de.apply_name = :created_id:) and (de.apply_id !="" or de.apply_name !=""))))', ['created_id' => $create_id]);
        }

        //费用所属公司
        if (!empty($create_company_id)) {
            $builder->andWhere('op.cost_company_id = :cost_company_id:', ['cost_company_id' => $create_company_id]);
        }

        if (!empty($cost_store_type)) {
            if ($cost_store_type == Enums::HEAD_OFFICE_STORE_FLAG) {
                $builder->andWhere('op.cost_store_type = :cost_store_type:', ['cost_store_type' => Enums::PAYMENT_COST_STORE_TYPE_02]);
            } else {
                $builder->andWhere('opd.cost_store_n_id = :cost_store_n_id:', ['cost_store_n_id' => $cost_store_type]);
            }
        }

        // 费用所属公司
        if (!empty($cost_company)) {
            $builder->andWhere('opd.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company]);
        }
        $builder->andWhere('op.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);

        return $builder;
    }

    /**
     * 报销可关联的借款单列表中,标记哪一条可以自动关联
     * @param $loan_list
     * @param int $cost_company_id 报销的费用所属公司
     * @param int $currency 报销单币种
     * @return array
     */
    public function getAutoOneLoan($loan_list, $cost_company_id, $currency)
    {
        $search = [];
        foreach ($loan_list as &$item) {
            $item['auto_select'] = false;
            //自动选中的条件
            $conditions = $cost_company_id == $item['create_company_id'] && $currency == $item['currency'] && in_array($item['loan_status'], [LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN]);
            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $conditions) {
                if (empty($search)) {
                    $search['created_at'] = $item['created_at'];
                    $search['id'] = $item['id'];
                    continue;
                } else {
                    //比较时间和id
                    if (strtotime($item['created_at']) < strtotime($search['created_at'])) {
                        $search['created_at'] = $item['created_at'];
                        $search['id'] = $item['id'];
                        continue;
                    } elseif (strtotime($item['created_at']) == strtotime($search['created_at']) && $item['id'] < $search['id']) {
                        $search['created_at'] = $item['created_at'];
                        $search['id'] = $item['id'];
                        continue;
                    }
                }
            }
        }
        //如果存在,赋值给list
        if (!empty($search)) {
            $id_loan_list = array_column($loan_list, null, 'id');
            $id_loan_list[$search['id']]['auto_select'] = true;
            $loan_list = array_values($id_loan_list);
        }
        return $loan_list;
    }

    /**
     * 我的申请/BY用户 待办数
     */
    public function getUserPendingCount($uid, $channel = '')
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE || $channel == UserService::OA_REDDOT_CHANNEL_BY_OUTER || empty($uid)) {
            return 0;
        }

        // 发起人待提交 和 申请人待签字单数
        $apply_pending_count_1 = Reimbursement::count([
            'conditions' => '(apply_id = :uid: AND status = :waiting_signed:)',
            'bind'       => [
                'uid'               => $uid,
                'waiting_signed'    => ReimbursementEnums::STATUS_WAITING_SIGNED,
            ],
        ]);
        $apply_pending_count_2 = Reimbursement::count([
            'conditions' => '(created_id = :uid: AND status = :waiting_submitted:)',
            'bind'       => [
                'uid'               => $uid,
                'waiting_submitted' => ReimbursementEnums::STATUS_WAITING_SUBMITTED,
            ],
        ]);

        $count = 0;

        // BY 报销申请待办
        if ($channel == UserService::OA_REDDOT_CHANNEL_BY_MENU) {
            // 共同住宿人待确认单数
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => Reimbursement::class]);
            $builder->leftjoin(ReimbursementDetailTravelRoommateRelModel::class, 'main.id = rel.re_id', 'rel');
            $builder->where('rel.apply_staff_id = :apply_staff_id: AND rel.confirm_status = :confirm_status:', [
                'apply_staff_id' => $uid,
                'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
            ]);
            $builder->andWhere('main.status = :status:', ['status' => ReimbursementEnums::STATUS_WAITING_CONFIRMED]);
            $count = (int)$builder->columns('COUNT(DISTINCT(main.id)) AS total')->getQuery()->getSingleResult()->total;
        }

        return ($apply_pending_count_1 + $apply_pending_count_2 + $count);
    }

    /**
     * 移动端申请列表(待处理/全部)
     * @param $condition
     * @return array
     */
    public function getApplyListFromMobile($condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$condition['pageNum'];

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        $user_id = $condition['user_id'] ?? '';
        if (empty($user_id)) {
            return $data;
        }

        $order_no         = isset($condition['order_no']) && mb_strlen($condition['order_no']) > 0 ? $condition['order_no'] : '';
        $apply_start_date = $condition['apply_start_date'] ?? '';
        $apply_end_date   = $condition['apply_end_date'] ?? '';
        $apply_status     = $condition['apply_status'] ?? '';
        $pay_status       = $condition['pay_status'] ?? '';
        $data_type        = $condition['data_type'] ?? ReimbursementEnums::LIST_TAB_TYPE_PENDING;


        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => Reimbursement::class]);

        if ($data_type == ReimbursementEnums::LIST_TAB_TYPE_PENDING) {
            $where_sql  = [
                '(main.created_id = :user_id: AND main.status = :waiting_submitted:)',
                '(main.apply_id = :user_id: AND main.status = :waiting_signed:)',
                '(main.status = :waiting_confirmed: AND roommate_rel.apply_staff_id = :user_id: AND roommate_rel.confirm_status = :confirm_status:)',
            ];
            $where_bind = [
                'user_id'           => $user_id,
                'waiting_submitted' => ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                'waiting_signed'    => ReimbursementEnums::STATUS_WAITING_SIGNED,
                'waiting_confirmed' => ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                'confirm_status'    => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
            ];

            $builder->leftjoin(ReimbursementDetailTravelRoommateRelModel::class, 'roommate_rel.re_id = main.id', 'roommate_rel');
        } else {
            $where_sql  = [
                'main.created_id = :user_id:',
                'main.apply_id = :user_id:',
            ];
            $where_bind = [
                'user_id' => $user_id,
            ];
        }

        $where_sql = implode(' OR ', $where_sql);
        $builder->where($where_sql, $where_bind);
        if (!empty($order_no)) {
            $builder->andWhere('main.no = :order_no:', ['order_no' => $condition['order_no'],]);
        }

        if (!empty($apply_status)) {
            $builder->andWhere('main.status = :apply_status:', ['apply_status' => $apply_status,]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => $pay_status,]);
        }

        if (!empty($apply_start_date) && !empty($apply_end_date)) {
            $builder->andWhere('main.apply_date >= :apply_start_date: AND main.apply_date <= :apply_end_date:', [
                'apply_start_date' => $apply_start_date,
                'apply_end_date'   => $apply_end_date,
            ]);
        }

        // 总条数
        $count = (int)$builder->columns('COUNT(DISTINCT(main.id)) AS total')->getQuery()->getSingleResult()->total;

        // 取列表
        $items = [];
        if ($count) {
            $columns = [
                'main.id',
                'main.no',
                'main.apply_date',
                'main.amount',
                'main.status',
                'main.pay_status',
                'main.created_id',
                'main.apply_id',
                'main.apply_name',
            ];

            $builder->columns($columns);
            $builder->orderBy('main.no DESC');
            $builder->groupBy('main.id');
            $builder->limit($page_size, $page_size * ($page_num - 1));

            $items = $builder->getQuery()->execute()->toArray();

            // 报销申请状态
            $status_item = $this->getStatusItem(self::LIST_TYPE_APPLY);
            $status_item = array_column($status_item, 'label', 'code');

            GlobalEnums::getSysDefaultCurrency();
            $currency_symbol_simple = GlobalEnums::$sys_default_currency_symbol_simple;

            foreach ($items as &$val) {
                $val['amount']           = $currency_symbol_simple . bcdiv($val['amount'], 1000, 2);
                $val['status_label']     = $status_item[$val['status']] ?? '';
                $val['pay_status_label'] = $val['pay_status'] ? static::$t->_(Enums::$loan_pay_status[$val['pay_status']]) : '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ],
        ];
    }

    /*
     * 返回可同步至金蝶的报销列表
     * @param array $params['kingdee_company_ids' => '金蝶BU公司ID组', 'date_start' => '数据范围起始日期', 'date_end' => '数据范围截止日期', 'pay_type' => '1应付,2付款', 'is_cancel'=>'是否取消付款，0否，1是', 'max_id' => '最大id值']
     * @return mixed
     */
    public function getSendKingDeeList($params)
    {
        $page_size = 100;
        $kingdee_company_ids = $params['kingdee_company_ids'];
        $date_start = $params['date_start'];
        $date_end = $params['date_end'];
        $pay_type = $params['pay_type'] ?? 0;
        $is_cancel = $params['is_cancel'] ?? 0;
        $max_id = $params['max_id'] ?? 0;
        $builder = $this->modelsManager->createBuilder();
        $builder->from(Reimbursement::class);
        $builder->where('status = :status: and id > :max_id:', ['status' => Enums::WF_STATE_APPROVED, 'max_id' => $max_id]);
        $builder->inWhere('cost_company_id', $kingdee_company_ids);
        if ($pay_type == KingDeeEnums::PAY_TYPE_PAYABLE && $is_cancel == KingDeeEnums::IS_CANCEL_PAY_NO) {
            //应付正向
            $builder->andWhere('payable_positive_is_send_kingdee = :is_send: and approved_at >= :date_start: and approved_at < :date_end:', ['is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        } else if ($pay_type == KingDeeEnums::PAY_TYPE_PAYABLE && $is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) {
            //应付反向
            $builder->andWhere('pay_status = :pay_status: and payable_negative_is_send_kingdee = :is_send: and pay_operate_date >= :date_start: and pay_operate_date < :date_end:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY, 'is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        } else if ($pay_type == KingDeeEnums::PAY_TYPE_PAYBILL) {
            //付款
            $builder->andWhere('pay_status = :pay_status: and paybill_is_send_kingdee = :is_send: and pay_at >= :date_start: and pay_at < :date_end: and real_amount > 0', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY, 'is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        }
        $builder->limit($page_size);
        $builder->orderBy('id ASC');
        return $builder->getQuery()->execute();
    }

    /**
     * 获取报销应付参数
     * @param object $item 报销单据对象信息
     * @param array $params['is_cancel' => '应付（付款）是否取消支付：0否（正向），1是（反向）', 'expire_date' => '到期日', 'vendor_id' => '供应商编码', 'sap_company_list' => 'sap公司ID组', 'account_subjects_list' => '会计科目组']
     * @return array
     */
    public function getPayableParams($item, $params)
    {
        $is_cancel = $params['is_cancel'];
        $details = $item->getDetails();
        $payable_params = [
            'bill_no' =>  ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? 'CN' : '') . get_country_code() . $item->no,
            'apply_date' => date('Y-m-d' , strtotime($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? $item->pay_operate_date : $item->approved_at)),
            'expire_date' => $params['expire_date'],
            'vendor_id' => $params['vendor_id'],
            'business_type_no' => $params['business_type_no'] ?? '',
            'currency' => $item->currency,
            'sap_company_id' => $params['sap_company_list'][$item->cost_company_id]['sap_company_id'] ?? '',
            'no' => $item->no,
            'amount' => bcdiv($item->amount, 1000, 2),
            'cost_store_name' => $item->cost_store_name,
            'is_offset_loan' => ($item->loan_amount > 0) ? '是' : '否',
            'loan_amount' => bcdiv($item->loan_amount, 1000, 2)
        ];
        foreach ($details as $detail) {
            $tax_not = bcdiv($detail->tax_not, 1000, 2);//不含税单价
            $amount = bcdiv($detail->amount, 1000, 2);//含税单价
            
            $_abstract = '';
            if (!empty($detail->budget_id)){
                $budget_object = BudgetObject::findFirst([
                    'id=:id:',
                    'bind'=>['id'=>$detail->budget_id]
                ]);
                $_abstract = $budget_object->name_en;
            }
            if (!empty($detail->product_id)){
                $budget_object_product = BudgetObjectProduct::findFirst([
                    'id=:id:',
                    'bind'=>['id'=>$detail->product_id]
                ]);
                $_abstract = !empty($_abstract) ? $_abstract .'(' . $budget_object_product->name_en . ')' : $budget_object_product->name_en;
            }
            $payable_params['details'][] = [
                'company_project' => $params['company_project_enum'][$params['sap_company_list'][$item->cost_company_id]['sap_company_id']] ?? '',
                'abstract' => $item->no . '_' . $detail->start_at . '_' . $detail->end_at . '_' . $_abstract,
                // budget_id 对应表budget_object.name_en
                //  product_id如果为空 就不查了 不为空去查budget_object_product.name_en
                'cost_center_code' => $detail->cost_center_code,
                'quantity' => $is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? -1 : 1,
                'tax_not_price' => $tax_not,
                'tax_not' => $tax_not,
                'amount_price' => $amount,
                'amount' => $amount,
                'rate' => DetailService::getInstance()->getViewRate($detail->rate),
                'tax' => bcdiv($detail->tax, 1000, 2),
                'subjects_code' => $params['account_subjects_list'][$detail->ledger_account_id]['subjects_code'] ?? '',
            ];
        }
        return $payable_params;
    }
}
