<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\EndroidQRCode;
use App\Library\Enums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\SettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\oa\ReimbursementDetailReferenceModel;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Models\oa\ReimbursementDraftModel;
use App\Models\oa\ReimbursementInvalidRecordModel;
use App\Models\oa\ReimbursementSignatureRecordModel;
use App\Models\oa\SettingInvoiceTaxNoModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Reimbursement\Models\HrStaffApplySupportStore;
use App\Modules\Reimbursement\Models\ReimbursementDetailTicketModel;
use App\Modules\Reimbursement\Models\ReimbursementMealAllowanceModel;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\OtherPccode;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;
use App\Modules\Setting\Services\CostCategoryReimbursementDetailService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\TripModel;
use App\Modules\User\Services\StaffService;
use App\Repository\backyard\BusinessTripRepository;
use App\Repository\backyard\HrJobDepartmentRelationRepository;
use App\Repository\backyard\HrStaffApplySupportStoreRepository;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\backyard\SysCityRepository;
use App\Repository\backyard\SysDistrictRepository;
use App\Repository\backyard\SysProvinceRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\oa\ReimbursementDomesticAccommodationQuotaRepository;
use App\Repository\oa\ReimbursementDomesticAirTicketQuotaRepository;
use App\Repository\oa\ReimbursementFuelQuotaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationAreaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationQuotaRepository;
use App\Repository\oa\SettingInvoiceTaxNoRepository;
use App\Repository\oa\SysExchangeRateRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use App\Modules\Reimbursement\Models\HrOutsourcingOrder;
use App\Modules\Contract\Models\SysStoreModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Library\Enums\GlobalEnums;
use App\Repository\HrStaffRepository;
use App\Repository\oa\SettingInvoiceHeaderRepository;

class AddService extends BaseService
{
    public static $not_must_params = [
        'business_type',
        //前端情况选择后, 这个字段会穿空, 需要过滤一下
        'loan_id'
    ];
    //是否需要罚金的报销实质
    public static $is_fine_arr = [
        '016'//水电费
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 验证报销单号是否被占用, 若被占用则重新生成新的可用的(暂不重新生成)
     */
    protected function checkAvailableNo($no)
    {
        static $retry_count = 1;

        // 验证单号是否已创建 或 占用
        $exist_data_by_no = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $no],
            'columns'    => ['id'],
        ]);

        if (!empty($exist_data_by_no)) {
            throw new ValidationException(static::$t->_('reimbursement_no_exist_hint', ['no' => $no]), ErrCode::$VALIDATE_ERROR);
        }

        return $no;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function one($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 验证单号是否已创建 或 占用
            $this->checkAvailableNo($data['no']);

            $data = $this->handleData($data, $user, ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT);

            $loanItem = [];

            // 报销有关联借款单: 抵扣报销额度(冲减借款金额)
            if (!empty($data['loan_id'])) {
                // 验证本次报销关联的借款单可抵扣金额
                $temp = \App\Modules\Loan\Services\ListService::getInstance()->getAmountFromCurrency(
                    $data['loan_amount'],    //乘以1000以后的数，方法里用is_submit判断。
                    $data['currency'],
                    $data['loan_id'],
                    $user['id'],
                    true,
                    $data['apply_id'],
                    trim($data['cost_company_id'])
                );

                $this->logger->info('报销关联借款单, 可冲减借款金额(getAmountFromCurrency):' . json_encode($temp, JSON_UNESCAPED_UNICODE));

                if ($temp['code'] == ErrCode::$VALIDATE_ERROR) {
                    throw new ValidationException($temp['message'], ErrCode::$VALIDATE_ERROR);
                } elseif ($temp['code'] == ErrCode::$SYSTEM_ERROR) {
                    throw new BusinessException($temp['message'], ErrCode::$BUSINESS_ERROR);
                } else {
                    if (!empty($temp['loan'])) {
                        $loanItem = $temp['loan'];
                    }

                    // 本次报销可冲抵的借款金额
                    $data['loan_amount'] = $temp['data'];
                    $data['real_amount'] = bcsub($data['payable_amount_all'], $data['loan_amount']);
                    if ($data['real_amount'] < 0) {
                        $data['real_amount'] = 0;
                    }
                }
            } else {
                //前端有可能会乱传
                $data['loan_amount'] = 0;
                $data['real_amount'] = $data['payable_amount_all'];
            }

            $model = new Reimbursement();
            if ($model->i_create($data) === false) {
                throw new BusinessException('报销创建失败 = ' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            //关联备用金
            if (!empty($data['rfano'])) {
                $rfrei         = new  ReserveFundReimburse();
                $rfrei->rfano  = $data['rfano'];
                $rfrei->rei_id = $model->id;
                if ($rfrei->save() === false) {
                    throw new BusinessException('备用金关联报销单创刊失败=' . get_data_object_error_msg($rfrei), ErrCode::$BUSINESS_ERROR);
                }
            }

            if (!empty($loanItem)) {
                $rels               = [];
                $temp               = [];
                $temp['re_id']      = $model->id;
                $temp['loan_id']    = $loanItem['id'];
                $temp['amount']     = $data['loan_amount'];
                $temp['created_at'] = date("Y-m-d H:i:s");
                $temp['updated_at'] = date("Y-m-d H:i:s");
                $rels[]             = $temp;

                $this->logger->info('报销关联借款单(reimbursement_rel_loan):' . json_encode($rels, JSON_UNESCAPED_UNICODE));

                $reimbursement_rel_loan_model = new ReimbursementRelLoan();
                if ($reimbursement_rel_loan_model->batch_insert($rels) === false) {
                    throw new BusinessException('报销-批量添加关联借款失败 = ' . get_data_object_error_msg($reimbursement_rel_loan_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            // 明细行数据处理
            $detail_save_result = $this->commonSaveDetailItem($data['expense'], $model, ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT);
            $amount_info        = $detail_save_result['amount_info'] ?? [];

            // 创建报销单事件处理
            $this->createEventCallBack(ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT, $data, $amount_info, $user);

            // 发BY消息: 待确认/待签字
            $msg_scence = '';
            $staff_ids  = [];
            if ($model->travel_is_have_roommate == ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES) {
                $msg_scence = ReimbursementEnums::MSG_SCENCE_WAITING_CONFIRMED;

                foreach ($data['expense'] as $expense) {
                    $staff_ids = array_merge($staff_ids, array_column($expense['travel_roommate_item'] ?? [], 'apply_staff_id'));
                }

                $staff_ids = array_unique(array_filter($staff_ids));
            } elseif ($model->apply_id != $model->created_id) {
                $msg_scence  = ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED;
                $staff_ids[] = $model->apply_id;
            }

            if (!empty($msg_scence) && !empty($staff_ids)) {
                $this->sendMsgNotice($msg_scence, $staff_ids, $model);
            }

            $db->commit();

            $result['no'] = $model->no;

            // 检测当前编号是否是当前用户下的草稿, 有则删
            if (!empty($data['is_submit']) && $data['source_type'] == ReimbursementEnums::SOURCE_TYPE_OA) {
                $draft_model = ReimbursementDraftModel::findFirst([
                    'conditions' => 'apply_no = :apply_no: AND created_id = :created_id:',
                    'bind'       => ['apply_no' => $data['no'], 'created_id' => $user['id']],
                ]);
                if (!empty($draft_model)) {
                    if ($draft_model->delete() === false) {
                        $this->logger->notice("reimbursement-create-clear-draft-result: 失败, {$draft_model->created_id} - {$draft_model->apply_no}, 原因可能是=" . get_data_object_error_msg($draft_model));
                    } else {
                        $this->logger->info("reimbursement-create-clear-draft-result: 成功, {$draft_model->created_id} - {$draft_model->apply_no}");
                    }
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出 6001 可提交
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code   = ErrCode::$SUCCESS;
                $result = [
                    'message'   => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? ReimbursementEnums::CAN_APPLY_NO : ReimbursementEnums::CAN_APPLY_YES,
                ];
            } else {
                $code = $e->getCode();
            }

            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('reimbursement-create-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result
        ];
    }

    /**
     * 添加文件附件
     * @param $data
     * @param $user
     * @return array
     */
    public function addFile($data, $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'ok';
        $real_message = '';

        try {
            // 报销单是否存在
            $reimbursement = $this->reimbursementInfo($data['id']);
            if (empty($reimbursement)) {
                throw new BusinessException('该报销单不存在，报销单ID => ' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($data['required_supplement_file'])) {
                $attach_arr = $oss_bucket_key = [];
                foreach ($data['required_supplement_file'] as $k => $file) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE;
                    $tmp['oss_bucket_key']  = $file['detail_id'];
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $file['bucket_name'];
                    $tmp['object_key']      = $file['object_key'];
                    $tmp['file_name']       = $file['file_name'];
                    $tmp['created_at']      = date('Y-m-d H:i:s');
                    $attach_arr[]           = $tmp;
                    $oss_bucket_key[]       = $file['detail_id'];
                }
                if (!empty($attach_arr) && !empty($oss_bucket_key)) {
                    // 删除历史关联附件
                    $old_attachment_model = AttachModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({oss_bucket_key:array})',
                        'bind'       => [
                            'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,
                            'oss_bucket_key'  => array_values(array_unique($oss_bucket_key)),
                        ],
                    ]);

                    if (!empty($old_attachment_model->toArray())) {
                        $old_attachment_model->delete();
                    }

                    // 编辑附件
                    $attach      = new AttachModel();
                    $attach_bool = $attach->batchInsert($attach_arr);
                    if ($attach_bool === false) {
                        throw new BusinessException('报销申请-附件添加失败，attachment data => ' .
                            json_encode($data['required_supplement_file'], JSON_UNESCAPED_UNICODE),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->error('reimbursement-add-attachment-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result ?? []
        ];
    }

    /**
     * 释放报销
     * 兼容老数据
     *
     * @param $id
     * @param $user
     */
    public function freeBudget($id, $user)
    {
        // 验证默认国家是否开启预算
        $budgetStatus = (new EnumsService())->getBudgetStatus();
        if (!$budgetStatus) {
            return true;
        }
        $reimbursement = $this->reimbursementInfo($id);

        // 兼容菲律宾/马来历史未开启预算的部分数据
        $state_date_time = '2022-01-01 00:00:00';
        $country_code    = get_country_code();
        if (in_array($country_code, ['MY', 'PH']) && $reimbursement['created_at'] < $state_date_time) {
            return true;
        }
        // 兼容印尼/越南历史未开启预算的部分数据 TODO 优化代码
        $state_date_time = '2022-03-31 00:00:00';
        if (in_array($country_code, ['ID', 'VN']) && $reimbursement['created_at'] < $state_date_time) {
            return true;
        }
        if (isset($reimbursement['detail']) && isset($reimbursement['detail'][0]['budget_id']) && $reimbursement['detail'][0]['budget_id']) {
            // 是 新数据 预算占用
            $freedAmount = [];
            foreach ($reimbursement['detail'] as $detail) {
                if (isset($freedAmount[$detail['level_code']])) {
                    $freedAmount[$detail['level_code']] += 0;
                } else {
                    $freedAmount[$detail['level_code']] = 0;
                }
            }

            $budgetService = new BudgetService();
            $result        = $budgetService->re_back_budget($reimbursement['no'], $user, BudgetService::ORDER_TYPE_1, $freedAmount);
            $this->getDI()->get('logger')->info('reimbursement  释放预算判断 params ' . json_encode([
                    $reimbursement['no'],
                    $user,
                    BudgetService::ORDER_TYPE_1,
                    $freedAmount
                ]) . ' results ' . json_encode([$result]));

            if ($result['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($result['message']);
            }
        }

        return true;
    }


    private function reimbursementInfo($id)
    {
        $reimbursement = Reimbursement::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $id]
        ]);
        $reimbursement = $reimbursement ? $reimbursement->toArray() : [];
        if ($reimbursement) {
            $detail                  = Detail::find([
                'conditions' => ' re_id = :re_id:',
                'bind'       => ['re_id' => $id]
            ])->toArray();
            $reimbursement['detail'] = $detail;
        }

        return $reimbursement;
    }

    /**
     * 验证报销实质中的出差单是否合规, 即已审批通过
     *
     * @param array $expense
     * @return bool
     * @throws ValidationException
     */
    protected function checkTravelSerialNoStatus(array $expense = [])
    {
        $travel_serial_no_list = array_filter(array_column($expense, 'travel_serial_no'));
        if (empty($travel_serial_no_list)) {
            return true;
        }

        // 获取申请人的出差单号是否均审批通过
        $trip_list = TripModel::find([
            'conditions' => 'serial_no IN ({serial_no:array}) AND status != :status:',
            'bind'       => ['serial_no' => array_values($travel_serial_no_list), 'status' => Enums::TRAFFIC_STATUS_APPROVAL],
            'columns'    => ['serial_no']
        ])->toArray();
        if (empty($trip_list)) {
            return true;
        }

        $trip_list = array_column($trip_list, 'serial_no');
        foreach ($travel_serial_no_list as $serial_no) {
            if (in_array($serial_no, $trip_list)) {
                throw new ValidationException(static::$t->_('reimbursement_travel_serial_no_error', ['travel_serial_no' => $serial_no]),
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }


    /**
     * 报销添加/重新提交的公共数据校验及处理
     *
     * @param $data
     * @param $user
     * @param $create_acion
     * @return array|string
     * @throws ValidationException
     */
    private function handleData($data, $user, $create_acion)
    {
        $country_code = get_country_code();

        // 校验是否限制本位币
        $is_restrict_self_currency = EnumsService::getInstance()->getSettingEnvValue('reimbursement_is_restrict_self_currency', '0');
        if ($is_restrict_self_currency == 1) {
            //获取本位币
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            if ($data['currency'] != $default_currency['code']) {
                throw new ValidationException(static::$t->_('reimbursement_currency_not_self'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 申请人信息
        $apply_staff_info = HrStaffInfoModel::getUserInfo($data['apply_id']);
        if (empty($apply_staff_info)) {
            throw new ValidationException(static::$t->_('apply_staff_info_not_exist_hris', ['staff_id' => $data['apply_id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        //申请人 是否 lnt 公司
        $isLnt = HrStaffRepository::isLntCompanyByInfo($apply_staff_info);
        if ($isLnt) {
            throw new ValidationException(static::$t->_('lnt_company_cannot_apply_reimbursement'), ErrCode::$VALIDATE_ERROR);
        }

        //泰国/菲律宾/马来 && 雇佣类型=个人不可申请报销
        $limit_country_item = [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE];
        if ((in_array($country_code,
                    $limit_country_item) && $apply_staff_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) || ($country_code == GlobalEnums::MY_COUNTRY_CODE && $apply_staff_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT)) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_formal'), ErrCode::$VALIDATE_ERROR);
        }

        // 发票抬头处理
        $invoice_header_model = SettingInvoiceHeaderRepository::getInstance()->getInfoById((int)$data['invoice_header_id']);
        if (empty($invoice_header_model)) {
            throw new ValidationException(static::$t->_('invoice_header_selected_error', ['id' => $data['invoice_header_id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        // 发票抬头对应公司 是否 与 费用所属公司一致
        $invoice_header_is_same = SettingEnums::INVOICE_HEADER_COMPANY_AND_COST_COMPANY_SAME;
        $related_company_ids    = explode(',', $invoice_header_model->related_company_ids);
        if (!in_array(SettingEnums::INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL, $related_company_ids) && !in_array($data['cost_company_id'],
                $related_company_ids)) {
            $invoice_header_is_same = SettingEnums::INVOICE_HEADER_COMPANY_AND_COST_COMPANY_NOT_SAME;
        }

        $data['invoice_header_name']    = $invoice_header_model->header_name;
        $data['invoice_header_is_same'] = $invoice_header_is_same;

        // 获取币种与系统默认币种的汇率
        $exchange_rate         = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        //金额都是乘以1000的
        $travel_amount      = 0;
        $local_amount       = 0;
        $amount             = 0;
        $payable_amount_all = 0;
        $budgetIds          = array_column($data['expense'], 'budget_id');

        // 验证 是否存在非末级科目ID
        if ($this->isHasNotEndBudgetIds($data, $budgetIds)) {
            throw new ValidationException(static::$t->_('reimbursement_budget_detail_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        // 验证出差单号是否合规
        $this->checkTravelSerialNoStatus($data['expense']);

        $budgetService = new BudgetService();
        $budgetList    = $budgetService->budgetObjectList($budgetIds);

        $firstDepartment             = StaffService::getInstance()->getParentDepartment($data['cost_department'], 1);
        $firstDepartmentId           = $firstDepartment['id'] ?? 0;
        $data['cost_sys_department'] = $firstDepartmentId;

        // 报销类型清单
        $reimbursement_type_item = array_fill(0, count(Enums::$budget_object_reimbursement_type_map), 0);

        //判断申请人所属网点id是否是shop类型网点
        $is_store_shop = (new StoreService())->isShopStore($data['apply_store_id']);

        $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');

        $tickets_no = $travel_serial_no = $budget_product_ids = [];

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);

        // 报销实质和费用明细(预算科目和product_id)
        $budget_product_relation = [];

        // 油费科目校验
        $fuel_cost_budget_limit_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_add_about_fuel_cost_budget_limit_config');
        $fuel_cost_standard_detail_map = $fuel_cost_budget_limit_config['fuel_cost_standard_detail_map'] ?? [];
        $fuel_cost_standard_detail_map = array_column($fuel_cost_standard_detail_map, 'amount', 'job_dept_id_key');

        // 网点支援距离
        $support_store_mileage_map = [];

        // 获取支援单号对应的距离
        $support_serial_nos = implode(',', array_column($data['expense'], 'support_serial_no'));
        $support_serial_nos = array_filter(array_unique(explode(',', $support_serial_nos)));
        if (!empty($support_serial_nos) && !empty($fuel_cost_budget_limit_config)) {
            $support_store_mileage_map = HrStaffApplySupportStore::find([
                'conditions' => 'serial_no IN ({serial_nos:array})',
                'bind'       => ['serial_nos' => array_values($support_serial_nos)],
                'columns'    => ['apart', 'serial_no'],
            ])->toArray();
            $support_store_mileage_map = array_column($support_store_mileage_map, 'apart', 'serial_no');
        }

        $exist_support_product_searial_list = [];

        //19703【MY|OA|报销】餐费报销金额限制
        $travel_serial_no_info                  = [];                                                                                          //每个出差编号下的相关信息
        $reimbursement_meal_expense_product_ids = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_meal_expense_product_ids');//获取餐补明细配置组

        // 差旅科目ID
        $travel_budget_id = static::getTravelTypeBudgetId();

        // 报销实质的数据处理
        foreach ($data['expense'] as $k => $expense) {
            //如果是总部，则清除费用网点
            if ($data['cost_store_type'] == 2) {
                unset($data['expense'][$k]['cost_store_n_id']);
                unset($data['expense'][$k]['cost_store_n_name']);
            }

            if (isset($data['cost_company_id']) && in_array($data['cost_company_id'], $cost_company_id) && empty($expense['cost_center_code'])) {
                throw new ValidationException(static::$t->_('cost_center_required_error'), ErrCode::$VALIDATE_ERROR);
            }
            //菲律宾 实质为水电费 必填写罚金
            if ($country_code == GlobalEnums::PH_COUNTRY_CODE && isset($budgetList[$expense['budget_id']]) && in_array($budgetList[$expense['budget_id']]['level_code'] ?? '',
                    self::$is_fine_arr) && !isset($expense['is_fine'])) {
                throw new ValidationException(static::$t->_('is_fine_required_error'), ErrCode::$VALIDATE_ERROR);
            }
            //除了水电费和菲律宾的罚金 一律为否
            if (($country_code != GlobalEnums::PH_COUNTRY_CODE || ($country_code == GlobalEnums::PH_COUNTRY_CODE && !in_array($budgetList[$expense['budget_id']]['level_code'] ?? '',
                            self::$is_fine_arr))) && isset($expense['is_fine']) && $expense['is_fine'] == Enums\ReimbursementEnums::IS_FINE) {
                throw new ValidationException(static::$t->_('is_fine_required_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 发票编号校验格式
            if (
                isset($expense['ticket_type'])
                &&
                (GlobalEnums::REIMBURSEMENT_TICKET_TYPE_ELECTRIC == $expense['ticket_type'] || (GlobalEnums::REIMBURSEMENT_TICKET_TYPE_PAPER == $expense['ticket_type'] && GlobalEnums::REIMBURSEMENT_TICKET_EXIST == $expense['is_ticket_no_exist']))
            ) {
                $temp_count = count($expense['invoices_ids'] ?? []);
                if ($temp_count < 1 || $temp_count > 10) {
                    throw new ValidationException(static::$t->_('params_error', ['param' => 'invoices_ids']),
                        ErrCode::$VALIDATE_ERROR);
                }

                foreach ($expense['invoices_ids'] as $invoice) {
                    // 格式校验
                    if (!preg_match("/^[0-9a-zA-Z\'\-\.\(\)\/]{1,50}$/", $invoice)) {
                        throw new ValidationException(static::$t->_('invoice_ids_format_error'), ErrCode::$VALIDATE_ERROR);
                    }

                    $tickets_no[] = $invoice;
                }
            }

            // 出差审批编号
            if (GlobalEnums::TH_COUNTRY_CODE == $country_code && $travel_budget_id == $expense['budget_id'] && !empty($expense['travel_serial_no'])) {
                $travel_serial_no[] = $expense['travel_serial_no'];
            }
            //vat 税额不为0 vat 税率不可为0
            if ($expense['tax'] != 0 && 0 == $expense['rate']) {
                throw new ValidationException(static::$t->_('vat_rate_is_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 报销明细ID
            $budget_product_ids[] = $expense['product_id'];

            // 关系数据
            $budget_product_relation[$expense['budget_id']][] = $expense['product_id'];

            $data['expense'][$k]['tax_not'] = bcmul(round($expense['tax_not'], 2), 1000);

            //历史税率传0.06 改后传6 为兼容历史数据, 6先除100再乘1000入库
            $rate                        = bcdiv((string)$expense['rate'], 100, 4);
            $data['expense'][$k]['rate'] = bcmul((string)$rate, 1000);
            $data['expense'][$k]['tax']  = bcmul(round($expense['tax'], 2), 1000);

            $data['expense'][$k]['budget_id']    = $expense['budget_id'];
            $data['expense'][$k]['level_code']   = $budgetList[$expense['budget_id']]['level_code'];
            $data['expense'][$k]['product_id']   = isset($expense['product_id']) ? $expense['product_id'] : 0;
            $data['expense'][$k]['product_name'] = isset($expense['product_name']) ? $expense['product_name'] : '';

            $t_amount = $data['expense'][$k]['tax'] + $data['expense'][$k]['tax_not'];

            $data['expense'][$k]['amount'] = bcmul(round($expense['amount'], 2), 1000);

            //[9542]新增字段
            //WHT税率 直接入库, 计算/100
            $data['expense'][$k]['wht_tax'] = (string)($expense['wht_tax'] ?? '');

            //WHT税额 = 发票金额（不含VAT含WHT）* WHT税率
            $data['expense'][$k]['wht_tax_amount'] = bcmul(round($expense['wht_tax_amount'] ?? 0, 2), 1000);

            //可抵扣VAT税率 直接入库, 计算/100
            $deductible_vat_tax                        = $expense['deductible_vat_tax'] ?? 0;
            $data['expense'][$k]['deductible_vat_tax'] = (string)$deductible_vat_tax;

            //可抵扣税额=VAT税额*可抵扣VAT税率
            $data['expense'][$k]['deductible_tax_amount'] = bcmul(round($expense['deductible_tax_amount'] ?? 0, 2), 1000);

            $data['expense'][$k]['payable_amount'] = bcmul(round($expense['payable_amount'], 2), 1000);


            if ($expense['category_a'] == Enums::REIMBURSEMENT_EXPENSE_TRAVEL) {
                $travel_amount = bcadd($travel_amount, $t_amount);
            }

            if ($expense['category_a'] == Enums::REIMBURSEMENT_EXPENSE_LOCAL) {
                $local_amount = bcadd($local_amount, $t_amount);
            }

            // 来源by给默认值
            if (ReimbursementEnums::SOURCE_TYPE_BY == $data['source_type']) {
                $data['expense'][$k]['wht_type'] = $wht_cat_map['/'] ?? 0;
            }

            $amount             = bcadd($amount, $t_amount);
            $payable_amount_all = bcadd($payable_amount_all, $data['expense'][$k]['payable_amount']);

            if (isset($expense['fuel_mileage']) && $expense['fuel_mileage']) {
                $data['expense'][$k]['fuel_mileage'] = bcmul($expense['fuel_mileage'], 1000);
            }

            if (isset($expense['fuel_start_mileage']) && $expense['fuel_start_mileage']) {
                $data['expense'][$k]['fuel_start_mileage'] = bcmul($expense['fuel_start_mileage'], 1000);
            }

            if (isset($expense['fuel_end_mileage']) && $expense['fuel_end_mileage']) {
                $data['expense'][$k]['fuel_end_mileage'] = bcmul($expense['fuel_end_mileage'], 1000);
            }

            // 油费限制校验
            if (!empty($fuel_cost_standard_detail_map)) {
                $_fule_mileage = 0;

                // 差旅油费
                $apply_job_dept_id_key = $data['apply_job_title_id'] . '_' . $data['sys_department_id'];
                if (in_array($expense['product_id'], $fuel_cost_budget_limit_config['trip_fuel_cost_product_ids'])) {
                    $apply_job_dept_id_key .= '_1';
                    $_fule_mileage         = is_numeric($expense['fuel_mileage']) ? $expense['fuel_mileage'] : 0;
                } elseif (in_array($expense['product_id'], $fuel_cost_budget_limit_config['support_fuel_cost_product_ids'])) {
                    // 支援油费
                    $apply_job_dept_id_key .= '_2';

                    // 多个支援单据
                    foreach ($expense['support_serial_no_item'] as $per_support_serial_no) {
                        // 支援油费明细+支援编号, 不可同时提交多个报销实质
                        $support_product_searial_key = $expense['product_id'] . '_' . $per_support_serial_no;
                        if (isset($exist_support_product_searial_list[$support_product_searial_key])) {
                            throw new ValidationException(static::$t->_('reimbursement_save_error_003', ['detail_no' => $k + 1]),
                                ErrCode::$VALIDATE_ERROR);
                        }

                        $exist_support_product_searial_list[$support_product_searial_key] = 1;

                        // 支援单距离合计
                        $_fule_mileage += ($support_store_mileage_map[$per_support_serial_no] ?? 0) / 1000;
                    }
                }

                $fuel_cost_standard_amount = $fuel_cost_standard_detail_map[$apply_job_dept_id_key] ?? null;
                if (!is_null($fuel_cost_standard_amount)) {
                    // 发票金额(含vat含wht) 根据汇率 转为当前国家币种的金额值
                    $_detail_amount = EnumsService::getInstance()->amountExchangeRateCalculation($expense['amount'], $data['exchange_rate'], 2);

                    // 油费报销标准金额(已是当前国家币种的金额)
                    $fuel_cost_standard_amount = is_numeric($fuel_cost_standard_amount) ? $fuel_cost_standard_amount : 0;

                    // 可报销的标准油费额度
                    $_standard_detail_amount = bcmul($_fule_mileage * $fuel_cost_standard_amount, 2, 2);

                    // 发票金额(含vat含wht) 超出 油费可报销金额
                    if (bccomp($_detail_amount, $_standard_detail_amount, 2) > 0) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_002',
                            ['detail_no' => $k + 1, 'detail_aomunt' => $_standard_detail_amount]), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }


            $temp_type = Enums::$budget_object_reimbursement_type_map[$budgetList[$expense['budget_id']]['level_code']] ?? Enums::REIMBURSEMENT_TYPE_OTHER;

            //如果是税金，需要判断网点类型是否是shop
            if ($temp_type == Enums::REIMBURSEMENT_TYPE_TAX) {
                //shop部门，且是广告牌税
                if ($is_store_shop && $expense['product_id'] == Enums::REIMBURSEMENT_EXPENSE_AD) {
                    $reimbursement_type_item[$temp_type]++;
                } else {
                    $reimbursement_type_item[Enums::REIMBURSEMENT_TYPE_OTHER]++;
                }
            } else {
                $reimbursement_type_item[$temp_type]++;
            }

            if (!empty($expense['travel_id'])) {
                // 当前报销实质信息
                $curr_budget_info = $budgetList[$expense['budget_id']] ?? [];
                $budget_info      = [
                    'level_code' => $curr_budget_info['level_code'] ?? '',
                    'product_id' => $expense['product_id'] ?? ''
                ];

                $used_reimbursement_total = $this->isTravelIdUsed($expense['travel_id'], $data['apply_id'], $data['source_type'], $budget_info);
                $limit                    = ($country_code == GlobalEnums::MY_COUNTRY_CODE) ? 1 : 0;
                if ($used_reimbursement_total > $limit) {
                    $hint_msg_key = 'travel_serial_no_is_overflow';
                    throw new ValidationException(static::$t->_($hint_msg_key, ['num' => $used_reimbursement_total]), ErrCode::$VALIDATE_ERROR);
                }

                //19703,明细ID在餐补明细配置组，按照差旅费编号汇总发票金额（含VAT含WHT）
                if (in_array($expense['product_id'], $reimbursement_meal_expense_product_ids)) {
                    // 发票金额(含vat含wht) 根据汇率 转为当前国家币种的金额值
                    $_detail_amount = EnumsService::getInstance()->amountExchangeRateCalculation($expense['amount'], $data['exchange_rate'], 2);
                    if (isset($travel_serial_no_info[$expense['travel_serial_no']])) {
                        $travel_serial_no_info[$expense['travel_serial_no']]['amount'] = bcadd($travel_serial_no_info[$expense['travel_serial_no']]['amount'],
                            $_detail_amount, 2);
                    } else {
                        $travel_serial_no_info[$expense['travel_serial_no']]['days_num']           = $expense['travel_days_num'] ?? 0;
                        $travel_serial_no_info[$expense['travel_serial_no']]['business_trip_type'] = $expense['travel_business_trip_type'] ?? 0;
                        $travel_serial_no_info[$expense['travel_serial_no']]['travel_start_at']    = $expense['travel_start_at'];
                        $travel_serial_no_info[$expense['travel_serial_no']]['amount']             = $_detail_amount;
                    }
                }
            }

            //19703存储差旅-出差天数、出差类型
            $data['expense'][$k]['travel_days_num']           = $expense['travel_days_num'] ?? 0;
            $data['expense'][$k]['travel_business_trip_type'] = $expense['travel_business_trip_type'] ?? 0;

            // 菲律宾的: 发票类型
            $data['expense'][$k]['invoice_type'] = $country_code == GlobalEnums::PH_COUNTRY_CODE ? $expense['invoice_type'] : GlobalEnums::FINANCIAL_INVOICE_TYPE_0;

            // 印尼: 是否有增值税票
            $data['expense'][$k]['is_with_vat_invoice'] = $country_code == GlobalEnums::ID_COUNTRY_CODE ? $expense['is_with_vat_invoice'] : GlobalEnums::IS_WITH_VAT_INVOICE_DEFAULT;

            $data['expense'][$k]['support_serial_no'] = '';
        }

        // 发票编号是否已存在
        if (!empty($tickets_no)) {
            $tickets_reimbursement_no = $this->getTicketsNo($tickets_no);
            if (!empty($tickets_reimbursement_no)) {
                throw new ValidationException(str_replace('{no}', $tickets_reimbursement_no, static::$t->_('invoice_ids_is_used')),
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        // 已经存在报销明细-油费，不能再申请油费 TODO
        if (GlobalEnums::TH_COUNTRY_CODE == $country_code && !empty($travel_serial_no) && $this->isExistOilProduct($travel_serial_no) && in_array(GlobalEnums::BUDGET_TRAVEL_OIL_PRODUCT_ID,
                $budget_product_ids)) {
            throw new ValidationException(static::$t->_('travel_oil_product_exist'), ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info('报销提交 - 报销类型[实质]==列表单号=' . $data['no'] . ' - type_item = ' . json_encode($reimbursement_type_item,
                JSON_UNESCAPED_UNICODE));

        $first_type   = 0;
        $not_zero_num = 0;
        foreach ($reimbursement_type_item as $type => $value) {
            //没有这个实质，就跳过
            if (empty($value)) {
                continue;
            }

            //默认是0=其他，要第一个实质不是其他的，如果都是其他，不影响最后结果
            if (empty($first_type)) {
                $first_type = $type;
            }

            //如果最后不是1，证明有两种不同审批流的实质。
            $not_zero_num++;
        }

        //v18028泰国、18960马来、菲律宾改为新的混合校验
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            $this->addApplyCheckNew($budgetIds);
        } else {
            // 如果非泰国Express, 保持原有校验逻辑
            //如果有两个key一样的，报第一个type不是0的错误。0是其他不报错
            if ($not_zero_num != 1) {
                throw new ValidationException(static::$t->_(Enums::$reimbursement_type_error[$first_type]), ErrCode::$VALIDATE_ERROR);
            }
        }

        $reimbursement_type = $first_type ?? Enums::REIMBURSEMENT_TYPE_OTHER;
        $this->logger->info('报销提交 - 单号=' . $data['no'] . '=报销类型[实质]提取 - type = ' . $reimbursement_type);

        $data['amount']             = $amount;
        $data['travel_amount']      = $travel_amount;
        $data['local_amount']       = $local_amount;
        $data['payable_amount_all'] = $payable_amount_all;
        $data['start_at']           = date('Y-m-d');
        $data['end_at']             = date('Y-m-d');
        $data['status']             = Enums::CONTRACT_STATUS_PENDING;
        $data['pay_status']         = Enums::LOAN_PAY_STATUS_PENDING;
        $data['pay_at']             = null;
        $data['created_at']         = date('Y-m-d H:i:s');
        $data['apply_date']         = date("Y-m-d", strtotime($data['created_at']));
        $data['updated_at']         = $data['created_at'];
        $data['created_id']         = $user['id'] ?? 0;
        $data['created_name']       = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');

        // 报销单状态重置
        $data['status'] = $this->getOrderNextStatus($create_acion, $data['travel_is_have_roommate'], $data['status']);

        // 申请人职级
        $data['apply_job_title_grade'] = $apply_staff_info['job_title_grade_v2'] ?? 0;
        $data['type']                  = $reimbursement_type;

        if ($data['source_type'] == ReimbursementEnums::SOURCE_TYPE_BY && empty($data['cost_company_id'])) {
            $dep_info                = SysDepartmentModel::findFirst($user['sys_department_id']);
            $data['cost_company_id'] = $dep_info->company_id;
        }

        /**
         * 只针对马来校验 my start
         * 1. 餐补额度计算
         * 2. 员工福利费-补充医疗费校验和计算
         **/
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            /** 19703【MY|OA|报销】餐费报销金额限制 开始**/
            // 1. 存在出差申请提交 && 餐费
            if ($travel_serial_no_info && $reimbursement_meal_expense_product_ids) {
                //出差申请单关联的所有已报销(待审批+(已通过且待支付或已支付))餐费金额(发票金额包含SST含WHT)
                $travel_serial_nos           = array_keys($travel_serial_no_info);
                $travel_serial_no_has_amount = $this->getTravelSerialNoAmount($travel_serial_nos, $reimbursement_meal_expense_product_ids);

                //获取申请人的所属国籍，如果所属国籍非马来西亚，则报销餐费额度为0
                $hr_staff_item = HrStaffItemsModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
                    'bind'       => [
                        'staff_info_id' => $apply_staff_info['staff_info_id'],
                    ]
                ]);
                //所属国籍为马来西亚
                if ($hr_staff_item && $hr_staff_item->value == StaffInfoEnums::WORKING_COUNTRY_MY) {
                    // 1.1 餐补额度相关
                    $allowance_list = $this->getMealAllowanceList();// 获取餐补额度配置项
                    $is_country_my  = true;
                } else {
                    $is_country_my = false;
                }

                foreach ($travel_serial_no_info as $travel_serial_no => $item) {
                    //申请人餐费额度计算规则
                    //如果所属国籍为马来西亚
                    if ($is_country_my) {
                        // 1.2 餐补额度计算
                        $day_type = $this->getMealDaysType($item['days_num'], $item['business_trip_type']);// 根据天数获取类型
                        // 1.3 根据职级获取类别
                        $last_job_title_grade = (new HrStaffRepository())->getStaffJobGradeLatestChangeInfo($apply_staff_info['staff_info_id'],
                            $item['travel_start_at']);
                        $job_title_grade      = ($last_job_title_grade && $last_job_title_grade['after_job_title_grade']) ? $last_job_title_grade['after_job_title_grade'] : $data['apply_job_title_grade'];
                        $job_title_type       = $this->getJobTitleGradeType($job_title_grade);
                        // 1.4 根据职级和金额获取餐补额度
                        $meal_allowance_amount = $allowance_list[$job_title_type][$day_type]['amount'] ?? 0;
                        // 1.5 出差天数*每天餐费报销额度
                        $travel_amount_limit = bcmul($meal_allowance_amount, $item['days_num'], 2);
                    } else {
                        //如果所属国籍非马来西亚，则报销餐费额度为0
                        $travel_amount_limit = 0;
                    }

                    //当前出差申请已报销单餐费金额总计，库里存的*1000的，所以这里/1000
                    $one_travel_serial_no_has_amount = bcdiv(($travel_serial_no_has_amount[$travel_serial_no]['amount'] ?? 0), 1000, 2);
                    //当前出差申请餐费金额总计 = 当前报销单当前出差申请单中餐费的合计 + 当前出差申请已报销单餐费金额总计
                    $apply_travel_serial_no_total_amount = bcadd($item['amount'], $one_travel_serial_no_has_amount, 2);
                    //如果大于则提示“出差申请单单号（取值出差单号）中餐费超出额度！”
                    if (bccomp($apply_travel_serial_no_total_amount, $travel_amount_limit, 2) === 1) {
                        throw new ValidationException(static::$t->_('reimbursement_travel_meal_expense_pass',
                            ['travel_serial_no' => $travel_serial_no]), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
            /** 19703【MY|OA|报销】餐费报销金额限制 结束**/

            // 2. 员工福利费-补充医疗费明细相关
            // 获取补充医疗费的明细ID配置
            $medical_product_id = EnumsService::getInstance()->getSettingEnvValue('budget_object_product_medical_id');

            // 是否申请了补充医疗费报销明细
            $is_apply_medical_product = false;

            // 初始化补充医疗费应付金额之和
            $medical_payable_amount_total = 0;

            // 补充医疗费的费用所属年
            $medical_cost_year = '';

            // 当前年
            $current_year = date('Y');

            // 当前日期
            $current_date = date('Y-m-d');

            // 上一年
            $last_year = date('Y', strtotime('-1 year'));

            // 上一年补充医疗费在今年的截止日期配置: 格式 YY-DD, 格式不对的, 按空置配置处理
            $last_year_medical_cost_expiration_date = EnumsService::getInstance()->getSettingEnvValue('last_year_medical_cost_reimbursement_expiration_date');
            if (!preg_match('/^\d?\d-\d?\d$/', $last_year_medical_cost_expiration_date)) {
                $last_year_medical_cost_expiration_date = '';
            } else {
                $last_year_medical_cost_expiration_date = $current_year . '-' . $last_year_medical_cost_expiration_date;
            }

            foreach ($data['expense'] as $k => $expense) {
                // 1.1 补充医疗费应付金额合计
                if ($expense['budget_id'] == Enums::BUDGET_OBJECT_FULI && $expense['product_id'] == $medical_product_id) {
                    // 雇佣类型是个人代理的, 不可申请补充医疗费的报销
                    if ($apply_staff_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
                        throw new ValidationException(static::$t->_('reimbursement_apply_medical_product_staff_type_error'),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $cost_start_year = mb_substr($expense['start_at'], 0, 4);
                    $cost_end_year   = mb_substr($expense['end_at'], 0, 4);

                    // 费用期间须为同一年
                    if ($cost_start_year != $cost_end_year) {
                        throw new ValidationException(static::$t->_('medical_reimbursement_error_001'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (!empty($medical_cost_year) && $medical_cost_year != $cost_end_year) {
                        throw new ValidationException(static::$t->_('medical_reimbursement_error_001'), ErrCode::$VALIDATE_ERROR);
                    }

                    // 截止日期未设置的, 费用只能报当前年
                    if (empty($last_year_medical_cost_expiration_date) && $cost_end_year != $current_year) {
                        throw new ValidationException(static::$t->_('medical_reimbursement_error_002'), ErrCode::$VALIDATE_ERROR);
                    }

                    // 设置了截止日期的情况
                    if (!empty($last_year_medical_cost_expiration_date)) {
                        // 已过截止日期的, 费用只能报当前年
                        if ($current_date > $last_year_medical_cost_expiration_date && $cost_end_year != $current_year) {
                            throw new ValidationException(static::$t->_('medical_reimbursement_error_002'), ErrCode::$VALIDATE_ERROR);
                        }

                        // 未过截止日期的, 费用只能报上一年 或 当前年
                        if ($current_date <= $last_year_medical_cost_expiration_date && !in_array($cost_end_year, [$current_year, $last_year])) {
                            throw new ValidationException(static::$t->_('medical_reimbursement_error_003'), ErrCode::$VALIDATE_ERROR);
                        }
                    }

                    // 记录费用所属年份
                    $medical_cost_year = $cost_end_year;

                    // 费用额度合计
                    $medical_payable_amount_total += $expense['payable_amount'];
                    $is_apply_medical_product     = true;
                }
            }

            $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 报销单号={$data['no']}, 申请人信息=" . json_encode($apply_staff_info, JSON_UNESCAPED_UNICODE));

            // 2.2 申请了补充医疗费相关明细, 相关校验
            if ($is_apply_medical_product) {
                // 汇率转换
                $medical_payable_amount_total = EnumsService::getInstance()->amountExchangeRateCalculation($medical_payable_amount_total,
                    $data['exchange_rate'], 0);

                $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 该费用明细ID={$medical_product_id}, 本次提交额度合计={$medical_payable_amount_total}, 费用所属年={$medical_cost_year}");

                $this->applyWelfareMedicalCheck($apply_staff_info, $medical_payable_amount_total, $medical_product_id, $medical_cost_year);
            }
        }
        /** 只针对马来校验 my end **/


        return $data;
    }

    /**
     * 报销单提交/重新提交时,补充医疗费的单独校验和处理
     *
     * @param array $apply_info 申请人信息
     * @param int $apply_payable_amount_total 员工福利费-补充医疗应付金额合计( * 1000 之后的整数)
     * @param int $medical_product_id 补充医疗费ID
     * @param string $medical_cost_year 补充医疗费费用所属年
     * @return bool
     * @throws ValidationException
     */
    private function applyWelfareMedicalCheck(
        array $apply_info,
        int $apply_payable_amount_total = 0,
        int $medical_product_id = 0,
        string $medical_cost_year = ''
    ) {
        // 1. 员工试用期状态 和 职级验证
        // 获取员工转正评估信息
        $staff_probation_info = (new HrStaffRepository)->getStaffProbationInfoByStaffId($apply_info['staff_info_id']);

        $this->logger->info('报销申请-员工福利费-补充医疗费的计算流程: 转正评估信息=' . json_encode($staff_probation_info, JSON_UNESCAPED_UNICODE));

        // 总部 或 网点 的员工: 试用期需 已转正
        // 转正评估信息为空, 按试用期处理
        if (empty($staff_probation_info) || ($staff_probation_info['status'] != StaffInfoEnums::PROBATION_POSITIVE)) {
            throw new ValidationException(static::$t->_('apply_staff_no_pass_probation', ['staff_id' => $apply_info['staff_info_id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        // 网点员工: 职级需大于 15 级
        if ($apply_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG && $apply_info['job_title_grade_v2'] <= StaffInfoEnums::JOB_TITLE_GRADE_V2_F15) {
            throw new ValidationException(static::$t->_('store_staff_job_title_grade_error', ['staff_id' => $apply_info['staff_info_id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        // 2. 补充医疗费可用金额验证
        // 2.1 福利生效日期: 总部取转正日期, 网点取职级变更为F15的最近日期
        // 默认福利生效日期为员工创建日期: 零时区
        $welfare_start_date = gmdate_customize_by_datetime($apply_info['created_at'], 'Y-m-d');

        // 如果员工通过面试正常入职的, 则取转正时间: 业务时区
        if (!empty($staff_probation_info['formal_at'])) {
            $welfare_start_date = $staff_probation_info['formal_at'];
        }

        // 如果是网点员工, 则取其职级变更记录的操作日期
        if ($apply_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
            $latest_change_info = (new HrStaffRepository())->getStaffJobGradeLatestChangeInfoByStaffId($apply_info['staff_info_id'],
                StaffInfoEnums::JOB_TITLE_GRADE_V2_F15);

            $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 职级由F15及以下变更为大于F15的最近日志=" . json_encode($latest_change_info, JSON_UNESCAPED_UNICODE));

            // 如果职级变更日期较近, 则取职级变更日期
            if (!empty($latest_change_info['change_date'])) {
                $welfare_start_date = $latest_change_info['change_date'] > $welfare_start_date ? $latest_change_info['change_date'] : $welfare_start_date;
            }
        }

        // 福利生效年份
        $welfare_start_year = mb_substr($welfare_start_date, 0, 4);

        $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 福利生效日期={$welfare_start_date}, 福利生效年份={$welfare_start_year}, 费用所属年份={$medical_cost_year}");

        // 费用所属年的 早于 福利生效年的, 费用不可报销
        if ($medical_cost_year < $welfare_start_year) {
            throw new ValidationException(static::$t->_('medical_reimbursement_error_004'), ErrCode::$VALIDATE_ERROR);
        }

        // 2.2 每年可用额度(币种为当前国家的本位币)
        $check_welfare_funds       = EnumsService::getInstance()->getSettingEnvValue('check_welfare_funds', 0);
        $per_year_available_amount = bcmul($check_welfare_funds, 1000, 0);

        // 每季度额度
        $per_season_available_amount = bcdiv($per_year_available_amount, 4, 0);

        $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 该费用每年额度={$per_year_available_amount}, 每季度额度={$per_season_available_amount}");

        // 可用额度计算(费用所属年的额度, 按费用所属年 与 当前自然年区别处理: 费用年度 要么是 上一年, 要么是 当前年; 根据上述所知: 费用所属年份 只可 大于 或 等于 福利生效年份)
        // 最大季度获取: 若费用年份是上一年, 则额度算到上一年的第4季度; 若费用年份是当前年, 则额度算到当前日期所属季度
        $welfare_funds_end_season = $medical_cost_year < date('Y') ? 4 : get_season_by_date(date('Y-m-d'));

        // 最小季度获取: 福利生效年份 早于 费用年份的, 额度起算季度为 第 1 季度; 若 福利生效年份 与 费用年份 是 同一年份的, 则起算季度 为 福利生效日所属季度
        $welfare_funds_start_season = $welfare_start_year < $medical_cost_year ? 1 : get_season_by_date($welfare_start_date);

        $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 可用额度起算季度={$welfare_funds_start_season}, 止算季度={$welfare_funds_end_season}");

        // 当前可用总额度(上一年 或 当前年, 与费用所属年份相关)
        $current_available_amount = bcmul($welfare_funds_end_season - $welfare_funds_start_season + 1, $per_season_available_amount, 0);

        // 获取申请人在费用所属年的福利费-补充医疗费已用额度
        $current_used_amount_total      = $this->getStaffUsedWelfareAmount($apply_info['staff_info_id'], $medical_product_id, $medical_cost_year);
        $current_plan_used_amount_total = bcadd($current_used_amount_total, $apply_payable_amount_total, 0);

        $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 费用年份已用额度={$current_used_amount_total}, 本次申请额度={$apply_payable_amount_total}");

        $this->logger->info("报销申请-员工福利费-补充医疗费的计算流程: 费用年份预使用总额度={$current_plan_used_amount_total}, 当前可用额度={$current_available_amount}");

        if (bccomp($current_plan_used_amount_total, $current_available_amount, 0) > 0) {
            $current_available_amount  = bcdiv($current_available_amount, 1000, 2);
            $current_used_amount_total = bcdiv($current_used_amount_total, 1000, 2);
            throw new ValidationException(static::$t->_('reimbursement_amount_more_than_new_error',
                ['limit_amount' => $current_available_amount, 'used' => $current_used_amount_total]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 获取报销新增/重新提交页默认值
     * @param $param
     * @param $user
     * @return array
     */
    public function defaultData($param, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException(static::$t->_('access_data_staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);

            // 是否生成单号: 重新提交场景 = false, 只需获取其他默认值; 新增场景 = true
            $is_make_no           = $param['is_make_no'] ?? true;
            $data['no']           = $is_make_no ? static::genSerialNo('BX', RedisKey::REIMBURSEMENT_CREATE_COUNTER) : '';
            $data['created_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
            $data['created_id']   = $user['id'];
            $setting_data         = EnumsService::getInstance()->getMultiEnvByCode([
                'reimbursement_is_restrict_self_currency',
                'sys_module_reimbursement_validation_tax_no',
                'sys_module_reimbursement_auto_tax_no'
            ]);
            //增加返回字段 是否限制本位币 "1" : 限制  "0" : 不限制
            $data['is_restrict_currency'] = !empty($setting_data['reimbursement_is_restrict_self_currency']) ? $setting_data['reimbursement_is_restrict_self_currency'] : '0';
            //是否启用发票税务号校验(长度和字符内容)
            $data['is_validation_tax_no'] = !empty($setting_data['sys_module_reimbursement_validation_tax_no']) ? $setting_data['sys_module_reimbursement_validation_tax_no'] : '0';
            //是否自动填充发票税务号
            $data['is_auto_tax_no'] = !empty($setting_data['sys_module_reimbursement_auto_tax_no']) ? $setting_data['sys_module_reimbursement_auto_tax_no'] : '0';

            $data = array_merge($data, $arr);

            // 是否有草稿
            $exist_draft            = ReimbursementDraftModel::findFirst([
                'conditions' => 'created_id = :created_id:',
                'bind'       => ['created_id' => $user['id']],
                'columns'    => ['id']
            ]) ? true : false;
            $data['is_exist_draft'] = $exist_draft ? true : false;
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->logger->warning('reimbursement-get-default-data-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     * 根据查找人和其最近出差
     *
     * @param $id
     * @param bool $is_reimbursement
     * @return array
     */
    public function getUser($id, $is_reimbursement = true)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';

        $data = [];
        try {
            $data = $this->getUserMetaFromBi($id, 1);

            $temp = CategoryService::getInstance()->getList($data['apply_user']);
            if ($temp['code'] != $code) {
                throw new \Exception($temp['message'], ErrCode::$SYSTEM_ERROR);
            }

            $data['expense'] = $temp['data'];

            // 该参数返回数据量巨大, 经与前端确认, 相关接口中该字段的返回值未用到, 因此去掉该字段的取值查询
//            $data['department'] = StaffService::getInstance()->departmentList();
            $data['department']    = [];
            $user['id']            = $id;
            $user['department_id'] = $data['apply_user']['apply_department_id'];
            $data['department_v2'] = ListService::getInstance()->getMyDeptList($user);

            $data['travels'] = [];

            // 如果是来自报销模块的调用, 则返回报销住宿费科目的限制配置
            if ($is_reimbursement) {
                //是否 lnt 验证 如果是 lnt公司 不能申请报销
                if ($data['apply_user']['is_lnt'] === true) {
                    $data = [];
                    throw new ValidationException(static::$t->_('lnt_company_cannot_apply_reimbursement'), ErrCode::$VALIDATE_ERROR);
                }

                if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
                    $data['travels'] = $this->getTravel($id, ListService::LIST_SOURCE_TYPE_1);
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $data    = [];
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('reimbursement-get-user-failed' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     *
     *
     * @param $costDepartment
     * @param $costStoreType
     * @return array
     *
     */
    public function getBudgetList($costDepartment, $costStoreType)
    {
        return $this->budgetList([
            'cost_department' => $costDepartment,
            'cost_store_type' => $costStoreType,
        ]);
    }

    /**
     * 获取科目树状列表
     *
     * @param $data
     * cost_department 费用部门
     * cost_store 费用网点
     * @param array $budgetIds
     * @return array
     */
    protected function budgetList($data, $budgetIds = [])
    {
        $budgetService = new BudgetService();
        $budgetTrees   = $budgetService->objectList([
            'department_id'     => $data['cost_department'],
            'organization_type' => $data['cost_store_type'],
            'budget_ids'        => $budgetIds,
        ], BudgetService::ORDER_TYPE_1);

        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);
        if ($endBudgetIds) {
            $budgets     = BudgetObject::find([
                'conditions' => ' id in ({ids:array}) ',
                'bind'       => ['ids' => $endBudgetIds]
            ])->toArray();
            $products    = $budgetService->purchaseProducts(array_column($budgets, 'level_code'), BudgetService::ORDER_TYPE_1);
            $budgetTrees = $this->bindProduct($budgetTrees, $products);
        }

        return $budgetTrees;
    }

    private function bindProduct($budgetTrees, $products)
    {
        $tipsMap = CostCategoryReimbursementDetailService::getInstance()->getMapByBizType(CostCategoryReimbursementDetailService::SUPPORT_BIZ_TYPE_REIMBURSEMENT);
        foreach ($budgetTrees as $k => $budgetTree) {
            $budgetTrees[$k]['tips'] = $tipsMap[$budgetTree['id']] ?? '';
            if (isset($budgetTree['list'])) {
                $budgetTrees[$k]['list'] = $this->bindProduct($budgetTree['list'], $products);
            } else {
                if (isset($products[$budgetTree['level_code']])) {
                    $budgetTrees[$k]['products'] = isset($products[$budgetTree['level_code']]) ? $products[$budgetTree['level_code']] : [];
                }
            }
        }
        return $budgetTrees;
    }

    /**
     * 是否存在 非末级的科目ID
     * @param $data
     * @param $budgetIds
     * @return int
     *
     */
    protected function isHasNotEndBudgetIds($data, $budgetIds)
    {
        $budgetTrees = $this->budgetList([
            'cost_department' => $data['cost_department'],
            'cost_store_type' => $data['cost_store_type'],
        ], $budgetIds);

        $budgetService = new BudgetService();
        $endBudgetIds  = $budgetService->endBudgetIds($budgetTrees);
        return array_diff($budgetIds, $endBudgetIds) ? 1 : 0;
    }

    public function getTravel($userId, $source_type)
    {
        // 获取已关联过报销实质-差率费的出差单号
        $travel_ids = $this->getTravelUsed($userId, $source_type);

        // 获取可用的所有差旅编号: v20673 去掉外出的类型
        $start   = date("Y-m-d", strtotime("-90 days"));
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['t' => TripModel::class]);
        $builder->andWhere('apply_user = :id: AND status = :status: AND end_time >= :start_time: AND end_time <= :end_time: AND business_trip_type != 5',
            [
                'id'         => $userId,
                'status'     => Enums::TRAFFIC_STATUS_APPROVAL,
                'start_time' => $start,
                'end_time'   => date('Y-m-d')
            ]);

        if (!empty($travel_ids)) {
            $builder->notInWhere('id', $travel_ids);
        }

        $items = $builder->getQuery()->execute();

        $data = [];
        if (empty($items)) {
            return $data;
        }

        foreach ($items as $item) {
            $tmp                              = [];
            $tmp['travel_id']                 = $item->id;
            $tmp['travel_serial_no']          = $item->serial_no;
            $tmp['travel_start']              = $item->departure_city;
            $tmp['travel_end']                = $item->destination_city;
            $tmp['travel_start_at']           = $item->start_time;
            $tmp['travel_end_at']             = $item->end_time;
            $tmp['travel_days_num']           = $item->days_num;
            $tmp['apply_user']                = $item->apply_user;
            $tmp['travel_business_trip_type'] = $item->business_trip_type;
            //19706 出差类型文本
            $tmp['travel_business_trip_type_text'] = static::$t->_(ReimbursementEnums::$travel_business_trip_type[$item->business_trip_type] ?? '');
            $data[]                                = $tmp;
        }
        return $data;
    }

    /**
     * 是否用过travel_id,true用过，false没用过
     * @param $travel_id
     * @param $userId
     * @param $source_type
     * @param $budget_info
     * @return mixed
     */
    public function isTravelIdUsed($travel_id, $userId, $source_type, $budget_info = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['d' => Detail::class]);
        $builder->leftjoin(Reimbursement::class, 'd.re_id=r.id', 'r');
        $builder->andWhere('r.apply_id = :id:', ["id" => $userId]);
        $builder->columns("d.travel_id,group_concat(d.re_id) re_ids");

        // 原逻辑
//        $builder->andWhere("r.status in (" . Enums::CONTRACT_STATUS_PENDING . "," . Enums::CONTRACT_STATUS_APPROVAL . ") and d.level_code = '001' and product_id = 480 and d.travel_id=".intval($travel_id));

        // 新逻辑 v10778
        $filter_condition = [
            'status_pending'  => Enums::WF_STATE_PENDING,
            'status_approval' => Enums::WF_STATE_APPROVED,
            'pay_status'      => [
                Enums::PAYMENT_PAY_STATUS_PAY,
                Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
            'level_code'      => GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE,// 差旅费
            'travel_id'       => (int)$travel_id,                      // 当前选择的出差单id
        ];

        $filter_sql = "(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))) AND d.level_code = :level_code: AND d.travel_id = :travel_id:";
        $builder->andwhere($filter_sql, $filter_condition);

        // 差旅费 - 油费
        if ($budget_info['product_id'] == GlobalEnums::BUDGET_TRAVEL_OIL_PRODUCT_ID) {
            // 油费
            $builder->andwhere('d.product_id = :product_id:', ['product_id' => GlobalEnums::BUDGET_TRAVEL_OIL_PRODUCT_ID]);
        } else {
            // 非油费, 只校验是否从OA端关联过报销单
            $builder->andWhere('r.source_type = :source_type:', ["source_type" => 1]);
        }


        //查询入口来源 1 oa  2 by
        // 不区分渠道进行校验 v10778
//        if(!empty((int)$source_type)){
//            $builder->andWhere('r.source_type = :source_type:', ["source_type" => (int)$source_type]);
//        }

        $builder->groupBy('d.travel_id');
        $builder->limit(1);
        $items = $builder->getQuery()->execute()->toArray();
        if (empty($items)) {
            return false;
        }
        return count(array_unique(explode(',', $items[0]['re_ids'])));
    }

    /**
     * 获取每一个出差申请单下指定明细ID的以报销的金额
     * @param array $travel_serial_nos 出差单编号
     * @param array $expense_product_ids 报销明细ID组
     * @return array
     */
    public function getTravelSerialNoAmount($travel_serial_nos = [], $expense_product_ids = [])
    {
        //获取本位币
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();

        //出差申请单关联的所有已报销(待审批+(已通过且待支付或已支付))餐费金额(发票金额包含SST含WHT)
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['d' => Detail::class]);
        $builder->leftjoin(Reimbursement::class, 'd.re_id = r.id', 'r');
        $builder->columns('SUM(if(r.currency=' . $default_currency['code'] . ', d.amount, d.amount*r.exchange_rate)) as amount, d.travel_serial_no');

        // 审批中、审批通过 且 已支付/待支付
        $filter_condition = [
            'status_pending'  => Enums::WF_STATE_PENDING,
            'status_approval' => Enums::WF_STATE_APPROVED,
            'pay_status'      => [
                Enums::PAYMENT_PAY_STATUS_PAY,
                Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
            'level_code'      => GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE,// 差旅费
        ];

        $builder->andWhere('(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))) AND d.level_code = :level_code:',
            $filter_condition);
        if ($travel_serial_nos) {
            $builder->inWhere('d.travel_serial_no', $travel_serial_nos);
        }
        if ($expense_product_ids) {
            $builder->inWhere('d.product_id', $expense_product_ids);
        }
        $builder->groupBy('d.travel_serial_no');
        $items = $builder->getQuery()->execute()->toArray();
        return array_column($items, null, 'travel_serial_no');
    }

    /**
     * 重新提交
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function recommit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $model = Reimbursement::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
                'for_update' => true
            ]);

            // 原数据校验
            $this->isCanRecommitByModelData($model, $data['id']);

            // 提交的单据号与原单号不匹配, 不可重新提交
            $this->isCanRecommitByNo($model->no, $data['no']);

            // 当前用户与原提交人不匹配, 不可重新提交
            $this->isCanRecommitByCreateId($model->created_id, $user['id']);

            // 当前用户与原申请人不匹配, 不可重新提交
            $this->isCanRecommitByApplyId($model->apply_id, $data['apply_id']);

            // 非驳回 和 撤销状态, 不可重新提交
            $this->isCanRecommitByStatus($model->status, $data['id']);

            // 老数据, 不可重新提交
            $this->isCanRecommitByCreateTime($model->created_at, $data['id']);

            // 公共数据处理
            $data = $this->handleData($data, $user, ReimbursementEnums::CREATE_ACTION_RESUBMIT);

            $loanItem = [];
            // 报销有关联借款单: 抵扣报销额度(冲减借款金额)
            if (!empty($data['loan_id'])) {
                // 验证本次报销关联的借款单可抵扣金额
                $temp = \App\Modules\Loan\Services\ListService::getInstance()->getAmountFromCurrency(
                    $data['loan_amount'],
                    $data['currency'],
                    $data['loan_id'],
                    $user['id'],
                    true,
                    $data['apply_id'],
                    trim($data['cost_company_id'])
                );

                $this->logger->info('报销关联借款单, 可冲减借款金额(getAmountFromCurrency):' . json_encode($temp, JSON_UNESCAPED_UNICODE));

                if ($temp['code'] == ErrCode::$VALIDATE_ERROR) {
                    throw new ValidationException($temp['message'], ErrCode::$VALIDATE_ERROR);
                } elseif ($temp['code'] == ErrCode::$SYSTEM_ERROR) {
                    throw new BusinessException($temp['message'], ErrCode::$BUSINESS_ERROR);
                } else {
                    if (!empty($temp['loan'])) {
                        $loanItem = $temp['loan'];
                    }

                    $data['loan_amount'] = $temp['data'];
                    $data['real_amount'] = bcsub($data['payable_amount_all'], $data['loan_amount']);
                    if ($data['real_amount'] < 0) {
                        $data['real_amount'] = 0;
                    }
                }
            } else {
                $data['loan_amount'] = 0;
                $data['real_amount'] = $data['payable_amount_all'];
            }

            $new_travel_is_have_roommate = $data['travel_is_have_roommate'];

            //修改前的报销主表数据
            $before_main_data = $model->toArray();
            unset($data['first_apply_date']);
            unset($data['travel_is_have_roommate']);
            if ($model->i_update($data) === false) {
                throw new BusinessException('报销更新失败 = ' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            // 共同住宿出差编码是否变动校验
            $travel_roommate_rel_models = $model->getDetailTravelRoommateRelModels();

            // 原单据 或 重提单据有共同住宿人, 则进行校验
            if (in_array(ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES, [$model->travel_is_have_roommate, $new_travel_is_have_roommate])) {
                $this->checkRoommateSerialNo($data['expense'], $travel_roommate_rel_models->toArray());
            }

            if ($travel_roommate_rel_models->delete() === false) {
                throw new BusinessException('报销明细-出差共同住宿老数据删除失败: ' . get_data_object_error_msg($travel_roommate_rel_models),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 修改前明细数据
            $before_detail_model = $model->getDetails();
            $before_detail_data  = $before_detail_model->toArray();

            // 将报销旧的明细行记日志
            $this->logger->info(['reimbursement_recommit_before_detail_data' => $before_detail_data]);

            // 删除之前关联的发票编号
            foreach ($before_detail_model as $detail) {
                $before_ticket_model = $detail->getTickets();
                if ($before_ticket_model->delete() === false) {
                    throw new BusinessException('报销明细关联的发票老数据删除失败: ' . get_data_object_error_msg($before_ticket_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            if ($before_detail_model->delete() === false) {
                throw new BusinessException('报销明细老数据删除失败: ' . get_data_object_error_msg($before_detail_model), ErrCode::$BUSINESS_ERROR);
            }

            $detail_support_rel_models = $model->getDetailSupportRel();
            if ($detail_support_rel_models->delete() === false) {
                throw new BusinessException('报销明细-支援单老数据删除失败: ' . get_data_object_error_msg($detail_support_rel_models), ErrCode::$BUSINESS_ERROR);
            }

            //关联备用金
            if (!empty($data['rfano'])) {
                // 新关联备用金
                $rfrei         = new  ReserveFundReimburse();
                $rfrei->rfano  = $data['rfano'];
                $rfrei->rei_id = $model->id;
                if ($rfrei->save() === false) {
                    throw new BusinessException('备用金关联报销单创建失败=' . get_data_object_error_msg($rfrei), ErrCode::$BUSINESS_ERROR);
                }
            }

            //拒绝的时候，会删除关联关系，所以这里只添加
            if (!empty($loanItem)) {
                $rels               = [];
                $temp               = [];
                $temp['re_id']      = $model->id;
                $temp['loan_id']    = $loanItem['id'];
                $temp['amount']     = $data['loan_amount'];
                $temp['created_at'] = date("Y-m-d H:i:s");
                $rels[]             = $temp;

                $this->logger->info('报销关联借款单(reimbursement_rel_loan):' . json_encode($rels, JSON_UNESCAPED_UNICODE));

                $reimbursement_rel_loan_model = new ReimbursementRelLoan();
                if ($reimbursement_rel_loan_model->batch_insert($rels) === false) {
                    throw new BusinessException('报销-批量添加关联借款失败 = ' . get_data_object_error_msg($reimbursement_rel_loan_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            // 明细行数据处理
            $detail_save_result = $this->commonSaveDetailItem($data['expense'], $model, ReimbursementEnums::CREATE_ACTION_RESUBMIT);
            $amount_info        = $detail_save_result['amount_info'] ?? [];

            $other_params = [
                'before_main_data'   => $before_main_data,
                'before_detail_data' => $before_detail_data,
            ];
            $this->createEventCallBack(ReimbursementEnums::CREATE_ACTION_RESUBMIT, $data, $amount_info, $user, $other_params);

            $db->commit();
        } catch (ValidationException $e) {
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code   = ErrCode::$SUCCESS;
                $result = [
                    'message'   => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? ReimbursementEnums::CAN_APPLY_NO : ReimbursementEnums::CAN_APPLY_YES,
                ];
            } else {
                $code = $e->getCode();
            }

            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('reimbursement-recommit-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result
        ];
    }

    /**
     * 共同住宿出差编号是否变动校验
     */
    protected function checkRoommateSerialNo($submit_expense, $exist_roommate_expense)
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return true;
        }

        $exist_detail_id_roommate_map = [];
        $exist_all_roommate           = [];
        foreach ($exist_roommate_expense as $value) {
            $exist_detail_id_roommate_map[$value['detail_id']][] = $value['serial_no'];
            $exist_all_roommate[]                                = $value['serial_no'];
        }

        $submit_detail_id_roommate_map = [];
        $submit_all_roommate           = [];
        foreach ($submit_expense as $expense) {
            $expense_travel_roommate_item = [];

            if (!empty($expense['travel_roommate_item'])) {
                $expense_travel_roommate_item = array_column($expense['travel_roommate_item'], 'serial_no');
            }

            if (!empty($expense_travel_roommate_item) && empty($expense['id'])) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
            }

            if (!empty($expense_travel_roommate_item) && !empty($expense['id'])) {
                $submit_detail_id_roommate_map[$expense['id']] = $expense_travel_roommate_item;
            }

            $submit_all_roommate = array_merge($submit_all_roommate, $expense_travel_roommate_item);
        }

        if (count($exist_all_roommate) != count($submit_all_roommate) || !arrays_is_equal($exist_all_roommate, $submit_all_roommate)) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
        }

        foreach ($exist_detail_id_roommate_map as $detail_id => $item) {
            $submit_detail_roommate = $submit_detail_id_roommate_map[$detail_id] ?? [];
            if (empty($submit_detail_roommate)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
            }

            if (count($item) != count($submit_detail_roommate) || !arrays_is_equal($item, $submit_detail_roommate)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
            }
        }

        foreach ($submit_detail_id_roommate_map as $detail_id => $item) {
            $exist_detail_roommate = $exist_detail_id_roommate_map[$detail_id] ?? [];
            if (empty($exist_detail_roommate)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
            }

            if (count($item) != count($exist_detail_roommate) || !arrays_is_equal($item, $exist_detail_roommate)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_027'), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * 报销新增/重新提交 明细行数据公共处理
     * @param $expense_item
     * @param $model
     * @param $create_action
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function commonSaveDetailItem($expense_item, $model, $create_action = '')
    {
        // 保存 外协申请审批编号 对应的数据
        $serial_nos_data = [];

        // 报销实质关联的支援单号
        $detail_support_rel_data = [];

        // 明细行关联的支付凭证
        $payment_voucher_attachments = [];

        // 明细行附件
        $attachments = [];

        // 明细行发票
        $detail_ticket_data = [];

        // 报销实质与转换后的金额
        $amount_info = [];

        // 差旅费-住宿明细关联的共同住宿出差单号
        $travel_roommate_rel = [];

        // 超出报销标准金额审批邮件附件
        $exceeds_standard_amount_email_file = [];

        // 油费里程图片附件(开始里程/结束里程)
        $fuel_mileage_file = [];

        // 明细行金额相关字段参考数据
        $detail_reference_data = [];

        // 补充附件
        $required_supplement_file = [];

        $travel_budget_id  = static::getTravelTypeBudgetId();
        $support_budget_id = static::getSupportTypeBudgetId();
        $accommodation_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);

        foreach ($expense_item as $expense) {
            if (isset($expense['id'])) {
                unset($expense['id']);
            }
            $expense['re_id'] = $model->id;

            //由于前端不论是不是支援费用都会把相应的入参给传递进来,日期字段不可赋予空字符串需要赋予null
            $expense['support_employment_begin_date'] = !empty($expense['support_employment_begin_date']) ? $expense['support_employment_begin_date'] : null;
            $expense['support_employment_end_date']   = !empty($expense['support_employment_end_date']) ? $expense['support_employment_end_date'] : null;
            $expense['fuel_use_date']                 = !empty($expense['fuel_use_date']) ? $expense['fuel_use_date'] : null;

            // 增值税发票
            $expense['invoice_no'] = $expense['invoice_no'] ?? '';

            //获取资产信息 006：劳务成本-外包，007：劳务成本-外协
            switch ((int)$expense['budget_template_type']) {
                case 1:
                    //当选择的是 劳务成本-外包 或者 劳务成本-外协 时需要增加保存的数据
                    if (!isset($expense['serial_no']) || empty($expense['serial_no'])) {
                        throw new ValidationException(static::$t->_('re_serial_no_qequired'), ErrCode::$VALIDATE_ERROR);
                    }

                    $serial_no_cache_info = $serial_nos_data[$expense['serial_no']] ?? [];
                    if (empty($serial_no_cache_info)) {
                        //检测编号是否已使用
                        $exists_serial_no = $this->user_serial_no([$expense['serial_no']]);

                        //获取单号信息
                        $serial_no_info = [];
                        if (!$exists_serial_no) {
                            $serial_no_info = $this->get_serial_no_info($expense['serial_no']);
                        }

                        if ($exists_serial_no || !$serial_no_info) {
                            throw new ValidationException(static::$t->_('re_serial_no_incorrect'),
                                ErrCode::$VALIDATE_ERROR);
                        }

                        $serial_nos_data[$expense['serial_no']] = $serial_no_cache_info = $serial_no_info;
                    }

                    $expense['serial_no']            = $serial_no_cache_info['serial_no'];
                    $expense['applicant_staff_name'] = $serial_no_cache_info['applicant_staff_name'];
                    $expense['sys_store_name']       = $serial_no_cache_info['sys_store_name'];
                    $expense['final_audit_num']      = $serial_no_cache_info['final_audit_num'];
                    $expense['employment_days']      = $serial_no_cache_info['employment_days'];
                    break;
            }

            $t_model = new Detail();
            if ($t_model->i_create($expense) === false) {
                throw new BusinessException('明细行创建失败,原因可能是:' . $t_model->getErrorMessagesString() . ';data=' . json_encode($expense,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($expense['amount_field_reference_data'])) {
                $detail_reference_data[] = [
                    're_id'          => $model->id,
                    'detail_id'      => $t_model->id,
                    'reference_data' => json_encode($expense['amount_field_reference_data'], JSON_UNESCAPED_UNICODE),
                    'created_at'     => date('Y-m-d H:i:s'),
                ];
            }

            // 金额需根据汇率转换为系统默认币种的金额: 与预算侧的币种金额保持一致
            $default_currency_amount = EnumsService::getInstance()->amountExchangeRateCalculation($expense['amount'], $model->exchange_rate, 0);
            $amount_info[]           = ['budget_id' => $expense['budget_id'], 'amount' => $default_currency_amount];

            // 构造明细与支援单号批量数据
            if ($expense['budget_id'] == $support_budget_id && !empty($expense['support_serial_no_item'])) {
                foreach ($expense['support_serial_no_item'] as $_support_serial_no) {
                    $detail_support_rel_data[] = [
                        're_id'             => $model->id,
                        'detail_id'         => $t_model->id,
                        'product_id'        => $expense['product_id'],
                        'support_serial_no' => $_support_serial_no,
                    ];
                }
            }

            // 关联发票编号和报销明细
            $invoices_ids = array_filter($expense['invoices_ids']);
            if (!empty($invoices_ids)) {
                foreach ($invoices_ids as $invoice) {
                    $detail_ticket_data[] = [
                        're_id'      => $model->id,
                        'detail_id'  => $t_model->id,
                        'invoice_no' => $invoice,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
            }

            // 附件
            if (!empty($expense['attachments'])) {
                foreach ($expense['attachments'] as $attachment) {
                    $attachments[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $attachment['bucket_name'],
                        'object_key'      => $attachment['object_key'],
                        'file_name'       => $attachment['file_name'],
                    ];
                }
            }

            // 重新提交的补充附件
            if ($create_action == ReimbursementEnums::CREATE_ACTION_RESUBMIT && !empty($expense['required_supplement_file'])) {
                foreach ($expense['required_supplement_file'] as $supplement_file) {
                    $required_supplement_file[] = [
                        'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_ATTACHMENT_FILE,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $supplement_file['bucket_name'],
                        'object_key'      => $supplement_file['object_key'],
                        'file_name'       => $supplement_file['file_name'],
                        'created_at'      => date('Y-m-d H:i:s'),
                    ];
                }
            }

            // 支付凭证
            if (!empty($expense['payment_voucher'])) {
                foreach ($expense['payment_voucher'] as $payment_voucher) {
                    $payment_voucher_attachments[] = [
                        'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_PAYMENT_VOUCHER,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $payment_voucher['bucket_name'],
                        'object_key'      => $payment_voucher['object_key'],
                        'file_name'       => $payment_voucher['file_name'],
                    ];
                }
            }

            // 构造明细与共同住宿出差单号批量数据
            $is_travel_roommate = $expense['budget_id'] == $travel_budget_id && in_array($expense['product_id'], $accommodation_ids) && $expense['travel_is_have_roommate'] == ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES;
            if ($is_travel_roommate && !empty($expense['travel_roommate_item'])) {
                foreach ($expense['travel_roommate_item'] as $roommate_info) {
                    $travel_roommate_rel[] = [
                        're_id'            => $model->id,
                        'detail_id'        => $t_model->id,
                        'product_id'       => $expense['product_id'],
                        'serial_no'        => $roommate_info['serial_no'],
                        'apply_staff_id'   => $roommate_info['apply_staff_id'],
                        'apply_staff_name' => $roommate_info['apply_staff_name'],
                        'confirm_status'   => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
                    ];
                }
            }

            // 超出报销标准金额审批邮件附件
            if (!empty($expense['exceeds_standard_amount_email_file'])) {
                foreach ($expense['exceeds_standard_amount_email_file'] as $email_file) {
                    $exceeds_standard_amount_email_file[] = [
                        'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_EXCEEDS_STANDARD_AMOUNT_EMAIL_FILE,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $email_file['bucket_name'],
                        'object_key'      => $email_file['object_key'],
                        'file_name'       => $email_file['file_name'],
                    ];
                }
            }

            // 油费里程照片附件
            if (!empty($expense['fuel_start_mileage_file'])) {
                foreach ($expense['fuel_start_mileage_file'] as $start_mileage_file) {
                    $fuel_mileage_file[] = [
                        'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_START_MILEAGE_FILE,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $start_mileage_file['bucket_name'],
                        'object_key'      => $start_mileage_file['object_key'],
                        'file_name'       => $start_mileage_file['file_name'],
                    ];
                }
            }

            if (!empty($expense['fuel_end_mileage_file'])) {
                foreach ($expense['fuel_end_mileage_file'] as $end_mileage_file) {
                    $fuel_mileage_file[] = [
                        'oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_END_MILEAGE_FILE,
                        'oss_bucket_key'  => $t_model->id,
                        'bucket_name'     => $end_mileage_file['bucket_name'],
                        'object_key'      => $end_mileage_file['object_key'],
                        'file_name'       => $end_mileage_file['file_name'],
                    ];
                }
            }
        }
        
        $sys_attachment_model = new AttachModel();

        // 支付凭证附件
        if (!empty($payment_voucher_attachments) && $sys_attachment_model->batch_insert($payment_voucher_attachments) === false) {
            throw new BusinessException('明细行支付凭证附件批写入失败, data=' . json_encode($payment_voucher_attachments,
                    JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 明细行附件
        if (!empty($attachments) && $sys_attachment_model->batch_insert($attachments) === false) {
            throw new BusinessException('明细行附件批写入失败, data=' . json_encode($attachments, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        // 补充附件
        if (!empty($required_supplement_file) && $sys_attachment_model->batch_insert($required_supplement_file) === false) {
            throw new BusinessException('明细行补充附件批写入失败, data=' . json_encode($required_supplement_file, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        // 超出报销标准附件
        if (!empty($exceeds_standard_amount_email_file) && $sys_attachment_model->batch_insert($exceeds_standard_amount_email_file) === false) {
            throw new BusinessException('超出报销标准金额审批邮件批写入失败, data=' . json_encode($exceeds_standard_amount_email_file, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        // 油费里程照片附件
        if (!empty($fuel_mileage_file) && $sys_attachment_model->batch_insert($fuel_mileage_file) === false) {
            throw new BusinessException('油费里程表照片批写入失败, data=' . json_encode($fuel_mileage_file, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        // 明细行发票
        $detail_ticket_model = new ReimbursementDetailTicketModel();
        if (!empty($detail_ticket_data) && $detail_ticket_model->batch_insert($detail_ticket_data) === false) {
            throw new BusinessException('明细行发票编号批写入失败, data=' . json_encode($detail_ticket_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 明细行与支援单关系创建
        $detail_support_rel_model = new ReimbursementDetailSupportRelModel();
        if (!empty($detail_support_rel_data) && $detail_support_rel_model->batch_insert($detail_support_rel_data) === false) {
            throw new BusinessException('明细行支援单批写入失败, data=' . json_encode($detail_support_rel_data, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        // 明细行与共同住宿出差单关系创建
        $ravel_roommate_rel_model = new ReimbursementDetailTravelRoommateRelModel();
        if (!empty($travel_roommate_rel) && $ravel_roommate_rel_model->batch_insert($travel_roommate_rel) === false) {
            throw new BusinessException('共同住宿人出差单批写入失败, data=' . json_encode($travel_roommate_rel, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        $detail_reference_model = new ReimbursementDetailReferenceModel();
        if (!empty($detail_reference_data) && $detail_reference_model->batch_insert($detail_reference_data) === false) {
            throw new BusinessException('明细行金额字段参考数据批写入失败, data=' . json_encode($detail_reference_data, JSON_UNESCAPED_UNICODE),
                ErrCode::$BUSINESS_ERROR);
        }

        return [
            'amount_info' => $amount_info,
        ];
    }

    /**
     * 获取业务侧明细行预算科目与相应金额
     * @param $expense_item
     * @param $exchange_rate
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     *
     */
    protected function getDetailBudgetAmount($expense_item, $exchange_rate)
    {
        // 报销实质与转换后的金额
        $amount_info = [];

        foreach ($expense_item as $expense) {
            // 金额需根据汇率转换为系统默认币种的金额: 与预算侧的币种金额保持一致
            $default_currency_amount = EnumsService::getInstance()->amountExchangeRateCalculation($expense['amount'], $exchange_rate, 0);
            $amount_info[]           = ['budget_id' => $expense['budget_id'], 'amount' => $default_currency_amount];
        }

        return $amount_info;
    }

    /**
     * DESC: 获取所有可用的 审批单号
     * @param $serial_no
     * @return array
     */
    public function get_serial_no($serial_no)
    {
        if (empty($serial_no) && $serial_no !== '0') {
            return [];
        }
        //获取所有符合的编码
        $Detail_builder = $builder = $this->modelsManager->createBuilder();
        $builder->from(HrOutsourcingOrder::class);
        $builder->where('serial_no LIKE :serial_no:', ['serial_no' => '%' . $serial_no . '%']);
        $builder->andWhere('status in(2,3)');
        $builder->columns('serial_no');
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        //从或的编号中获取 不可使用的编号
        $list      = array_column($list, 'serial_no');
        $user_list = $this->user_serial_no($list);

        //过滤掉不符合的编号
        return array_diff($list, (array)$user_list);
    }

    /**
     * DESC: 获取外协员工工单详情
     * @param $serial_no
     * @return array
     */
    public function get_serial_no_info($serial_no)
    {
        if (empty($serial_no)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => HrOutsourcingOrder::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'si.staff_info_id=h.staff_info_id', 'si');//员工信息
        $builder->leftjoin(SysStoreModel::class, 's.id=h.store_id', 's');                     //网点
        $builder->where('h.serial_no = :serial_no:', ['serial_no' => $serial_no]);
        $builder->andWhere('h.status in(2,3)');
        $builder->columns('h.serial_no, concat("(",si.id,")",si.name) as applicant_staff_name,  s.name as sys_store_name,h.employment_days,h.final_audit_num');
        $data = $builder->getQuery()->getSingleResult();
        return $data ? $data->toArray() : [];
    }

    /**
     * DESC: $serial_no_list集合中筛选出已经使用过的serial_no
     * @param $serial_no_list
     * @return array
     */
    public function user_serial_no($serial_no_list)
    {
        if (empty($serial_no_list)) {
            return [];
        }
        $serial_nos     = '"' . implode('","', $serial_no_list) . '"';
        $Detail_builder = $this->modelsManager->createBuilder();
        $Detail_builder->from(['d' => Detail::class]);
        $Detail_builder->leftjoin(Reimbursement::class, 'd.re_id=r.id', 'r');
        $Detail_builder->where("d.serial_no in ({$serial_nos})");

        // 原条件: status in (1,3), 即审核中 和 审核通过的不允许再次被关联
//        $Detail_builder->andwhere("r.status in(1,3)");

        // 新条件: 审核中 ，审核通过 且 已支付/待支付的，不允许再次关联
        // v10778
        $filter_condition = [
            'status_pending'  => Enums::WF_STATE_PENDING,
            'status_approval' => Enums::WF_STATE_APPROVED,
            'pay_status'      => [
                Enums::PAYMENT_PAY_STATUS_PAY,
                Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
        ];
        $Detail_builder->andwhere("r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))",
            $filter_condition);

        $Detail_builder->columns('d.serial_no');
        $user_list = $Detail_builder->getQuery()->execute()->toArray();
        return $user_list = array_column((array)$user_list, 'serial_no');
    }

    /**
     * 获取申请人福利费-补充医疗费报销金额已用额度(合计)
     *
     * @param int $apply_id 申请人工号
     * @param int $medical_product_id 补充医疗费明细ID
     * @param string $medical_cost_year 费用所属年份
     * @return int
     */
    private function getStaffUsedWelfareAmount(int $apply_id, int $medical_product_id, string $medical_cost_year)
    {
        if (empty($apply_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['r' => Reimbursement::class]);
        $builder->leftjoin(Detail::class, 'rd.re_id = r.id', 'rd');
        $builder->where('r.apply_id = :apply_id:', ['apply_id' => $apply_id]);
        $builder->andWhere('rd.budget_id = :budget_id: AND rd.product_id = :product_id:', [
            'budget_id'  => Enums::BUDGET_OBJECT_FULI,
            'product_id' => $medical_product_id
        ]);

        $builder->andWhere('rd.start_at LIKE :cost_start_year: AND rd.end_at LIKE :cost_end_year:', [
            'cost_start_year' => "$medical_cost_year%",
            'cost_end_year'   => "$medical_cost_year%"
        ]);

        $builder->andWhere('(r.status = :pending_status:) OR (r.status = :approved_status: AND r.pay_status IN ({pay_status_item:array}))', [
            'pending_status'  => Enums::WF_STATE_PENDING,
            'approved_status' => Enums::WF_STATE_APPROVED,
            'pay_status_item' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]
        ]);

        $builder->columns('rd.payable_amount, r.exchange_rate');
        $amount_list = $builder->getQuery()->execute()->toArray();

        $total_amount = 0;
        foreach ($amount_list as $amount) {
            $total_amount += EnumsService::getInstance()->amountExchangeRateCalculation($amount['payable_amount'], $amount['exchange_rate'], 0);
        }

        return $total_amount;
    }

    /**
     * DESC: 筛选出所有符合条件的pc_code
     * @param $pc_code
     * @return array
     */
    function get_pc_code_list($pc_code)
    {
        if (empty($pc_code)) {
            return [];
        }

        // 通过部门pc_code表查询
        $Detail_builder = $this->modelsManager->createBuilder();
        $Detail_builder->from(['d' => Pccode::class]);
        $Detail_builder->where("d.pc_code like :pc_code:", ['pc_code' => "%{$pc_code}%"]);
        $Detail_builder->columns('d.department_name pc_name,d.pc_code');
        $pccode_list = $Detail_builder->getQuery()->execute()->toArray();

        // 通过其它pc_code表查找
        $ODetail_builder = $this->modelsManager->createBuilder();
        $ODetail_builder->from(['o' => OtherPccode::class]);
        $ODetail_builder->where("o.pc_code like :pc_code:", ['pc_code' => "%{$pc_code}%"]);
        $ODetail_builder->columns('o.name pc_name,o.pc_code');
        $pccode_other_list = $ODetail_builder->getQuery()->execute()->toArray();

        return array_merge($pccode_list, $pccode_other_list);
    }

    /**
     * DESC: 获取所有餐补额度配置项
     * @return array
     */
    function getMealAllowanceList()
    {
        $list          = ReimbursementMealAllowanceModel::find();
        $list          = !empty($list) ? $list->toArray() : [];
        $allowanceList = [];

        foreach ($list as $item) {
            $allowanceList[$item['grade_type']][$item['border_day_type']] = [
                'amount'   => $item['amount'],
                'currency' => $item['currency']
            ];
        }

        return $allowanceList;
    }

    /**
     * DESC: 根据天数获取餐补类别
     * @param $days
     * @param $business_trip_type
     * @return integer
     */
    function getMealDaysType($days = 0, $business_trip_type = 0)
    {
        // 境内 天数=1
        if ($days == 1 && $business_trip_type == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN) {
            $type = 1;
            // 境内 天数>1
        } elseif ($days > 1 && $business_trip_type == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN) {
            $type = 2;
            // 境外 天数=1
        } elseif ($days == 1 && $business_trip_type == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_OUT) {
            $type = 3;
        } elseif ($days > 1 && $business_trip_type == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_OUT) {
            // 境外 天数>1
            $type = 4;
        } else {
            $type = 0;
        }

        return $type;
    }

    /**
     * DESC: 根据职级获取餐补类别
     * @param $job_title_grade
     * @return integer
     */
    function getJobTitleGradeType($job_title_grade = 0)
    {
        // 职级配置
        $ranking        = EnvModel::getEnvByCode('job-title-ranking');
        $ranking_obj    = !empty($ranking) ? json_decode($ranking, true) : null;
        $job_title_type = 0;
        if (!empty($ranking_obj)) {
            $index = 1;
            foreach ($ranking_obj as $level => $fomula) {
                $result = false;
                $fomula = str_replace('$p' . $index, $job_title_grade, $fomula);
                eval("\$result=" . $fomula . ";");
                if ($result === true) {
                    $job_title_type = $index;
                    break;
                }
                $index++;
            }
        }

        return $job_title_type;
    }

    /**
     * DESC: 取已存在发票编号对应报销单
     * @param $tickets_no array
     * @return string
     */
    function getTicketsNo($ticketsNo = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['rd' => ReimbursementDetailTicketModel::class]);
        $builder->leftjoin(Reimbursement::class, 'r.id=rd.re_id', 'r');
        $filter_condition = [
            'status_pending'  => Enums::WF_STATE_PENDING,
            'status_approval' => Enums::WF_STATE_APPROVED,
            'pay_status'      => [
                Enums::PAYMENT_PAY_STATUS_PAY,
                Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
            'invoice_no'      => $ticketsNo,// 当前选择的出差单id
        ];

        $filter_sql = "(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))) AND rd.invoice_no IN ({invoice_no:array})";
        $builder->andwhere($filter_sql, $filter_condition);
        $builder->columns('r.no,rd.invoice_no');
        $ticketList = $builder->getQuery()->execute()->toArray();
        if (empty($ticketList)) {
            return '';
        }
        // 验证大小写严格匹配
        $ticketInvoiceNoList = array_values(array_column($ticketList, 'invoice_no'));
        $ticketNoList        = array_column($ticketList, null, 'invoice_no');
        foreach ($ticketsNo as $no) {
            if (in_array($no, $ticketInvoiceNoList)) {
                return $ticketNoList[$no]['no'];
            }
        }

        return '';
    }

    /**
     * DESC: 取已存在的差旅序列号
     * @param $travel_serial_no array
     * @return mixed
     */
    function getTravelSerialNo($travel_serial_no = [])
    {
        return Detail::find([
            'conditions' => ' travel_serial_no IN ({travel_serial_no:array}) ',
            'bind'       => ['travel_serial_no' => $travel_serial_no],
            'columns'    => 'travel_serial_no,count(travel_serial_no) total',
            'group'      => 'travel_serial_no'
        ])->toArray();
    }

    /**
     * DESC: 是否存在油费
     * @param $travel_serial_no array
     * @return mixed
     */
    function isExistOilProduct($travel_serial_no = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['rd' => Detail::class]);
        $builder->leftjoin(Reimbursement::class, 'r.id=rd.re_id', 'r');
        $builder->where("r.source_type = :source_type: and rd.level_code = :level_code: and rd.product_id = :product_id: and rd.travel_serial_no IN ({travel_serial_no:array})",
            [
                'source_type'      => ReimbursementEnums::SOURCE_TYPE_BY,
                'level_code'       => GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE,
                'product_id'       => GlobalEnums::BUDGET_TRAVEL_OIL_PRODUCT_ID,
                'travel_serial_no' => $travel_serial_no
            ]);
        $filter_condition = [
            'status_pending'  => Enums::WF_STATE_PENDING,
            'status_approval' => Enums::WF_STATE_APPROVED,
            'pay_status'      => [
                Enums::PAYMENT_PAY_STATUS_PAY,
                Enums::PAYMENT_PAY_STATUS_PENDING,
            ]
        ];
        $filter_sql       = "(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array})))";
        $builder->andwhere($filter_sql, $filter_condition);
        $detail_list = $builder->getQuery()->execute()->toArray();

        return !empty($detail_list);
    }

    /**
     * 获取当前用户支援网点列表
     * @param $params
     * @return mixed
     */
    public function getStoreSupportList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        ini_set('memory_limit', '512M');

        try {
            // 报销明细在途的支援单号
            $time_1         = get_curr_micro_time();
            $serial_no_list = $this->getSupportSerialNoList($params['product_id']);
            $this->logger->info([
                'getStoreSupportListExecTime' => [
                    'get_serial_no_list'   => get_exec_time($time_1),
                    'serial_no_list_count' => count($serial_no_list),
                ],
                'params'                      => $params,
            ]);

            $time_2 = get_curr_micro_time();

            // 管理大区下的网点
            $manage_store_ids = RegionRepository::getInstance()->getStoreListByManagerId($params['apply_id']);

            // 管理片区下的网点
            if (empty($manage_store_ids)) {
                $manage_store_ids = PieceRepository::getInstance()->getStoreListByManagerId($params['apply_id']);
            }

            // 管理的网点
            if (empty($manage_store_ids)) {
                $manage_store_ids = (new StoreRepository())->getListByManagerId($params['apply_id']);
            }

            $this->logger->info([
                'getStoreSupportListExecTime' => [
                    'get_manage_store_ids'   => get_exec_time($time_2),
                    'manage_store_ids_count' => count($manage_store_ids),
                ],
            ]);

            $time_3 = get_curr_micro_time();

            // 可报销支援单号天数
            $reimbursement_support_order_number_days = EnumsService::getInstance()->getSettingEnvValue('reimbursement_support_order_number_days');
            $reimbursement_support_order_number_days = (int)$reimbursement_support_order_number_days;
            $start_employment_end_date = date('Y-m-d', strtotime("-{$reimbursement_support_order_number_days} days"));

            // 取数范围
            $and_where      = 'staff_info_id = :apply_id:';
            $and_where_bind = ['apply_id' => $params['apply_id']];

            $manage_store_ids = array_filter(array_column($manage_store_ids, 'store_id'));
            if (!empty($manage_store_ids)) {
                $and_where                         .= ' OR staff_store_id IN ({staff_store_ids:array})';
                $and_where_bind['staff_store_ids'] = array_values($manage_store_ids);
            }

            // 符合条件的支援单
            $builder = $this->modelsManager->createBuilder();
            $builder->from(HrStaffApplySupportStore::class);

            // 支援清单中申请状态=已通过 && 生效状态=2已生效、3已失效
            $builder->where('status = :status: AND support_status in ({support_status:array})', [
                'status'         => ByWorkflowEnums::BY_OPERATE_PASS,
                'support_status' => [
                    ReimbursementEnums::STORE_SUPPORT_STATUS_2,
                    ReimbursementEnums::STORE_SUPPORT_STATUS_3,
                ],
            ]);

            // 申请人 和 管辖范围的取数条件
            $builder->andWhere($and_where, $and_where_bind);

            if (!empty($params['support_serial_no'])) {
                $builder->andWhere('serial_no = :support_serial_no:', ['support_serial_no' => $params['support_serial_no']]);
            } else {
                $builder->andWhere('serial_no > :empty_serial_no:', ['empty_serial_no' => '']);
            }

            // 支援员工工号
            if (!empty($params['staff_info_id'])) {
                $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
            }

            // 原网点
            if (!empty($params['staff_store_id'])) {
                if ($params['staff_store_id'] == Enums::PAYMENT_HEADER_STORE_ID) {
                    $params['staff_store_id'] = Enums::HEAD_OFFICE_STORE_FLAG;
                }

                $builder->andWhere('staff_store_id = :staff_store_id:', ['staff_store_id' => $params['staff_store_id']]);
            }

            // 支援网点
            if (!empty($params['support_store_id'])) {
                if ($params['support_store_id'] == Enums::PAYMENT_HEADER_STORE_ID) {
                    $params['support_store_id'] = Enums::HEAD_OFFICE_STORE_FLAG;
                }

                $builder->andWhere('store_id = :support_store_id:', ['support_store_id' => $params['support_store_id']]);
            }

            // 支援开始日期
            if (!empty($params['support_start_date'])) {
                $builder->andWhere('employment_end_date >= :support_start_date:', ['support_start_date' => $params['support_start_date']]);
            }

            // 支援结束日期
            if (!empty($params['support_end_date'])) {
                $builder->andWhere('employment_begin_date <= :support_end_date:', ['support_end_date' => $params['support_end_date']]);
            }

            $builder->andWhere('employment_end_date >= :start_support_end_date:', ['start_support_end_date' => $start_employment_end_date]);

            // 剔除报销明细在途的支援单 v1
//            if ($serial_no_list) {
//                $builder->notInWhere('serial_no', $serial_no_list);
//            }

            // 剔除报销明细在途的支援单 v2
            if ($serial_no_list) {
                $serial_no_list_chunk = array_chunk($serial_no_list, 3000);
                foreach ($serial_no_list_chunk as $chunk) {
                    $builder->notInWhere('serial_no', $chunk);
                }
            }

            $total_count = (int)$builder->columns('COUNT(id) AS count')->getQuery()->getSingleResult()->count;

            $this->logger->info([
                'getStoreSupportListExecTime' => [
                    'get_total_count' => get_exec_time($time_3),
                    'total_count'     => $total_count,
                ],
            ]);

            if ($total_count) {
                $time_4 = get_curr_micro_time();

                $columns = [
                    'staff_info_id',
                    'staff_store_id',
                    'serial_no AS support_serial_no',
                    'job_title_id',
                    'store_id AS support_store_id',
                    'employment_begin_date',
                    'employment_end_date',
                    'employment_days',
                    'status',
                ];
                $builder->columns($columns);
                $builder->orderBy('support_serial_no ASC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();

                $this->logger->info([
                    'getStoreSupportListExecTime' => [
                        'get_items'   => get_exec_time($time_4),
                        'items_count' => count($items),
                    ],
                ]);

                $time_5 = get_curr_micro_time();

                $items = $this->handleStoreSupportList($items);

                $this->logger->info([
                    'getStoreSupportListExecTime' => [
                        'get_items_handle' => get_exec_time($time_5),
                    ],
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->warning(['获取支援网点单号列表失败' => $params, 'message' => $e->getMessage()]);
        }

        return [
            'items'      => $items ?? [],
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count ?? 0,
            ],
        ];
    }

    /**
     * 校验报销实质是否允许混合提交
     *
     * @param array $budget_product_relation key:budget_id  value: [product_id]
     * @param string $apply_store_id 网点id
     * @return mixed
     * @throws ValidationException
     * @date 2022/7/8
     */
    public function addApplyCheck(array $budget_product_relation, string $apply_store_id)
    {
        $country_code = get_country_code();
        //归类
        $subject_type = $this->getBudgetType($budget_product_relation);
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            //泰国校验
            if (key_exists('water_and_electricity', $subject_type) && count($subject_type) > 1) {
                //水电费仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_water_and_electricity'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('own_fleet', $subject_type) && count($subject_type) > 1) {
                //自有车队仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_own_fleet'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('ad', $subject_type) && count($subject_type) > 1) {
                //税金及附加-广告牌税仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_ad'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('welfare_and_morale', $subject_type) && count($subject_type) > 1) {
                //员工福利费、员工团建费不能与其他分类混合提交，请检查！
                throw new ValidationException(self::$t->_('reimbursement_validation_welfare_and_morale'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('labour_services', $subject_type)) {
                if (count($subject_type) > 1) {
                    //劳务成本-外包、劳务成本-外协 不能与其他分类混合提交，请检查！
                    throw new ValidationException(self::$t->_('reimbursement_validation_labour_services'), ErrCode::$VALIDATE_ERROR);
                } else {
                    //network 网点类型
                    $store_kv = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                    if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                        $store_kv = OrdinaryPaymentEnums::FLOW_ID_PH_ARRAY_KEY;
                    }
                    $network_category = $store_kv['network'];
                    //网点信息
                    $store_data = (new StoreRepository())->getStoreDetail($apply_store_id);
                    //network 外包和外协不能一起提交
                    if (isset($store_data['category']) && in_array($store_data['category'],
                            $network_category) && count($budget_product_relation) > 1) {
                        throw new ValidationException(self::$t->_('reimbursement_validation_network_labour_services'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            //菲律宾校验
            if (key_exists('own_fleet', $subject_type) && count($subject_type) > 1) {
                //自有车队仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_own_fleet'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('welfare_and_morale', $subject_type) && count($subject_type) > 1) {
                //员工福利费、员工团建费不能与其他分类混合提交，请检查！
                throw new ValidationException(self::$t->_('reimbursement_validation_welfare_and_morale'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('labour_services', $subject_type)) {
                if (count($subject_type) > 1) {
                    //劳务成本-外包、劳务成本-外协 不能与其他分类混合提交，请检查！
                    throw new ValidationException(self::$t->_('reimbursement_validation_labour_services'), ErrCode::$VALIDATE_ERROR);
                } else {
                    //network 网点类型
                    $store_kv = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                    if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                        $store_kv = OrdinaryPaymentEnums::FLOW_ID_PH_ARRAY_KEY;
                    }
                    $network_category = $store_kv['network'];
                    //网点信息
                    $store_data = (new StoreRepository())->getStoreDetail($apply_store_id);
                    //network 外包和外协不能一起提交
                    if (isset($store_data['category']) && in_array($store_data['category'],
                            $network_category) && count($budget_product_relation) > 1) {
                        throw new ValidationException(self::$t->_('reimbursement_validation_network_labour_services'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            //马来校验
            if (key_exists('water_and_electricity', $subject_type) && count($subject_type) > 1) {
                //水电费仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_water_and_electricity'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('welfare_and_morale', $subject_type) && count($subject_type) > 1) {
                //员工福利费、员工团建费不能与其他分类混合提交，请检查！
                throw new ValidationException(self::$t->_('reimbursement_validation_welfare_and_morale'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('own_fleet', $subject_type) && count($subject_type) > 1) {
                //自有车队仅能单独提交，谢谢！
                throw new ValidationException(self::$t->_('reimbursement_validation_own_fleet'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('thirdparty_freight', $subject_type) && count($subject_type) > 1) {
                //第三方运费-支干线、第三方运费-加盟商不能与其他分类混合提交，请检查！
                throw new ValidationException(self::$t->_('reimbursement_validation_thirdparty_freight'), ErrCode::$VALIDATE_ERROR);
            }
            if (key_exists('labour_services', $subject_type) && count($subject_type) > 1) {
                //劳务成本-外包、劳务成本-外协 不能与其他分类混合提交，请检查！
                throw new ValidationException(self::$t->_('reimbursement_validation_labour_services'), ErrCode::$VALIDATE_ERROR);
            }
        }
        return true;
    }

    /**
     * v18028 校验付款分类是否允许混合提交
     *
     * @param array $budget_ids
     * @return mixed
     * @throws ValidationException
     */
    public function addApplyCheckNew(array $budget_ids)
    {
        $mixed_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_mixed_check_config');
        foreach ($budget_ids as $budget_id) {
            foreach ($mixed_config as $one_config) {
                if (in_array($budget_id, $one_config)) {
                    //科目id中是否存在不在分组中的id
                    $diff_budget_arr = array_values(array_diff($budget_ids, $one_config));
                    if (!empty($diff_budget_arr)) {
                        $budget_name_data = (new BudgetService())->getBudgetByIds([$budget_id, $diff_budget_arr[0]]);
                        throw new ValidationException(self::$t->_('reimbursement_mixed_check_error',
                            ['budget_1' => $budget_name_data[$budget_id], 'budget_2' => $budget_name_data[$diff_budget_arr[0]]]),
                            ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        }
        return true;
    }

    /**
     * v18028、v18960 获取预算科目的归类
     * @param $budget_ids
     * @return array
     */
    public function getBudgetTypeNew($budget_ids)
    {
        $subject_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_create_check_config');
        $subject_type   = [];
        foreach ($budget_ids as $budget_id) {
            $find_success = 0;
            foreach ($subject_config as $subject_name => $ids) {
                //匹配budget_id
                if (in_array($budget_id, $ids)) {
                    $find_success                = 1;
                    $subject_type[$subject_name] = isset($subject_type[$subject_name]) ? $subject_type[$subject_name] + 1 : 1;
                }
            }
            //没找到归为其他
            if ($find_success === 0) {
                $subject_type['other'] = isset($subject_type['other']) ? $subject_type['other'] + 1 : 1;
            }
        }
        return $subject_type;
    }

    /**
     * 获取预算科目的归类
     * @param $budget_product_relation
     * @date 2022/7/11
     * @return array
     *
     */
    public function getBudgetType($budget_product_relation)
    {
        $subjectConfing = json_decode((new EnvModel())->getEnvByCode('reimbursement_create_chek_config'), true);
        //ad广告牌税处理
        $ad_budget_arr = [];
        foreach ($subjectConfing as $subject_name => $ids) {
            foreach ($ids as $ad_budget_product) {
                if (stristr($ad_budget_product, '-') !== false) {
                    $ad_budget                   = explode('-', $ad_budget_product)[0];
                    $ad_product                  = explode('-', $ad_budget_product)[1];
                    $ad_budget_arr[$ad_budget][] = $ad_product;
                }
            }
        }

        $subject_type = [];
        foreach ($budget_product_relation as $budget_id => $product_id_arr) {
            //找分类
            $find_success = 0;
            foreach ($subjectConfing as $subject_name => $ids) {
                //ad(广告牌税是子类,匹配product_id)
                if ($subject_name == 'ad') {
                    if (key_exists($budget_id, $ad_budget_arr)) {
                        foreach ($product_id_arr as $pv) {
                            if (in_array($pv, $ad_budget_arr[$budget_id])) {
                                $subject_type['ad'] = isset($subject_type['ad']) ? $subject_type['ad'] + 1 : 1;
                            } else {
                                $subject_type['other'] = isset($subject_type['other']) ? $subject_type['other'] + 1 : 1;
                            }
                        }
                        $find_success = 1; //ad或者other已经确定好了,不用下边再判断other了
                    }
                } else {
                    //匹配budget_id
                    if (in_array($budget_id, $ids)) {
                        $find_success                = 1;
                        $subject_type[$subject_name] = isset($subject_type[$subject_name]) ? $subject_type[$subject_name] + 1 : 1;
                    }
                }
            }
            //没找到归为其他
            if ($find_success === 0) {
                $subject_type['other'] = isset($subject_type['other']) ? $subject_type['other'] + 1 : 1;
            }
        }
        return $subject_type;
    }

    /**
     * 获取税号信息
     * @param $invoice_tax_no
     * @return array
     * @date 2023/9/6
     */
    public function getInvoiceTaxNo($invoice_tax_no)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //是否开启了自动填写税号信息, 如果开启去查询数据
            $auto_tax_no = EnumsService::getInstance()->getSettingEnvValue('sys_module_reimbursement_auto_tax_no', '0');
            if ($auto_tax_no == '1') {
                //查询税号信息
                $data = SettingInvoiceTaxNoRepository::getInstance()->getInfoByNo($invoice_tax_no);
                if (!empty($data)) {
                    $data['rate'] = DetailService::getInstance()->getViewRate($data['rate']);
                }
            }
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('reimbursement-get-invoice-tax-no-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => !empty($data) ? $data : (object)[]
        ];
    }

    /**
     * 报错发票税号信息
     * 1. 如果不存在税号,就添加
     * 2. 如果存在税号,就更新
     * @param object $item reimbursementModel的object
     * @return bool
     */
    public function saveInvoiceTaxNo(object $item)
    {
        try {
            //同步发票税号信息
            $details    = $item->getDetails()->toArray();
            $details_kv = [];
            foreach ($details as $v) {
                $v['invoice_tax_no'] = trim($v['invoice_tax_no']);
                if (empty($v['invoice_tax_no'])) {
                    continue;
                }
                //由于数据库大小写不敏感, 统一用大写的key
                $details_kv[strtoupper($v['invoice_tax_no'])] = $v;
            }
            if (empty($details_kv)) {
                return true;
            }
            $tax_nos      = array_keys($details_kv);
            $invoice_data = SettingInvoiceTaxNoRepository::getInstance()->getDataByNos($tax_nos);
            //处理需要更新的
            foreach ($invoice_data as $one_invoice) {
                $one_invoice->invoice_tax_no = trim($one_invoice->invoice_tax_no);
                //找数据时统一用大写的key
                $upper_invoice_tax_no         = strtoupper($one_invoice->invoice_tax_no);
                $one_invoice->enterprise_name = $details_kv[$upper_invoice_tax_no]['enterprise_name'];
                $one_invoice->rate            = $details_kv[$upper_invoice_tax_no]['rate'];
                $one_invoice->company_addr    = $details_kv[$upper_invoice_tax_no]['company_addr'];
                $one_invoice->origin_no       = $item->no;
                $one_invoice->updated_at      = date('Y-m-d H:i:s');
                if ($one_invoice->save() == false) {
                    $this->logger->notice('同步发票税号信息失败, 数据:' . json_encode($one_invoice->toArray(),
                            JSON_UNESCAPED_UNICODE) . ' 可能的原因是:' . get_data_object_error_msg($one_invoice));
                }
                unset($details_kv[$upper_invoice_tax_no]);
            }
            //剩下的都是需要添加的
            $insert_data = [];
            foreach ($details_kv as $k_no => $v) {
                if (!empty($v['invoice_tax_no'])) {
                    $insert_data[] = [
                        'invoice_tax_no'  => $v['invoice_tax_no'],
                        'enterprise_name' => $v['enterprise_name'],
                        'company_addr'    => $v['company_addr'],
                        'rate'            => $v['rate'],
                        'origin_no'       => $item->no,
                        'created_at'      => date('Y-m-d H:i:s'),
                        'updated_at'      => date('Y-m-d H:i:s'),
                    ];
                }
            }
            if (!empty($insert_data)) {
                $invoice_model = new SettingInvoiceTaxNoModel();
                if ($invoice_model->batch_insert($insert_data) === false) {
                    $this->logger->notice('添加发票税号信息失败, 数据:' . json_encode($insert_data,
                            JSON_UNESCAPED_UNICODE) . ' 可能的原因是:' . get_data_object_error_msg($invoice_model));
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning('添加发票税号信息异常, 数据:' . json_encode($details ?? [],
                    JSON_UNESCAPED_UNICODE) . ' message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
        }
        return true;
    }

    /**
     * 验证出差是否已经报销
     * @param $serial_no
     * @return bool
     */
    public function checkBusinessTripIsReimbursed($serial_no): bool
    {
        if (empty($serial_no)) {
            return false;
        }

        // 是否是在途的出差单
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('rd.travel_serial_no');
        $builder->from(['r' => Reimbursement::class]);
        $builder->innerJoin(Detail::class, 'r.id=rd.re_id', 'rd');
        $builder->where('rd.travel_serial_no = :serial_no:', ['serial_no' => $serial_no]);
        $builder->andWhere('r.status in ({status:array}) and r.pay_status in ({pay_status:array})', [
            'status'     => [
                ReimbursementEnums::STATUS_WAIT,
                ReimbursementEnums::STATUS_PASS,
                ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                ReimbursementEnums::STATUS_WAITING_SIGNED,
                ReimbursementEnums::STATUS_WAITING_SUBMITTED,
            ],
            'pay_status' => [ReimbursementEnums::PAY_STATUS_WAIT, ReimbursementEnums::PAY_STATUS_PAY],
        ]);
        $re = $builder->getQuery()->execute()->toArray();

        if (!empty($re)) {
            return true;
        }

        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return false;
        }

        // 是否是在途的共同住宿出差单
        $lodging_fee_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
        $lodging_fee_ids = array_filter($lodging_fee_ids);
        if (empty($lodging_fee_ids)) {
            $this->logger->notice('出差单取消/修改-判断是否是在途的共同住宿出差单: 住宿费明细ID未配置[reimbursement_special_expense_details_type]');
            return false;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('rel.serial_no');
        $builder->from(['r' => Reimbursement::class]);
        $builder->innerJoin(ReimbursementDetailTravelRoommateRelModel::class, 'r.id = rel.re_id', 'rel');
        $builder->inWhere('rel.product_id ', $lodging_fee_ids);
        $builder->andWhere('rel.serial_no = :serial_no:', ['serial_no' => $serial_no,]);
        $builder->andWhere('r.status in ({status:array}) and r.pay_status in ({pay_status:array})', [
            'status'     => [
                ReimbursementEnums::STATUS_WAIT,
                ReimbursementEnums::STATUS_PASS,
                ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                ReimbursementEnums::STATUS_WAITING_SIGNED,
                ReimbursementEnums::STATUS_WAITING_SUBMITTED,
            ],
            'pay_status' => [ReimbursementEnums::PAY_STATUS_WAIT, ReimbursementEnums::PAY_STATUS_PAY],
        ]);

        $re = $builder->getQuery()->execute()->toArray();
        return !empty($re);
    }


    /**
     *
     * 保存单据草稿
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function saveDraft($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = [];

        try {
            // 验证单号是否被正式单据占用
            $this->checkAvailableNo($data['no']);

            // 单号是否被其他人的草稿占用
            $exist_draft_data = ReimbursementDraftModel::findFirst([
                'conditions' => 'apply_no = :apply_no:',
                'bind'       => ['apply_no' => $data['no']],
                'columns'    => ['id', 'created_id']
            ]);
            if (!empty($exist_draft_data) && $exist_draft_data->created_id != $user['id']) {
                throw new ValidationException(static::$t->_('reimbursement_no_exist_hint', ['no' => $data['no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 当前用户的草稿
            $draft_model = ReimbursementDraftModel::findFirst([
                'conditions' => 'created_id = :created_id:',
                'bind'       => ['created_id' => $user['id']],
            ]);
            if (empty($draft_model)) {
                $draft_model             = new ReimbursementDraftModel();
                $draft_model->created_id = $user['id'];
                $draft_model->created_at = date('Y-m-d H:i:s');
            }

            $this->logger->info(['reimbursement_save_draft_before_content' => $draft_model->content ? json_decode($draft_model->content, true) : '']);

            // 处理基础数据
            // 发票抬头对应公司 是否 与 费用所属公司一致
            $invoice_header_model   = SettingInvoiceHeaderRepository::getInstance()->getInfoById((int)$data['invoice_header_id'] ?? 0);
            $invoice_header_is_same = SettingEnums::INVOICE_HEADER_COMPANY_AND_COST_COMPANY_SAME;
            $related_company_ids    = !empty($invoice_header_model->related_company_ids) ? explode(',',
                $invoice_header_model->related_company_ids) : [];
            if (!in_array(SettingEnums::INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL, $related_company_ids) && !in_array($data['cost_company_id'] ?? 0,
                    $related_company_ids)) {
                $invoice_header_is_same = SettingEnums::INVOICE_HEADER_COMPANY_AND_COST_COMPANY_NOT_SAME;
            }

            $data['invoice_header_name']    = $invoice_header_model->header_name ?? '';
            $data['invoice_header_is_same'] = $invoice_header_is_same;

            // 获取币种与系统默认币种的汇率
            $exchange_rate         = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
            $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

            $first_department = [];
            if (!empty($data['cost_department'])) {
                $first_department = StaffService::getInstance()->getParentDepartment($data['cost_department'], 1);
            }
            $data['cost_sys_department'] = $first_department['id'] ?? 0;

            $budget_ids     = array_column($data['expense'], 'budget_id');
            $budget_service = new BudgetService();
            $budget_list    = $budget_service->budgetObjectList($budget_ids);

            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);

            // 金额都是乘以1000的
            $travel_amount      = 0;
            $local_amount       = 0;
            $amount             = 0;
            $payable_amount_all = 0;

            // 行数据处理
            foreach ($data['expense'] as $k => $expense) {
                // 来源by给默认值
                if (ReimbursementEnums::SOURCE_TYPE_BY == $data['source_type']) {
                    $data['expense'][$k]['wht_type'] = $wht_cat_map['/'] ?? 0;
                }

                $data['expense'][$k]['tax_not'] = bcmul(round($expense['tax_not'] ?? 0, 2), 1000);

                // 历史税率传0.06 改后传6 为兼容历史数据, 6先除100再乘1000入库
                $rate                        = bcdiv((string)($expense['rate'] ?? 0), 100, 4);
                $data['expense'][$k]['rate'] = bcmul((string)$rate, 1000);
                $data['expense'][$k]['tax']  = bcmul(round($expense['tax'] ?? 0, 2), 1000);

                $data['expense'][$k]['budget_id']    = $expense['budget_id'] ?? 0;
                $data['expense'][$k]['level_code']   = $budget_list[$expense['budget_id']]['level_code'] ?? '';
                $data['expense'][$k]['product_id']   = isset($expense['product_id']) ? $expense['product_id'] : 0;
                $data['expense'][$k]['product_name'] = isset($expense['product_name']) ? $expense['product_name'] : '';

                $data['expense'][$k]['amount'] = bcmul(round($expense['amount'] ?? 0, 2), 1000);

                // [9542]新增字段
                // WHT税率 直接入库, 计算/100
                $data['expense'][$k]['wht_tax'] = (string)($expense['wht_tax'] ?? '');

                //WHT税额 = 发票金额（不含VAT含WHT）* WHT税率
                $data['expense'][$k]['wht_tax_amount'] = bcmul(round($expense['wht_tax_amount'] ?? 0, 2), 1000);

                // 可抵扣VAT税率 直接入库, 计算/100
                $deductible_vat_tax                        = $expense['deductible_vat_tax'] ?? 0;
                $data['expense'][$k]['deductible_vat_tax'] = (string)$deductible_vat_tax;

                // 可抵扣税额=VAT税额*可抵扣VAT税率
                $data['expense'][$k]['deductible_tax_amount'] = bcmul(round($expense['deductible_tax_amount'] ?? 0, 2), 1000);

                $data['expense'][$k]['payable_amount'] = bcmul(round($expense['payable_amount'] ?? 0, 2), 1000);

                $t_amount = $data['expense'][$k]['tax'] + $data['expense'][$k]['tax_not'];
                if (isset($expense['category_a']) && $expense['category_a'] == Enums::REIMBURSEMENT_EXPENSE_TRAVEL) {
                    $travel_amount = bcadd($travel_amount, $t_amount);
                } elseif (isset($expense['category_a']) && $expense['category_a'] == Enums::REIMBURSEMENT_EXPENSE_LOCAL) {
                    $local_amount = bcadd($local_amount, $t_amount);
                }

                $amount             = bcadd($amount, $t_amount);
                $payable_amount_all = bcadd($payable_amount_all, $data['expense'][$k]['payable_amount']);

                if (isset($expense['fuel_mileage']) && $expense['fuel_mileage']) {
                    $data['expense'][$k]['fuel_mileage'] = bcmul($expense['fuel_mileage'], 1000);
                }

                if (isset($expense['fuel_start_mileage']) && $expense['fuel_start_mileage']) {
                    $data['expense'][$k]['fuel_start_mileage'] = bcmul($expense['fuel_start_mileage'], 1000);
                }

                if (isset($expense['fuel_end_mileage']) && $expense['fuel_end_mileage']) {
                    $data['expense'][$k]['fuel_end_mileage'] = bcmul($expense['fuel_end_mileage'], 1000);
                }

                // 19703存储差旅-出差天数、出差类型
                $data['expense'][$k]['travel_days_num']           = $expense['travel_days_num'] ?? 0;
                $data['expense'][$k]['travel_business_trip_type'] = $expense['travel_business_trip_type'] ?? 0;

                // 菲律宾的: 发票类型
                $data['expense'][$k]['invoice_type'] = !empty($expense['invoice_type']) ? $expense['invoice_type'] : GlobalEnums::FINANCIAL_INVOICE_TYPE_0;

                // 印尼: 是否有增值税票
                $data['expense'][$k]['is_with_vat_invoice'] = !empty($expense['is_with_vat_invoice']) ? $expense['is_with_vat_invoice'] : GlobalEnums::IS_WITH_VAT_INVOICE_DEFAULT;

                // 马来: 支付方式
                $data['expense'][$k]['payment_method'] = !empty($expense['payment_method']) ? $expense['payment_method'] : 0;
            }

            // 报销类型清单
            $reimbursement_type_item = array_fill(0, count(Enums::$budget_object_reimbursement_type_map), 0);
            foreach ($reimbursement_type_item as $type => $value) {
                // 没有这个实质，就跳过
                if (empty($value)) {
                    continue;
                }

                // 默认是0=其他，要第一个实质不是其他的，如果都是其他，不影响最后结果
                if (empty($first_type)) {
                    $first_type = $type;
                }
            }

            $data['amount']             = $amount;
            $data['travel_amount']      = $travel_amount;
            $data['local_amount']       = $local_amount;
            $data['payable_amount_all'] = $payable_amount_all;
            $data['start_at']           = date('Y-m-d');
            $data['end_at']             = date('Y-m-d');
            $data['status']             = ''; // Enums::WF_STATE_PENDING
            $data['pay_status']         = Enums::PAYMENT_PAY_STATUS_PENDING;
            $data['pay_at']             = '';
            $data['created_at']         = date('Y-m-d H:i:s');
            $data['apply_date']         = date('Y-m-d', strtotime($data['created_at']));
            $data['updated_at']         = $data['created_at'];
            $data['created_id']         = $user['id'];
            $data['created_name']       = get_name_and_nick_name($user['name'] ?? '', $user['nick_name'] ?? '');

            // 申请人职级
            $apply_staff_info              = HrStaffInfoModel::getUserInfo($data['apply_id'] ?? '');
            $data['apply_job_title_grade'] = $apply_staff_info['job_title_grade_v2'] ?? 0;
            $data['type']                  = $first_type ?? Enums::REIMBURSEMENT_TYPE_OTHER;

            if ($data['source_type'] == ReimbursementEnums::SOURCE_TYPE_BY && empty($data['cost_company_id'])) {
                $dep_info                = SysDepartmentModel::findFirst($user['sys_department_id']);
                $data['cost_company_id'] = $dep_info->company_id ?? 0;
            }

            // 冲抵借款的相关字段默认值
            $data['loan_amount'] = 0;
            $data['real_amount'] = $data['payable_amount_all'] ?? '';

            $this->logger->info(['reimbursement_save_draft_new_content' => $data]);

            $draft_model->content     = json_encode($data, JSON_UNESCAPED_UNICODE);
            $draft_model->apply_no    = $data['no'];
            $draft_model->data_source = $data['source_type'];
            $draft_model->updated_id  = $user['id'];
            $draft_model->updated_at  = date('Y-m-d H:i:s');

            // 暂存
            if ($draft_model->save() === false) {
                throw new BusinessException('报销暂存save失败, ' . get_data_object_error_msg($draft_model), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('reimbursement-saveDraft-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result
        ];
    }

    /**
     * 计算合计金额
     *
     * @param $params
     * @param $default_currency
     * @return array
     *
     */
    public function calculateTotalAmount($params, $default_currency)
    {
        $amount             = '0';
        $payable_amount_all = '0';
        foreach ($params['expense'] as $val) {
            // 重新提交,不涉及币种转换, 直接累计
            if ($params['submit_type'] == ReimbursementEnums::CREATE_ACTION_RESUBMIT) {
                $amount             = bcadd($amount, $val['amount'], 2);
                $payable_amount_all = bcadd($payable_amount_all, $val['payable_amount'], 2);
                continue;
            }

            // 新增提交: 金额转换计算
            if ($val['invoice_currency'] != $default_currency) {
                $val['amount']         = $this->conversionAmountByExchangeRate($val['invoice_currency'], $val['invoice_date'], $val['amount'])['amount'];
                $val['payable_amount'] = $this->conversionAmountByExchangeRate($val['invoice_currency'], $val['invoice_date'], $val['payable_amount'])['amount'];
            }

            $amount             = bcadd($amount, $val['amount'], 2);
            $payable_amount_all = bcadd($payable_amount_all, $val['payable_amount'], 2);
        }

        return [
            'amount'             => $amount,
            'payable_amount_all' => $payable_amount_all,
        ];
    }

    /**
     * 获取申请人的身份证文件
     *
     * @param $apply_id
     * @return string
     */
    public function getApplyIDInfo($apply_id)
    {
        if (empty($apply_id)) {
            return '';
        }

        $annex_path_front = (new HrStaffRepository())->getStaffIdentityV2($apply_id)['annex_path_front'] ?? '';
        if (!empty($annex_path_front)) {
            return $annex_path_front;
        }

        $model = Reimbursement::findFirst([
            'conditions' => 'apply_id = :apply_id: AND status = :status:',
            'bind'       => ['apply_id' => $apply_id, 'status' => ReimbursementEnums::STATUS_PASS],
            'order'      => 'apply_date DESC',
        ]);

        if (empty($model)) {
            return '';
        }

        return ReimbursementSignatureRecordModel::findFirst([
                'conditions' => 'no = :no: AND signature_status = :signature_status:',
                'bind'       => ['no' => $model->no, 'signature_status' => ReimbursementEnums::SIGN_STATUS_AGREED],
                'columns'    => ['identity_file'],
            ])->identity_file ?? '';
    }

    /**
     * 提交申请人签字信息
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function submitApplySignatureInfo($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['no']]), ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_DATA_NULL);
            }

            if ($model->apply_id != $user['id']) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_023'), ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_APPLY_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_024'), ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_STATUS_ERROR);
            }

            $current_action = ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED;

            $this->saveSignAgreeData($current_action, $model, $params, $user);

            // 业务侧数据
            $biz_params                        = $model->toArray();
            $biz_params['is_submit']           = 1;
            $biz_params['apply_identity_url']  = $params['identity_file'];
            $biz_params['apply_signature_url'] = $params['signature_file'];

            $expense_item = $model->getDetails()->toArray();
            $amount_info  = $this->getDetailBudgetAmount($expense_item, $model->exchange_rate);
            $this->createEventCallBack($current_action, $biz_params, $amount_info, $user, $expense_item);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning(['reimbursement_save_aignature_info' => $e->getMessage()]);
        } catch (\Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(['reimbursement_save_aignature_info' => $e->getMessage()]);
        }

        // 创建异常
        $auto_submit_success_code_item = [
            ErrCode::$SUCCESS,
            ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_DATA_NULL,
            ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_APPLY_ERROR,
            ErrCode::$REIMBURSEMENT_SIGN_SUBMIT_STATUS_ERROR,
        ];
        if (!in_array($code, $auto_submit_success_code_item)) {
            $handle_res = $this->saveSignAutoSubmitFailHandel($model, $params, $user);
            $code       = $handle_res['code'];
            $message    = $handle_res['message'];
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 签字完成, 自动提交失败处理
     *
     * 更新为待提交 并 给发起人发消息提醒
     *
     */
    protected function saveSignAutoSubmitFailHandel($model, $params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $this->saveSignAgreeData(ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED_AUTO_SUBMIT_FAIL, $model, $params, $user);

            // 手机端操作, 发送失败提醒消息
            if (in_array($params['signature_channel'], [ReimbursementEnums::OPERATE_CHANNEL_BY, ReimbursementEnums::OPERATE_CHANNEL_KIT])) {
                $staff_ids = [
                    $model->created_id,
                ];
                $this->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_AUTO_SUBMIT_FAIL, $staff_ids, $model);
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(['reimbursement_save_aignature_info' => $e->getMessage()]);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 处理签字同意提交的数据
     */
    protected function saveSignAgreeData($current_action, $model, $params, $user)
    {
        // 单据状态更新
        $model_update = [
            'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
            'updated_at'             => date('Y-m-d H:i:s'),
            'last_update_id'         => $user['id'],
            'last_update_name'       => $user['name'],
            'last_update_department' => $user['department'],
            'last_update_job_title'  => $user['job_title'],
            'last_update_at'         => date('Y-m-d H:i:s'),
        ];
        if ($model->i_update($model_update) === false) {
            throw new BusinessException('报销签字提交-状态更新异常,' . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
        }

        $sign_params = [
            'identity_file'     => $params['identity_file'],
            'signature_file'    => $params['signature_file'],
            'signature_status'  => ReimbursementEnums::SIGN_STATUS_AGREED,
            'signature_channel' => $params['signature_channel'] ?? 0,
        ];

        return $this->createSignatureRecord($model, $sign_params, $user);
    }

    /**
     * 生成申请人签字二维码
     */
    public function generateSignatureQRcode($params, $user)
    {
        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $params['no']],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['no']]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->apply_id != $user['id']) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_023'), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->status != ReimbursementEnums::STATUS_WAITING_SIGNED) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_024'), ErrCode::$VALIDATE_ERROR);
        }

        // 生成签字二维码
        $page_url      = env('by_url_prefix_new') . ReimbursementEnums::MSG_PAGE_PATH_TO_SIGN . "?order_no={$model->no}&src=qrcode";
        $qrcode_text   = 'flashbackyard://fe/html?url=' . urlencode($page_url);
        $qrcode_size   = 300;
        $qrcode_margin = 10;

        $qrcode_logo_path   = BASE_PATH . '/public/images/flash_logo.png';
        $qrcode_logo_width  = 60;
        $qrcode_logo_height = 60;

        $image_content = (new EndroidQRCode($qrcode_text, $qrcode_size, $qrcode_margin))
            ->setLogo($qrcode_logo_path, $qrcode_logo_width, $qrcode_logo_height)
            ->getBase64Image();
        return [
            'qrtoken_base64' => $image_content,
        ];
    }

    /**
     * 申请人签字状态查询
     */
    public function getSignatureStatus($params, $user)
    {
        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $params['no']],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['no']]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->apply_id != $user['id']) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_023'), ErrCode::$VALIDATE_ERROR);
        }

        $signature_record_model = ReimbursementSignatureRecordModel::findFirst([
            'conditions' => 'no = :no: AND signature_id = :signature_id:',
            'bind'       => ['no' => $model->no, 'signature_id' => $user['id']],
            'columns'    => ['id', 'signature_status'],
        ]);

        return [
            'signature_status' => !empty($signature_record_model->signature_status) ? $signature_record_model->signature_status : '0',
        ];
    }

    /**
     * 获取报销相关规则配置
     *
     * @return array
     */
    public function getConfig()
    {
        $data = [];

        $lodging_budget_limit_config         = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_add_about_lodging_budget_limit_config');
        $data['lodging_budget_limit_config'] = !empty($lodging_budget_limit_config) ? $lodging_budget_limit_config : null;

        // 差旅费下的住宿费明细ID
//        $data['lodging_fee_id'] = EnumsService::getInstance()->getSettingEnvValue('reimbursement_travel_lodging_fee_id');

        // 报销相关枚举配置
        $reimbursement_enums = EnumsService::getInstance()->getReimbursementEnums();
        $data                = array_merge($data, $reimbursement_enums);

        return $data;
    }

    /**
     * ODO OCR识别
     */
    public function getODOByOCR($image_url, $user)
    {
        $info_id    = $user['id'];
        $secret_key = env('ai_odo_orc_secret_key', '');
        $uri        = '/v2/odo-ocr';

        if (empty($secret_key)) {
            throw new BusinessException('ai_odo_orc_secret_key 配置异常[env], 请确认', ErrCode::$BUSINESS_ERROR);
        }

        $header = [
            'X-FLE-TOKEN: ' . $this->genOCRToken($info_id, $secret_key, $uri),
            'Content-Type: application/x-www-form-urlencoded',
        ];

        $json_params = 'url=' . $image_url . '&image=&area=' . strtolower(get_country_code());
        $this->logger->info(['odo_ocr_request' => $json_params, 'header' => $header]);
        $res_data = curl_request(env('ai_url') . $uri, $json_params, 'POST', $header);
        $res_data = json_decode($res_data, true);
        $this->logger->info(['odo_ocr_response' => $res_data]);

        return $res_data['result']['odo'] ?? '';
    }

    /**
     * 生成OCR ODO接口token
     * @param $info_id
     * @param $secret_key
     * @param $uri
     * @return string
     */
    private function genOCRToken($info_id, $secret_key, $uri)
    {
        // 获取当前时间戳（毫秒）
        $timestamp = round(microtime(true) * 1000);

        // 构建待签名字符串
        $to_token = "POST" . "\n" . $uri . "\n" . $timestamp . "\n" . $info_id;

        // 使用HMAC-SHA1算法生成签名
        $digest = hash_hmac('sha1', $to_token, $secret_key, true);

        // Base64编码
        $token = base64_encode($digest);

        // 拼接最终token
        return $timestamp . "_" . $info_id . "_" . $token;
    }

    /**
     * 作废报销单(发起人)
     */
    public function invalidOrder($params, $user)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['no']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->created_id != $user['id']) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $user['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_CONFIRMED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_030', ['status' => $model->status]), ErrCode::$VALIDATE_ERROR);
            }

            $current_action = ReimbursementEnums::CREATE_ACTION_CREATED_INVALID;
            $update         = [
                'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
                'pay_status'             => Enums::LOAN_PAY_STATUS_NOTPAY,
                'updated_at'             => date('Y-m-d H:i:s'),
                'last_update_id'         => $user['id'],
                'last_update_name'       => $user['name'],
                'last_update_department' => $user['department'],
                'last_update_job_title'  => $user['job_title'],
                'last_update_at'         => date('Y-m-d H:i:s'),
            ];
            if ($model->i_update($update) === false) {
                throw new BusinessException('手动作废-状态更新失败,' . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
            }

            $th_lang = self::getTranslation('th');
            $en_lang = self::getTranslation('en');
            $params['invalid_reason'] = $en_lang->_('reimbursement_save_error_036') . $th_lang->_('reimbursement_save_error_036');
            $params['operate_type'] = ReimbursementEnums::INVALID_TYPE_1;

            $this->createInvalidRecord($model, $params, $user);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    /**
     * 创建签字记录
     */
    public function createSignatureRecord($model, $params, $user)
    {
        $signature_record = [
            'no'                => $model->no,
            'signature_id'      => $user['id'],
            'identity_file'     => $params['identity_file'] ?? '',
            'signature_file'    => $params['signature_file'] ?? '',
            'signature_at'      => date('Y-m-d H:i:s'),
            'signature_channel' => $params['signature_channel'],
            'signature_status'  => $params['signature_status'],
            'reject_reason'     => $params['reject_reason'] ?? '',
        ];

        $signature_record_model = new ReimbursementSignatureRecordModel();
        if ($signature_record_model->i_create($signature_record) === false) {
            throw new BusinessException('报销签字提交-签字记录表写入异常,' . $signature_record_model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 报销单作废场景
     *
     * 创建作废记录
     * 释放关联借款
     * 释放关联备用金
     */
    public function createInvalidRecord($model, $params, $user)
    {
        $invalid_record       = [
            'no'              => $model->no,
            'operate_id'      => $user['id'],
            'operate_at'      => date('Y-m-d H:i:s'),
            'operate_channel' => $params['operate_channel'] ?? '',
            'operate_type'    => $params['operate_type'] ?? 0,
            'invalid_reason'  => $params['invalid_reason'] ?? '',
        ];
        $invalid_record_model = new ReimbursementInvalidRecordModel();
        if ($invalid_record_model->i_create($invalid_record) === false) {
            throw new BusinessException("报销单作废-作废记录写入失败[{$model->no}]," . $invalid_record_model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
        }

        // 释放关联借款
        $reimbursement_flow_service = new ReimbursementFlowService();
        $reimbursement_flow_service->freeLoans($model);

        // 释放备用金关联
        $reimbursement_flow_service->freeReserves($model);

        return true;
    }

    /**
     * 手动提交
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function manualSubmit($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['no']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->created_id != $user['id']) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_023'), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_SUBMITTED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_024'), ErrCode::$VALIDATE_ERROR);
            }

            $current_action = ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT;
            $model_update   = [
                'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
                'updated_at'             => date('Y-m-d H:i:s'),
                'last_update_id'         => $user['id'],
                'last_update_name'       => $user['name'],
                'last_update_department' => $user['department'],
                'last_update_job_title'  => $user['job_title'],
                'last_update_at'         => date('Y-m-d H:i:s'),
            ];
            if ($model->i_update($model_update) === false) {
                throw new BusinessException('报销签字提交-状态更新异常,' . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
            }

            $biz_params              = $model->toArray();
            $biz_params['is_submit'] = 1;

            // 申请人签字文件
            $signature_record_model = ReimbursementSignatureRecordModel::findFirst([
                'conditions' => 'no = :no: AND signature_id = :signature_id: AND signature_status = :signature_status:',
                'bind' => ['no' => $model->no , 'signature_id' => $model->apply_id, 'signature_status' => ReimbursementEnums::SIGN_STATUS_AGREED],
                'columns' => ['identity_file', 'signature_file']
            ]);
            if (empty($signature_record_model)) {
                throw new BusinessException("报销签字提交-申请人同意的签字文件不存在[{$model->no}-{$model->apply_id}]", ErrCode::$BUSINESS_ERROR);
            }

            $biz_params['apply_identity_url']  = $signature_record_model->identity_file;
            $biz_params['apply_signature_url'] = $signature_record_model->signature_file;

            // 提取预算占用数据
            $expense_item = $model->getDetails()->toArray();
            $amount_info  = $this->getDetailBudgetAmount($expense_item, $model->exchange_rate);
            $this->createEventCallBack($current_action, $biz_params, $amount_info, $user, $expense_item);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning(['reimbursement_manual_submit' => $e->getMessage()]);
        } catch (\Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(['reimbursement_manual_submit' => $e->getMessage()]);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 发送签字提醒
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function sendSignatureReminder($params)
    {
        $order_no = $params['order_no'];
        $user_id  = $params['user_id'];

        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->created_id != $user_id) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->apply_id == $user_id) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_033', ['user_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->status != ReimbursementEnums::STATUS_WAITING_SIGNED) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_032'), ErrCode::$VALIDATE_ERROR);
        }

        $staff_ids = [
            $model->apply_id,
        ];
        return $this->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED, $staff_ids, $model);
    }

    /**
     * 共同住宿人拒绝确认
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function rejectConfirm($params, $user)
    {
        $order_no = $params['order_no'];
        $user_id  = $user['id'];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $this->logger->info([
                'reject_confirm_reimbursement_travel_roommate' => [
                    'params' => $params,
                    'user'   => $user,
                ],
            ]);

            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $order_no],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_CONFIRMED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_034', ['apply_status' => $model->status]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 报销单关联的当前用户的共同住宿单
            $rel_models = ReimbursementDetailTravelRoommateRelModel::find([
                'conditions' => 're_id = :re_id: AND apply_staff_id = :apply_staff_id: AND confirm_status = :confirm_status:',
                'bind'       => [
                    're_id'          => $model->id,
                    'apply_staff_id' => $user_id,
                    'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
                ],
                'for_update' => true,
            ]);

            $this->logger->info(['reject_confirm_travel_roommate_rel_before_data' => $rel_models->toArray()]);

            foreach ($rel_models as $rel_model) {
                $rel_update = [
                    'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_4,
                    'confirm_at'     => date('Y-m-d H:i:s'),
                ];

                if ($rel_model->i_update($rel_update) === false) {
                    throw new BusinessException("拒绝确认-共同住宿单更新失败[{$rel_model->id}], " . $rel_model->getErrorMessagesString(),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            $current_action = ReimbursementEnums::CREATE_ACTION_REJECT_CONFIRM;
            $model_update   = [
                'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
                'pay_status'             => ReimbursementEnums::PAY_STATUS_UN_PAY,
                'updated_at'             => date('Y-m-d H:i:s'),
                'last_update_id'         => $user['id'],
                'last_update_name'       => $user['name'],
                'last_update_department' => $user['department'],
                'last_update_job_title'  => $user['job_title'],
                'last_update_at'         => date('Y-m-d H:i:s'),
            ];

            $this->logger->info(['reject_confirm_reimbursement_update_data' => $model_update]);
            if ($model->i_update($model_update) === false) {
                throw new BusinessException("拒绝确认-报销单状态更新失败[{$model->no}], " . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
            }

            $th_lang = self::getTranslation('th');
            $en_lang = self::getTranslation('en');

            $invalid_reason = $en_lang->_('reimbursement_save_error_035') . $th_lang->_('reimbursement_save_error_035');

            $invalid_params = [
                'operate_channel' => $params['platform'] ?? 0,
                'operate_type'    => ReimbursementEnums::INVALID_TYPE_2,
                'invalid_reason'  => $invalid_reason,
            ];
            $this->createInvalidRecord($model, $invalid_params, $user);

            $staff_ids = [
                $model->created_id,
            ];
            $this->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_REJECT_CONFIRM, $staff_ids, $model);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    /**
     * 共同住宿人确认无误
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function agreeConfirm($params, $user)
    {
        $order_no = $params['order_no'];
        $user_id  = $user['id'];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $this->logger->info([
                'agree_confirm_reimbursement_travel_roommate' => [
                    'params' => $params,
                    'user'   => $user,
                ],
            ]);

            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $order_no],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_CONFIRMED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_034', ['apply_status' => $model->status]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 报销单关联的共同住宿单
            $rel_models = ReimbursementDetailTravelRoommateRelModel::find([
                'conditions' => 're_id = :re_id:',
                'bind'       => ['re_id' => $model->id],
                'for_update' => true,
            ]);

            $this->logger->info(['agree_confirm_travel_roommate_rel_before_data' => $rel_models->toArray()]);

            $all_total_roommate_rel = count($rel_models);
            $agree_confirmed_total  = 0;
            foreach ($rel_models as $rel_model) {
                if (in_array($rel_model->confirm_status,
                    [ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_2, ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_3,])
                ) {
                    $agree_confirmed_total++;
                    continue;
                }

                if ($rel_model->apply_staff_id == $user_id && $rel_model->confirm_status == ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1) {
                    $rel_update = [
                        'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_2,
                        'confirm_at'     => date('Y-m-d H:i:s'),
                    ];

                    if ($rel_model->i_update($rel_update) === false) {
                        throw new BusinessException("确认无误-共同住宿单更新失败[{$rel_model->id}], " . $rel_model->getErrorMessagesString(),
                            ErrCode::$BUSINESS_ERROR);
                    }

                    $agree_confirmed_total++;
                }
            }

            // 已全部确认完毕
            if ($agree_confirmed_total >= $all_total_roommate_rel) {
                $current_action = ReimbursementEnums::CREATE_ACTION_ALL_CONFIRMED;
                $model_update   = [
                    'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
                    'updated_at'             => date('Y-m-d H:i:s'),
                    'last_update_id'         => $user['id'],
                    'last_update_name'       => $user['name'],
                    'last_update_department' => $user['department'],
                    'last_update_job_title'  => $user['job_title'],
                    'last_update_at'         => date('Y-m-d H:i:s'),
                ];

                $this->logger->info(['agree_confirm_reimbursement_update_data' => $model_update]);
                if ($model->i_update($model_update) === false) {
                    throw new BusinessException("确认无误-报销单状态更新失败[{$model->no}], " . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
                }

                $staff_ids = [
                    $model->apply_id,
                ];
                $this->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED, $staff_ids, $model);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    /**
     * 拒绝签字(申请人)
     *
     * @param $params
     * @param $user
     * @return string
     */
    public function rejectSign($params, $user)
    {
        $order_no = $params['order_no'];
        $user_id  = $user['id'];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $this->logger->info(['reject_sign_reimbursement' => ['params' => $params, 'user' => $user,],]);

            $model = Reimbursement::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $order_no],
                'for_update' => true,
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->apply_id != $user_id) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($model->status != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_037', ['apply_status' => $model->status]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $current_action = ReimbursementEnums::CREATE_ACTION_APPLY_REJECT_SIGN;
            $model_update   = [
                'status'                 => $this->getOrderNextStatus($current_action, $model->travel_is_have_roommate, $model->status),
                'pay_status'             => ReimbursementEnums::PAY_STATUS_UN_PAY,
                'updated_at'             => date('Y-m-d H:i:s'),
                'last_update_id'         => $user['id'],
                'last_update_name'       => $user['name'],
                'last_update_department' => $user['department'],
                'last_update_job_title'  => $user['job_title'],
                'last_update_at'         => date('Y-m-d H:i:s'),
            ];

            $this->logger->info(['reject_sign_reimbursement_update_data' => $model_update]);
            if ($model->i_update($model_update) === false) {
                throw new BusinessException("拒绝签字-报销单状态更新失败[{$model->no}], " . $model->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
            }

            $sign_params = [
                'signature_status'  => ReimbursementEnums::SIGN_STATUS_REJECTED,
                'reject_reason'     => $params['reject_reason'],
                'signature_channel' => $params['platform'] ?? 0,
            ];
            $this->createSignatureRecord($model, $sign_params, $user);

            $invalid_params = [
                'operate_channel' => $params['platform'] ?? 0,
                'operate_type'    => ReimbursementEnums::INVALID_TYPE_3,
                'invalid_reason'  => $params['reject_reason'],
            ];
            $this->createInvalidRecord($model, $invalid_params, $user);

            $staff_ids = [
                $model->created_id,
            ];
            $this->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_REJECT_SIGN, $staff_ids, $model);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    /**
     * 上传申请人支援网点单号
     * @param $params
     * @return mixed
     */
    public function uploadApplySupportOrder($params, $user)
    {
        ini_set('memory_limit', '512M');

        $product_id = $params['product_id'];

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        $check_result     = false;
        $total_number     = 0;
        $success_number   = 0;
        $error_number     = 0;
        $check_file_url   = '';
        $result_list      = [];

        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('bank_flow_upload_file_type_error'), ErrCode::$VALIDATE_ERROR);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->getSheetData();

            // 读取文件的起始位置 默认0行是表头 1行是数据
            $excel_header = array_shift($excel_data);

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            $excel_data = trim_array($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('bank_flow_data_empty'), ErrCode::$VALIDATE_ERROR);
            }

            $total_number = count($excel_data);
            if ($total_number > ReimbursementEnums::UPLOAD_SUPPORT_ORDER_MAX_COUNT) {
                throw new ValidationException(static::$t->_('upload_apply_support_order_error_001', ['max_count' => ReimbursementEnums::UPLOAD_SUPPORT_ORDER_MAX_COUNT]), ErrCode::$VALIDATE_ERROR);
            }

            $excel_all_support_no_list = array_column($excel_data, 0);
            $excel_support_no_list     = array_filter(array_unique($excel_all_support_no_list));
            if (empty($excel_support_no_list)) {
                $total_number = 0;
                throw new ValidationException(static::$t->_('upload_apply_support_order_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 支援单号是否有重复
            $excel_all_support_no_count = array_count_values($excel_all_support_no_list);

            // 支援单基本信息
            $time_1                = get_curr_micro_time();
            $exist_support_no_list = HrStaffApplySupportStoreRepository::getInstance()->getListBySupportNoList($excel_support_no_list);
            $exist_support_no_list = array_column($exist_support_no_list, null, 'support_serial_no');
            $this->logger->info([
                'getListBySupportNoListExecTime' => [
                    'get_serial_no_list'   => get_exec_time($time_1),
                    'serial_no_list_count' => count($exist_support_no_list),
                ],
                'params'                         => $excel_support_no_list,
            ]);

            // 是否是有效可选范围
            $apply_related_support_no_list = $this->getApplyRelatedSupportList($params['apply_id'], $excel_support_no_list);
            $apply_related_support_no_list = array_column($apply_related_support_no_list, null, 'serial_no');


            // 是否在途
            $time_2         = get_curr_micro_time();
            $used_support_no_list = $this->getSupportSerialNoList($product_id, $excel_support_no_list);
            $this->logger->info([
                'getUsedSupportNoListExecTime' => [
                    'get_serial_no_list'   => get_exec_time($time_2),
                    'serial_no_list_count' => count($used_support_no_list),
                ],
                'params'                      => $params,
            ]);

            // 费用明细类型配置
            $accommodation_fees_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
            $transportation_ids     = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_TRANSPORTATION);

            // 支援费用允许报销的交通方式
            $allow_transportation_modes = static::getSupportAllowTransportationMode();

            $product_info = (new BudgetService())->get_product_info($product_id);
            $product_name = $product_info['name'] ?? '';

            $today_date = date('Y-m-d');

            $correct_item = [];
            foreach ($excel_data as $index => $row) {
                $excel_support_no = $row[0] ?? '';
                if (mb_strlen($excel_support_no) < 1) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_002');
                    continue;
                }

                if ($excel_all_support_no_count[$excel_support_no] > 1) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_010');
                    continue;
                }

                $exist_info = $exist_support_no_list[$excel_support_no] ?? [];
                if (empty($exist_info)) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_003');
                    continue;
                }

                if ($exist_info['status'] != ByWorkflowEnums::BY_OPERATE_PASS || !in_array($exist_info['support_status'],
                        [ReimbursementEnums::STORE_SUPPORT_STATUS_2, ReimbursementEnums::STORE_SUPPORT_STATUS_3])) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_005');
                    continue;
                }

                if ($exist_info['employment_end_date'] >= $today_date) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_006');
                    continue;
                }

                if (in_array($excel_support_no, $used_support_no_list)) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_007', ['product_name' => $product_name]);
                    continue;
                }

                $can_use_info = $apply_related_support_no_list[$excel_support_no] ?? [];
                if (empty($can_use_info)) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_004');
                    continue;
                }

                if (in_array($product_id, $accommodation_fees_ids) && $can_use_info['is_stay'] != 1) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_008');
                    continue;
                }

                if (in_array($product_id, $transportation_ids) && !empty($can_use_info['transportation_mode']) && !in_array($can_use_info['transportation_mode'], $allow_transportation_modes)) {
                    $excel_data[$index][1] = static::$t->_('upload_apply_support_order_error_009');
                    continue;
                }

                $excel_data[$index][1] = static::$t->_('excel_result_success');

                $correct_item[] = $excel_support_no;
            }

            $success_number = count($correct_item);
            $error_number   = $total_number - $success_number;
            $check_result   = $total_number == $success_number;

            // 校验有误, 生成结果文件
            if (!$check_result) {
                $excel_header[1] = static::$t->_('check_result');
                $file_name       = 'Batch_import_support_results_' . date('YmdHis') . '.xlsx';
                $error_file_info = $this->exportExcel($excel_header, $excel_data, $file_name);
                if (empty($error_file_info['data'])) {
                    throw new ValidationException(static::$t->_('upload_apply_support_order_error_011'), ErrCode::$VALIDATE_ERROR);
                }

                $check_file_url = $error_file_info['data'];
            } else {
                $result_list = $correct_item;
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning(['导入支援网点单号失败' => $params, 'message' => $e->getMessage()]);
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(['导入支援网点单号失败' => $params, 'message' => $e->getMessage()]);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'check_result'     => $check_result,
                'all_num'          => $total_number,
                'success_num'      => $success_number,
                'failed_num'       => $error_number,
                'url'              => $check_file_url,
                'tasking_exist'    => false,
                'result_list'      => $result_list,
            ],
        ];
    }

}
