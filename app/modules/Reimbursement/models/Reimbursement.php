<?php

namespace App\Modules\Reimbursement\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PaperDocumentModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\PaperDocument\Services\ConfirmStaffService;
use App\Modules\Reimbursement\Services\AddService;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;

class Reimbursement extends Base implements BankFlowModelInterface, PayModelInterface, PaperDocumentModelInterface
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('reimbursement');

        $this->hasMany(
            'id',
            Detail::class,
            're_id', [
                'alias'  => 'Details',
                'params' => [
                    'order' => 'id ASC',
                ],
            ]
        );


        $this->hasMany(
            'id',
            ReimbursementRelLoan::class,
            're_id',
            [
                'alias'  => 'Loans',
                'params' => [
                    'conditions' => 'is_deleted = :is_deleted:',
                    'bind'       => ['is_deleted' => GlobalEnums::IS_NO_DELETED],
                ],
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailSupportRelModel::class,
            're_id',
            [
                'alias' => 'DetailSupportRel',
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailSupportRelModel::class,
            're_id',
            [
                'alias'  => 'DetailSupportRelSerialNo',
                'params' => [
                    'columns' => ['support_serial_no', 're_id', 'detail_id'],
                ],
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailTravelRoommateRelModel::class,
            're_id',
            [
                'alias'  => 'DetailTravelRoommateRel',
                'params' => [
                    'columns' => ['detail_id', 'serial_no', 'apply_staff_id', 'apply_staff_name', 'confirm_status', 'confirm_at'],
                ],
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailTravelRoommateRelModel::class,
            're_id',
            [
                'alias' => 'DetailTravelRoommateRelModels',
                'params' => [
                    'for_update' => true
                ],
            ]
        );

    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $no],
            ]
        );
    }

    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'no in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind       = [
            'nos'            => $no,
            'status'         => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status'     => Enums::LOAN_PAY_STATUS_PENDING,
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions         = 'no in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            $conditions .= ' and confirm_status = :confirm_status:';
            $bind['confirm_status'] = Enums\PaperDocumentEnums::CONFIRM_STATE_COMPLETE;
        }

        return self::find(
            [
                'conditions' => $conditions,
                'bind'       => $bind,
            ]
        );
    }


    public function getFormatData()
    {
        return [
            'oa_value'   => $this->id,
            'oa_type'    => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
            'no'         => $this->no,
            'amount'     => bcdiv($this->real_amount, 1000, 2),   //报销用实付金额
            'currency'   => $this->currency,
            'status'     => $this->status,
            'pay_status' => $this->pay_status,
        ];
    }

    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found reimbursement or reimbursement pay_status is error');
        }


        $item                     = [];
        $item['pay_status']       = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_bank_id']      = $data['bank_id'];
        $item['pay_bank_name']    = $data['bank_name'];
        $item['pay_bank_account'] = $data['bank_account'];
        $item['pay_at']           = $data['date'];
        $item['pay_operate_date'] = date('Y-m-d H:i:s');
        $item['payer_id']         = $data['create_id'];
        $item['updated_at']       = date("Y-m-d H:i:s");
        $item['remark']           = $data['ticket_no'];
        $item['pay_from']         = 2;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("报销-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $sql  = 'update reimbursement set 
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         pay_bank_id="' . $data['bank_id'] . '",
                         pay_bank_name="' . $data['bank_name'] . '",
                         pay_bank_account="' . $data['bank_account'] . '",
                         pay_at="' . $data['date'] . '",
                         pay_operate_date="' . date('Y-m-d H:i:s') . '",
                         payer_id="' . $data['create_id'] . '",
                         updated_at="' . date("Y-m-d H:i:s") . '",
                         remark="' . $data['ticket_no'] . '",
                         pay_from=2 where id in (' . implode(',', $ids) . ')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('报销付款-批量更新失败==' . $sql);
        }
        return true;
    }


    public function cancel($user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not reimbursement or reimbursement pay_status is error');
        }

        $item                     = [];
        $item['pay_status']       = Enums::LOAN_PAY_STATUS_PENDING;
        $item['pay_bank_id']      = '';
        $item['pay_bank_name']    = '';
        $item['pay_bank_account'] = '';
        $item['pay_at']           = null;
        $item['payer_id']         = null;
        $item['updated_at']       = date("Y-m-d H:i:s");
        $item['remark']           = '';
        $item['pay_from']         = 1;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("报销-撤销支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        $sql  = 'update reimbursement_detail set 
                        is_deduct = ' . intval($data['is_deduct']) . '        
                        where id in (' . implode(',', $ids) . ')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('报销-批量确认失败==' . $sql);
        }
        return true;
    }


    /**
     * 获得支付模块需要的数据
     *
     * @return array
     */
    public function getPayData()
    {
       $date_at = (new \DateTime())->modify('monday next week')->modify('+2 days')->format('Y-m-d');
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $date_at = date('Y-m-d');
        }
       $arr = [
            'oa_type'                      => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
            'no'                           => $this->no,
            'apply_staff_id'               => $this->apply_id,
            'apply_staff_name'             => $this->apply_name,
            'cost_department_id'           => $this->cost_department,
            'cost_department_name'         => $this->cost_department_name,
            'apply_date'                   => $this->apply_date,
            'pay_method'                   => Enums::PAYMENT_METHOD_BANK_TRANSFER,
            'currency'                     => $this->currency,
            'pays'                         => [
                [
                    'bank_name'         => $this->bank_type,              //收款人银行名称
                    'bank_account'      => $this->bank_account,           //收款人账号
                    'bank_account_name' => $this->bank_name,              //收款人户名
                ],
            ],
            'amount_total_no_tax'          => 0,                                              //不含税金额
            'amount_total_vat'             => 0,                                              //税额
            'amount_total_have_tax'        => 0,                                              //含税金额（含VAT含WHT）
            'amount_total_wht'             => 0,                                              //wht总计
            'amount_total_have_tax_no_wht' => 0,                                              //含税金额总计（含VAT不含WHT）
            'amount_loan'                  => bcdiv($this->loan_amount, 1000, 2),             //冲减借款金额,
            'amount_reserve'               => 0,
            'amount_discount'              => 0,                                                      //折扣
            'amount_total_actually'        => bcdiv($this->real_amount, 1000, 2),                     //实付金额
            'amount_remark'                => '',                                                      //备注，报销无
            'default_planned_pay_date'     => $date_at,//应付日期
            'planned_pay_date'             => $date_at,//计划支付日期
        ];
        ////费用公司id
        //$arr['cost_company_id'] = SysDepartmentModel::getCompanyIdByDepartmentId($arr['cost_department_id']);
        $arr['cost_company_id'] = $this->cost_company_id ?? '';
        //费用公司名字
        $arr['cost_company_name'] = '';
        if (!empty($arr['cost_company_id'])) {
            $arr['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($arr['cost_company_id']);
        }


        $details = $this->getDetails();
        foreach ($details as $detail) {
            $arr['amount_total_no_tax'] = bcadd($arr['amount_total_no_tax'], bcdiv($detail->tax_not, 1000, 2),
                2);              //不含税金额总计 = 所有报销实质的发票金额（不含VAT含WHT）合计
            $arr['amount_total_vat']    = bcadd($arr['amount_total_vat'], bcdiv($detail->tax, 1000, 2),
                2);              //VAT总计 = 所有报销实质的VAT税额合计
            $arr['amount_total_wht']    = bcadd($arr['amount_total_wht'], bcdiv($detail->wht_tax_amount, 1000, 2),
                2);              //WHT总计 = 所有报销实质的WHT税额合计

            //$arr['amount_total_have_tax'] = bcadd($arr['amount_total_have_tax'],bcdiv($detail->amount,1000,2),2); //含税金额总计（含VAT含WHT）
        }
        // v11955 含税金额总计（含VAT含WHT）= 不含税金额总计+VAT总计
        $arr['amount_total_have_tax'] = bcadd($arr['amount_total_no_tax'], $arr['amount_total_vat'],
            2);                                                               //含税金额总计（含VAT含WHT）= 不含税金额总计+VAT总计
        // v11955 含税金额总计（含VAT含WHT）= 不含税金额总计+VAT总计
        //$arr['amount_total_have_tax_no_wht'] = $arr['amount_total_have_tax'];       //含税金额总计(含VAT不含WHT) = 含税金额总计（含VAT含WHT)
        $arr['amount_total_have_tax_no_wht'] = bcsub(bcadd($arr['amount_total_no_tax'], $arr['amount_total_vat'], 2),
            $arr['amount_total_wht'], 2);                                     //含税金额总计(含VAT不含WHT) = 不含税金额总计+VAT总计-WHT总计
        $arr['pays'][0]['amount']            = $arr['amount_total_actually']; //支付金额
        // v11955 冲减备用金金额: 如果报销内关联过备用金字段，则显示报销的实付金额总计
        $rfrei = ReserveFundReimburse::findFirst('rei_id=' . $this->id);
        if ($rfrei) {
            $arr['amount_reserve'] = $arr['amount_total_actually'];
        }

        return $arr;
    }


    /**
     * 获取回调的参数
     *
     * @param $data
     * @return array
     */
    public function getPayCallBackData($data)
    {
        //user里面有id就行
        //pay($id, $data, $user,$is_from=1)
        $new                     = [];
        $new['pay_at']           = $data['pay_bank_flow_date'] ?? date("Y-m-d H:i:s");
        $new['updated_at']       = date("Y-m-d H:i:s");
        $new['pay_status']       = $data['pay_status'];
        $new['remark']           = $data['pay_remark'] ?? "";
        $new['pay_bank_id']      = $data['pay_bank_id'] ?? 1;
        $new['pay_bank_name']    = $data['pay_bank_name'] ?? 'Thai Military Bank';
        $new['pay_bank_account'] = $data['pay_bank_account'] ?? '**********';

        if ($data['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY) {
            $new['remark'] = $data['not_pay_reason'];
            $new['pay_at'] = null;
        }


        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     *
     * @param $no
     * @param $pay_id
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no, $pay_id)
    {
        $myself = self::findFirst(
            [
                'conditions' => 'no = :no: and status = :status: and pay_status = :pay_status:',
                'bind'       => [
                    'no'         => $no,
                    'status'     => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PENDING,
                ],
            ]
        );
        if (!isset($myself->apply_id)) {
            return [];
        }
        $apply_info                     = AddService::getInstance()->getUserMetaFromBi($myself->apply_id, 1);
        $bank_info                      = [];
        $bank_info['bank_account']      = $apply_info['apply_user']['bank_account'] ?? '';
        $bank_info['bank_account_name'] = $apply_info['apply_user']['bank_name'] ?? '';
        $bank_info['bank_name']         = $apply_info['apply_user']['bank_type'] ?? '';
        return [
            'type'  => 1,
            'items' => $bank_info,
        ];
    }

    //打上支付模块标记
    public function updatePayTag(): bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module' => 1]) === false) {
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'no = :no: AND status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind'       => [
                'no'            => $data['payment_no'],
                'status'        => Enums::WF_STATE_APPROVED,
                'pay_status'    => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 本模块的单据, 不可变更收款人信息
        if (empty($main_model)) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($main_model->toArray(),
                JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info  = $data['pay'][0] ?? [];
        $sync_data = [
            'bank_name'              => $pay_info['bank_account_name'],
            'bank_type'              => $pay_info['bank_name'],
            'bank_account'           => $pay_info['bank_account'],
            'updated_at'             => date('Y-m-d H:i:s'),
            'last_update_id'         => $user['id'],
            'last_update_name'       => $user['name'],
            'last_update_department' => $user['department'],
            'last_update_job_title'  => $user['job_title'],
            'last_update_at'         => date('Y-m-d H:i:s'),
        ];

        if ($main_model->i_update($sync_data) === false) {
            throw new BusinessException('报销申请单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($main_model),
                ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($main_model->toArray(),
                JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 获取纸质单据确认需要的数据
     * @return array
     */
    public function getPaperDocumentData(): array
    {
        $arr = [
            'module_id'               => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
            'serial_no'               => $this->no,
            'business_id'             => $this->id,
            'created_id'              => $this->created_id,
            'created_name'            => $this->created_name,
            'created_department_name' => $this->created_department_name,
            'created_department_id'   => $this->created_department_id,
            'created_company_name'    => $this->created_company_name,
            'apply_id'                => $this->apply_id,
            'apply_name'              => $this->apply_name,
            'apply_department_id'     => $this->apply_department_id,
            'apply_department_name'   => $this->apply_department_name,
            'apply_company_name'      => $this->apply_company_name,
            'apply_center_code'       => $this->apply_center_code,
            'apply_store_id'          => $this->apply_store_id,
            'apply_store_name'        => $this->apply_store_name,
            'cost_company_id'         => $this->cost_company_id,
            'apply_mobile'            => $this->apply_mobile,
            'apply_date'              => $this->apply_date,
            'cost_department'         => $this->cost_department,
            'cost_department_name'    => $this->cost_department_name,
            'cost_store_type'         => $this->cost_store_type,
            'cost_store_name'         => $this->cost_store_name,
            'currency'                => $this->currency,
            'payable_amount_all'      => $this->payable_amount_all,
            'country_code'            => $this->country_code,
        ];

        //MY,如果存在纸质单据，则待提交，不存在则为已补全
        //其他国家，待确认
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $details        = $this->getDetails();
            $ticketTypeList = array_column($details->toArray(), 'ticket_type');
            if (in_array(GlobalEnums::REIMBURSEMENT_TICKET_TYPE_PAPER, $ticketTypeList)) {
                $arr['confirm_status'] = Enums\PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT;
            } else {
                $arr['confirm_status']         = Enums\PaperDocumentEnums::CONFIRM_STATE_COMPLETE;
            }
        } else {
            $arr['confirm_status'] = Enums\PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM;
        }

        return $arr;
    }
}
