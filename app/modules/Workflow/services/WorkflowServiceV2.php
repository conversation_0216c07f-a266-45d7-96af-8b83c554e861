<?php

namespace App\Modules\Workflow\Services;

use App\Library\AccessDataEnums;
use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Enums\OAWorkflowEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use app\models\backyard\ToolStaffInfoModel;
use App\Models\oa\StaffManageListRelateModel;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WorkflowCarbonCopyModel;
use App\Models\oa\WorkflowRequestNodeFyrMiddleModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Hc\Services\SysService;
use App\Modules\Loan\Services\LoanFlowService;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Models\bi\HrStaffManageRegionsModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\User\Models\HrStaffItemsModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Models\StoreModel;
use App\Modules\User\Services\UserService;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\Workflow\Models\WorkflowSubNodeModel;
use App\Modules\Workflow\Models\WorkflowNodeRelateModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\User\Services\StaffService;
use App\Repository\oa\SysAttachmentRepository;
use App\Repository\oa\WorkflowNodeRepository;
use App\Repository\StoreRepository;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Model\ResultsetInterface;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class WorkflowServiceV2 extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 创建审批申请
     *
     * @param $data
     * @param $user
     * @param $info
     * @param int $continue_node_id 起始审批流节点,如果是0则从开始节点发起
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($data, $user, $info, $continue_node_id = 0)
    {
        //创建审批申请
        $store_id = 0;
        if (isset($info['store_id'])) {
            $store_id = $info['store_id'];
        }
        //审批流ID
        $flowId = $data['flow_id'] ?? $this->getFlowIdByBizType($data['biz_type'], $store_id, $info);
        //从特定节点开始审批
        $current_flow_node_id = 0;
        if (!empty($continue_node_id)) {
            //查询这个节点是否存在
            $node_info = WorkflowNodeRepository::getInstance()->getNodeById($continue_node_id);
            if (!empty($node_info) && $node_info['type'] == Enums::WF_NODE_ACTION) {
                $current_flow_node_id = $continue_node_id;
            }
        }
        if (empty($current_flow_node_id)) {
            $current_flow_node_id = $this->getWorkflowFirstNode($flowId, $user, $info);
        }

        $request = new WorkflowRequestModel();
        $request->name = $data['name'];
        $request->biz_type = $data['biz_type'];
        $request->biz_value = $data['id'];
        $request->flow_id = $flowId;
        $request->current_flow_node_id = $current_flow_node_id;
        $request->state = Enums::WF_STATE_PENDING;
        $request->created_at = date('Y-m-d H:i:s');
        $request->create_staff_id = $user['id'];
        $request->viewer_ids = $user['id'];
        $request->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_NO;
        $request->save();

        //启动工作流
        $this->process($request, $user, Enums::WF_ACTION_APPLY, $info, null, true);

        return $request->id;
    }


    /**
     * 审批通过操作
     *
     * @param $request
     * @param $user
     * @param $info
     * @param null $note
     * @return bool|string
     * @throws BusinessException
     * @throws ValidationException
     */
    public function doApprove($request, $user, $info, $note = null)
    {
        return $this->process($request, $user, Enums::WF_ACTION_APPROVE, $info, $note);
    }

    /**
     * 审批驳回操作
     *
     * @param $request
     * @param $user
     * @param $info
     * @param $note
     * @return bool|string
     * @throws BusinessException
     * @throws ValidationException
     */
    public function doReject($request, $user, $info, $note)
    {
        return $this->process($request, $user, Enums::WF_ACTION_REJECT, $info, $note);
    }

    /**
     * 审批撤销操作
     *
     * @param $request
     * @param $user
     * @param $info
     * @param $note
     * @return bool|string
     * @throws BusinessException
     * @throws ValidationException
     */
    public function doCancel($request, $user, $info, $note)
    {
        return $this->process($request, $user, Enums::WF_ACTION_CANCEL, $info, $note);
    }

    /**
     * 工作流处理，这可以优化，拒绝，撤销都不用找下一个审批节点，直接完成就行。就通过用找
     *
     * @param WorkflowRequestModel $request
     * @param $user
     * @param $action
     * @param $info
     * @param $note
     * @param bool $continue 发起审批时是否从指定节点开始
     * @return bool|WorkflowRequestModel
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function process($request, $user, $action, $info, $note = null, $continue = false)
    {
        $flowId = $request->flow_id;
        $nodeAuditors = empty($request->current_node_auditor_id) ? [] : explode(',', $request->current_node_auditor_id);
        $msgStaffIds = $nodeAuditors;

        //避免一个节点的两个用户，同事打开，审批，一个拒绝，一个同意
        if ($request->state != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('workflow_state_is_not_pending'), ErrCode::$VALIDATE_ERROR);
        }

        //如果是审批人，要判断审批的权限，报同样的错误，都是该节点已经有人审批了。
        if (!empty($nodeAuditors)) {
            //20250730邮件需求，针对报销、普通付款的单据创建审批、撤回审批操作才走此流判断，审批走else逻辑
            if (($request->biz_type == Enums::WF_REIMBURSEMENT_TYPE || $request->biz_type == Enums::ORDINARY_PAYMENT_BIZ_TYPE) && ($action == Enums::WF_ACTION_APPLY || $action == Enums::WF_ACTION_CANCEL)) {
                //报销申请的创建人，与request的create_staff_id有可能不同，所以加了info里面的create_staff_id
                if (!in_array($user['id'], $nodeAuditors) && $user['id'] != $request->create_staff_id && $user['id'] != $info['create_staff_id']) {
                    $this->logger->info("审批异常[节点已有人审批,可忽略]：[biz_type={$request->biz_type}, flow_id={$request->flow_id}, request_id={$request->id}, 审批流创建人={$request->create_staff_id}, 当前节点={$request->current_flow_node_id}, 当前节点审批人={$request->current_node_auditor_id}, 历史节点审批人={$request->viewer_ids}, 登录用户={$user['id']} / {$user['name']}, action={$action}");

                    throw new ValidationException(static::$t->_('workflow_state_is_not_pending'), ErrCode::$VALIDATE_ERROR);
                }
            } else if (in_array($request->biz_type, [Enums::WF_STORE_RENTING_CONTRACT_TYPE, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE])) {

                if (!in_array($user['id'], $nodeAuditors) && $user['id'] != $info['submitter_id']) {
                    $this->logger->info("审批异常[节点已有人审批,可忽略]：[biz_type={$request->biz_type}, flow_id={$request->flow_id}, request_id={$request->id}, 审批流创建人={$request->create_staff_id}, 当前节点={$request->current_flow_node_id}, 当前节点审批人={$request->current_node_auditor_id}, 历史节点审批人={$request->viewer_ids}, 登录用户={$user['id']} / {$user['name']}, action={$action}");

                    throw new ValidationException(static::$t->_("workflow_state_is_not_pending"));
                }

            } else {
                if (!in_array($user['id'], $nodeAuditors) && $user['id'] != $request->create_staff_id) {
                    $this->logger->info("审批异常[节点已有人审批,可忽略]：[biz_type={$request->biz_type}, flow_id={$request->flow_id}, request_id={$request->id}, 审批流创建人={$request->create_staff_id}, 当前节点={$request->current_flow_node_id}, 当前节点审批人={$request->current_node_auditor_id}, 历史节点审批人={$request->viewer_ids}, 登录用户={$user['id']} / {$user['name']}, action={$action}");

                    throw new ValidationException(static::$t->_('workflow_state_is_not_pending'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        // 获取节点条件: 所有节点
        $flowNodes = $this->getFlowNodes($flowId);

        // 当前节点
        $currentNode = $this->getCurrentNode($flowNodes, $request->current_flow_node_id);

        // 获得节点对应的所有审批人[node_id=>auditor_ids]
        $flowAllNodesAuditors = $this->getAllFlowNodeAuditors($flowNodes, $info, $request);

        // 获取当前待审批子节点
        $currentSubNode = $this->getCurrentSubNode($request, $currentNode, $flowAllNodesAuditors, $user);

        // 记录当前节点审核日志
        $log_model = $this->saveAuditLog($request, $currentSubNode ? $currentSubNode->id : 0, $user, $action, $note);
        // 记录当前节点审批日志附件信息
        $this->saveAuditLogAttachment($log_model, $info['attachment'] ?? []);

        // 获取下一个节点的ID
        // 当前节点会签类型 与 或签类型 不同的处理方式

        // 审批人会签节点
        // 验证该节点所有审批人审批状态, 均通过, 则继续查找下一节点; 有2+未审批, 则置当前节点为一下节点
        $pending_auditor_count = 0;
        if ($currentNode->type == Enums::WF_NODE_ACTION && $currentNode->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN) {
            // 会签节点待审批人的数量(已固化的)
            $pending_auditor_count = $this->getCountersignNodePendingAuditorCount($request);
        }

        // 子节点会签节点
        // 验证该节点子节点的审批状态, 均通过, 则继续推进下一节点; 有2+子节点未审批, 则下一节点仍为当前节点
        $pending_sub_node_count = 0;
        if ($currentNode->type == Enums::WF_NODE_ACTION && $currentNode->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            // 会签节点待审批子节点的数量(已固化的)
            $pending_sub_node_list = $this->getRequestNodeAuditorListByStatus($request->id, $currentNode->id, Enums::WF_STATE_PENDING);
            $pending_sub_node_count = count(array_unique(array_column($pending_sub_node_list, 'sub_node_id')));
        }

        // [1] 当前节点为审批人会签 且 有待审批人, 继续推进的节点仍为当前节点
        if ($pending_auditor_count >= 2) {
            $nextNodeId = $currentNode->id;
            $nextNode = $currentNode;

            // 会签同意: 单独处理, 因为仍有 1+ 个待审批人的前提下, 会签同意操作不需要继续找下一节点的审批人
            $action = $action == Enums::WF_ACTION_APPROVE ? Enums::WF_ACTION_COUNTERSIGN_APPROVE : $action;
        } else if ($pending_sub_node_count >= 2) {
            // [2] 当前节点为子节点会签 且 有待审批的子节点, 继续推进的节点仍为当前节点
            $nextNodeId = $currentNode->id;
            $nextNode = $currentNode;

            $action = $action == Enums::WF_ACTION_APPROVE ? Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE : $action;
        } else if ($action == Enums::WF_ACTION_APPLY && $continue === true && $currentNode->type == Enums::WF_NODE_ACTION) {
            // [3] 发起审批时,要从指定节点开始, 继续推进的节点仍为当前节点
            $nextNodeId = $currentNode->id;
            $nextNode = $currentNode;
        } else {
            // [4] 或签节点 或 会签节点已审批完毕: 继续推进的节点为下一节点
            $nextNodeId = $this->getNextNode($flowId, $request->current_flow_node_id, $info);
            $nextNode = $this->getCurrentNode($flowNodes, $nextNodeId);
        }
        // 更新当前节点/子节点/审批人-审批状态
        $this->updateNodeAndAuditorStatus($request, $currentSubNode ? $currentSubNode->id : 0, $user, $action);
        if ($nextNode->type != Enums::WF_NODE_CC) {
            $request->current_flow_node_id = $nextNode->id;
            $request->updated_at = date('Y-m-d H:i:s');
        }
        // 审批节点是最终节点并且是同意驳回操作
        if ($nextNode->type == Enums::WF_NODE_FINAL && in_array($action, [Enums::WF_ACTION_APPROVE, Enums::WF_ACTION_REJECT])) {
            $request->state = $action == Enums::WF_ACTION_APPROVE ? Enums::WF_STATE_APPROVED : Enums::WF_STATE_REJECTED;

            // 如果是通过
            if ($action == Enums::WF_ACTION_APPROVE) {
                //19706 当前审批节点对单据审批通过，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_APPROVED);

                $request->current_node_auditor_id = '';
                $request->approved_at = date('Y-m-d H:i:s');
                WorkflowEventService::getInstance()->handleEvent(WorkflowEventService::$pass, ['request' => $request]);
            } else {
                //19706 当前审批节点对单据审批驳回，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_REJECTED);

                $request->current_node_auditor_id = '';
                $request->rejected_at =  date('Y-m-d H:i:s');
                WorkflowEventService::getInstance()->handleEvent(WorkflowEventService::$reject, ['request' => $request]);
            }

        } else {
            switch ($action) {
                case Enums::WF_ACTION_APPLY:
                case Enums::WF_ACTION_APPROVE:
                case Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE:
                    // 注意区分: 自动跳过 与 自动通过
                    // 提取下一节点审批人
                    $nodeAuditors = $flowAllNodesAuditors[$nextNodeId] ?? [];
                    // 初始化节点-审批人的审批状态
                    $this->initNodeAndAuditorStatus($request, $nodeAuditors, $nextNode);

                    // 提取下一节点待审批人: 含自动同意的逻辑处理
                    $nodeAuditors = $this->getNodeAuditorsByAuto($nodeAuditors, $request, $nextNode, $info);
                    // 抄送相关人
                    $this->setNodeAuditorCc($request, $nodeAuditors, $nextNode);
                    // 抄送节点特殊处理
                    if ($nextNode->type == Enums::WF_NODE_CC) {
                        if (!empty($nodeAuditors['auditor_ids'])) {
                            //把抄送人追加进viewer_ids
                            $request->viewer_ids = $request->viewer_ids . ',' . implode(',', $nodeAuditors['auditor_ids']);
                        }
                    }
                    // 待审批人如果为空，则找下一个节点
                    $is_final = false;
                    // 如果当前是抄送节点, 也继续找下一个节点
                    while (empty($nodeAuditors['auditor_ids']) || $nextNode->type == Enums::WF_NODE_CC) {
                        $nextNodeId = $this->getNextNode($flowId, $nextNodeId, $info);
                        $nextNode = $this->getCurrentNode($flowNodes, $nextNodeId);
                        //非抄送节点保持原逻辑, 抄送节点不设置request
                        if ($nextNode->type != Enums::WF_NODE_CC) {
                            $request->current_flow_node_id = $nextNode->id;
                            $request->updated_at = date('Y-m-d H:i:s');
                        }
                        $nodeAuditors = $flowAllNodesAuditors[$nextNodeId] ?? [];

                        // 初始化节点-审批人的审批状态
                        $this->initNodeAndAuditorStatus($request, $nodeAuditors, $nextNode);

                        // 提取下一节点待审批人: 含自动同意的逻辑处理
                        $nodeAuditors = $this->getNodeAuditorsByAuto($nodeAuditors, $request, $nextNode, $info);
                        // 抄送相关人
                        $this->setNodeAuditorCc($request, $nodeAuditors, $nextNode);
                        // 抄送节点特殊处理
                        if ($nextNode->type == Enums::WF_NODE_CC) {
                            if (!empty($nodeAuditors['auditor_ids'])) {
                                //把抄送人追加进viewer_ids
                                $request->viewer_ids = $request->viewer_ids . ',' . implode(',', $nodeAuditors['auditor_ids']);
                            }
                            // 审批人值为空, 让循环继续找下个节点
                            $nodeAuditors['auditor_ids'] = [];
                        }
                        // 如果下一个节点为空，且是结束节点
                        if (empty($nodeAuditors['auditor_ids']) && $nextNode->type == Enums::WF_NODE_FINAL) {
                            $is_final = true;
                            break;
                        }
                    }
                    //如果是最后节点，while循环找到的
                    if ($is_final) {
                        //19706 当前审批节点对单据审批通过，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                        WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_APPROVED);

                        $request->state = Enums::WF_STATE_APPROVED;
                        $request->current_node_auditor_id = '';
                        $request->approved_at = date('Y-m-d H:i:s');
                        WorkflowEventService::getInstance()->handleEvent(WorkflowEventService::$pass, ['request' => $request]);
                    } else if ($currentNode->id == $request->current_flow_node_id) {
                        //19706 当前审批节点对单据审批通过，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                        //当某个子节点中工号审批通过，给该工号所处的子节点上所有审批人发push，不给其他子节点发
                        if ($currentNode->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN && $action == Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE) {
                            $request->current_node_auditor_id = implode(',', $flowAllNodesAuditors[$request->current_flow_node_id]['sub_nodes'][$currentSubNode->id]['auditor_ids']);
                            WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_APPROVED);
                        }
                        // 还是停留在当前一级节点: 无需发邮件, 无需追加 viewer_ids
                        $request->current_node_auditor_id = implode(",", $nodeAuditors['auditor_ids']);
                    } else {
                        $now_current_id = $request->current_flow_node_id;
                        if ($action == Enums::WF_ACTION_APPROVE) {
                            //19706 当前审批节点对单据审批通过，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                            $request->current_flow_node_id = $currentNode->id;
                            WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_APPROVED);
                        }

                        // 推进到: 下个一级节点
                        $request->current_flow_node_id = $now_current_id;
                        $request->current_node_auditor_id = implode(",", $nodeAuditors['auditor_ids']);
                        $request->viewer_ids = $request->viewer_ids . ',' . implode(",", $nodeAuditors['auditor_ids']);

                        // 合并本次节点审批人
                        $msgStaffIds = array_merge($msgStaffIds, $nodeAuditors['auditor_ids']);

                        // 给待审批人发送邮件
                        $this->sendEmailToAuditors($request, $nodeAuditors['auditor_ids']);

                        //19706 当单据流转到当前审批节点，给当前审批节点的所有工号发送push
                        WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_PENDING);
                    }

                    break;
                case Enums::WF_ACTION_COUNTERSIGN_APPROVE:
                    $current_node_auditor_id = $request->current_node_auditor_id;

                    //19706 当前审批节点对单据审批通过，工号会签：给当前操作的审批工号发push，不给当前节点其他工号发
                    $request->current_node_auditor_id = $user['id'];
                    WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_APPROVED);

                    // 审批人会签同意
                    // 重置request待审批人: 剔除已审批的工号
                    $request->current_node_auditor_id = implode(',', array_diff(explode(',', $current_node_auditor_id), [$user['id']]));
                    break;
                case Enums::WF_ACTION_REJECT:
                    //19706 当前审批节点对单据审批驳回，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                    WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_REJECTED);

                    $request->state = Enums::WF_STATE_REJECTED;
                    $request->current_node_auditor_id = '';
                    $request->rejected_at = date('Y-m-d H:i:s');
                    $request->rejeact_info = $note ?? '';
                    WorkflowEventService::getInstance()->handleEvent(WorkflowEventService::$reject, ['request' => $request]);
                    break;
                case Enums::WF_ACTION_CANCEL:
                    //19706 当申请人对单据操作撤回，给当前审批节点的所有工号发送push（或签情况下会给当前节点所有工号发push）
                    WorkflowEventService::getInstance()->pushEvent($request, Enums::WF_STATE_CANCEL);

                    $request->state = Enums::WF_STATE_CANCEL;
                    $request->current_node_auditor_id = '';
                    $request->canceled_at = date('Y-m-d H:i:s');
                    break;
                default:
                    break;
            }
        }

        //删除上次节点，和本次节点待审核数量
        $this->delUnReadNumsKeyByStaffIds($msgStaffIds);

        $request->save();
        return $request;
    }

    /**
     * 获取当前应审批的子节点
     * @param $request
     * @param $current_node
     * @param $flow_all_nodes_auditors
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function getCurrentSubNode($request, $current_node, $flow_all_nodes_auditors, $user)
    {
        // 非子节点会签, 返回空子节点
        if ($current_node->type != Enums::WF_NODE_ACTION || $current_node->node_audit_type != Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            return false;
        }

        // 获取子节点列表
        $all_sub_node = $this->getFlowSubNodes($current_node->flow_id,  $current_node->id);

        // 获取当前节点的子节点审批列表
        $request_node_list = $this->getSubNodeAuditList($request, $current_node);
        $_request_node_list = [];
        foreach ($request_node_list as $k => $v) {
            $_request_node_list[$v['sub_node_id'].'_'.$v['auditor_id']] = $v['audit_status'];
        }
        $request_node_list = $_request_node_list;

        // 找到待审批的子节点
        foreach ($all_sub_node as $sub_node) {
            // 返回当前用户所在的待审批子节点(已固化的)
            if (!empty($request_node_list)) {
                $request_node_user_audit_status = $request_node_list[$sub_node->id.'_'.$user['id']] ?? 0;
                if ($request_node_user_audit_status == Enums::WF_STATE_PENDING) {
                    return $sub_node;
                }

                continue;
            }

            // 审批记录里无节点信息, 返回第一个有审批人的子节点(未固化,从子节点配置中读取)
            $curr_sub_node_auditors = $flow_all_nodes_auditors[$sub_node->parent_node_id]['sub_nodes'][$sub_node->id]['auditor_ids'] ?? [];
            if (in_array($user['id'], $curr_sub_node_auditors)) {
                return $sub_node;
            }
        }

        return false;
    }

    /**
     * 获取子节点审批状态列表
     * @param $request
     * @param $current_node
     * @return mixed
     */
    public function getSubNodeAuditList($request, $current_node)
    {
        return WorkflowRequestNodeAuditorModel::find([
            'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id:',
            'bind' => ['request_id' => $request->id, 'flow_node_id' => $current_node->id],
            'order' => 'sub_node_id ASC'
        ])->toArray();
    }

    /**
     * @param $request
     * @return bool
     */
    public function canCancel($request)
    {
        $log = WorkflowAuditLogModel::find([
            'conditions' => 'request_id= :req_id: and audit_action>0',
            'bind' => [
                'req_id' => $request->id,
            ],
        ]);

        return $request->state == Enums::WF_STATE_PENDING && $log->count() <= 1;
    }

    /**
     * 业务单是否处在待审批阶段[工单已提交 但 第一个节点待审批]
     *
     * true: 刚提交待审批 [一般此阶段时, 业务侧需要处理某些特定逻辑];
     * false: 审批中 或 到终态
     *
     * @param $request
     * @return bool
     */
    public function isPendingApproval($request)
    {
        $log = WorkflowAuditLogModel::findFirst([
            'conditions' => 'request_id = :req_id: AND audit_action > 0',
            'bind' => [
                'req_id' => $request->id,
            ]
        ]);

        return $request->state == Enums::WF_STATE_PENDING && empty($log->id);
    }

    /**
     * 批量获取已开始审批的业务单据ID列表(即有节点已审批, 含已终态的)
     *
     * @param array $biz_ids 业务单据ID
     * @param array $biz_types 业务审批类型
     * @return array
     */
    public function getApprovedWorkflowBizValueList(array $biz_ids, array $biz_types)
    {
        if (empty($biz_ids) || empty($biz_types)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftJoin(WorkflowAuditLogModel::class, 'request.id = log.request_id', 'log');
        $builder->where('request.is_abandon = :is_abandon:', ['is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->inWhere('request.biz_type', $biz_types);
        $builder->inWhere('request.biz_value', $biz_ids);
        $builder->andWhere('log.audit_action >= :audit_action:', ['audit_action' => Enums::WF_ACTION_APPROVE]);
        $builder->columns('request.biz_value');
        $list = $builder->getQuery()->execute()->toArray();
        if (!empty($list)) {
            $list = array_unique(array_column($list, 'biz_value'));
        }
        return $list;
    }

    /**
     * 保存申请的审批日志
     * @param WorkflowRequestModel $request
     * @param int $current_sub_node_id
     * @param $user
     * @param $action
     * @param $note
     * @param bool $is_auto
     * @param array $node_auditors 节点审批人
     * @param null $node 当前节点对象
     * @return WorkflowAuditLogModel
     * @throws BusinessException
     */
    public function saveAuditLog($request, $current_sub_node_id = 0, $user, $action, $note = null, $is_auto = false, $node_auditors = [], $node = null)
    {

        // 同意的操作
        $approval_item = [
            Enums::WF_ACTION_APPROVE,
            Enums::WF_ACTION_COUNTERSIGN_APPROVE,
            Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE,
        ];

        // 如果是申请, 实时获取申请人信息
        if ($action == Enums::WF_ACTION_APPLY) {
            $auditor_info = $this->getUserList([$user['id']])[0] ?? [];

            //兼容工具号hcm没有员工数据
            if (empty($auditor_info)) {
                $auditor_info['staff_name']      = $user['name'] ?? '';
                $auditor_info['nick_name']       = '';
                $auditor_info['department_name'] = $user['department'] ?? '';
                $auditor_info['job_title_name']  = $user['job_title'] ?? '';
            }

            $user['name']       = $auditor_info['staff_name'] ?? '';
            $user['nick_name']  = $auditor_info['staff_name'] ?? '';
            $user['department'] = $auditor_info['department_name'] ?? '';
            $user['job_title']  = $auditor_info['job_title_name'] ?? '';
        }
        // 如果是抄送, 通过审批人获取信息
        if ($action == Enums::WF_ACTION_CARBON_COPY && !empty($node_auditors)) {
            $auditor_info = $this->getUserList($node_auditors);
            $auditor_info = array_column($auditor_info, null, 'staff_id');
            $log = new WorkflowAuditLogModel();
            foreach ($node_auditors as $staff_id) {
                $insert_data[] = [
                    'request_id' => $request->id,
                    'flow_id' => $request->flow_id,
                    'flow_node_id' => $node->id,
                    'sub_node_id' => !empty($current_sub_node_id) ? $current_sub_node_id : 0,
                    'staff_id' => $staff_id,
                    'staff_name' => $this->getNameAndNickName($auditor_info[$staff_id]['staff_name'], $auditor_info[$staff_id]['staff_nick_name']),
                    'staff_department' => $auditor_info[$staff_id]['department_name'],
                    'staff_job_title' => $auditor_info[$staff_id]['job_title_name'],
                    'audit_action' => $action,
                    'audit_info' => $note ?? '',
                    'audit_at' => date('Y-m-d H:i:s'),
                ];
            }
            if (!empty($insert_data) && $log->batch_insert($insert_data) === false) {
                throw new BusinessException('审批日志保存失败[抄送], log_data=' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            $log = new WorkflowAuditLogModel();
            $log->save(
                [
                    'request_id' => $request->id,
                    'flow_id' => $request->flow_id,
                    'flow_node_id' => $request->current_flow_node_id,
                    'sub_node_id' => !empty($current_sub_node_id) ? $current_sub_node_id : 0,
                    'staff_id' => $user['id'],
                    'staff_name' => $this->getNameAndNickName($user['name'], $user['nick_name']),
                    'staff_department' => $user['department'],
                    'staff_job_title' => $user['job_title'],
                    'audit_action' => $is_auto && in_array($action, $approval_item) ? Enums::WF_ACTION_AUTO_APPROVE : $action,
                    'audit_info' => $note ?? '',
                    'audit_at' => date('Y-m-d H:i:s'),
                ]
            );
        }

        return $log;
    }

    /**
     * 记录当前节点审批日志附件信息
     * @param object $log_model
     * @param array $attachment 附件组
     */
    public function saveAuditLogAttachment($log_model, $attachment)
    {
        //存在附件信息需要跟审批日志绑定到一起
        if (!empty($attachment)) {
            $attachments = [];
            foreach ($attachment as $item) {
                $tmp = [];
                $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_WORKFLOW_ATTACHMENT;
                $tmp['oss_bucket_key'] = $log_model->id;
                $tmp['bucket_name'] = $item['bucket_name'];
                $tmp['object_key'] = $item['object_key'];
                $tmp['file_name'] = $item['file_name'];
                $attachments[] = $tmp;
            }
            (new SysAttachmentModel())->batch_insert($attachments);
        }
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @param bool $is_only 是否只要这个request的
     * @return array
     */
    public function getAuditLogs($request, $is_only = false)
    {
        $data = [];

        // 因为驳回会创建多个request,查找的时候，需要全部找出来
        $ids = [];
        if ($is_only) {
            if (!empty($request->id)) {
                $ids[] = ['id' => $request->id, 'flow_id' => $request->flow_id];
            }
        } else if (is_object($request)) {
            $ids = WorkflowRequestModel::find([
                'columns' => 'id, flow_id',
                'conditions' => 'biz_type =:biz_type: and biz_value=:biz_value:',
                'bind' => [
                    'biz_type' => $request->biz_type,
                    'biz_value' => $request->biz_value,
                ]
            ])->toArray();
        }

        if (empty($ids)) {
            return $data;
        }

        $flow_ids = array_column($ids, 'flow_id');
        $ids      = array_column($ids, "id");

        $logs = WorkflowAuditLogModel::find([
            'conditions' => 'request_id in ({ids:array})',
            'bind'       => ['ids' => $ids],
            'order'      => 'audit_at desc',
        ])->toArray();

        if (!empty($logs)) {
            $fyrs = new FYRService();
            $comment_service = WorkflowCommentService::getInstance();

            // 待审批人
            $pending_auditor_ids = !empty($request->current_node_auditor_id) ? explode(',', $request->current_node_auditor_id) : [];

            // 实时获取日志审批人信息 getUserList
            $auditor_ids = array_values(array_unique(array_merge($pending_auditor_ids, array_column($logs, 'staff_id'))));
            $auditor_info_list = $this->getUserList($auditor_ids);
            $auditor_info_list = array_column($auditor_info_list, null, 'staff_id');

            // 获取审批日志下的附件信息
            $log_ids = array_values(array_column($logs, 'id'));
            $attachments = SysAttachmentRepository::getInstance()->getAttachmentsListById($log_ids, Enums::OSS_BUCKET_TYPE_WORKFLOW_ATTACHMENT);

            // 获取节点审批类型
            $node_list = WorkflowNodeModel::find([
                'conditions' => 'flow_id IN ({flow_ids:array})',
                'bind'       => ['flow_ids' => $flow_ids],
                'columns'    => ['id', 'node_audit_type']
            ])->toArray();
            $node_list = array_column($node_list, 'node_audit_type', 'id');

            // 归并子节点
            $_parent_node_id_k_map = [];
            // 抄送节点合并
            $cc_log = [];
            foreach ($logs as $k => $log) {
                $log['fyr_list']        = [];
                $log['node_audit_type'] = empty($node_list[$log['flow_node_id']]) ? Enums::WF_NODE_AUDIT_TYPE_OR_SIGN : Enums::WF_NODE_AUDIT_TYPE_COMMON_COUNTERSIGN;

                //按照工作流节点加工作流申请ID 为key唯一 如果反复出现撤回重新提交 flow_node_id做不到唯一
                $parent_node_key = $log['flow_node_id'] . '_' . $log['request_id'];
                $_parent_node_id_k_map[$parent_node_key] = $_parent_node_id_k_map[$parent_node_key] ?? $k;

                // 审批人所在节点标识
                $node_flag = static::getNodeFlagByAuditorAction($log['audit_action']);
                $node_flag_name = static::$t->_(Enums::$wf_node_flag_item[$node_flag]);

                $_log = [
                    'id'                => $log['id'],
                    'request_id'        => $log['request_id'],
                    'flow_node_id'      => $log['flow_node_id'],
                    'staff_id'          => $log['staff_id'],
                    'staff_name'        => $log['staff_name'],
                    'staff_department'  => $log['staff_department'],
                    'job_title'         => $log['staff_job_title'],
                    'action_name'       => self::$t->_(Enums::$actions[$log['audit_action']]),
                    'audit_at'          => $log['audit_at'],
                    'audit_at_datetime' => $log['audit_at'],
                    'action'            => (int)$log['audit_action'],
                    'node_flag'         => $node_flag,
                    'node_flag_name'    => $node_flag_name,
                    'info'              => $log['audit_info'],
                    'node_audit_type'   => (int)$log['node_audit_type'],
                    'sub_node_list'     => [],
                    'fyr_list'          => $log['audit_action'] == Enums::WF_ACTION_APPLY ? [] : $fyrs->getLogs($log['request_id'], $log['flow_id'], $log['flow_node_id']),
                    'comment_list'      => $log['audit_action'] == Enums::WF_ACTION_APPLY ? [] : $comment_service->getLogsByNodeId($log['request_id'], $log['flow_node_id'], $log['sub_node_id']),
                    'attachments'       => $attachments[$log['id']] ?? []//附件组
                ];

                // 子节点
                if ($log['sub_node_id']) {
                    unset($logs[$k]);

                    //通过flow_node_id和request_id做唯一 获取数据放入sub_node_list集合
                    if (isset($logs[$_parent_node_id_k_map[$parent_node_key]]['sub_node_list'])) {
                        $_log['node_audit_type'] = Enums::WF_NODE_AUDIT_TYPE_OR_SIGN;
                        unset($_log['sub_node_list']);
                        $logs[$_parent_node_id_k_map[$parent_node_key]]['sub_node_list'][] = $_log;
                    } else {
                        $logs[$_parent_node_id_k_map[$parent_node_key]] = $_log;
                        $_log['node_audit_type']  = Enums::WF_NODE_AUDIT_TYPE_OR_SIGN;
                        unset($_log['sub_node_list']);
                        $logs[$_parent_node_id_k_map[$parent_node_key]]['sub_node_list'][] = $_log;
                    }

                    continue;
                }

                // 抄送节点审批人合并
                if ($_log['action'] == Enums::WF_ACTION_CARBON_COPY) {
                    if (!isset($cc_log[$_parent_node_id_k_map[$parent_node_key]])) {
                        $cc_log[$_parent_node_id_k_map[$parent_node_key]] = $_log;
                        $cc_log[$_parent_node_id_k_map[$parent_node_key]]['list'] = [];
                    }
                    //设置logs中的list字段,并存放此节点的多个抄送人
                    $cc_log[$_parent_node_id_k_map[$parent_node_key]]['list'][] = [
                        'staff_id' => $_log['staff_id'],
                        'staff_name' => $_log['staff_name'],
                        'staff_department' => $_log['staff_department'],
                        'job_title' => $_log['job_title'],
                    ];
                    if ($_parent_node_id_k_map[$parent_node_key] != $k) {
                        unset($logs[$k]);
                    }
                    $logs[$_parent_node_id_k_map[$parent_node_key]] = $cc_log[$_parent_node_id_k_map[$parent_node_key]];
                    //不需要再给$logs[$k]赋值
                    continue;
                }

                $logs[$k] = $_log;
            }

            // 输出审批日志(不含申请记录)
            $logs = array_values($logs);
            $data = array_sort($logs, 'id', SORT_DESC);
            $logs = null;
            // 最后一条审批记录
            $last = current($data);

            $lastTime = strtotime($last['audit_at']);
            // 待审批节点单独处理: 待审批人的信息实时取
            if ($request->state == Enums::WF_STATE_PENDING) {
                // 待审批节点标识
                $pending_approval_node_flag = static::getNodeFlagByAuditorAction(Enums::WF_ACTION_NULL);
                $pending_approval_node_flag_name = static::$t->_(Enums::$wf_node_flag_item[$pending_approval_node_flag]);

                // 子节点会签
                if ($node_list[$request->current_flow_node_id] == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
                    // 当前一级节点所有待审批人
                    $all_pending_auditors = explode(',', $request->current_node_auditor_id);

                    // 一级节点的子节点/审批人固化列表
                    $sub_nodes_auditors = $this->getRequestNodeAuditorListByStatus($request->id, $request->current_flow_node_id, '');

                    $sub_nodes = [];
                    foreach ($sub_nodes_auditors as $sub_node) {
                        $sub_nodes[$sub_node['sub_node_id']][] = $sub_node['auditor_id'];
                    }

                    $sub_node_audit_log = WorkflowAuditLogModel::find([
                        'conditions' => 'request_id = :request_id: AND flow_id = :flow_id: AND flow_node_id = :flow_node_id:',
                        'bind'       => [
                            'request_id'   => $request->id,
                            'flow_id'      => $request->flow_id,
                            'flow_node_id' => $request->current_flow_node_id,
                        ],
                        'order'      => 'audit_at DESC',
                    ])->toArray();
                    $sub_node_audit_log = array_column($sub_node_audit_log, null, 'sub_node_id');

                    // 子节点所在节点，未全部审批完毕，获取意见征询
                    $fyr_list = $fyrs->getLogs($request->id, $request->flow_id, $request->current_flow_node_id);

                    // 归并子节点审批日志
                    $_sub_node_approval_log = [];
                    $_sub_node_pending_log  = [];
                    foreach ($sub_nodes as $sub_node_id => $sub_node_auditors) {
                        $_sub_node_audit_log = $sub_node_audit_log[$sub_node_id] ?? [];

                        // 已审批日志追加
                        if (!empty($_sub_node_audit_log)) {
                            // 审批人所在节点标识
                            $_node_flag = static::getNodeFlagByAuditorAction($_sub_node_audit_log['audit_action']);
                            $_node_flag_name = static::$t->_(Enums::$wf_node_flag_item[$_node_flag]);

                            $_sub_node_approval_log[] = [
                                'staff_id'          => $_sub_node_audit_log['staff_id'],
                                'flow_node_id'      => $_sub_node_audit_log['flow_node_id'],
                                'staff_name'        => $_sub_node_audit_log['staff_name'],
                                'staff_department'  => $_sub_node_audit_log['staff_department'],
                                'job_title'         => $_sub_node_audit_log['staff_job_title'],
                                'action_name'       => self::$t->_(Enums::$actions[$_sub_node_audit_log['audit_action']]),
                                'audit_at'          => $_sub_node_audit_log['audit_at'],
                                'audit_at_datetime' => $_sub_node_audit_log['audit_at'],
                                'action'            => (int)$_sub_node_audit_log['audit_action'],
                                'node_flag'         => $_node_flag,
                                'node_flag_name'    => $_node_flag_name,
                                'info'              => $_sub_node_audit_log['audit_info'],
                                'node_audit_type'   => Enums::WF_NODE_AUDIT_TYPE_OR_SIGN,
                                'fyr_list'          => $fyr_list,
                                'comment_list'      => $comment_service->getLogsByNodeId($request->id, $request->current_flow_node_id, $sub_node_id),
                            ];

                            continue;
                        }

                        // 子节点待审批人: 去除符合自动同意的人
                        $sub_node_auditors = array_values(array_intersect($sub_node_auditors, $all_pending_auditors));

                        // 待审批子节点: 只有一个审批人
                        if (count($sub_node_auditors) == 1) {
                            $current = $auditor_info_list[$sub_node_auditors[0]] ?? [];
                            if ($current) {
                                $node_flag = static::getNodeFlagByAuditorAction(Enums::WF_ACTION_NULL);
                                $node_flag_name = static::$t->_(Enums::$wf_node_flag_item[$node_flag]);

                                $current = [
                                    'staff_id'          => $current['staff_id'],
                                    'staff_name'        => $this->getNameAndNickName($current['staff_name'] ?? '', $current['staff_nick_name'] ?? ''),
                                    'flow_node_id'      => '',
                                    'staff_department'  => $current['department_name'] ?? '',
                                    'job_title'         => $current['job_title_name'] ?? '',
                                    'action_name'       => static::$t->_(Enums::$actions[Enums::WF_ACTION_NULL]),
                                    'audit_at_datetime' => $last['audit_at'],
                                    'audit_at'          => format_duration($lastTime, time()),
                                    'action'            => Enums::WF_ACTION_NULL,
                                    'node_flag'         => $pending_approval_node_flag,
                                    'node_flag_name'    => $pending_approval_node_flag_name,
                                    'info'              => '',
                                    'node_audit_type'   => Enums::WF_NODE_AUDIT_TYPE_OR_SIGN,
                                    'fyr_list'          => $fyr_list,
                                    'comment_list'      => [],
                                ];

                                array_unshift($_sub_node_pending_log, $current);
                            }
                        } else {
                            // 多个人审批人
                            $current = [];
                            foreach ($sub_node_auditors as $auditor_id) {
                                $tmp = $auditor_info_list[$auditor_id] ?? [];
                                if (empty($tmp)) {
                                    continue;
                                }

                                if (empty($current)) {
                                    $current = [
                                        'staff_id'          => $tmp['staff_id'],
                                        'staff_name'        => $this->getNameAndNickName($tmp['staff_name'] ?? '', $tmp['staff_nick_name'] ?? ''),
                                        'flow_node_id'      => '',
                                        'staff_department'  => $tmp['department_name'],
                                        'job_title'         => $tmp['job_title_name'] ?? '',
                                        'action_name'       => static::$t->_(Enums::$actions[Enums::WF_ACTION_NULL]),
                                        'audit_at_datetime' => $last['audit_at'],
                                        'audit_at'          => format_duration($lastTime, time()),
                                        'action'            => Enums::WF_ACTION_NULL,
                                        'node_flag'         => $pending_approval_node_flag,
                                        'node_flag_name'    => $pending_approval_node_flag_name,
                                        'info'              => '',
                                        'node_audit_type'   => Enums::WF_NODE_AUDIT_TYPE_OR_SIGN,
                                        'fyr_list'          => $fyr_list,
                                        'comment_list'      => [],
                                    ];

                                    $current['list'] = [];
                                }

                                $t                     = [];
                                $t['staff_id']         = $tmp['staff_id'];
                                $t['staff_name']       = $this->getNameAndNickName($tmp['staff_name'] ?? '', $tmp['staff_nick_name'] ?? '');
                                $t['staff_department'] = $tmp['department_name'];
                                $t['job_title']        = $tmp['job_title_name'];
                                $current['list'][]     = $t;
                            }

                            if (!empty($current)) {
                                array_unshift($_sub_node_pending_log, $current);
                            }
                        }
                    }

                    $sub_node_list_log                   = array_merge($_sub_node_pending_log, $_sub_node_approval_log);
                    $current_node_log                    = current($sub_node_list_log);
                    $current_node_log['node_audit_type'] = Enums::WF_NODE_AUDIT_TYPE_COMMON_COUNTERSIGN;
                    $current_node_log['sub_node_list']   = $sub_node_list_log;
                    $node_list_log_arr                   = [];

                    //获取最后一个节点里面的数据 把staff_id和 flow_node_id组合
                    foreach ($sub_node_list_log as $item_node_list_log) {
                        if (!empty($item_node_list_log['staff_id']) && !empty($item_node_list_log['flow_node_id'])) {
                            $node_list_log_arr[] = $item_node_list_log['staff_id'] .'_'. $item_node_list_log['flow_node_id'];
                        }
                    }
                    $data_sub_node_list = [];
                    if (!empty($node_list_log_arr)) {
                        foreach ($data as $key => $log_data) {
                            if (!empty($log_data['sub_node_list'])) {
                                foreach ($log_data['sub_node_list'] as $sub_node_list) {
                                    //去除最近一次节点里面如果有三个审批人会签，两个同意一个待审核的数据
                                    if ($sub_node_list['request_id'] == $last['request_id'] && $sub_node_list['flow_node_id'] == $last['flow_node_id'] && in_array($sub_node_list['staff_id'] .'_'. $sub_node_list['flow_node_id'], $node_list_log_arr)) {
                                        unset($data[$key]);
                                    }
                                }
                            }

                            $data_sub_node_list[] = $data[$key] ?? [];
                        }

                        $data = array_values(array_filter($data_sub_node_list));
                    }

                    array_unshift($data, $current_node_log);

                } else {
                    // 或签 或 审批人会签
                    $auditor_ids = array_unique(array_filter(explode(',', $request->current_node_auditor_id)));

                    // 获取一级节点的审批类型
                    if ($node_list[$request->current_flow_node_id] == Enums::WF_NODE_AUDIT_TYPE_OR_SIGN) {
                        $node_audit_type = Enums::WF_NODE_AUDIT_TYPE_OR_SIGN;
                    } else {
                        $node_audit_type = Enums::WF_NODE_AUDIT_TYPE_COMMON_COUNTERSIGN;
                    }

                    if (!empty($auditor_ids)) {
                        // 如果只有一个审批人
                        if (count($auditor_ids) == 1) {
                            $current = $auditor_info_list[$auditor_ids[0]] ?? [];

                            if ($current) {
                                $current = [
                                    'staff_id'          => $current['staff_id'],
                                    'staff_name'        => $this->getNameAndNickName($current['staff_name'] ?? '', $current['staff_nick_name'] ?? ''),
                                    'staff_department'  => $current['department_name'] ?? '',
                                    'job_title'         => $current['job_title_name'] ?? '',
                                    'action_name'       => static::$t->_(Enums::$actions[Enums::WF_ACTION_NULL]),
                                    'audit_at_datetime' => $last['audit_at'],
                                    'audit_at'          => format_duration($lastTime, time()),
                                    'action'            => Enums::WF_ACTION_NULL,
                                    'node_flag'         => $pending_approval_node_flag,
                                    'node_flag_name'    => $pending_approval_node_flag_name,
                                    'info'              => '',
                                    'node_audit_type'   => $node_audit_type,
                                    'sub_node_list'     => [],
                                    'fyr_list'          => $fyrs->getLogs($request->id, $request->flow_id, $request->current_flow_node_id),
                                    'comment_list'      => $comment_service->getLogsByNodeId($request->id, $request->current_flow_node_id),
                                ];

                                array_unshift($data, $current);
                            }
                        } else {
                            // 多个人审批人
                            $current = [];

                            foreach ($auditor_ids as $auditor_id) {
                                $tmp = $auditor_info_list[$auditor_id] ?? [];
                                if (empty($tmp)) {
                                    continue;
                                }

                                if (empty($current)) {
                                    $current = [
                                        'staff_id'          => $tmp['staff_id'],
                                        'staff_name'        => $this->getNameAndNickName($tmp['staff_name'] ?? '', $tmp['staff_nick_name'] ?? ''),
                                        'staff_department'  => $tmp['department_name'] ?? '',
                                        'job_title'         => $tmp['job_title_name'] ?? '',
                                        'action_name'       => static::$t->_(Enums::$actions[Enums::WF_ACTION_NULL]),
                                        'audit_at_datetime' => $last['audit_at'],
                                        'audit_at'          => format_duration($lastTime, time()),
                                        'action'            => Enums::WF_ACTION_NULL,
                                        'node_flag'         => $pending_approval_node_flag,
                                        'node_flag_name'    => $pending_approval_node_flag_name,
                                        'info'              => '',
                                        'node_audit_type'   => $node_audit_type,
                                        'sub_node_list'     => [],
                                        'fyr_list'          => $fyrs->getLogs($request->id, $request->flow_id, $request->current_flow_node_id),
                                        'comment_list'      => $comment_service->getLogsByNodeId($request->id, $request->current_flow_node_id),
                                    ];

                                    $current['list'] = [];
                                }

                                $t                     = [];
                                $t['staff_id']         = $tmp['staff_id'];
                                $t['staff_name']       = $this->getNameAndNickName($tmp['staff_name'] ?? '', $tmp['staff_nick_name'] ?? '');
                                $t['staff_department'] = $tmp['department_name'] ?? '';
                                $t['job_title']        = $tmp['job_title_name'] ?? '';
                                $current['list'][]     = $t;
                            }

                            if (!empty($current)) {
                                array_unshift($data, $current);
                            }
                        }
                    }
                }
            }

            // 如果是有子节点, 且子节点有自动同意的, 则一级节点展示为同意
            $data = array_map(function ($v) {
                if (!empty($v['sub_node_list']) && $v['action'] == Enums::WF_ACTION_AUTO_APPROVE) {
                    $v['action']      = Enums::WF_ACTION_APPROVE;
                    $v['action_name'] = self::$t->_(Enums::$actions[Enums::WF_ACTION_APPROVE]);
                }

                return $v;
            }, $data);
        }

        return $data;
    }

    /**
     * 审批流日志: 根据审批人操作 置换 所在节点的标识
     *
     * @param int $action
     * @return int
     */
    private static function getNodeFlagByAuditorAction(int $action)
    {
        return Enums::$wf_action_and_node_flag_rel[$action] ?? Enums::WF_NODE_FLAG_AUDIT;
    }

    /**
     * 获取节点审核人ID
     * @param $node
     * @param $info
     * @return array
     * @throws BusinessException
     */
    public function getNodeAuditors($node, $info)
    {
        switch ($node['auditor_type']) {
            case Enums::AUDITOR_TYPE_STAFF_INFO_ID:
                $auditors = empty($node['auditor_id']) ? [] : explode(',', $node['auditor_id']);
                break;
            case Enums::AUDITOR_TYPE_STAFF_ROLE:
                $auditors = $this->getPositionStaffIds($node['auditor_id']);//根据角色获取审核人ID
                break;
            case Enums::AUDITOR_TYPE_SUBMITTER_DIRECT_SUPERIOR:
                $auditors = $this->getDirectSuperior($info['submitter_id']);
                break;
            case Enums::AUDITOR_TYPE_SUBMITTER_DEPARTMENT_MANAGER:
                $auditors = $this->getDepartmentManager($info['department_id']);
                break;
            case Enums::AUDITOR_TYPE_SUBMITTER_NODE_DEPARTMENT:
                //$auditors = $this->getSecondDepartmentManagerByNodeId($info['node_department_id']);
                $auditors = $this->getFirstDepartmentManagerByNodeId($info['node_department_id'],2);
                break;
            case Enums::AUDITOR_TYPE_SUBMITTER_DEPARTMENT:
                $auditors = $this->getFirstDepartmentManagerByNodeId($info['node_department_id'],1);
                //$auditors = $this->getDepartmentManagerById($info['department_id']);
                break;
            case Enums::AUDITOR_TYPE_SUBMITTER_THREE_DEPARTMENT:
                $auditors = $this->getFirstDepartmentManagerByNodeId($info['node_department_id'],3);
                break;
            //COO也是指定账号
            case Enums::AUDITOR_TYPE_COO:
                $auditors = $this->getGroupManagerByDepartmentId($info['node_department_id']);
                break;
            case Enums::AUDITOR_TYPE_BRANCH_MANAGER:
                $auditors = $this->getStoreManagerById($info['store_id']);
                break;
            //level1-4以后的应该不用了
            case Enums::AUDITOR_TYPE_BRANCH_LEVEL1:
                $auditors = $this->getBranchAuditorsByLevel($info['store_id'], 1);
                break;
            case Enums::AUDITOR_TYPE_BRANCH_LEVEL2:
                $auditors = $this->getBranchAuditorsByLevel($info['store_id'], 2);
                break;
            case Enums::AUDITOR_TYPE_BRANCH_LEVEL3:
                $auditors = $this->getBranchAuditorsByLevel($info['store_id'], 3);
                break;
            case Enums::AUDITOR_TYPE_BRANCH_LEVEL4:
                $auditors = $this->getBranchAuditorsByLevel($info['store_id'], 4);
                break;
            case Enums::AUDITOR_TYPE_BRANCH_MANAGER_BY_POSITION:
                $auditors = $this->getStaffIdsByStoredIdAndJobTitle($info['store_id'], Enums::JOB_TITLE_BRANCH_SUPERVISOR);
                break;
            // 根据网点查找所在大区和片区下，在配置表中有权限的，且职位是District manager的在职员工
            case Enums::AUDITOR_TYPE_BRANCH_BUSINESS_MANAGER:
                //$auditors = $this->getStaffIdsByStoredIdAndJobTitle($info['store_id'],Enums::JOB_TITLE_DISTRICT_SUPERVISOR);
                $auditors = $this->getPieceManagerByStoreId($info['store_id'],Enums::JOB_TITLE_DISTRICT_MANAGER);
                break;
            // 根据网点查找所在大区下，在配置表中有权限的，且职位是area manager的在职员工
            case Enums::AUDITOR_TYPE_BRANCH_BUSINESS_SENIOR_MANAGER:
                //$auditors = $this->getStaffIdsByStoredIdAndJobTitle($info['store_id'],Enums::JOB_TITLE_REGIONAL_MANAGER);
                $auditors = $this->getRegionManagerByStoreId($info['store_id'], Enums::JOB_TITLE_AREA_MANAGER);
                break;
            case Enums::AUDITOR_TYPE_COMPANY_MANAGER:
                $auditors = $this->getCompanyManagerByDepartmentId($info['node_department_id']);
                break;
            // 根据网点查找所在大区下，在配置表中有权限的，且职位是shop area manager的在职员工
            case Enums::AUDITOR_TYPE_SHOP_BRANCH_SENIOR_MANAGER:
                //$auditors = $this->getStaffIdsByStoredIdAndJobTitle($info['store_id'],Enums::JOB_TITLE_AREA_MANAGER);
                $auditors = $this->getRegionManagerByStoreId($info['store_id'], Enums::JOB_TITLE_SHOP_AREA_MANAGER);
                break;
            case Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_SUBMITTER_DEPARTMENT_MANAGER:
                // 取数工单系统 - 提交人所属部门负责人(细分)
                $auditors = $this->getAccessDataSysDepartmentManagerIds([$info['submitter_department_id']], $info['audit_stage'], $node['auditor_type']);
                break;
            case Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_DATA_DEPARTMENT_MANAGER:
                // 取数工单系统 - 数据部门负责人(细分)
                $auditors = $this->getAccessDataSysDepartmentManagerIds([$info['data_department_id']], $info['audit_stage'], $node['auditor_type']);
                break;
            case Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_RELATED_DEPARTMENT_MANAGER:
                // 取数工单系统 - 相关部门负责人(细分的部门, 动态获取)
                $auditors = $this->getAccessDataSysDepartmentManagerIds($info['transfer_department_id_item'], $info['audit_stage'], $node['auditor_type']);
                break;
            case Enums::AUDITOR_TYPE_JOB:
                $auditors = $this->getStaffIdsByJobId($node['auditor_id']);
                break;
            case Enums::AUDITOR_TYPE_DM_APPLY:
                /**
                 * 【OA-PH】菲律宾报价管理 v1.5需求
                 * https://l8bx01gcjr.feishu.cn/docs/doccnPeQYewrZB093eYylB8WI1g
                 * 若申请人为DM，审批流取值的逻辑：一级审批人为组织架构中的申请人管辖片区的负责人
                 */
                $auditors = $this->getStaffIdsByPieceManagerId($info['create_id']);
                break;
            case Enums::AUDITOR_TYPE_HC_HRBP:
                $auditors = $this->getHcHrbp($info['submitter_id']);
                break;

            case Enums::AUDITOR_TYPE_HUB_AREA_MANAGER:
                $auditors = $this->getRegionManagerByStoreId($info['store_id'], Enums::JOB_TITLE_HUB_AREA_MANAGER);
                break;

            case Enums::AUDITOR_TYPE_COMPANY_C_LEVEL_GROUP_CEO_MANAGER:
                /**
                 * 需求
                 * https://l8bx01gcjr.feishu.cn/docs/doccntBEZwfuSNxpFcNpxsn9HLe
                 * */
                $auditors = $this->getManagerByDepartmentId($info['node_department_id']);
                break;
            case Enums::AUDITOR_TYPE_CROSS_DEPARTMENT_ID:
                $auditors = $this->getCrossDepartmentId($info);
                break;
            case Enums::AUDITOR_TYPE_DYNAMIC_UID:
                $auditors = [];
                $extend_text = json_decode($node['extend_text'], true);
                $auditor = $info['auditor_ids'][$extend_text['dynamic_auditor_key'] ?? ''];
                if(!empty($auditor)){
                    $auditors    = explode(',', $auditor);
                }
                break;
            case Enums::AUDITOR_TYPE_NODE_AM_BY_ORG:
             //根据组织架构找大区负责人,和找am负责人的区别在于，am是通过找到大区负责人在去根据职位匹配，新的是直接找大区负责人在看负责人在职状态
                $auditors = (new StoreRepository())->findAMEx($info['store_id']);
                break;
            case Enums::AUDITOR_TYPE_DESIGNATIVE_ORG:
                // 节点审批人为指定的组织架构的负责人
                $auditors = $this->getOrgManagerByDepartmentIds($node['auditor_id']);
                break;
            case Enums::AUDITOR_TYPE_GROUP_ORG:
                //审批人指定财务分组审批人
                $auditors = $this->getStaffManageListIds($node['auditor_id'], $info['create_company_id'] ?? 0);
                break;
            case Enums::AUDITOR_TYPE_NODE_DEPARTMENT_GROUP_ORG:
                //找指定分组审批人 - 所属部门ID
                $auditors = $this->getStaffManageListIds($node['auditor_id'], $info['node_department_id'] ?? 0);
                break;
            case Enums::AUDITOR_TYPE_DEPARTMENT_ORG:
                //根据指定部门id 找负责人
                $auditors = $this->getDepartmentManagerById($info['node_department_id']);
                break;
            case Enums::AUDITOR_TYPE_ORDINARY_PAYMENT_DM_ORG:
                //根据网点查找片区负责人，和查找dm负责人的区别在于，dm是通过找到片区负责人在去根据职位匹配，新的是直接找片区负责人在看负责人在职状态
                $auditors = (new StoreRepository())->findDmHead($info['store_id']);
                break;
            case Enums::AUDITOR_TYPE_ASSIGN_DEPARTMENT_MANAGER:
                //根据WorkflowParams中设置的指定部门id获取部门负责人
                $auditors = $this->getDepartmentManagerById($info['set_department_id']);
                break;
            case Enums::AUDITOR_TYPE_ASSIGN_DEPARTMENT_COMPANY_MANAGER:
                //根据WorkflowParams中设置的指定部门id获取公司负责人
                $auditors = $this->getCompanyManagerByDepartmentId($info['set_department_id']);
                break;
            case Enums::AUDITOR_TYPE_ASSIGN_DEPARTMENT_C_LEVEL_MANAGER:
                //根据WorkflowParams中设置的指定部门id获取Clevel级别负责人
                $auditors = $this->getCLevelManagerByNodeId($info['set_department_id'], OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE);
                break;
            default:
                $auditors = [];
                break;
        }

        $auditors = !is_array($auditors) ? [$auditors] : $auditors;
        $auditors = array_filter($auditors);

        // 仅保留在职的
        if (!empty($auditors)) {
            $auditors = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id IN ({ids:array}) AND state = :state:',
                'bind' => ['ids' => array_values($auditors), 'state' => Enums\StaffInfoEnums::STAFF_STATE_IN],
                'columns' => ['staff_info_id']
            ])->toArray();

            $auditors = array_column($auditors, 'staff_info_id');
        }
        return $auditors;
    }

    /**
     * 【OA-PH】菲律宾报价管理 v1.5需求
     * https://l8bx01gcjr.feishu.cn/docs/doccnPeQYewrZB093eYylB8WI1g
     * 若申请人为DM，审批流取值的逻辑：一级审批人为组织架构中的申请人管辖片区的负责人
     * 根据DM片区负责人（申请人）找到所属的所有大区以及所有大区的负责人工号
     * @param int $create_staff_id 申请人工号
     * @return array
     */
    public function getStaffIdsByPieceManagerId(int $create_staff_id)
    {
        $piece_list = SysManagePieceModel::find([
            'conditions' => ' manager_id = :manager_id: and deleted =  :deleted: ',
            'bind' => [
                'manager_id' => $create_staff_id,
                'deleted' => 0
            ]
        ])->toArray();
        if (!empty($piece_list)) {
            //找到所有的大区ID组
            $manage_region_ids = array_column($piece_list, 'manage_region_id');
            //存在信息找到该片区所属的所有大区信息
            $region_list = SysManageRegionModel::find([
                'conditions' => ' id in ({ids:array}) and deleted =  :deleted: ',
                'bind' => [
                    'ids' => $manage_region_ids,
                    'deleted' => 0
                ]
            ])->toArray();
            if (!empty($region_list)) {
                //找到所有大区的所有负责人信息
                $manager_ids =  array_column($region_list, 'manager_id');
                $staffs = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({ids:array}) and state = :state:',
                    'bind' => ['ids' => $manager_ids, "state" => 1]
                ])->toArray();
                if (!empty($staffs)) {
                    //能找到这些大区负责人信息并且是在职的
                    return array_column($staffs, 'staff_info_id');
                }
            }
        }
        return [];
    }

    public function getHcHrbp($submitter_id){
        return SysService::getInstance()->getBpHeadByStaff($submitter_id);
    }

    /**
     * 根据
     *
     * @param $jobId
     * @return array
     */
    public function getStaffIdsByJobId($jobId)
    {
        $staffs = HrStaffInfoModel::find([
            'conditions' => ' job_title = :job_title: and state =  :state: ',
            'bind' => [
                'job_title' => $jobId,
                'state' => 1
            ]
        ])->toArray();

        return array_column($staffs, 'staff_info_id');
    }

    /**
     *
     * 根据角色ID获取相关人
     *
     * @param $positionId
     *
     * @return array
     */
    public function getPositionStaffIds($positionId)
    {
        $staffPositions = HrStaffInfoPositionModel::find([
            'conditions' => ' position_category = :position_id: ',
            'bind' => [
                'position_id' => $positionId
            ]
        ])->toArray();

        return array_column($staffPositions, 'staff_info_id');
    }

    /**
     * 获取节点审核人ID
     * @param $staff_info_id
     * @param $next_flag 1继续找上一级，0不找了
     * @return array
     * @throws BusinessException
     */
    public function getDirectSuperior($staff_info_id, $next_flag = 1)
    {
        $higher = HrStaffItemsModel::findFirst([
            'conditions' => 'staff_info_id = ?1 and item = ?2',
            'bind' => [
                1 => $staff_info_id,
                2 => 'MANGER'
            ],
        ]);

        if (empty($higher)) {
            return [];
        }

        $higher = $higher->toArray();
        $manager_id = $higher['value'] ?? [];
        if (empty($manager_id)) {
            return [];
        }

        //终止，只找一次上级
        if (empty($next_flag)) {
            return $manager_id;
        }

        $staff = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind' => [
                1 => $manager_id
            ],
        ]);

        /**
         * 如果存在，且在职
         */
        if (!empty($staff) && $staff->state == 1) {
            return $manager_id;
        } else {
            return $this->getDirectSuperior($manager_id, 0);
        }
    }

    /**
     * 获取节点审核人ID==这个基本上不用了。
     * @param $department_id
     * @return array
     * @throws BusinessException
     */
    public function getDepartmentManager($department_id)
    {

        //有可能是二级部门id
        $item = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                "id" => intval($department_id)
            ]
        ]);

        if (empty($item)) {
            throw new BusinessException('Department not exists');
        }

        $item = $item->toArray();
        //如果不是一级部门
        if ($item['ancestry'] != 1) {
            $department_id = $item['ancestry'];
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'sys_department_id = ?1 and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind' => [
                1 => intval($department_id),
            ],
            'order' => 'job_title_grade DESC'
        ]);

        if (empty($staffInfo)) {
            throw new BusinessException('Department Highest manager not exists', ErrCode::$BUSINESS_ERROR);
        }
        $staffInfo = $staffInfo->toArray();

        return $staffInfo['staff_info_id'] ?? [];
    }

    /** 获取当前审核节点
     *
     * @param $nodes
     * @param $currentNodeId
     * @return array
     * @throws BusinessException
     */
    public function getCurrentNode($nodes, $currentNodeId)
    {
        foreach ($nodes as $k => $v) {
            if ($v->id == $currentNodeId) {
                return $v;
            }
        }

        throw new BusinessException('current_node not exists', ErrCode::$BUSINESS_ERROR);
    }


    /**
     * 获取审批流
     * @param $flowId
     * @return \Phalcon\Mvc\Model
     */
    public function getFlow($flowId)
    {
        return WorkflowModel::findFirst($flowId);
    }

    /**
     * 获取全部审批流
     * @return ResultsetInterface
     */
    public function getAllFlow()
    {
        return WorkflowModel::find();
    }

    /**
     * 获取审批流一级节点的全部子节点
     *
     * @param $flow_id
     * @param $node_id
     * @return ResultsetInterface
     * @throws BusinessException
     */
    public function getFlowSubNodes($flow_id, $node_id)
    {
        $sub_node_models = WorkflowSubNodeModel::find(
            [
                'conditions' => 'flow_id = :flow_id: AND parent_node_id = :parent_node_id:',
                'bind' => ['flow_id' => $flow_id, 'parent_node_id' => $node_id],
                "order" => 'id ASC',
            ]
        );

        if (!count($sub_node_models)) {
            throw new BusinessException("子节点未设置，请检查: flow_id={$flow_id}, node_id={$node_id}");
        }

        return $sub_node_models;
    }

    /**
     * 获取指定子节点
     *
     * @param $sub_node_ids
     * @return ResultsetInterface
     */
    public function getSubNodeListByIds($sub_node_ids)
    {
        if (empty($sub_node_ids)) {
            return false;
        }

        return WorkflowSubNodeModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind' => ['ids' => $sub_node_ids]
        ]);
    }

    /**
     * 获取审批流全部节点
     *
     * @param $flowId
     * @return ResultsetInterface
     * @throws BusinessException
     */
    public function getFlowNodes($flowId)
    {
        $node_models = WorkflowNodeModel::find(
            [
                'conditions' => 'flow_id = ?1',
                'bind' => [
                    1 => $flowId,
                ],
                "order" => "id DESC",
            ]
        );

        if (!count($node_models)) {
            throw new BusinessException("节点未设置，请检查: flow_id={$flowId}");
        }

        return $node_models;
    }

    /**
     * 获取审批流节点关系
     *
     * @param $flow_id
     * @param $node_id
     * @return ResultsetInterface
     */
    public function getFlowNodeRelate($flow_id, $node_id)
    {
        return WorkflowNodeRelateModel::find(
            [
                'conditions' => 'from_node_id = :node: and flow_id = :flow:',
                'bind' => [
                    'node' => $node_id,
                    'flow' => $flow_id
                ],
                'order' => 'sort desc'
            ]
        );
    }

    /**
     * 获取审批流节点流向条件
     *
     * @param $flowId
     * @param $node
     * @param $info
     * @return mixed
     * @throws BusinessException
     */
    public function getNextNode($flowId, $node, $info)
    {
        $lines = $this->getFlowNodeRelate($flowId, $node)->toArray();
        if (empty($lines)) {
            //无下一个节点了
            return [];
        }

        //注册stream_wrapper
        registerStream();

        $class = new WorkflowNodeRelation();
        $nodeId = null;

        foreach ($lines as $v) {
            //无有效条件、参数
            if (empty($v['valuate_code']) || empty($v['valuate_formula'])) {
                return $v['to_node_id'];
            }
            $params = [];
            $methods = explode(',', $v['valuate_code']);
            foreach ($methods as $k => $method) {
                $method = trim($method);
                if (!method_exists($class, $method)) {
                    throw new BusinessException('Handler Method not exists');
                }
                $key = 'p' . ($k + 1);
                $params[$key] = $class->$method($info);
            }

            //当前关系的全部
            $formula = $v['valuate_formula'] ?? '';
            $result = include 'var://<?php extract($params); return ' . $formula . ';';
            if ($result === true) {
                $nodeId = $v['to_node_id'];
                break;
            }
        }

        if (empty($nodeId)) {
            throw new BusinessException("Invalid node ID, current node = {$node} , params = " . json_encode($info, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return $nodeId;
    }

    /**
     * @param $flowId
     * @param $type
     * @return array|mixed
     * @throws BusinessException
     */
    public function getFlowNodeByType($flowId, $type)
    {
        $nodes = $this->getFlowNodes($flowId);
        foreach ($nodes as $k => $v) {
            if ($v->type == $type) {
                return $v;
            }
        }
    }

    /**
     * 根据业务类型获取审批流ID
     * @param integer $bizType
     * @param integer $store_id
     * @param array $info
     * @return int
     * @throws
     */
    public function getFlowIdByBizType($bizType, $store_id = 0, $info = [])
    {
        $countryCode = get_country_code();
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        switch ($bizType) {
            //1改成24改成32，通过getFlowId获得，不走这个
            case Enums::WF_CONTRACT_TYPE1:
                $data = Enums::WF_CONTRACT_TYPE32;
                break;
            case Enums::WF_CONTRACT_TYPE20:
                $data = Enums::WF_CONTRACT_TYPE59;
                break;
            case Enums::WF_CONTRACT_TYPE21:
                $data = Enums::WF_CONTRACT_TYPE60;
                break;
            case Enums::WF_CONTRACT_TYPE22:
                $data = Enums::WF_CONTRACT_TYPE61;
                break;
            case Enums::WF_CONTRACT_TYPE23:
                $data = Enums::WF_CONTRACT_TYPE62;
                break;
            case Enums::WF_CONTRACT_TYPE24:
                $data = Enums::WF_CONTRACT_TYPE63;
                break;
            case Enums::WF_LOAN_TYPE:
                //借款申请
                if ($countryCode == GlobalEnums::TH_COUNTRY_CODE) {
                    //V21791 申请人所属一级部门等于PMD-Thailand-新增审批流 416  其他 329
                    $department_config = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
                    $data = (isset($department_config['pmd']) && $info['department_id'] == $department_config['pmd']) ? Enums::WF_LOAN_APPLY_PMD_WF_ID : Enums::WF_LOAN_APPLY_COMMON_WF_ID;
                } else if ($countryCode == GlobalEnums::MY_COUNTRY_CODE) {
                    //18588需求马来 - 借款申请审批流分为快递和非快递
                    $data = ($info['create_company_id'] == $company_ids['FlashExpress']) ? Enums::WF_FLOW_ID_LOAN_APPLY_FLASH_EXPRESS : Enums::WF_FLOW_ID_LOAN_APPLY_NOT_FLASH_EXPRESS;
                } else if ($countryCode == GlobalEnums::PH_COUNTRY_CODE) {
                    //18588需求菲律宾 - 借款申请审批流分为快递和非快递
                    if ($info['create_company_id'] == $company_ids['FlashExpress']) {
                        //快递时还需区分是否是Network Managemen 及 子部门的审批流拆分
                        $sys_department_ids = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_ids');
                        $data = ($sys_department_ids['network_management'] == $info['department_id']) ? Enums::WF_FLOW_ID_LOAN_APPLY_FLASH_EXPRESS : Enums::WF_FLOW_ID_LOAN_APPLY_FLASH_EXPRESS_NOT_NW;
                    } else {
                        //18588需求非快递公司
                        $data =  Enums::WF_FLOW_ID_LOAN_APPLY_NOT_FLASH_EXPRESS;
                    }
                } else if (($countryCode == GlobalEnums::LA_COUNTRY_CODE && $info['create_company_id'] == $company_ids['FlashLaos'])) {
                    $data = Enums::WF_FLOW_ID_LOAN_APPLY_FLASH_LAO;
                } else if (isset($info['create_company_id']) && in_array($info['create_company_id'], [
                        $company_ids['FlashFullfillment'],
                        $company_ids['FlashMoney'],
                        $company_ids['FCommerce'],
                        $company_ids['FlashPay'],
                    ])) {
                    $data = Enums::WF_LOAN_APPLY_COMPANY_WF_ID;

                } else {
                    $data = Enums::WF_LOAN_APPLY_WF_ID;
                }

                break;
            case Enums::WF_PURCHASE_APPLY:
                //V17219-采购申请单-资产部申请人固定工号组
                $purchase_apply_asset_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_apply_asset_staff_ids');

                //V17219-采购申请单-IT申请人固定工号组
                $purchase_apply_it_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_apply_it_staff_ids');
                if ($info['in_group_asset_management_department'] == true || in_array($info['submitter_id'], $purchase_apply_asset_staff_ids)) {
                    // Group Asset Management部门及其子部门 ||  申请人工号是配置的资产部固定工号
                    if ($info['has_asset_barcode'] == 1) {
                        //有barcode
                        if ($info['company_id'] == $company_ids['FlashExpress']) {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW;
                        } else if ($info['company_id'] == $company_ids['FCommerce']) {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE;
                        } else {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW_ASSET_OTHER;
                        }
                    } else {
                        //无barcode
                        $data = ($info['company_id'] == $company_ids['FCommerce']) ? Enums::WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE : Enums::WF_PURCHASE_APPLY_FLOW_OTHER;
                    }
                } else if ($info['in_it_department'] == true || in_array($info['submitter_id'], $purchase_apply_it_staff_ids)) {
                    //IT部门及其子部门 || 申请人工号是配置的IT固定工号
                    if ($info['has_asset_barcode'] == 1) {
                        //有barcode
                        if ($info['company_id'] == $company_ids['FlashExpress']) {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW_IT;
                        } else if ($info['company_id'] == $company_ids['FCommerce']) {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE;
                        } else {
                            $data = Enums::WF_PURCHASE_APPLY_FLOW_IT_OTHER;
                        }
                    } else {
                        //无barcode
                        $data = ($info['company_id'] == $company_ids['FCommerce']) ? Enums::WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE : Enums::WF_PURCHASE_APPLY_FLOW_OTHER;
                    }
                } else {
                    //其他部门
                    //V21791 申请人所属一级部门等于PMD-Thailand-新增审批流 415
                    $department_config = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
                    $data = ($info['company_id'] == $company_ids['FCommerce']) ? Enums::WF_PURCHASE_APPLY_FLOW_OTHER_COMMERCE : ($info['company_id'] == $company_ids['FlashExpress'] && isset($department_config['pmd']) && $info['department_id'] == $department_config['pmd'] ? Enums::WF_PURCHASE_APPLY_FLOW_PMD : Enums::WF_PURCHASE_APPLY_FLOW_OTHER);
                }
                break;
            case Enums::WF_PURCHASE_PAYMENT:
                $company = $info['company_id'] ?? '';

                if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                    if ($countryCode == GlobalEnums::TH_COUNTRY_CODE && in_array($company, [$company_ids['FlashHomeOperation'], $company_ids['FlashHomeHolding']])) {
                        // 泰国 Flash Home 独立审批流
                        $data = Enums::WF_PURCHASE_PAYMENT_FLASH_HOME;
                    } else if ($company == $company_ids['FlashFullfillment']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_FFM;
                    } else if ($company == $company_ids['FlashPay']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_PAY;
                    } else if ($company == $company_ids['FlashMoney'] || $company == $company_ids['FlashPico']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_MONEY;
                    } else if ($company == $company_ids['FCommerce']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_COMMERCE;
                    } else if ($company == $company_ids['FlashSupplyChainManagement']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_SUPPLY;
                    } else {
                        $data = Enums::WF_PURCHASE_PAYMENT_DEFAULT_FLOW;
                    }

                } else if ($countryCode == GlobalEnums::LA_COUNTRY_CODE) {
                    // 老挝 express 235, 子公司 走37, 其他走 34
                    if (in_array((string)$company, [
                        $company_ids['FlashFullfillment'],
                        $company_ids['FlashPay'],
                        $company_ids['FlashMoney']
                    ])) {
                        $data = Enums::WF_PURCHASE_PAYMENT_FFM;
                    } else if ($company == $company_ids['FlashExpress']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_LA_EXPRESS;
                    } else {
                        $data = Enums::WF_PURCHASE_PAYMENT_FLOW;
                    }

                } else if (in_array($countryCode, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
                    //15424增加马来和印尼和越南FCommerce审批流,其他规则从之前[除泰国菲律宾老挝以外的国家]逻辑中集成
                    if ($company && in_array($company, [
                            $company_ids['FlashFullfillment'],
                            $company_ids['FlashPay'],
                            $company_ids['FlashMoney'],
                        ])) {
                        $data = Enums::WF_PURCHASE_PAYMENT_FFM;
                    } else if ($company && $company == $company_ids['FCommerce']) {
                        $data = Enums::WF_PURCHASE_PAYMENT_ONLY_COMMERCE;
                    } else {
                        $data = Enums::WF_PURCHASE_PAYMENT_FLOW;
                    }
                }
                break;
            case Enums::WF_PURCHASE_ORDER:
                //默认审批流
                $data = Enums::WF_PURCHASE_ORDER_OTHER_FLOW;
                $company = $info['company_id'] ?? '';
                if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                    //泰国、菲律宾
                    if ($countryCode == GlobalEnums::TH_COUNTRY_CODE && in_array($company, [$company_ids['FlashHomeOperation'], $company_ids['FlashHomeHolding']])) {
                        // 泰国 Flash Home独立审批流
                        $data = Enums::WF_PURCHASE_ORDER_FLASH_HOME;
                    } else if ($company == $company_ids['FlashFullfillment']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_FFM;
                    } else if ($company == $company_ids['FlashPay']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_PAY;
                    } else if ($company == $company_ids['FlashMoney'] || $company == $company_ids['FlashPico']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_MONEY;
                    } else if ($company == $company_ids['FCommerce']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_COMMERCE;
                    } else if ($company == $company_ids['FlashSupplyChainManagement']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_SUPPLY;
                    }
                } else if ($countryCode == GlobalEnums::LA_COUNTRY_CODE) {
                    //老挝
                    if (in_array((string)$company, [$company_ids['FlashFullfillment'], $company_ids['FlashPay'], $company_ids['FlashMoney']])) {
                        $data = Enums::WF_PURCHASE_ORDER_FFM;
                    } else if ($company == $company_ids['FlashExpress']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_EXPRESS;
                    }
                } else if (in_array($countryCode, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
                    //马来 、印尼、越南
                    if (in_array((string)$company, [$company_ids['FlashFullfillment'], $company_ids['FlashPay'], $company_ids['FlashMoney']])) {
                        $data = Enums::WF_PURCHASE_ORDER_FFM;
                    } else if ($company == $company_ids['FCommerce']) {
                        $data = Enums::WF_PURCHASE_ORDER_ONLY_COMMERCE;
                    }
                }
                break;
            case Enums::WF_PURCHASE_ACCEPTANCE:
                if (GlobalEnums::TH_COUNTRY_CODE == get_country_code() && $info['acceptance_cate'] == Enums\PurchaseEnums::ACCEPTANCE_TYPE_9) {
                    $data = Enums::PURCHASE_ACCEPTANCE_FLOW_ID_V2;
                } else {
                    $data = Enums::PURCHASE_ACCEPTANCE_FLOW_ID_V1;

                }
                break;
            case Enums::WF_PURCHASE_SAMPLE:
                $data = Enums::PURCHASE_SAMPLE_FLOW_ID;
                break;
            case Enums::WF_REIMBURSEMENT_TYPE:
                // 老挝只有一条审批流,v10533 老挝增加FFM
                if (GlobalEnums::LA_COUNTRY_CODE == $countryCode) {
                    $data = Enums::REIMBURSEMENT_HEADER_WF_ID;//报销总部
                    if(isset($info['company_id']) && in_array($info['company_id'], [$company_ids['FlashFullfillment']]) ){
                        $data = Enums::REIMBURSEMENT_FFM_WF_ID;
                    }
                } else if (GlobalEnums::ID_COUNTRY_CODE == $countryCode){
                    if (isset($info['company_id']) && in_array((string)$info['company_id'], [
                            $company_ids['FlashFullfillment'],
                            $company_ids['FlashPay'],
                            $company_ids['FCommerce'],
                        ])) {
                        $data = Enums::REIMBURSEMENT_FFM_WF_ID;
                    } else {
                        $data = Enums::REIMBURSEMENT_HEADER_WF_ID;//报销总部
                    }
                } else if (GlobalEnums::VN_COUNTRY_CODE == $countryCode) {
                    $company_id = isset($info['company_id']) ? (string)$info['company_id'] : '';

                    if ($company_id == $company_ids['FlashFullfillment']) {
                        $data = Enums::REIMBURSEMENT_FFM_WF_ID;
                    } else if ($company_id == $company_ids['FlashPay']) {
                        $data = Enums::REIMBURSEMENT_PAY_WF_ID;
                    } else if ($company_id == $company_ids['FCommerce']) {
                        $data = Enums::REIMBURSEMENT_COMMERCE_WF_ID;
                    } else if ($company_id == $company_ids['FlashMoney']) {
                        $data = Enums::REIMBURSEMENT_MONEY_WF_ID;
                    } else {
                        $data = Enums::REIMBURSEMENT_DEFAULT_WF_ID;
                    }

                } else if (isset($info['company_id'])
                    && in_array((string)$info['company_id'], [
                        $company_ids['FlashFullfillment'],
                        $company_ids['FlashPay'],
                        $company_ids['FlashMoney'],
                        $company_ids['FCommerce'],
                    ])) {

                    $data = Enums::REIMBURSEMENT_FFM_WF_ID; //报销部门(Flash Fullfillment、Flash Laos)
                } else if ($store_id == -1) {
                    $data = Enums::REIMBURSEMENT_HEADER_WF_ID;//报销总部
                } else {
                    // 是否是Network Bulky部门的
                    $sysDepartment = StaffService::getInstance()->getParentDepartment($info['department_id'], 1);
                    if (GlobalEnums::TH_COUNTRY_CODE == $countryCode && $info['cost_store_type'] == 1 && trim($sysDepartment['id']) == Enums::SYS_DEPARTMENT_ONE_ID) {
                        $data = Enums::REIMBURSEMENT_INTEL_STORE_WF_ID;//报销申请网点
                    } else if ($info['type'] == Enums::REIMBURSEMENT_TYPE_TAX) {
                        // 创建的时候判断了
                        $data = Enums::REIMBURSEMENT_GGP_WF_ID;
                    } else {
                        $store = $this->getStoreById($store_id);
                        switch ($store['category']) {
                            //network
                            case 1:
                            case 2:
                            case 10:
                                $data = Enums::REIMBURSEMENT_NETWORK_WF_ID; //报销网点（Network）
                                break;
                            //门店shop
                            case 4:
                            case 5:
                            case 7:
                                $data = Enums::REIMBURSEMENT_SHOP_WF_ID;//报销网点（门店shop）
                                break;
                            //hub
                            case 8:
                            case 9:
                            case 12:
                                $data = Enums::REIMBURSEMENT_HUB_WF_ID; //报销网点（hub）
                                break;
                            default:
                                throw new BusinessException(
                                    "not found the store id=" . $store_id . "==category=" . $store['category']
                                );
                        }
                    }
                }

                break;
            case  Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                $data = 33;
                break;
            case  Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                // 网点租房付款
                $data = Enums::PAYMENT_STORE_RENTING_WF_ID;
                break;
            case  Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                // 普通付款
                $data = Enums::ORDINARY_PAYMENT_WF_ID;
                break;
            case Enums::WF_SALARY_APPLY:
                // 薪资发放审批 =这个已经用getFlowId方法返回的了
                $data = Enums::WF_SALARY_APPLY_WF_ID;
                break;
            case Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE:
                // 取数需求工单
                $data = Enums::WF_ACCESS_DATA_WORK_ORDER_WF_ID;
                break;
            case Enums::WF_CRM_QUOTATION:
                if ($countryCode == GlobalEnums::PH_COUNTRY_CODE) {
                    $data = Enums::WF_CRM_QUOTATION_WF_ID_PH;
                }else{
                    $data = Enums::WF_CRM_QUOTATION_WF_ID;
                }
                break;
            case Enums::WF_LOAN_BACK_TYPE:
                // 借款归还
                $data = Enums::WF_LOAN_BACK_WF_ID;
                if (isset($info['create_company_id'])) {
                    if ($countryCode == GlobalEnums::PH_COUNTRY_CODE) {
                        $data = Enums::WF_LOAN_BACK_UNIFY_WF_ID;
                    } else if (($countryCode == GlobalEnums::LA_COUNTRY_CODE && $info['create_company_id'] == $company_ids['FlashLaos']) || ($countryCode == GlobalEnums::MY_COUNTRY_CODE && $info['create_company_id'] == $company_ids['FlashExpress'])) {
                        $data = Enums::WF_FLOW_ID_LOAN_RETURN_FLASH_LAO;
                    } else if ($countryCode == GlobalEnums::TH_COUNTRY_CODE && $info['create_company_id'] == $company_ids['FlashExpress']) {
                        $data = Enums::WF_LOAN_BACK_COMPANY_FLASH_TH_WF_ID;
                    } else if (in_array($info['create_company_id'], [$company_ids['FlashFullfillment'], $company_ids['FlashMoney'], $company_ids['FCommerce'], $company_ids['FlashPay']])) {
                        $data = Enums::WF_LOAN_BACK_COMPANY_WF_ID;
                    }
                }

                break;
            case Enums::WF_BUDGET_ADJUST_TYPE:
                // 预算调整
                if ($info['type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INPUT) {
                    $data = BudgetAdjustEnums::WF_BUDGET_ADJUST_IN_FLOW;
                } elseif ($info['type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT) {
                    $data = BudgetAdjustEnums::WF_BUDGET_ADJUST_OUT_FLOW;
                } elseif ($info['type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT) {
                    $data = BudgetAdjustEnums::WF_BUDGET_ADJUST_INOUT_FLOW;
                } else {
                    $data = BudgetAdjustEnums::WF_BUDGET_ADJUST_WF_ID;
                }
                break;
            default:
                $data = intval($bizType);
                break;
        }

        return $data;
    }

    /**
     * 获取起始节点
     * @param int $flowId 审批流ID
     * @param array $user 申请人
     * @param $info
     * @return mixed
     * @throws BusinessException
     */
    public function getWorkflowFirstNode($flowId, $user, $info)
    {
        //获取全部审批节点
        $first_nodes = WorkflowNodeModel::findFirst(
            [
                'conditions' => 'flow_id = :flow_id: AND type = :type:',
                'bind' => ['flow_id' => $flowId, 'type' => Enums::WF_NODE_APPLY],
            ]
        );

        if (empty($first_nodes)) {
            throw new BusinessException("审批流的开始节点查找失败, FLowId = {$flowId}");
        }

        return $first_nodes->id ? $first_nodes->id : 0;
    }

    /**
     * 获取审批流下节点及节点关系
     * @param int $flow_id 审批流
     * @return array
     */
    public function getFlowNodesAndRelations($flow_id)
    {

        $nodes = WorkflowNodeModel::find([
            'conditions' => 'flow_id = :flow:',
            'columns' => 'id,name,extend_text',
            'bind' => [
                'flow' => $flow_id,
            ],
        ])->toArray();

        if (!empty($nodes)) {
            foreach ($nodes as $key => $node) {
                $extends = !empty($node['extend_text']) ? json_decode($node['extend_text'], true) : [];
                unset($nodes[$key]['extend_text']);
                $nodes[$key] = array_merge($nodes[$key], $extends);
            }
        }

        $relations = WorkflowNodeRelateModel::find([
            'conditions' => 'flow_id = :flow:',
            'columns' => "from_node_id as [from], to_node_id as to",
            'bind' => [
                'flow' => $flow_id,
            ],
        ])->toArray();

        return [
            'name' => $flow_id,
            'nodeList' => $nodes,
            'lineList' => $relations
        ];
    }

    /**
     * 重新数据链条
     */
    public function getWFLine($flowId)
    {
        //主审批流详情
        $_node = $this->getFlow($flowId)->toArray();
        //节点详情
        $wk = $this->getFlowNodes($flowId)->toArray();
        $node = [];
        $node['name'] = $_node['name'];
        foreach ($wk as $key => $val) {

            $_tmp = [
                "id" => $val['id'],
                "name" => $val['name'],
            ];
            //扩展属性
            $extend_text = json_decode($val['extend_text'] ?? '', true);
            $_tmp = empty($extend_text) ? $_tmp : array_merge($_tmp, $extend_text);
            $node['nodeList'][] = $_tmp;
            $fnr = $this->getFlowNodeRelate($flowId, $val['id'])->toArray();
            if (!empty($fnr)) {
                //线的关系
                foreach ($fnr as $kf => $vf) {
                    $node['lineList'][] = [
                        'from' => $vf['from_node_id'],
                        'to' => $vf['to_node_id'],
                        'label' => $vf['remark'],
                    ];
                }

            }
        }

        return $node ?? [];
    }

    /**
     * 根据部门ID直接获得部门负责人
     *
     * @param $department_id
     * @return array
     */
    public function getDepartmentManagerById($department_id)
    {
        if (empty($department_id)) {
            return [];
        }
        $depart = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $department_id,
            ]
        ]);
        if (empty($depart)) {
            return [];
        }
        return $depart->manager_id ?? [];
    }

    /**
     * 获得所有节点审批人
     * @param $nodes
     * @param $info
     * @param $request
     * @return array
     * @throws BusinessException
     */
    public function getAllFlowNodeAuditors($nodes, $info, $request)
    {
        $data = [];
        foreach ($nodes as $node) {
            $_node_auditor_ids = [
                'auditor_ids' => [],
                'is_auto' => false,
                'sub_nodes' => []
            ];

            // 如果是子节点会签, 则进一步查找子节点的审批人
            if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
                $_all_sub_node_auditor_ids = $_sub_nodes = [];

                // 判断是否有固化, 若固化, 则子节点 与 对应审批人 均取固化的
                $solidified_sub_node_list = $this->getSubNodeAuditList($request, $node);
                if (!empty($solidified_sub_node_list)) {
                    foreach ($solidified_sub_node_list as $sub_node) {
                        $_all_sub_node_auditor_ids[] = $sub_node['auditor_id'];
                        $_sub_nodes[$sub_node['sub_node_id']]['auditor_ids'][] = $sub_node['auditor_id'];
                        $_sub_nodes[$sub_node['sub_node_id']]['is_auto'] = false;
                    }
                } else {
                    // 未固化, 实时取子节点配置
                    $sub_node_models = $this->getFlowSubNodes($node->flow_id, $node->id);
                    foreach ($sub_node_models as $sub_node) {
                        $_sub_node_auditor_ids = $this->getNodeAuditors($sub_node->toArray(), $info);
                        $_all_sub_node_auditor_ids = array_merge($_all_sub_node_auditor_ids, $_sub_node_auditor_ids);
                        $_sub_nodes[$sub_node->id]['auditor_ids'] = $_sub_node_auditor_ids;
                        $_sub_nodes[$sub_node->id]['is_auto'] = false;
                    }
                }

                $_node_auditor_ids['sub_nodes'] = $_sub_nodes;
                $_node_auditor_ids['auditor_ids'] = array_values(array_unique($_all_sub_node_auditor_ids));

            } else {
                $_node_auditor_ids['auditor_ids'] = $this->getNodeAuditors($node->toArray(), $info);
            }

            $data[$node->id] = $_node_auditor_ids;
        }

        return $data;
    }

    /**
     * 获取子节点固化的审批人
     * @param $request
     * @param $node
     *
     * @return mixed
     */
    public function getWorkflowSubNodeSolidifiedAuditors($request, $node) {
        $sub_nodes = [];
        $solidified_sub_node_list = $this->getSubNodeAuditList($request, $node);

        // 待处理的向前排
        $audit_status_column = array_column($solidified_sub_node_list, 'audit_status');
        array_multisort($audit_status_column, SORT_ASC, $solidified_sub_node_list);

        foreach ($solidified_sub_node_list as $sub_node) {
            $sub_nodes[$sub_node['sub_node_id']][] = $sub_node['auditor_id'];
        }

        return $sub_nodes;
    }

    /**
     *
     * 找出审批流后面的线
     * @param $flowId
     * @param $currentNodeId
     * @param $info
     * @param $nodes
     * @return array
     * @throws BusinessException
     */

    public function getFlowNodeAuditorsByNodeId($flowId, $currentNodeId, $info, $nodes)
    {
        $lines = [];
        do {
            $nextNodeId = $this->getNextNode($flowId, $currentNodeId, $info);
            if (!empty($nextNodeId)) {
                $lines[$nextNodeId] = $nodes[$nextNodeId] ?? [];
            }
            $currentNodeId = $nextNodeId;
        } while (!empty($currentNodeId));

        // 不要要倒序了，保留key
        return $lines;
    }

    /**
     * 查找当前节点，以后的审批节点，是否有相等的审批人
     * @param array $flowNodeAuditors
     * @param array $cur_auditors
     * @return int|string
     */
    public function getSameAuditorsNodeId($flowNodeAuditors, $cur_auditors)
    {
        foreach ($flowNodeAuditors as $k => $v) {
            if (!empty($v) && $v == $cur_auditors) {
                return $k;
            }
        }
        return 0;
    }


    /**
     * 判断需要COO&CPO审批的部门，如果不在返回true
     * @param $info
     * @return bool
     */
    public function isNeedCooDepartment($info)
    {
        $coo_department_ids = env("workflow_coo_department_ids", "21,45,44,46,20,12,25,66,67,68,69,70,71,4,30,31,32,33,34,26,60,61,62,13");
        $cpo_department_ids = env("workflow_cpo_department_ids", "7,47,48,49,50");

        $department_ids = $coo_department_ids . "," . $cpo_department_ids;
        $department_ids = trim($department_ids, ",");

        $arr = explode(",", $department_ids);
        //如果都不在
        if (!in_array($info['department_id'], $arr) && !in_array($info['node_department_id'], $arr)) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否，不是跳过的审批ID
     * @param $auditor_id
     * @return bool
     */

    public function isNotStepAuditorId($auditor_id)
    {
        $ids = env("workflow_not_step_auditor_ids", "55356,17008");
        $idArr = explode(",", $ids);
        if (in_array($auditor_id, $idArr)) {
            return true;
        }
        return false;
    }

    /**
     * 是否是财务部门
     * @param $info
     * @return int
     */
    public function isFinance($info)
    {
        $finance_department_id = env("finance_department_id", 17);
        if ($info['department_id'] == $finance_department_id) {
            return 1;
        }
        return 0;
    }


    public function isCooDepartmentIds($info)
    {
        $coo_department_ids = env("workflow_coo_department_ids", "21,45,44,46,20,12,25,66,67,68,69,70,71,4,30,31,32,33,34,26,60,61,62,13");
        $arr = explode(",", $coo_department_ids);
        //如果都不在
        if (!in_array($info['department_id'], $arr) && !in_array($info['node_department_id'], $arr)) {
            return false;
        }
        return true;
    }

    public function isCpoDepartmentIds($info)
    {
        $cpo_department_ids = env("workflow_cpo_department_ids", "7,47,48,49,50");
        $arr = explode(",", $cpo_department_ids);
        //如果都不在
        if (!in_array($info['department_id'], $arr) && !in_array($info['node_department_id'], $arr)) {
            return false;
        }
        return true;
    }


    /**
     * 获得部门负责人
     * @param $store_id
     * @return array|mixed
     */
    public function getStoreManagerById($store_id)
    {
        $store = $this->getStoreById($store_id);
        if (empty($store)) {
            return [];
        }
        return $store['manager_id'] ?? [];
    }

    public function getBranchAuditorsByLevel($store_id, $level)
    {
        $store = $this->getStoreById($store_id);
        if (empty($store)) {
            return [];
        }

        $str = "";
        switch ($level) {
            //Supervisor
            case 1:
                switch ($store['category']) {
                    //Network Management
                    case 1:
                    case 2:
                    case 10:
                        //$str = env("re_branch_level1_nm","24901");
                        $str = EnvModel::getEnvByCode("re_branch_level1_nm", '24901');
                        break;
                    //Shop Management Onsite
                    case 9:
                        //$str = env("re_branch_level1_sm_onsite","21715");
                        $str = EnvModel::getEnvByCode("re_branch_level1_sm_onsite", '21715');
                        break;
                    //Shop Management Shop-project
                    case 4:
                    case 5:
                        //$str = env("re_branch_level1_sm_sp","20467");
                        $str = EnvModel::getEnvByCode("re_branch_level1_sm_sp", '20467');
                        break;
                    //Shop Management U-project
                    case 7:
                        //$str = env("re_branch_level1_sm_up","33173");
                        $str = EnvModel::getEnvByCode("re_branch_level1_sm_up", '33173');
                        break;
                    //HUB Management
                    case 8:
                        //$str = env("re_branch_level1_hub","30286");
                        $str = EnvModel::getEnvByCode("re_branch_level1_hub", '30286');
                        break;
                }
                break;
            //Manager
            case 2:
                switch ($store['category']) {
                    //Network Management
                    case 1:
                    case 2:
                    case 10:
                        //$str = env("re_branch_level2_nm","17109");
                        //因为17109离职，改成19616
                        $str = EnvModel::getEnvByCode("re_branch_level2_nm", '19616');
                        break;
                    //Shop Management Onsite
                    case 9:
                    case 4:
                    case 5:
                    case 7:
                        //$str = env("re_branch_level2_sm","17574");
                        $str = EnvModel::getEnvByCode("re_branch_level2_sm", '17574');
                        break;
                }
                break;
            //Senior Manager
            case 3:
                switch ($store['category']) {
                    //Network Management
                    case 1:
                    case 2:
                    case 10:
                        //$str = env("re_branch_level3_nm","31849");
                        $str = EnvModel::getEnvByCode("re_branch_level3_nm", '31849');
                        break;
                }
                break;
            //Director
            case 4:
                switch ($store['category']) {
                    //Network Management
                    case 1:
                    case 2:
                    case 10:
                        $str = EnvModel::getEnvByCode("re_branch_level4_nm", '21848');
                        break;
                    case 8:
                        $str = EnvModel::getEnvByCode("re_branch_level4_hub", '21848');
                        break;
                }
        }

        if (empty($str)) {
            return [];
        }
        //逗号分隔
        return explode(",", $str);
    }


    /**
     * 获得网点相关数据，会缓存
     * @param $store_id
     * @return mixed
     */
    public function getStoreById($store_id)
    {
        if (empty($store_id)) {
            return [];
        }

        static $storeArr = [];

        if (isset($storeArr[$store_id])) {
            return $storeArr[$store_id];
        }

        $store = StoreModel::findFirst([
            'conditions' => 'id = :id: AND state = :state:',
            'bind' => ['id' => $store_id, 'state' => Enums::STORE_STATE_ACTIVE]
        ]);

        if (empty($store)) {
            $storeArr[$store_id] = [];
        } else {
            $storeArr[$store_id] = $store->toArray();
        }

        return $storeArr[$store_id];
    }

    /**
     * @param $staffId
     * @return array
     */
    public function getUserInfo($staffId)
    {
        $staff = (new UserService())->getUserByIdInRbi($staffId);
        if (empty($staff)) {
            return [];
        }
        $department = $staff->getDepartment();
        $jobTitle = $staff->getJobTitle();

        return [
            'staff_id' => $staff->staff_info_id,
            'staff_name' => $staff->name,
            'staff_nick_name' => $staff->nick_name ?? '',
            'email' => $staff->email ?? "",
            'mobile' => $staff->mobile ?? "",
            'job_title_name' => $jobTitle->job_name ?? '',
            'department_name' => $department->name ?? '',
        ];
    }

    /**
     * @param $staff_ids
     * @return array
     */
    public function getUserList($staff_ids)
    {
        $staff_ids = array_unique(array_filter($staff_ids));
        if (empty($staff_ids)) {
            return [];
        }

        $staffs = (new UserService())->getUserListByStaffIds($staff_ids);
        $staff_array = $staffs->toArray();
        if (empty($staff_array)) {
            return [];
        }

        $node_department_ids = array_filter(array_unique(array_column($staff_array,  'node_department_id')));

        $department_info_list = [];
        if (!empty($node_department_ids)) {
            // 实时获取直属部门名称
            $department_info_list = SysDepartmentModel::find([
                'conditions' => 'id IN ({ids:array})',
                'bind' => ['ids' => array_values($node_department_ids)],
                'columns' => ['id', 'name']
            ])->toArray();
            $department_info_list = array_column($department_info_list, 'name', 'id');
        }

        $list = [];
        foreach ($staffs as $staff) {
            $jobTitle = $staff->getJobTitle();

            $list[] = [
                'staff_id' => $staff->staff_info_id,
                'staff_name' => $staff->name,
                'staff_nick_name' => $staff->nick_name ?? '',
                'email' => $staff->email ?? "",
                'mobile' => $staff->mobile ?? "",
                'job_title_name' => $jobTitle->job_name ?? '',
                'department_id' => $staff->node_department_id ?? '',
                'department_name' => $department_info_list[$staff->node_department_id] ?? '',
            ];
        }

        $staffs = null;

        return $list;
    }

    /**
     * 获得节点审批人通过info里面的申请人: 处理自动通过的场景
     *
     * @param $node_auditors // 节点待审批人
     * @param $request
     * @param $node // 一级节点
     * @param array $info 审批信息参数组
     * @return array
     */
    public function getNodeAuditorsByAuto($node_auditors, $request, $node, $info)
    {
        if (empty($node_auditors['auditor_ids'])) {
            return $node_auditors;
        }
        // 抄送节点不参与自动通过逻辑,也不能影响其他节点的自动通过
        if ($node->type == Enums::WF_NODE_CC) {
            $node_auditors['auto_auditor_ids'] = [];
            return $node_auditors;
        }
        // 一级节点审批类型
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            // 获取当前节点的待审批的子节点
            $pending_sub_nodes = $this->getRequestNodeAuditorListByStatus($request->id, $node->id, Enums::WF_STATE_PENDING);
            if (empty($pending_sub_nodes)) {
                return $node_auditors;
            }

            // 子节点会签
            $node_auditors['auditor_ids'] = [];
            $node_auditors['sub_nodes'] = [];

            // 待审批子节点相关信息: 取固化的
            $pending_node_auditors = [];
            foreach ($pending_sub_nodes as $pending) {
                $pending_node_auditors[$pending['sub_node_id']]['auditor_ids'][] = $pending['auditor_id'];
                $pending_node_auditors[$pending['sub_node_id']]['is_auto'] = false;
            }

            $all_sub_node_total = count($pending_node_auditors);
            $sub_node_auto_approval_count = 0;
            foreach ($pending_node_auditors as $sub_node_id => $sub_node_auditors) {
                $sub_node_auditors = $this->getNodeAuditorAutoInfo($request, $sub_node_auditors, $node, $info, $sub_node_id);

                // 更新节点/子节点 与 审批人为自动同意
                // 该处逻辑仅支持子节点为或签的
                if ($sub_node_auditors['is_auto']) {
                    // 重置节点/子节点审批人: 若该子节点自动通过, 则置空该子节点审批人
                    $sub_node_auditors['auditor_ids'] = [];

                    $sub_node_auto_approval_count++;
                    $this->handleAutoApproval($request, $sub_node_id, Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE, $sub_node_auditors['auto_auditor_ids']);
                }

                // 子节点审批人: 取固化的且经是否自动同意逻辑验证过的
                $node_auditors['sub_nodes'][$sub_node_id] = $sub_node_auditors;

                // 重建一级节点总审批人
                $node_auditors['auditor_ids'] = array_merge($node_auditors['auditor_ids'], $sub_node_auditors['auditor_ids']);
            }

            $node_auditors['auditor_ids'] = array_values(array_unique($node_auditors['auditor_ids']));

            // 全部子节点均自动同意, 则清空审批人, 便于外层继续查找下个一级节点
            $node_auditors = $sub_node_auto_approval_count == $all_sub_node_total ? [] : $node_auditors;
        } else {
            // 或签 及 审批人会签
            $node_auditors = $this->getNodeAuditorAutoInfo($request, $node_auditors, $node, $info);

            // 更新节点/子节点 与 审批人为自动同意
            if ($node_auditors['is_auto']) {
                // 非或签同意/审批人会签同意的行为, 默认为或签同意
                $action = $node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_OR_SIGN ? Enums::WF_ACTION_APPROVE : Enums::WF_ACTION_COUNTERSIGN_APPROVE;

                $this->handleAutoApproval($request, 0, $action, $node_auditors['auto_auditor_ids']);

                // 或签: 自动同意的处理完毕, 清空审批人, 便于外层继续查找下个一级节点
                // 会签: 剩余人继续审批
                $node_auditors = $node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN ? $node_auditors : [];
            }
        }

        return $node_auditors;
    }

    /**
     * 获取指定状态的节点列表(含子节点)
     *
     * @param $request_id
     * @param $node_id
     * @param $audit_status
     * @return mixed
     */
    public function getRequestNodeAuditorListByStatus($request_id, $node_id, $audit_status = '')
    {
        $conditions = 'request_id = :request_id: AND flow_node_id = :flow_node_id:';
        $bind = [
            'request_id' => $request_id,
            'flow_node_id' => $node_id,
        ];

        if (!empty($audit_status)) {
            $conditions .= ' AND audit_status = :audit_status:';
            $bind['audit_status'] = $audit_status;
        }

        return WorkflowRequestNodeAuditorModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'order' => 'sub_node_id ASC'
        ])->toArray();
    }

    /**
     * 处理自动同意
     * @param $request
     * @param $sub_node_id
     * @param $action
     * @param $auditor_ids
     */
    public function handleAutoApproval($request, $sub_node_id, $action, $auditor_ids)
    {
        // 日志表、节点与审批人状态表
        $auditor_info_list = $this->getUserList($auditor_ids);
        $auditor_info_list = array_column($auditor_info_list, null, 'staff_id');
        foreach ($auditor_ids as $auditor_id) {
            $auditor_info = $auditor_info_list[$auditor_id] ?? [];
            if (!empty($auditor_info)) {
                $auditor_info['id'] = $auditor_info['staff_id'];
                $auditor_info['name'] = $auditor_info['staff_name'];
                $auditor_info['nick_name'] = $auditor_info['staff_nick_name'];
                $auditor_info['department'] = $auditor_info['department_name'];
                $auditor_info['job_title'] = $auditor_info['job_title_name'];
            } else {
                $auditor_info['id'] = $auditor_id;
                $auditor_info['name'] = '';
                $auditor_info['nick_name'] = '';
                $auditor_info['department'] = '';
                $auditor_info['job_title'] = '';
            }

            // 更新审批行为日志
            $this->saveAuditLog($request, $sub_node_id, $auditor_info, $action, [], true);

            // 更新节点/子节点/审批状态
            $this->updateNodeAndAuditorStatus($request, $sub_node_id, $auditor_info, $action, true);
        }
    }


    /**
     * 验证节点审批人自动通过类型
     * @param object $request
     * @param array $node_auditors
     * @param object $node // 一级节点
     * @param array $info 审批信息参数组
     * @param integer $sub_node_id 子节点id
     * @return array
     */
    public function getNodeAuditorAutoInfo($request, $node_auditors, $node, $info, $sub_node_id = 0)
    {
        // 该业务历史通过操作的审批人
        $request_history_approvals = $this->getRequestHistoryApprovals($request, Enums::WF_ACTION_APPROVE);

        // 1. 是否含历史审批人, 含最近的审批人 自动同意
        $intersect_auditor = array_values(array_intersect($node_auditors['auditor_ids'], $request_history_approvals));

        if (!empty($intersect_auditor)) {
            // 自动同意的人: 如果是审批人会签, 则取所有交集人; 其他只取第一个交集人
            if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN) {
                $node_auditors['auto_auditor_ids'] =  $intersect_auditor;
            } else {
                $node_auditors['auto_auditor_ids'] =  [current($intersect_auditor)];
            }

            // 剩余待审批人, 剔除自动同意的人(无须审批的人)
            $node_auditors['auditor_ids'] = array_values(array_diff($node_auditors['auditor_ids'], $intersect_auditor));
            $node_auditors['is_auto'] = true;

            return $node_auditors;
        }

        // 2. 获取该审批流的提交人
        $request_applicant = $this->getRequestHistoryApprovals($request, Enums::WF_ACTION_APPLY);
        $request_applicant = current($request_applicant);

        $is_contain_submitter = in_array($request_applicant, $node_auditors['auditor_ids']);
        $auditor_total = count($node_auditors['auditor_ids']);
        if ($is_contain_submitter && ($auditor_total == 1 || ($auditor_total > 1 && $node->audit_type == Enums::WF_NODE_LOGIC_AUDIT_TYPE_AUTO_APPROVAL))) {
            // (含提交人 且 只有一个待审批人) 或 (含提交人 且 多个待审批人 且 设置了含提交人时)直接自动通过
            // 自动同意的人
            $node_auditors['auto_auditor_ids'] = [$request_applicant];

            // 剩余待审批人
            $node_auditors['auditor_ids'] = [];
            $node_auditors['is_auto'] = true;

            return $node_auditors;
        } else if ($is_contain_submitter) {
            // 含提交人, 审批人剔除提交人后接着审批, 不可自动同意
            $node_auditors['auto_auditor_ids'] = [];

            // 剩余待审批人, 待审批人剔除提交人
            $node_auditors['auditor_ids'] = array_values(array_diff($node_auditors['auditor_ids'], [$request_applicant]));
            $node_auditors['is_auto'] = false;

            return $node_auditors;
        }

        // 3.v16911需求,某节点需经过单无需人审批 && 特定条件下才需要自动审批 && 需要发送邮件给自动审批人
        if($node->audit_type == Enums::WF_NODE_LOGIC_AUDIT_TYPE_AUTO_APPROVAL_IGNORE) {
            //需要队列的信息
            $push_data = ['request_id' => $request->id, 'flow_node_id' => $node->id, 'sub_node_id' => $sub_node_id];
            if ($sub_node_id) {
                //若主节点是子节点会签需要传递子节点ID，需要判断子节点是否需要自动跳过
                $sub_node_info = WorkflowSubNodeModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $sub_node_id]
                ]);
                //满足跳转逻辑
                if (!empty($sub_node_info) && $sub_node_info->audit_type == Enums::WF_NODE_LOGIC_AUDIT_TYPE_AUTO_APPROVAL_IGNORE) {
                    $node_auditors = $this->autoApprovalIgnore($node, $node_auditors, $info, $push_data);
                }
            } else {
                //非子节点，则判断主节点是否需要自动跳过
                $node_auditors = $this->autoApprovalIgnore($node, $node_auditors, $info, $push_data);
            }
        }

        return $node_auditors;
    }

    /**
     * 16911【ALL|OA】采购申请优化
     * 针对节点audit_type = 4的自动通过，无需点击审批的节点入队列
     * @param object $node 节点信息组
     * @param array $node_auditors 若节点是子节点会签，则是子节点审批人自动通过，否则是节点审批人自动通过
     * @param array $info 审批信息参数组
     * @param array $push_data['request_id'=>'审批ID', 'flow_node_id'=>'节点ID', 'sub_node_id'=>'子节点ID'] 入自动通过队列的信息组
     * @return mixed
     */
    private function autoApprovalIgnore($node, $node_auditors, $info, $push_data)
    {
        $extend_text = json_decode($node->extend_text, true);
        if (json_last_error() == JSON_ERROR_NONE && !empty($extend_text) && !empty($extend_text['valuate_formula']) && !empty($extend_text['valuate_code'])) {
            $params = [];
            $methods = explode(',', $extend_text['valuate_code']);
            $class = new WorkflowNodeRelation();
            foreach ($methods as $k => $method) {
                $method = trim($method);
                $key = 'p' . ($k + 1);
                $params[$key] = $class->$method($info);
            }
            //当前关系的全部
            $formula = $extend_text['valuate_formula'];
            $result = include 'var://<?php extract($params); return ' . $formula . ';';
            if ($result === true) {
                if (!empty($extend_text['is_cc']) && $extend_text['is_cc']) {
                    //入自动同意队列
                    (new WorkflowMailService())->redisListAdd($push_data);
                }
                //满足条件，则自动通过，无需点击审批
                $node_auditors['auto_auditor_ids'] = array_values($node_auditors['auditor_ids']);
                // 剩余待审批人
                $node_auditors['auditor_ids'] = [];
                $node_auditors['is_auto'] = true;
            }
        }

        return $node_auditors;
    }

    /**
     * 获取审批流历史处理人
     * @param $request
     * @param $audit_action
     * @return mixed
     *
     */
    public function getRequestHistoryApprovals($request, $audit_action)
    {
        $approvals = WorkflowAuditLogModel::find([
            'conditions' => 'request_id = :request_id: AND audit_action = :audit_action:',
            'bind' => ['request_id' => $request->id, 'audit_action' => $audit_action],
            'columns' => ['staff_id']
        ])->toArray();

        return !empty($approvals) ? array_values(array_unique(array_column($approvals, 'staff_id'))) : [];
    }

    /**
     * 根据node_id找到二级部门负责人
     *
     * @param $department_id
     * @return array
     */
    public function getSecondDepartmentManagerByNodeId($department_id)
    {
        $depart = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $department_id,
            ]
        ]);

        if (empty($depart)) {
            return [];
        }

        if (empty($depart)) {
            return [];
        }

        //公司,C-level没有一级部门负责人
        if ($depart->level == 0 || $depart->level == 99) {
            return [];
        }

        $second_id = 0;
        $departIdArr = explode("/", $depart->ancestry_v3);
        if ($depart->type == 2) {
            //999/222/1/18/105,是第5个
            if (count($departIdArr) < 5) {
                return [];
            }
            $second_id = $departIdArr[4];
        } elseif ($depart->type == 3) {
            //C-level下的一级部门ss
            if ($depart->level == 1) {
                return [];
            } else {
                $temp = array_values(array_diff($departIdArr, $this->getGroupBossAndGroupCeoDepartment()));
                if (!empty($temp) && count($temp) > 2) {
                    $second_id = $temp[1];
                }
            }
        } else {
            return [];
        }
        return $this->getDepartmentManagerById($second_id);
    }


    /**
     * 根据子部门id，找一级部门负责人id
     *
     * @param $node_department_id
     * @param int $level
     * @return array
     */
    public function getFirstDepartmentManagerByNodeId($node_department_id,int $level=1)
    {
        if (empty($node_department_id)) {
            return [];
        }
        $depart = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id = ?1 and deleted = 0',
                'bind' => [
                    1 => $node_department_id,
                ]
            ]
        );

        if (empty($depart)) {
            return [];
        }
        //本身就是这个级别直接取部门负责人
        if ($depart->level == $level){
            return $depart->manager_id ?? [];
        }
        //公司,C-level没有一级部门负责人
        if ($depart->level == 0 || $depart->level == 99) {
            return [];
        }


        //$first_id = 0;
        //if ($depart->type == 2) {
        //    //999/222/1/18/105,是第4个
        //    if (count($departIdArr) < 4) {
        //        return [];
        //    }
        //    $first_id = $departIdArr[3];
        //} elseif ($depart->type == 3) {
        //    //C-level下的一级部门
        //    if ($depart->level == 1) {
        //        return $depart->manager_id;
        //    } else {
        //        $temp = array_values(array_diff($departIdArr, $this->getGroupBossAndGroupCeoDepartment()));
        //        if (!empty($temp)) {
        //            $first_id = $temp[0];
        //        }
        //    }
        //} else {
        //    return [];
        //}
        //查询部门链中指定level的部门
        $departIdArr = explode("/", $depart->ancestry_v3);
        if(empty($departIdArr)){
            return [];
        }
        $level_department = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id in ({ids:array}) and deleted = 0 and level=:level:',
                'bind' => [
                    'ids' => $departIdArr,
                    'level' => $level,
                ]
            ]
        );
        if (!$level_department) {
            return [];
        }
        return $level_department->manager_id ?? [];
    }

    /**
     * 根据部门id，找CLevel负责人
     *
     * @param $node_department_id
     * @param int $type
     * @return array
     */
    public function getCLevelManagerByNodeId($node_department_id,int $type = OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE)
    {
        if (empty($node_department_id)) {
            return [];
        }
        $depart = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id = :id: and deleted = :deleted:',
                'bind' => [
                    'id' => $node_department_id,
                    'deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]
        );

        if (empty($depart)) {
            return [];
        }
        //本身就是这个级别直接取部门负责人
        if ($depart->type == OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE){
            return $depart->manager_id ?? [];
        }
        //查询部门链中指定level的部门
        $depart_id_arr = explode('/', $depart->ancestry_v3);
        if(empty($depart_id_arr)){
            return [];
        }
        $level_department = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id in ({ids:array}) and deleted = :deleted: and type=:type:',
                'bind' => [
                    'ids' => $depart_id_arr,
                    'deleted' => GlobalEnums::IS_NO_DELETED,
                    'type' => $type,
                ]
            ]
        );
        if (!$level_department) {
            return [];
        }
        return $level_department->manager_id ?? [];
    }


    /**
     * 获得该网点BranchSupervisor职位的员工id
     * @param $store_id
     * @param $job_title_id
     * @return array
     */
    public function getStaffIdsByStoredIdAndJobTitle($store_id, $job_title_id)
    {

        $staffs = HrStaffInfoModel::find(
            [
                'conditions' => 'sys_store_id= ?1 and job_title= ?2 and state=1 and is_sub_staff = 0',
                'bind' => [
                    1 => $store_id,
                    2 => $job_title_id
                ]
            ]
        )->toArray();

        if (empty($staffs)) {
            return [];
        }
        return array_column($staffs, "staff_info_id");
    }


    /**
     * 根据node_id找到公司负责人
     *
     * @param $department_id
     * @return array
     */
    public function getCompanyManagerByDepartmentId($department_id)
    {
        if (empty($department_id)) {
            return [];
        }
        $depart = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $department_id,
            ]
        ]);
        if (empty($depart)) {
            return [];
        }
        //公司也在部门表里
        return $this->getDepartmentManagerById($depart->company_id);
    }

    /**
     * 获得组织BOSS
     *
     * @param int $department_id
     * @return int|array manager_id
     */
    public function getGroupManagerByDepartmentId(int $department_id)
    {
        $depart = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind' => [
                1 => $department_id,
            ]
        ]);
        if (empty($depart)) {
            return [];
        }

        //如果组织负责人为空，就是组织本身
        if (empty($depart->group_boss_id)) {
            $manager_id = $depart->manager_id;
        } else {
            $manager_id = $this->getDepartmentManagerById($depart->group_boss_id);
        }
        //55356直接跳过
        if (empty($manager_id) || $manager_id == 55356) {
            return [];
        }
        return $manager_id;
    }

    /**
     * 获取组织架构中指定组织的负责人
     * 说明: 节点审批人 为 一个或多个指定组织负责人的场景
     *
     * @param string $department_ids
     * @return array
     */
    public function getOrgManagerByDepartmentIds(string $department_ids)
    {
        if (empty($department_ids)) {
            return [];
        }

        $department_ids = array_values(array_filter(explode(',', $department_ids)));
        $org_manager_ids = SysDepartmentModel::find([
            'conditions' => 'id IN ({ids:array}) AND deleted = :deleted:',
            'bind' => ['ids' => $department_ids, 'deleted' => GlobalEnums::IS_NO_DELETED],
            'columns' => ['manager_id']
        ])->toArray();

        return array_column($org_manager_ids, 'manager_id');
    }

    /**
     * 获得网点对应的片区经理id
     * @param  $store_id
     * @param  $job_title_id
     * @return array
     */
    public function getPieceManagerByStoreId($store_id,$job_title_id)
    {
        $store = $this->getStoreById($store_id);
        if (empty($store)) {
            return [];
        }

        $region_id = $store['manage_region'] ?? "";
        $piece_id = $store['manage_piece'] ?? "";

        if (empty($region_id) || empty($piece_id)) {
            return [];
        }

        $regions = HrStaffManageRegionsModel::find(
            [
                'conditions' => 'region_id=?1 and piece_id=?2',
                'bind' => [1 => $region_id, 2 => $piece_id]
            ]
        )->toArray();

        if (empty($regions)) {
            return [];
        }

        $staff_ids = array_column($regions, "staff_info_id");

        $staffs = HrStaffInfoModel::find(
            [
                'conditions' => 'staff_info_id in ({ids:array}) and job_title = :job_title: and state=1 and is_sub_staff = 0',
                'bind' => ['ids' => $staff_ids,'job_title' => $job_title_id]
            ]
        )->toArray();

        if (empty($staffs)) {
            return [];
        }
        return array_column($staffs, "staff_info_id");
    }

    /**
     * 获得网点对应的大区经理id+职位限定，network=79,shop=11
     * @param $store_id
     * @param $job_title_id
     * @return array
     */
    public function getRegionManagerByStoreId($store_id, $job_title_id)
    {
        $store = $this->getStoreById($store_id);
        if (empty($store)) {
            return [];
        }

        $region_id = $store['manage_region'] ?? "";

        if (empty($region_id)) {
            return [];
        }

        $regions = HrStaffManageRegionsModel::find(
            [
                'conditions' => 'region_id=?1 and piece_id=0',
                'bind' => [1 => $region_id]
            ]
        )->toArray();

        if (empty($regions)) {
            return [];
        }

        $staff_ids = array_column($regions, "staff_info_id");

        $staffs = HrStaffInfoModel::find(
            [
                'conditions' => 'staff_info_id in ({ids:array}) and state=1 and job_title = :job_title_id: and is_sub_staff = 0',
                'bind' => ['ids' => $staff_ids, "job_title_id" => $job_title_id]
            ]
        )->toArray();

        if (empty($staffs)) {
            return [];
        }
        return array_column($staffs, "staff_info_id");
    }
    public function getGroupBossAndGroupCeoDepartment()
    {
        return [999, 222, 333, 444];
    }

    /**
     * 取数工单系统 - 指定部门负责人(细分)
     * 该系统的组织架构 != OA 组织架构, 是取数系统中单独维护的
     * @param array $access_data_sys_department_ids
     * @param int $audit_stage
     * @param int $auditor_type
     * @return mixed
     */
    public function getAccessDataSysDepartmentManagerIds(array $access_data_sys_department_ids, int $audit_stage, int $auditor_type)
    {
        if (empty($access_data_sys_department_ids)) {
            return [];
        }

        // 节点审批类型为需求部门负责人 但 审批阶段非需求部门审批, 需要跳过
        if ($auditor_type == Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_SUBMITTER_DEPARTMENT_MANAGER && $audit_stage != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
            return [];
        }

        // 获取指定细分部门的负责人id
        $data_sys_department_managers = AccessDataStaffInfoModel::find([
            'conditions' => 'department_id IN ({dep_ids:array}) AND duty_flag = :duty_flag: AND is_del = :is_del:',
            'bind' => ['dep_ids' => $access_data_sys_department_ids, 'duty_flag' => 1, 'is_del' => 0],
            'columns' => ['staff_id']
        ])->toArray();

        if (!empty($data_sys_department_managers)) {
            $staff_ids = array_column($data_sys_department_managers, "staff_id");

            // 提取到的负责人是否离职:
            $staffs = StaffInfoModel::find(
                [
                    'conditions' => 'id IN ({ids:array}) AND state = 1',
                    'bind' => ['ids' => $staff_ids],
                    'columns' => ['id']
                ]
            )->toArray();


            return $staffs ? array_column($staffs, 'id') : [];
        }

        return [];
    }

    /**
     * 会签节点待审批的人数
     * @param $request
     * @return mixed
     */
    public function getCountersignNodePendingAuditorCount($request)
    {
        $auditor_ids = WorkflowRequestNodeAuditorModel::find([
            'conditions' => 'biz_type = :biz_type: AND biz_value = :biz_value: AND flow_node_id = :flow_node_id: AND audit_status = :audit_status: and request_id = :request_id:',
            'bind' => [
                'biz_type' => $request->biz_type,
                'biz_value' => $request->biz_value,
                'flow_node_id' => $request->current_flow_node_id,
                'audit_status' => Enums::WF_STATE_PENDING,
                'request_id'    => $request->id,
            ],
            'columns' => ['auditor_id']
        ])->toArray();

        // 在职的实际人数
        $staff_ids = [];
        if (!empty($auditor_ids)) {
            $auditor_ids = array_column($auditor_ids, 'auditor_id');
            $staff_ids = StaffInfoModel::find([
                'conditions' => 'id IN ({ids:array}) AND state = 1',
                'bind' => ['ids' => $auditor_ids],
                'columns' => ['id']
            ])->toArray();
        }

        return count($staff_ids);
    }

    /**
     * 初始化节点与审批人关系
     * @param $request
     * @param $auditors
     * @param $node
     */
    public function initNodeAndAuditorStatus($request, $auditors, $node)
    {
        if ($node->type == Enums::WF_NODE_CC) {
            return ;
        }
        if (empty($auditors['sub_nodes'])) {
            // 一级节点
            $this->createFlowNodeAuditorRelate($request, $auditors['auditor_ids'], 0);
        } else {
            // 一级节点-子节点
            foreach ($auditors['sub_nodes'] as $sub_node_id => $sub_node) {
                if (!empty($sub_node['auditor_ids'])) {
                    $this->createFlowNodeAuditorRelate($request, $sub_node['auditor_ids'], $sub_node_id);
                }
            }
        }
    }

    /**
     * 获取财务分组审批人员
     * 说明: 节点审批人 为 财务人员配置的管理分组
     * @param $group_id
     * @param $department_id
     * @return array
     */
    public function getStaffManageListIds($group_id, $department_id)
    {
        if (empty($department_id) || empty($group_id)) {
            return [];
        }

        $staff_list = StaffManageListRelateModel::find([
            'conditions' => 'group_id = :group_id: AND  department_id = :department_id: AND is_deleted = :is_deleted:',
            'bind'       => ['group_id' => $group_id, 'department_id' => $department_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            'columns'    => ['staff_info_id']
        ])->toArray();
        return array_column($staff_list, 'staff_info_id');
    }

    /**
     * 创建节点/子节点-审批人关系
     * @param $request
     * @param $auditor_ids
     * @param $sub_node_id
     */
    public function createFlowNodeAuditorRelate($request, $auditor_ids, $sub_node_id = 0)
    {
        if (empty($auditor_ids)) {
            return ;
        }

        foreach ($auditor_ids as $auditor) {
            $node_auditor_model = WorkflowRequestNodeAuditorModel::findFirst([
                'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND sub_node_id = :sub_node_id: AND auditor_id = :auditor_id:',
                'bind' => [
                    'request_id' => $request->id,
                    'flow_node_id' => $request->current_flow_node_id,
                    'sub_node_id' => $sub_node_id,
                    'auditor_id' => $auditor,
                ],
            ]);


            if (empty($node_auditor_model)) {
                $node_auditor_model = new WorkflowRequestNodeAuditorModel();

                $node_auditor_model->request_id = $request->id;
                $node_auditor_model->flow_id = $request->flow_id;
                $node_auditor_model->biz_type = $request->biz_type;
                $node_auditor_model->biz_value = $request->biz_value;
                $node_auditor_model->flow_node_id = $request->current_flow_node_id;
                $node_auditor_model->sub_node_id = $sub_node_id;
                $node_auditor_model->creator_id = $request->create_staff_id;
                $node_auditor_model->auditor_id = $auditor;
                $node_auditor_model->audit_status = Enums::WF_STATE_PENDING;
                $node_auditor_model->created_at = date('Y-m-d H:i:s');
                $node_auditor_model->save();
            }
        }
    }

    /**
     * 更新节点/审批人 - 审批状态
     * @param $request // 当前业务审批对象
     * @param $current_sub_node_id // 当前子节点ID
     * @param $user // 当前审批人
     * @param $action // 当前审批行为
     * @param $is_auto // 是否自动同意
     * @return mixed
     */
    public function updateNodeAndAuditorStatus($request, $current_sub_node_id = 0, $user, $action, $is_auto = false)
    {
        if ($action == Enums::WF_ACTION_APPLY) {
            return ;
        }

        // 驳回/撤销: 针对一级节点
        $node_handle_actions = [
            Enums::WF_ACTION_REJECT,
            Enums::WF_ACTION_CANCEL
        ];
        if (in_array($action, $node_handle_actions)) {
            // 一级节点
            $request_node = WorkflowRequestNodeAuditorModel::find([
                'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id:',
                'bind' => [
                    'request_id' => $request->id,
                    'flow_node_id' => $request->current_flow_node_id,
                ]
            ]);
        } else {
            // 当前节点
            $request_node = WorkflowRequestNodeAuditorModel::find([
                'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND sub_node_id = :sub_node_id:',
                'bind' => [
                    'request_id' => $request->id,
                    'flow_node_id' => $request->current_flow_node_id,
                    'sub_node_id' => !empty($current_sub_node_id) ? $current_sub_node_id : 0
                ]
            ]);
        }

        switch ($action) {
            // 通过操作: 或签 或 会签待审批人/待审批子节点 <= 1
            case Enums::WF_ACTION_APPROVE:
                $audit_status = $is_auto ? Enums::WF_STATE_AUTO_APPROVED : Enums::WF_STATE_APPROVED;
                foreach ($request_node as $node_auditor) {
                    if ($node_auditor->audit_status != $audit_status) {
                        $node_auditor->audit_status = $audit_status;
                        $node_auditor->final_auditor_id = $user['id'];
                        $node_auditor->audit_at = date('Y-m-d H:i:s');
                        $node_auditor->save();
                    }
                }

                break;

            // 审批人会签通过: 待审批人 >= 2: 仅更新当前用户审批状态为通过
            case Enums::WF_ACTION_COUNTERSIGN_APPROVE:
                $audit_status = $is_auto ? Enums::WF_STATE_AUTO_APPROVED : Enums::WF_STATE_APPROVED;
                foreach ($request_node as $node_auditor) {
                    if ($node_auditor->auditor_id == $user['id']) {
                        $node_auditor->audit_status = $audit_status;
                        $node_auditor->final_auditor_id = $user['id'];
                        $node_auditor->audit_at = date('Y-m-d H:i:s');
                        $node_auditor->save();
                    }
                }

                break;

            // 子节点会签: 通过, 更新子节点的审批状态为通过
            case Enums::WF_ACTION_SUB_NODE_COUNTERSIGN_APPROVE:
                $audit_status = $is_auto ? Enums::WF_STATE_AUTO_APPROVED : Enums::WF_STATE_APPROVED;
                foreach ($request_node as $node_auditor) {
                    $node_auditor->audit_status = $audit_status;
                    $node_auditor->final_auditor_id = $user['id'];
                    $node_auditor->audit_at = date('Y-m-d H:i:s');
                    $node_auditor->save();
                }

                break;

            // 驳回/撤销操作: 将当前节点所有人的审批状态变更为驳回/撤销
            case Enums::WF_ACTION_REJECT:
            case Enums::WF_ACTION_CANCEL:
                $audit_status = $action == Enums::WF_ACTION_REJECT ? Enums::WF_STATE_REJECTED : Enums::WF_STATE_CANCEL;
                foreach ($request_node as $node_auditor) {
                    $node_auditor->audit_status = $audit_status;
                    $node_auditor->final_auditor_id = $user['id'];
                    $node_auditor->audit_at = date('Y-m-d H:i:s');
                    $node_auditor->save();
                }

                break;

            default:
        }

    }


    /**
     * 是否是泰国AP节点
     * @param $request
     * @return integer
     */
    public function isAfterApTH($request)
    {
        $item = WorkflowNodeModel::findFirst(
            [
                'conditions' => 'flow_id = :flow_id: and node_tag=' . Enums::WF_NODE_TAG_AP_LOCAL,
                'bind' => ['flow_id' => $request->flow_id]
            ]
        );

        //如果不存在，或者当前审批节点，小于AP泰国节点，则返回0
        if (empty($item) || $request->current_flow_node_id < $item->id) {
            return 0;
        }
        return 1;
    }

    /**
     * 找公司负责人clevel负责人group ceo 负责人
     * 需求
     * https://l8bx01gcjr.feishu.cn/docs/doccntBEZwfuSNxpFcNpxsn9HLe#dIedTi
     * 只找一个公司负责人>组织负责人>Group CEO
     *
     * @param $department_id
     * @return array|mixed
     */
    public function getManagerByDepartmentId($department_id)
    {
        $depart = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1 and deleted = 0',
            'bind'       => [
                1 => $department_id,
            ]
        ]);
        if (empty($depart)) {
            return [];
        }
        if (1 == $depart->type) {//公司
            $auditors = $this->getDepartmentManagerById($depart->company_id);
        }

        if ($depart->type != 1) {
            $ancestry_v3 = explode("/", $depart->ancestry_v3);
            $departments = SysDepartmentModel::find([
                'conditions' => 'id IN ({ids:array}) AND type IN (1,4,5) AND deleted = 0',
                'bind'       => ['ids' => $ancestry_v3],
                'columns'    => ['id', 'name', 'type', 'level', 'ancestry', 'company_id', 'manager_id'],
            ])->toArray();

            $manager_ids = array_column($departments, 'manager_id', 'type');


            if (isset($manager_ids[5])) {
                $auditors = $manager_ids[5];
            }

            if (isset($manager_ids[4])) {
                $auditors = $manager_ids[4];
            }
            if (isset($manager_ids[1])) {
                $auditors = $manager_ids[1];
            }
        }

        $staff = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1 AND state =1',
            'bind'       => [
                1 => $auditors
            ],
        ]);

        /**
         * 如果存在，且在职
         */
        if (!empty($staff) && $staff->state == 1) {
            return $auditors;
        }

        return [];
    }

    /**
     * 找跨部门负责人
     * https://l8bx01gcjr.feishu.cn/docs/doccndcrMiIDMQ1BhPEXJVw1orb
     * 根据申请人所属部门找申请人所属的一级部门，如果所属的一级部门不等于 预算所属部门，则需要预算所属部门的负责人审核
     *
     * @param array $info
     *
     * @return array
     */
    public function getCrossDepartmentId(array $info)
    {
        if (isset($info['sys_department_id']) && count($info['first_department']) == 1 && $info['first_department'][0] == $info['sys_department_id']) {
            return [];
        } else {
            return !empty($info['first_department_manager']) ? array_values($info['first_department_manager']) : [];
        }
    }

    /**
     * 获取审批流相关业务工单各审核状态的列表
     *
     * @param object $builder
     * @param int $workflow_process_state 审批处理标识 1-待处理 2-已处理 3-已征询
     * @param array $biz_type 业务类型
     * @param int $user_id 当前用户
     * @param string $biz_table_alias 业务表别名
     *
     * @return mixed
     */
    public function getBizWorkflowOrderListByProcessState(object $builder, int $workflow_process_state, array $biz_type, int $user_id = 0, string $biz_table_alias = 'main')
    {
        if (empty($biz_type) || empty($user_id)) {
            return $builder;
        }

        $builder->leftjoin(WorkflowRequestModel::class, "request.biz_value = {$biz_table_alias}.id", 'request');
        $builder->andWhere('request.biz_type IN ({biz_type:array}) AND request.is_abandon = :is_abandon:', [
            'biz_type' => $biz_type,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);

        // 待处理/征询中/征询已回复的公共取数条件[非已完成的]
        if ($workflow_process_state != GlobalEnums::AUDIT_TAB_PROCESSED) {
            $builder->leftjoin(WorkflowRequestNodeFyrMiddleModel::class, 'request.id = fyr_middle.request_id AND request.current_flow_node_id = fyr_middle.flow_node_id', 'fyr_middle');
            $builder->andWhere('request.state = :pending_state: AND FIND_IN_SET(:current_user_id:, request.current_node_auditor_id)', [
                'pending_state' => Enums::WF_STATE_PENDING,
                'current_user_id' => $user_id,
            ]);
        }

        // 当前用户的审批数据
        switch ($workflow_process_state) {
            // 待处理
            case GlobalEnums::AUDIT_TAB_PENDING:
                // 待审批 且 (未发起征询 或  审批不在征询相关审批人中)
                $builder->andWhere('fyr_middle.id IS NULL OR NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids)', ['current_user_id' => $user_id]);
                $builder->orderBy('request.updated_at ASC');
                break;

            // 征询中
            case GlobalEnums::AUDIT_TAB_CONSULTED:
                // 待审批 且 审批人在征询相关审批人中 但 不在回复相关审批人中
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids) AND NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                $builder->orderBy('fyr_middle.updated_at ASC');
                break;

            // 征询已回复
            case GlobalEnums::AUDIT_TAB_REPLIED:
                // 待审批 且 审批人在回复相关审批人中
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                $builder->orderBy('fyr_middle.updated_at ASC');
                break;

            // 已处理
            case GlobalEnums::AUDIT_TAB_PROCESSED:
                $builder->leftjoin(WorkflowAuditLogModel::class, 'request.id = log.request_id', 'log');
                $builder->andWhere('log.staff_id = :current_user_id:', ['current_user_id' => $user_id]);
                $builder->inWhere('log.audit_action', [Enums::WF_ACTION_APPROVE, Enums::WF_ACTION_REJECT, Enums::WF_ACTION_AUTO_APPROVE]);
                $builder->orderBy('log.audit_at DESC');
                break;
        }

        return $builder;
    }

    /**
     * 指定业务的待审批数统计
     *
     * @param array $biz_type 业务类型定义
     * @param int $user_id 当前用户
     * @param bool $is_fyr 是否含意见征询
     * @param int $workflow_process_state 审批处理标识 1-待处理 2-已处理 3-已征询
     *
     * @return mixed
     */
    public function getBizPendingCountByProcessState($biz_type = [], $user_id = 0, $is_fyr = true, int $workflow_process_state = 1)
    {
        if (empty($biz_type) || empty($user_id) || !in_array($workflow_process_state, GlobalEnums::AUDIT_TAB_PENDING_STATE_ITEM)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(DISTINCT request.id) AS total');
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->where('request.biz_type IN ({biz_type:array}) AND request.state = :pending_state: AND request.is_abandon = :is_abandon: AND FIND_IN_SET(:current_user_id:, request.current_node_auditor_id)', [
            'biz_type' => $biz_type,
            'pending_state' => Enums::WF_STATE_PENDING,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
            'current_user_id' => $user_id,
        ]);

        switch ($workflow_process_state) {
            // 待处理
            case GlobalEnums::AUDIT_TAB_PENDING:
                // 审批模块 含 意见征询流程的, 取数条件需结合征询相关审批人字段(若未对接征询流程, 则无需考虑)
                // 待审批 且 审批人不在征询相关审批人中的(即当前节点无相关人发起过征询)
                if ($is_fyr) {
                    // 待审批 且 (未发起征询 或  审批不在征询相关审批人中)
                    $builder->leftjoin(WorkflowRequestNodeFyrMiddleModel::class, 'request.id = fyr_middle.request_id AND request.current_flow_node_id = fyr_middle.flow_node_id', 'fyr_middle');
                    $builder->andWhere('fyr_middle.id IS NULL OR NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids)', ['current_user_id' => $user_id]);
                }
                break;

            // 征询中
            case GlobalEnums::AUDIT_TAB_CONSULTED:
                // 待审批 且 审批人在征询相关审批人中 但 不在回复相关审批人中
                $builder->leftjoin(WorkflowRequestNodeFyrMiddleModel::class, 'request.id = fyr_middle.request_id AND request.current_flow_node_id = fyr_middle.flow_node_id', 'fyr_middle');
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids) AND NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                break;

            // 征询已回复
            case GlobalEnums::AUDIT_TAB_REPLIED:
                // 待审批 且 审批人在回复相关审批人中
                $builder->leftjoin(WorkflowRequestNodeFyrMiddleModel::class, 'request.id = fyr_middle.request_id AND request.current_flow_node_id = fyr_middle.flow_node_id', 'fyr_middle');
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                break;
        }

        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 添加抄送信息(同时添加抄送的审批日志)
     *
     * @param $request
     * @param $node_auditors
     * @param $node
     * @return bool
     * @throws BusinessException
     */
    public function setNodeAuditorCc($request, $node_auditors, $node)
    {
        if (empty($node_auditors['auditor_ids']) || $node->type != Enums::WF_NODE_CC) {
            return true;
        }

        //申请人信息,通过  产品确认固化
        $apply_info = $this->getUserList([$request->create_staff_id])[0] ?? [];
        $apply_staff_id = $request->create_staff_id;
        $apply_staff_name = $apply_info['staff_name'] ?? '';
        $apply_department_id = $apply_info['department_id'] ?? 0;
        $apply_department_name = $apply_info['department_name'] ?? '';
        //摘要信息
        switch($request->biz_type) {
            case Enums::WF_STORE_RENTING_CONTRACT_TYPE ;   //网点房屋租赁合同
            case Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE ;   //网点房屋租赁合同-作废
            case Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE ;   //网点房屋租赁合同-终止
            case Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE ;   //网点房屋租赁合同-续签
                //查询租房合同信息,拼接摘要
                $contract_summary = ContractStoreRentingService::getInstance()->getSummary($request->biz_value);
                $summary = json_encode($contract_summary, JSON_UNESCAPED_UNICODE);
                break;
            case Enums::WF_LOAN_TYPE://借款
                //查询借款信息,拼接摘要
                $contract_summary = (new LoanFlowService())->getSummary($request->biz_value);
                $summary = json_encode($contract_summary, JSON_UNESCAPED_UNICODE);
                break;
            default :
                //产品提供个默认格式,比如申请单号等 产品确认没有摘要就空着
                $summary = '';
                break;
        }
        //抄送时间
        $now_datetime = date('Y-m-d H:i:s');
        $insert_data = $insert_log_data = [];
        foreach ($node_auditors['auditor_ids'] as $auditor) {
            $workflow_cc_info = WorkflowCarbonCopyModel::findFirst([
                'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND cc_staff_info_id = :cc_staff_info_id:',
                'bind' => [
                    'request_id' => $request->id,
                    'flow_node_id' => $node->id,
                    'cc_staff_info_id' => $auditor,
                ],
            ]);
            if (empty($workflow_cc_info)) {
                $insert_data[] = [
                    'request_id' => $request->id,
                    'biz_type' => $request->biz_type,
                    'biz_value' => $request->biz_value,
                    'flow_id' => $request->flow_id,
                    'flow_node_id' => $node->id,
                    'summary' => $summary,
                    'apply_staff_info_id' => $apply_staff_id,
                    'apply_staff_name' => $apply_staff_name,
                    'apply_department_id' => $apply_department_id,
                    'apply_department_name' => $apply_department_name,
                    'cc_staff_info_id' => $auditor,
                    'apply_at' => $request->created_at,
                    'carbon_copy_at' => $now_datetime,
                    'created_at' => $now_datetime,
                    'updated_at' => $now_datetime,
                    'status' => OAWorkflowEnums::WORKFLOW_CC_STATUS_UNREAD
                ];
            }
        }
        //批量写入
        $workflow_cc_model = new WorkflowCarbonCopyModel();
        if (!empty($insert_data) && $workflow_cc_model->batch_insert($insert_data) === false) {
            throw new BusinessException('抄送数据入库失败, data=' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        // 记录当前节点审核日志
        $this->saveAuditLog($request, 0, [], Enums::WF_ACTION_CARBON_COPY, null, false, $node_auditors['auditor_ids'], $node);

        return true;
    }

    /**
     * by 审批详情信息 和审批流
     * @param $params  参数 locale ｜ union_id ｜ type ｜ date_created
     * @param $user
     * @throws BusinessException
     * @return array
     */
    public function getAuditDetailFromBy($params, $user)
    {
        $local = $params['locale'] ?? 'zh-CN';
        $ac    = new ApiClient('by', '', 'getDetailForHcm', $local);
        $ac->setParams(
            [
                [
                    'id'           => $params['union_id'],//兼容旧数据 xxx_123
                    'staff_id'     => $user,//当前登陆人
                    'type'         => $params['type'] ?? 0,//审批类型
                    'isCommit'     => 2,            //1 我申请的, 2 我审批
                    'date_created' => $params['date_created'] ?? '',// 创建时间
                ],
            ]
        );
        $return = $ac->execute();
        $this->logger->info(sprintf("get_audit_detail == result: %s", json_encode($return)));
        if (!$return) {
            throw new BusinessException('Invalid return');
        }
        return $return['result'] ?? [];
    }

}
