<?php

namespace App\Modules\Deposit\Services;

use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Models\LedgerAccount;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Deposit\Models\DepositEditLogModel;
use App\Modules\Material\Services\WmsPlanService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\OrdinaryPayment\Services\BaseService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentFlowService;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\User\Models\AttachModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Library\Enums\DepositEnums;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentDetailService;
use App\Modules\Hc\Models\HrStaffInfoByModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Models\DepositLossModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Payment\Services\StoreRentingListService;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Modules\Budget\Services\BaseService as BudgetBaseService;
use App\Modules\Organization\Services\DepartmentService;
use App\Repository\DepartmentRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;


class DepositService extends BaseService
{

    private static $instance;

    /**
     * 构造函数
     * DepositService
     */
    private function __construct()
    {
    }

    /**
     * 单例
     * @return DepositService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 我的押金列表
     *
     * @Date 8/6/22 10:07 PM
     * @param array $params 查询条件
     * @param array $user 用户数据
     * @param int $type 类型
     * @return array
     * @author: peak pan
     */
    public function getList(array $params, array $user, int $type = 1)
    {
        $deposit_evn                  = EnumsService::getInstance()->getSettingEnvValueMap('deposit_business_budget_object_id_config');
        $params['deposit_budget_ids'] = $deposit_evn[$params['type']] ?? [];
        switch ($params['type']) {
            case DepositEnums::DEPOSIT_REIMBURSEMENT:
                //报销
                return ListService::getInstance()->getDepositList($params, $user, $type);
                break;
            case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                //普通付款
                return OrdinaryPaymentDetailService::getInstance()->getDepositList($params, $user, $type);
                break;
            case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                //采购
                return PaymentService::getInstance()->getDepositList($params, $user, $type);
                break;
            case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                //租房
                return StoreRentingListService::getInstance()->getDepositList($params, $user, $type);
                break;
        }
    }


    /**
     * 导出
     *
     * @Date 8/6/22 10:07 PM
     * @param array $params 查询条件
     * @param array $user 用户数据
     * @param int $limit_nub 条数限制
     * @return array
     * @throws GuzzleException
     * @author: peak pan
     */
    public function download(array $params, array $user, int $limit_nub)
    {
        $params['source'] = DepositEnums::IS_EXPORT;
        $type_all         = $params['type'] ?? 0;

        $reimburse_arr = [];
        $ordinary_arr  = [];
        $purchase_arr  = [];
        $payment_arr   = [];
        if ($params['type'] == DepositEnums::DEPOSIT_REIMBURSEMENT || empty($type_all)) {
            $params['type'] = DepositEnums::DEPOSIT_REIMBURSEMENT;
            $reimburse_arr  = $this->exportData($params, $user) ?? [];
        }
        if ($params['type'] == DepositEnums::DEPOSIT_ORDINARY_PAYMENT || empty($type_all)) {
            $params['type']     = DepositEnums::DEPOSIT_ORDINARY_PAYMENT;
            $params['type_all'] = $type_all;
            $ordinary_arr       = $this->exportData($params, $user) ?? [];
        }
        if ($params['type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT || empty($type_all)) {
            $params['type'] = DepositEnums::DEPOSIT_PURCHASE_PAYMENT;
            $purchase_arr   = $this->exportData($params, $user) ?? [];
        }
        if ($params['type'] == DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING || empty($type_all)) {
            $params['type'] = DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING;
            $payment_arr    = $this->exportData($params, $user) ?? [];
        }

        return $this->getDepositExport(array_filter(array_merge($reimburse_arr, $ordinary_arr, $purchase_arr,
            $payment_arr)), $limit_nub);
    }


    /**
     * 封装对象
     * @Date: 8/7/22 11:39 AM
     * @param array $data 查询条件
     * @return array
     * @throws ValidationException
     **@author: peak pan
     */
    public function getMainObj($data)
    {
        $ordinary_payment_obj = OrdinaryPayment::findFirst([
            'conditions' => 'apply_no = :apply_no:',
            'bind'       => ['apply_no' => $data['apply_no']],
        ]);
        if (empty($ordinary_payment_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }

        $payment_detail_obj = OrdinaryPaymentDetail::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['id']],
        ]);

        if (empty($payment_detail_obj->id)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }
        $deposit_money = bcsub(bcadd($payment_detail_obj->amount_no_tax, $payment_detail_obj->amount_vat, 3),
                $payment_detail_obj->amount_wht, 3) * 1000;//押金总金额 每行的不含税金额+vat金额-WHT金额

        // 申请人所属部门信息
        $ordinary_payment_obj->biz_apply_department_id   = $ordinary_payment_obj->apply_node_department_id;
        $ordinary_payment_obj->biz_apply_department_name = $ordinary_payment_obj->apply_node_department_name;

        return [$ordinary_payment_obj, $payment_detail_obj, $deposit_money];
    }

    /**
     * 封装对象
     * @Date: 8/7/22 11:39 AM
     * @param array $data 查询条件
     * @return array
     * @throws ValidationException
     **@author: peak pan
     */
    public function getReimbursementObj($data)
    {
        $main_obj = Reimbursement::findFirst([
            'conditions' => 'no = :apply_no:',
            'bind'       => ['apply_no' => $data['apply_no']],
        ]);

        if (empty($main_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }
        $detail_obj = Detail::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['id']],
        ]);
        if (empty($detail_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }
        $deposit_money = bcsub(bcadd($detail_obj->tax_not, $detail_obj->tax, 3), $detail_obj->wht_tax_amount,
            3);//押金总金额 每行的不含税金额+vat金额-WHT金额

        // 申请人所属部门信息
        $main_obj->biz_apply_department_id   = $main_obj->apply_department_id;
        $main_obj->biz_apply_department_name = $main_obj->apply_department_name;

        return [$main_obj, $detail_obj, $deposit_money];
    }

    /**
     * 封装对象
     * @Date: 8/7/22 11:39 AM
     * @param array $data 查询条件
     * @return array
     * @throws ValidationException
     **@author: peak pan
     */
    public function getPurchasePaymentObj($data)
    {
        $main_obj = PurchasePayment::findFirst([
            'conditions' => 'ppno = :apply_no:',
            'bind'       => ['apply_no' => $data['apply_no']],
        ]);
        if (empty($main_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }

        $detail_obj = PurchasePaymentReceipt::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['id']],
        ]);

        if (empty($detail_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }
        $deposit_money = floatval(bcsub($detail_obj->tax_total_price, $detail_obj->wht_amount,
            3));//押金总金额 每行的不含税金额-WHT金额

        // 申请人所属部门信息
        $main_obj->biz_apply_department_id   = $main_obj->create_department_id;
        $main_obj->biz_apply_department_name = $main_obj->create_department_name;

        return [$main_obj, $detail_obj, $deposit_money];
    }


    /**
     * 租房数据对象
     * @Date: 8/10/22 11:11 AM
     * @param array $data 查询条件
     * @return array
     * @throws ValidationException
     **@author: peak pan
     */
    public function getOrdinaryPaymentObj($data)
    {
        $main_obj = PaymentStoreRenting::findFirst([
            'conditions' => 'apply_no = :apply_no:',
            'bind'       => ['apply_no' => $data['apply_no']],
        ]);
        if (empty($main_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }

        $detail_obj = PaymentStoreRentingDetail::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['id']],
        ]);

        if (empty($detail_obj)) {
            throw new ValidationException(self::$t['repeatedly_apply_no_not_null'], ErrCode::$VALIDATE_ERROR);
        }
        $deposit_money = bcsub(bcadd($detail_obj->amount, $detail_obj->vat_amount, 3), $detail_obj->wht_amount,
                3) * 1000;//押金总金额 每行的不含税金额-WHT金额

        // 申请人所属部门信息
        $main_obj->biz_apply_department_id   = $main_obj->create_node_department_id;
        $main_obj->biz_apply_department_name = $main_obj->create_node_department_name;

        return [$main_obj, $detail_obj, $deposit_money];
    }


    /**
     * 根据账号获取用户详情
     * @Date: 8/1/22 4:48 PM
     * @param array $params 查询条件
     * @return array
     * @throws ValidationException
     **@author: peak pan
     */
    public function applyIdByName(array $params)
    {
        $code                = ErrCode::$SUCCESS;
        $message             = '';
        $deposit_staff_array = [];
        try {
            $type          = $params['type'] ?? 2;//类型：1原责任人 2新责任人；不填默认新责任人
            $deposit_staff = HrStaffInfoByModel::findFirst([
                'conditions' => 'staff_info_id =:staff_info_id: and  formal=:formal:',
                'bind'       => [
                    'staff_info_id' => $params['id'],
                    'formal'        => StaffInfoEnums::FORMAL_IN,
                ],
                'columns'    => 'staff_info_id,state,name,hire_type',
            ]);
            if (empty($deposit_staff)) {
                //数据库无此人员
                throw new ValidationException(self::$t['deposit_staff_not_user_info'], ErrCode::$VALIDATE_ERROR);
            }
            //新责任人逻辑自有
            if ($type == 2) {
                if ($deposit_staff->state != StaffInfoEnums::STAFF_STATE_IN) {
                    //该账号的人员已经离职或是停职
                    throw new ValidationException(self::$t['deposit_staff_job_stop_duty'], ErrCode::$VALIDATE_ERROR);
                }

                //19887需求 雇佣类型不包含个人代理、兼职个人代理
                if (in_array($deposit_staff->hire_type,
                    [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT])) {
                    throw new ValidationException(self::$t['deposit_staff_hire_type_invalid'],
                        ErrCode::$VALIDATE_ERROR);
                }
            }
            $deposit_staff_array = $deposit_staff->toArray();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('deposit_apply_id_by_name:' . $real_message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $deposit_staff_array,
        ];
    }

    /**
     * 获取详情
     * @Date: 7/29/22 2:34 PM
     * @param array $params 查询条件
     * @param array $user 用户数据
     * @param bool $detail_type 类型
     * @return array
     **@author: peak pan
     */
    public function getDetail(array $params, array $user, $detail_type = false)
    {
        $params['uid'] = $user['id'];
        if ($detail_type) {
            $deposit_array = DepositModel::findFirst([
                'conditions' => 'detail_id = :detail_id:',
                'bind'       => ['detail_id' => $params['id']],
            ]);

            if (empty($deposit_array)) {
                return [
                    'code'    => ErrCode::$VALIDATE_ERROR,
                    'message' => self::$t['cheque_account_empty_data'],
                    'data'    => [],
                ];
            } else {
                $params['type'] = $deposit_array->toArray()['deposit_type'];
            }
        }
        switch ($params['type']) {
            case DepositEnums::DEPOSIT_REIMBURSEMENT:
                //报销
                return ListService::getInstance()->depositDetail($params);
            case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                //普通付款
                return OrdinaryPaymentDetailService::getInstance()->depositDetail($params);
                break;
            case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                //采购
                return PaymentService::getInstance()->depositDetail($params);
                break;
            case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                //租房
                return StoreRentingListService::getInstance()->depositDetail($params);
                break;
        }
    }

    /**
     * 编辑
     *
     * @Date: 7/29/22 2:34 PM
     * @param array $params 查询条件
     * @param array $user 用户数据
     * @return array
     * @throws ValidationException
     * @author: peak pan
     */
    public function edit(array $params, array $user)
    {
        switch ($params['type']) {
            case DepositEnums::DEPOSIT_REIMBURSEMENT:
                //报销
                $detail_obj = Detail::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                    'columns'    => [
                        'id',
                        're_id',
                    ],
                ]);
                if (empty($detail_obj)) {
                    throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
                }

                $main_obj = Reimbursement::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $detail_obj->re_id],
                    'columns'    => [
                        'id',
                        'no AS biz_apply_no',
                        'apply_id AS biz_apply_id',
                        'apply_name AS biz_apply_name',
                        'apply_department_id AS biz_apply_department_id',
                        'apply_department_name AS biz_apply_department_name',
                    ],
                ]);

                break;
            case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                //普通付款
                $detail_obj = OrdinaryPaymentDetail::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                    'columns'    => [
                        'id',
                        'ordinary_payment_id',
                        'contract_no',
                    ],
                ]);
                if (empty($detail_obj)) {
                    throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
                }

                $main_obj = OrdinaryPayment::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $detail_obj->ordinary_payment_id],
                    'columns'    => [
                        'id',
                        'apply_no AS biz_apply_no',
                        'apply_id AS biz_apply_id',
                        'apply_name AS biz_apply_name',
                        'apply_node_department_id AS biz_apply_department_id',
                        'apply_node_department_name AS biz_apply_department_name',
                    ],
                ]);

                break;
            case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                //采购
                $detail_obj = PurchasePaymentReceipt::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                    'columns'    => [
                        'id',
                        'ppid',
                    ],
                ]);
                if (empty($detail_obj)) {
                    throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
                }

                $main_obj = PurchasePayment::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $detail_obj->ppid],
                    'columns'    => [
                        'id',
                        'contract_no',
                        'ppno AS biz_apply_no',
                        'create_id AS biz_apply_id',
                        'create_name AS biz_apply_name',
                        'create_department_id AS biz_apply_department_id',
                        'create_department_name AS biz_apply_department_name',
                    ],
                ]);

                break;
            case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                //租房
                $detail_obj = PaymentStoreRentingDetail::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                    'columns'    => [
                        'id',
                        'store_renting_id',
                        'contract_no',
                    ],
                ]);
                if (empty($detail_obj)) {
                    throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
                }

                $main_obj = PaymentStoreRenting::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $detail_obj->store_renting_id],
                    'columns'    => [
                        'id',
                        'apply_no AS biz_apply_no',
                        'create_id AS biz_apply_id',
                        'create_name AS biz_apply_name',
                        'create_node_department_id AS biz_apply_department_id',
                        'create_node_department_name AS biz_apply_department_name',
                    ],
                ]);

                break;
            default:
                throw new ValidationException(static::$t->_('params_error', ['param' => 'type-' . $params['type']]),
                    ErrCode::$VALIDATE_ERROR);
        }

        if (empty($main_obj)) {
            throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
        }

        return $this->depositEdit($params, $detail_obj, $main_obj, $user);
    }


    /**
     * 归还添加
     *
     * @Date: 8/6/22 3:22 PM
     * @param array $data 查询条件
     * @param array $user 用户数据
     * @return array
     * @author: peak pan
     */
    public function addApply(array $data, array $user)
    {
        $apply_no_fix = substr($data['apply_no'], 0, 2);
        $data['type'] = DepositEnums::$deposit_no_fix_arr[$apply_no_fix];
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $deposit_money = 0;
        $main_obj      = [];
        try {
            switch ($data['type']) {
                case DepositEnums::DEPOSIT_REIMBURSEMENT:
                    [$main_obj, $detail_obj, $deposit_money] = $this->getReimbursementObj($data);
                    break;
                case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                    [$main_obj, $detail_obj, $deposit_money] = $this->getMainObj($data);
                    break;
                case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                    [$main_obj, $detail_obj, $deposit_money] = $this->getPurchasePaymentObj($data);
                    break;
                case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                    [$main_obj, $detail_obj, $deposit_money] = $this->getOrdinaryPaymentObj($data);
                    break;
            }

            if ($data['bank_flow_date'] > date('Y-m-d', time())) {
                //银行流水日期 归还金额不能大于押金金额
                throw new ValidationException(self::$t['deposit_add_not_qe_bank_flow_date'], ErrCode::$VALIDATE_ERROR);
            }

            if ((bccomp($data['return_money'], $deposit_money, 2)) > 0) {
                //归还金额不能大于押金金额
                throw new ValidationException(self::$t['deposit_add_return_money_ge_deposit_money'],
                    ErrCode::$VALIDATE_ERROR);
            }

            if (bccomp(bcadd($data['loss_money'], $data['return_money'], 2), $data['deposit_money'], 2) != 0) {
                throw new ValidationException(self::$t['deposit_return_status_in_save_status_loss_money_err'],
                    ErrCode::$VALIDATE_ERROR);
            }

            if (bccomp(bcsub($deposit_money, $data['return_money'], 3), $data['loss_money'], 3) < 0) {
                //损失金额有误
                throw new ValidationException(self::$t['deposit_add_loss_money_err'], ErrCode::$VALIDATE_ERROR);
            }
            if (count($data['deposit_loss']) > 0) {
                $sum_deposit_loss = array_column($data['deposit_loss'], 'loss_money');
                $loss_money_add   = 0;
                foreach ($sum_deposit_loss as $loss_money) {
                    $loss_money_add = bcadd($loss_money_add, $loss_money, 2);
                }
                if (floatval($loss_money_add) != floatval($data['loss_money'])) {
                    throw new ValidationException(self::$t['deposit_add_loss_money_not_eq_err'],
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            if (count(array_unique(array_column($data['deposit_loss'], 'loss_organization_id'))) > 1) {
                throw new ValidationException(self::$t['deposit_add_loss_organization_id_err'],
                    ErrCode::$VALIDATE_ERROR);
            }
            //存在就是修改  不存在就是添加
            $deposit_model  = new DepositModel();
            $apply_data_obj = $deposit_model->findFirst(
                [
                    'conditions' => 'detail_id = :detail_id:',
                    'bind'       => ['detail_id' => $data['id']],
                ]
            );

            if (!empty($apply_data_obj->id) && !empty($apply_data_obj->return_money)) {
                //查询是否有撤销的
                $deposit_return_model = new DepositReturnModel();
                $deposit_return_obj   = $deposit_return_model->findFirst(
                    [
                        'conditions' => 'deposit_id = :deposit_id:',
                        'bind'       => ['deposit_id' => $apply_data_obj->id],
                    ]
                );

                if (empty($deposit_return_obj)) {
                    throw new ValidationException(self::$t['deposit_add_apply_is_empty'], ErrCode::$VALIDATE_ERROR);
                }
                $work_flow_request = workflowRequestModel::find(
                    [
                        'conditions' => 'biz_value =:biz_value: and biz_type=:biz_type:',
                        'bind'       => [
                            'biz_value' => $deposit_return_obj->id,
                            'biz_type'  => Enums::DEPOSIT_RETURN_BIZ_TYPE,
                        ],
                    ]
                )->toArray();

                if (empty($work_flow_request)) {
                    throw new ValidationException(self::$t['deposit_add_apply_is_empty'], ErrCode::$VALIDATE_ERROR);
                }

                $work_flow_ids = array_column($work_flow_request, 'id');

                $work_flow_logs = WorkflowAuditLogModel::find(
                    [
                        'conditions' => ' audit_action=:audit_action: and request_id in ({request_id:array}) ',
                        'bind'       => [
                            'request_id'   => $work_flow_ids,
                            'audit_action' => Enums::WF_ACTION_REJECT,
                        ],
                    ]
                )->toArray();
                if (empty($work_flow_logs)) {
                    throw new ValidationException(self::$t['deposit_add_apply_is_empty'], ErrCode::$VALIDATE_ERROR);
                }
            }

            if ($data['type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT || $data['type'] == DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING) {
                $apply_id   = $main_obj->create_id;
                $apply_name = $main_obj->create_name;
            } else {
                $apply_id   = $main_obj->apply_id;
                $apply_name = $main_obj->apply_name;
            }
            if (!empty($apply_data_obj) && $apply_data_obj->return_status != DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                throw new ValidationException(self::$t['deposit_return_status_not_err'], ErrCode::$VALIDATE_ERROR);
            }

            $apply_data['deposit_type']  = $data['type'];
            $apply_data['business_no']   = $data['apply_no'];                                                        //根据不同的类型主表code
            $apply_data['detail_id']     = $data['id'];                                                              //根据不同的类型 详情表id
            $apply_data['apply_id']      = !empty($apply_data_obj->apply_id) ? $apply_data_obj->apply_id : $apply_id;//负责人id
            $apply_data['return_status'] = DepositEnums::DEPOSIT_RETURN_STATUS_ING;                                  //归还状态  默认为归还

            $contract_no = $detail_obj->contract_no ?? '';
            if (!empty($apply_data_obj) && !empty($apply_data_obj->contract_no)) {
                $contract_no = $apply_data_obj->contract_no;
            } else {
                if ($data['type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT) {
                    $contract_no = $main_obj->contract_no ?? '';
                }
            }
            $apply_data['contract_no']   = $contract_no;                                                                   //合同编号
            $apply_data['apply_name']    = !empty($apply_data_obj->apply_name) ? $apply_data_obj->apply_name : $apply_name;//负责人姓名
            $apply_data['deposit_money'] = $deposit_money;
            $apply_data['loss_money']    = $data['loss_money'] * 1000;  //损失总金额
            $apply_data['return_money']  = $data['return_money'] * 1000;//归还总金额
            $apply_data['is_deleted']    = DepositEnums::DEPOSIT_IS_DELETED_YES;
            $apply_data['created_at']    = date('Y-m-d H:i:s', time());
            $apply_data['updated_at']    = date('Y-m-d H:i:s', time());

            if (!empty($apply_data_obj->id)) {
                //先添加的转交或是状态变更
                $bool          = $apply_data_obj->save($apply_data);
                $deposit_model = $apply_data_obj;
            } else {
                // 创建押金, 将申请人所属部门ID 和 部门名称固化到押金表
                $apply_data['apply_node_department_id']   = $main_obj->biz_apply_department_id ?? 0;
                $apply_data['apply_node_department_name'] = $main_obj->biz_apply_department_name ?? '';

                $bool = $deposit_model->create($apply_data);
            }

            if ($bool === false) {
                throw new BusinessException('押金管理-列表-归还-主表数据创建失败 = ' . json_encode(['apply_data' => $apply_data],
                        JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($deposit_model),
                    ErrCode::$ORDINARY_PAYMENT_CREATE_MAIN_ERROR);
            }

            //开始添加归还表
            $deposit_return_model = new DepositReturnModel();
            $deposit_return_obj   = $deposit_return_model->findFirst(
                [
                    'conditions' => 'deposit_id = :deposit_id:',
                    'bind'       => ['deposit_id' => $apply_data_obj->id],
                ]
            );

            if (empty($deposit_return_obj)) {
                $deposit_return      = $this->getDepositReturnFormatData($deposit_model, $data, $user);
                $deposit_return_bool = $deposit_return_model->create($deposit_return);
            } else {
                //驳回过 先删除之前的loss
                $deposit_loss_obj = DepositLossModel::find(
                    [
                        'conditions' => 'deposit_return_id = :deposit_return_id:',
                        'bind'       => ['deposit_return_id' => $deposit_return_obj->id],
                    ]
                );

                if (!empty($deposit_loss_obj->toArray())) {
                    $deposit_loss_del = $deposit_loss_obj->delete();
                    if ($deposit_loss_del === false) {
                        throw new BusinessException('删除之前的押金损失失败 ;' . $deposit_loss_obj->id . ' ;可能的原因是: ' . get_data_object_error_msg($deposit_loss_obj),
                            ErrCode::$DEPOSIT_SERVICE_ADD_LOSS_APPLY_ERROR);
                    }
                }
                $deposit_return       = $this->getDepositReturnFormatDataOne($deposit_model, $data, $user);
                $deposit_return_bool  = $deposit_return_obj->save($deposit_return);
                $deposit_return_model = $deposit_return_obj;
                $req                  = $this->getRequest($deposit_return_obj->id);
                if (empty($req)) {
                    throw new BusinessException("没有找到req=" . $deposit_return_obj->id);
                }
                //老的改成被遗弃
                $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
                $req->save();
            }

            if ($deposit_return_bool === false) {
                throw new BusinessException('押金管理-列表-归还-押金归还表数据创建失败： ' . json_encode($deposit_return,
                        JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($deposit_return_obj),
                    ErrCode::$DEPOSIT_SERVICE_ADD_APPLY_ERROR);
            }

            //押金损失表
            if (!empty($data['deposit_loss']) && array_sum(array_column($data['deposit_loss'], 'loss_money')) > 0) {
                $deposit_loss      = $this->getDepositLossFormatData($deposit_model, $deposit_return_model, $data);
                $deposit_loss_bool = (new DepositLossModel())->batch_insert($deposit_loss);

                if ($deposit_loss_bool === false) {
                    throw new BusinessException('押金管理-列表-归还-押金损失表数据创建失败： ' . json_encode($deposit_return,
                            JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($deposit_loss_bool),
                        ErrCode::$DEPOSIT_SERVICE_ADD_LOSS_APPLY_ERROR);
                }
            }
            $tmpList = [];
            // 删除历史关联附件
            $old_model = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:',
                'bind'       => [
                    'oss_bucket_type' => Enums::$oss_deposit_type_add[$data['type']]['mian'],
                    'oss_bucket_key'  => $deposit_model->id,
                ],
            ]);
            if (!empty($old_model->toArray())) {
                $deposit_attachment_del = $old_model->delete();
                if ($deposit_attachment_del === false) {
                    throw new BusinessException('删除之前的损失押金附件失败 ;' . $deposit_model->id . ' ;可能的原因是: ' . get_data_object_error_msg($old_model),
                        ErrCode::$DEPOSIT_SERVICE_DEPOSIT_EDIT_ATTACHMENT_ERROR);
                }
            }

            if (!empty($data['attachment'])) {
                $attach_bool = $this->addAttachments($data, Enums::$oss_deposit_type_add[$data['type']]['mian'],
                    $deposit_model->id);
                if ($attach_bool === false) {
                    throw new BusinessException('押金管理-列表-归还-归还详情附件创建失败： ' . json_encode($tmpList,
                            JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($attach_bool),
                        ErrCode::$DEPOSIT_SERVICE_ADD_APPLY_ATTACHMENT_ERROR);
                }
            }
            //预算
            if (!empty($data['deposit_loss']) && array_sum(array_column($data['deposit_loss'], 'loss_money')) > 0) {
                //报销预算 、采购预算 、普通付款预算
                $budgetServer = new BudgetService();
                foreach ($data['deposit_loss'] as &$deposit_loss_item) {
                    $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($main_obj->currency);
                    $exchange_rate = $exchange_rate ? $exchange_rate : 1;

                    $default_currency_amount         = EnumsService::getInstance()->amountExchangeRateCalculation(bcmul($deposit_loss_item['loss_money'],
                        1000), $exchange_rate, 0);
                    $deposit_loss_item['budget_id']  = $deposit_loss_item['loss_budget_id'];
                    $deposit_loss_item['level_code'] = $deposit_loss_item['level_code'];
                    $deposit_loss_item['amount']     = $default_currency_amount;
                }


                $check_budgets_data = [
                    'cost_store'      => $data['deposit_loss'][0]['loss_organization_id'],
                    'cost_store_type' => $data['deposit_loss'][0]['loss_organization_id'],
                    'cost_department' => $data['deposit_loss'][0]['loss_department_id'],
                ];

                if (in_array(DepositEnums::$deposit_reimbursement_mapping_order_type[$data['type']],
                    [DepositEnums::DEPOSIT_ORDINARY_PAYMENT, DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING])) {
                    $loss_organization_id = $data['deposit_loss'][0]['loss_organization_id'];
                    if ($data['deposit_loss'][0]['loss_organization_id'] == DepositEnums::ORGANIZATION_TYPE_COM) {
                        $loss_organization_id = Enums::HEAD_OFFICE_STORE_FLAG;
                    }
                    $check_budgets_data = [
                        'cost_store'      => $loss_organization_id,
                        'cost_store_type' => $loss_organization_id,
                        'cost_department' => $data['deposit_loss'][0]['loss_department_id'],
                    ];
                }

                $budgetServer->checkBudgets($deposit_return_model->return_code, $data['deposit_loss'],
                    DepositEnums::$deposit_reimbursement_mapping_order_type[$data['type']], $check_budgets_data, 1,
                    $user['id']);
            }
            //注入审批流
            $user['cost_company_id'] = $main_obj->cost_company_id;
            $flow_bool               = $this->createRequest($deposit_return_model, $user, $main_obj);
            if ($flow_bool === false) {
                throw new BusinessException('押金管理-列表-归还-归还详情' . '; 可能的原因是: ' . get_data_object_error_msg($flow_bool),
                    ErrCode::$DEPOSIT_SERVICE_ADD_APPLY_DEPOSIT_RETURN_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code   = ErrCode::$SUCCESS;
                $result = [
                    'message'   => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1,
                ];
            } else {
                $code = $e->getCode();
            }
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('deposit-create-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? [],
        ];
    }


    /**
     * 注入审批流
     * @Date: 8/6/22 11:55 AM
     * @param object $model 数据对象
     * @param array $user 用户数据
     * @param object $ordinary_payment_obj 用户数据
     * @return int|mixed
     * @throws BusinessException
     * @throws ValidationException
     **@author: peak pan
     */
    public function createRequest($model, $user, $ordinary_payment_obj)
    {
        $data['id']       = $model->id;
        $data['name']     = $model->return_code . '审批申请';
        $data['biz_type'] = Enums::DEPOSIT_RETURN_BIZ_TYPE;
        $data['flow_id']  = $this->getFlowId($model, $user);
        $data['currency'] = $ordinary_payment_obj->currency;

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($model, $user));
    }


    /**
     * 获取审批流
     * @Date: 8/6/22 11:59 AM
     * @param object $model 数据对象
     * @param array $user 用户数据
     * @return int
     **@author: peak pan
     */
    public function getFlowId($model = null, $user)
    {
        $company_ids = json_decode(EnvModel::getEnvByCode('deposit_cost_company_id_config'), true);

        if (in_array($user['cost_company_id'], $company_ids)) {
            if (empty($model->loss_money)) {
                $flow_id = Enums::DEPOSIT_FLOW_ID_IN_COST_COMPANY;
            } else {
                $flow_id = Enums::DEPOSIT_FLOW_ID_NOTIN_COST_COMPANY;
            }
        } else {
            if (empty($model->loss_money)) {
                $flow_id = Enums::DEPOSIT_FLOW_ID_INES_COST_COMPANY;
            } else {
                $flow_id = Enums::DEPOSIT_FLOW_ID_NOTINES_COST_COMPANY;
            }
        }
        return $flow_id;
    }

    /**
     * 组装审批流数据
     * @Date: 8/6/22 11:58 AM
     * @param object $model 数据对象
     * @param array $user 用户数据
     * @return array
     **@author: peak pan
     */
    public function getWorkflowParams($model, $user)
    {
        $user_info = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "bind"       => ['staff_info_id' => $model->create_id],
        ]);
        if (empty($user_info->id)) {
            return [];
        }
        $user_info       = $user_info->toArray();
        $cost_store_type = $user_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_COST_STORE_TYPE_01 : Enums::PAYMENT_COST_STORE_TYPE_02;
        return [
            'total_amount'       => $model->return_money,
            'submitter_id'       => $user['id'],                     // 申请人用，审批流创建人
            'department_id'      => $user_info['sys_department_id'], //申请人一级部门ID
            'node_department_id' => $user_info['node_department_id'],//申请人部门id
            'cost_store_type'    => $cost_store_type,                //1 :总部,2:网点
            'store_id'           => $user['sys_store_id'],           //申请人所属网点id
            'type'               => 0,
            'create_staff_id'    => $user['id'],//发起人工号
            'is_have_welfare'    => false,
        ];
    }


    /**
     * 归还数据格式化
     * @Date: 8/5/22 3:36 PM
     * @param object $deposit_model 数据对象
     * @param array $user 用户数据
     * @param array $data 用户数据
     * @return array
     * @author: peak pan
     */
    private function getDepositReturnFormatData($deposit_model, $data, $user)
    {
        $current_time = date('Y-m-d H:i:s', time());
        return [
            'deposit_type'       => $deposit_model->deposit_type,
            'deposit_id'         => $deposit_model->id,
            'create_id'          => $user['id'],
            'return_date'        => date('Y-m-d', time()),
            'status'             => DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
            'return_code'        => static::genSerialNo(DepositEnums::DEPOSIT_PREFIX, 'get_deposit_return_format_no'),
            'loss_money'         => $deposit_model->loss_money,
            'return_money'       => $deposit_model->return_money,
            'deposit_money'      => $deposit_model->deposit_money,
            'bank_flow_date'     => $data['bank_flow_date'],
            'return_info'        => $data['return_info'],
            'other_return_money' => !empty($data['other_return_money']) ? bcmul($data['other_return_money'], 1000) : 0,
            'other_return_info'  => $data['other_return_info'] ?? '',
            'is_deleted'         => GlobalEnums::IS_NO_DELETED,
            'created_at'         => $current_time,
            'updated_at'         => $current_time,
        ];
    }


    /**
     * 损失数据格式化
     * @Date: 8/5/22 3:36 PM
     * @param object $apply_data_obj 主表数据对象
     * @param object $deposit_return_bool 返还表对象
     * @param array $data 封装数据
     * @return array
     **@author: peak pan
     */

    private function getDepositLossFormatData($apply_data_obj, $deposit_return_bool, $data)
    {
        $loss_budget_ids        = array_values(array_filter(array_unique(array_column($data['deposit_loss'],
            'loss_budget_id'))));
        $loss_budget_id_by_code = $this->getBudgetIdsByCode($loss_budget_ids);
        $deposit_loss           = [];
        foreach ($data['deposit_loss'] as $loss_info) {
            if ($loss_info['loss_money'] > 0) {
                $deposit_loss[] = [
                    'id'                   => $loss_info['id'],
                    'deposit_id'           => $apply_data_obj->id,
                    'deposit_return_id'    => $deposit_return_bool->id,
                    'loss_money'           => $loss_info['loss_money'] * 1000,
                    'loss_bear_id'         => $loss_info['loss_bear_id'] ?? '',
                    'loss_department_id'   => $loss_info['loss_department_id'] ?? '',
                    'loss_organization_id' => $loss_info['loss_organization_id'] ?? '',
                    'loss_budget_id'       => $loss_info['loss_budget_id'] ?? '',
                    'level_code'           => $loss_budget_id_by_code[$loss_info['loss_budget_id']] ?? '',
                    'created_at'           => date('Y-m-d H:i:s', time()),
                    'updated_at'           => date('Y-m-d H:i:s', time()),
                ];
            }
        }
        return $deposit_loss;
    }


    /**
     * 批量添加附件
     * @Date: 8/5/22 4:57 PM
     * @param srting $oss_bucket_type 类型
     * @param srting $oss_bucket_key oss_bucket_key
     * @param array $data 封装数据
     * @return array
     **@author: peak pan
     */

    public function addAttachments($data, $oss_bucket_type, $oss_bucket_key)
    {
        if (!empty($data['attachment'])) {
            $attach  = new AttachModel();
            $tmpList = [];
            foreach ($data['attachment'] as $attachment) {
                $tmp                    = [];
                $tmp['oss_bucket_type'] = $oss_bucket_type;
                $tmp['oss_bucket_key']  = $oss_bucket_key;
                $tmp['sub_type']        = DepositEnums::EXPORT_TYPE_ZERO;
                $tmp['bucket_name']     = $attachment['bucket_name'];
                $tmp['object_key']      = $attachment['object_key'];
                $tmp['file_name']       = $attachment['file_name'];
                $tmpList[]              = $tmp;
            }
            $attach_bool = $attach->batchInsert($tmpList);
            return $attach_bool;
        }
    }


    /**
     * 获取申请创建页 - 基本信息 - 默认数据
     * @return array
     */
    public function getCreatePageBaseInfoDefaultData()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $deposit_models = DepositEnums::$deposit_arr;
            foreach ($deposit_models as &$item_) {
                $item_['label'] = static::$t->_($item_['label']);
            }
            $data['deposit_models'] = $deposit_models;

            $deposit_status = ContractEnums::$contract_status_arr;
            foreach ($deposit_status as &$item_status) {
                $item_status['label'] = static::$t->_($item_status['label']);
            }
            $data['deposit_status'] = $deposit_status;


            $deposit_return_status = DepositEnums::$contract_return_arr;
            foreach ($deposit_return_status as &$item_return_status) {
                $item_return_status['label'] = static::$t->_($item_return_status['label']);
            }
            $data['deposit_return_status'] = $deposit_return_status;

            //COO下的公司名称和id 费用所属公司
            $get_cost_company = (new PurchaseService())->getCooCostCompany();

            $data['cost_company'] = $get_cost_company;

            //损失承担方
            $data['loss_cost_company'] = array_merge(DepositEnums::$vendor_arr, $get_cost_company);

            //编辑枚举
            $deposit_edit_config = DepositEnums::$deposit_edit_edit;
            foreach ($deposit_edit_config as &$item_edit_config) {
                $item_edit_config['label'] = static::$t->_($item_edit_config['label']);
            }
            $data['deposit_edit_config'] = $deposit_edit_config;


            //网点和总部枚举
            $cost_store_list = DepositEnums::$cost_store_list;
            foreach ($cost_store_list as &$item_store_list) {
                $item_store_list['label'] = static::$t->_($item_store_list['label']);
            }
            $data['cost_store_list'] = $cost_store_list;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('get_create_page_base_info_default_data: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 获取申请创建页 - 合同列表
     * 获取归档合同中状态为已归档的所有合同列表
     * @param array $params
     * @return array
     */
    public function getContractList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];
        try {
            $data = $this->getArchiveContract($params['cno']);
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取申请创建页 - 合同列表数据异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 押金使用 获取科目树状列表封装数据
     * @Date: 8/3/22 8:39 PM
     * @param array $params 条件
     * @return array
     **@author: peak pan
     */
    public function getBudgetList($params)
    {
        //以前的定义  cost_store_type 含义：1-网点，2-总部，由于科目数据表中 网点和总部的value值正好和接口定义的相反，所以在此需要转换下
        return $this->budgetList([
            'cost_department' => $params['cost_department_id'],
            'cost_store_type' => $params['cost_store_type'],
        ]);
    }

    /**
     * 押金使用 获取科目树状列表
     * @Date: 8/3/22 8:39 PM
     * @param array $data 封装数据
     * @return array
     **@author: peak pan
     */
    protected function budgetList($data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        //查询所有预算科目
        $object_code        = [];
        $budget_object_code = [];
        $month              = date('Y-m');

        $budgetStatus = (new EnumsService())->getBudgetStatus();
        $name         = $this->getLangColumn(static::$language);
        // 当前国家是否开启预算
        if ($budgetStatus) {
            $budget_obj_data = BudgetObject::find([
                'conditions' => 'is_delete =:is_delete:',
                'bind'       => ['is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES],
            ])->toArray();
            //处理预算科目
            foreach ($budget_obj_data as $k => $v) {
                if ($v['is_budget'] == BudgetBaseService::IS_BUDGET) {
                    $budget_object_code[] = $v['level_code'];
                } else {
                    if ($v['is_budget'] == BudgetBaseService::IS_NO_BUDGET) {
                        $object_code[] = $v['level_code'];
                    }
                }
            }
            //根据预算科目 查询是否有预算
            $budget_object_str = "'" . implode("','", $budget_object_code) . "'";

            $budget_object_code = BudgetObjectDepartment::find([
                'conditions' => "department_id =:department_id: and object_code in ({$budget_object_str}) and organization_type = :organization_type: and  month ='{$month}'  and is_delete=:is_delete:",
                'bind'       => [
                    'department_id'     => $data['cost_department'],
                    'organization_type' => $data['cost_store_type'],
                    'is_delete'         => DepositEnums::DEPOSIT_IS_DELETED_YES,
                ],
                'columns'    => 'distinct(object_code) as object_code',
            ])->toArray();

            if (!empty($budget_object_code)) {
                $budget_object_code = array_column($budget_object_code, 'object_code');
            }

            //by 不展示不管控的预算
            if (!isset($user_info['source_type'])) {
                $object_codes = array_unique(array_merge($budget_object_code, $object_code));
            } else {
                $object_codes = $budget_object_code;
            }
            //没有预算科目列表为空
            if (empty($object_codes)) {
                return [];
            }
            //该部门 关联的所有 最底级别科目
            $builder = $this->modelsManager->createBuilder();
            //整理语言环境
            $name   = $this->getLangColumn(static::$language);
            $column = "distinct(o.level_code),o.id,o.level_code,o.{$name},o.name_en,o.template_type,o.bar_code,o.is_budget";
            $builder->columns($column);
            $builder->from(['o' => BudgetObject::class]);
            //默认不传code  取顶级
            $builder->andWhere('o.level = :object_level:', ['object_level' => DepositEnums::OBJECT_LEVEL]);

            $builder->inWhere('o.level_code', $object_codes);

            $budgets = $builder->getQuery()->execute()->toArray();
            $results = [];
            foreach ($budgets as $budget) {
                $tmp       = [
                    'id'            => (int)$budget['id'],
                    'level_code'    => $budget['level_code'],
                    'name'          => $budget[$name],
                    'name_en'       => $budget[$this->getLangColumn('en')],
                    'template_type' => $budget['template_type'],
                    'bar_code'      => $budget['bar_code'],
                    'is_budget'     => $budget['is_budget'],
                ];
                $results[] = $tmp;
            }
        } else {
            $results = BudgetObject::find([
                'condition' => 'is_delete=:is_delete:',
                'bind'      => ['is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES],
                'columns'   => "id,level_code,{$name} as name,template_type,bar_code",
            ])->toArray();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $results,
        ];
    }


    /**
     * 共用详情部分
     * @Date: 8/4/22 9:33 PM
     * @param object $main_model 主表对象
     * @param object $detail_model 详情对象
     * @param array $params 条件数据
     * @return array
     * @throws BusinessException
     * @author: peak pan
     */
    public function getDepositInfo($main_model, $detail_model, $params = [])
    {
        $deposit_model              = DepositModel::findFirst([
            'detail_id = :detail_id:',
            'bind' => ['detail_id' => $main_model->id],
        ]);
        $contract_no_status_by_name = [];
        if (in_array($params['type'], [DepositEnums::DEPOSIT_PURCHASE_PAYMENT])) {
            $contract_no = !empty($deposit_model->contract_no) ? $deposit_model->contract_no : $detail_model->contract_no;
        } else {
            $contract_no = !empty($deposit_model->contract_no) ? $deposit_model->contract_no : $main_model->contract_no;
        }

        $contract_no_arr = [];
        if (!empty($contract_no)) {
            $contract_no_arr = ContractArchive::findFirst([
                'conditions' => 'cno =:cno:',
                'bind'       => [
                    'cno' => $contract_no,
                ],
                'columns'    => 'cno,status',
            ]);
            if (!empty($contract_no_arr->cno)) {
                $contract_no_arr            = $contract_no_arr->toArray();
                $contract_no_status_by_name = [$contract_no_arr['cno'] => $contract_no_arr['status']];
            }
        }
        $wht_category_arr = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');

        $vat7_rate_list_arr = array_column(EnumsService::getInstance()->getFormatVatRateConfig(), 'label', 'value');

        // 网点租房付款费用类型枚举
        $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();


        $budget_id_arr = empty($main_model->budget_id) ? [] : DepositService::getInstance()->getBudgetObjectIdByName([$main_model->budget_id]);

        $data_ledger_account = LedgerAccount::findFirst(
            [
                'columns'    => 'id,account,name_en,name_cn,name_th',
                'conditions' => 'id =:id:',
                'bind'       => ['id' => $main_model->ledger_account_id],
            ]
        );

        $contract_status_text   = !empty($contract_no_arr['status']) && !empty(ContractEnums::$contract_archive_status[$contract_no_arr['status']]) ? static::$t[ContractEnums::$contract_archive_status[$contract_no_arr['status']]] : '';
        $data['payment_detail'] = [
            'contract_no'           => $contract_no,
            'contract_status'       => $contract_no_arr['status'] ?? '',
            'contract_status_name'  => !empty($contract_no_status_by_name[$contract_no]) ? static::$t[ContractEnums::$contract_archive_status[$contract_no_status_by_name[$contract_no]]] : $contract_status_text,
            'budget_id'             => $main_model->budget_id,
            //预算分类
            'budget_name'           => $budget_id_arr[$main_model->budget_id],
            'budget_name_en'        => '',
            'cost_category'         => !empty($main_model->product_id) ? $main_model->product_id : '',
            //明细分类
            'cost_category_child'   => '',
            //子明细分类
            'ledger_account_id'     => $data_ledger_account->name_en ?? '',
            //核算科目
            'cost_store_name'       => $main_model->cost_store_name,
            //费用所属网点和总部
            'cost_center_name'      => $main_model->cost_center_name,
            //费用所属中心
            'cost_start_date'       => $main_model->cost_start_date . ' - ' . $main_model->cost_end_date,
            //费用发生日期
            'amount_no_tax'         => !empty($main_model->amount_no_tax) ? $main_model->amount_no_tax : '0.00',
            //不含税金额 含wht
            'vat_rate'              => !empty($main_model->vat_rate) ? $main_model->vat_rate . '%' : '0%',
            //sst税率 vat
            'amount_vat'            => !empty($main_model->amount_vat) ? $main_model->amount_vat : '0.00',
            //vat税额
            'amount_have_tax'       => !empty($main_model->amount_have_tax) ? $main_model->amount_have_tax : '0.00',
            //含税金额
            'wht_category'          => !empty($main_model->wht_category) && !empty($wht_category_arr[$main_model->wht_category]) ? $wht_category_arr[$main_model->wht_category] : '/',
            //wht类别
            'wht_rate'              => !empty($main_model->wht_rate) ? $main_model->wht_rate . '%' : '0%',
            //wht税率
            'amount_wht'            => !empty($main_model->amount_wht) ? $main_model->amount_wht : '0.00',
            //wht税额
            'amount_total_actually' => bcsub(bcadd($main_model->amount_no_tax, $main_model->amount_vat, 3),
                $main_model->amount_wht, 2)
            //押金总金额 每行的不含税金额+vat金额-WHT金额,//实付金额
        ];

        switch ($params['type']) {
            case DepositEnums::DEPOSIT_REIMBURSEMENT:
                //报销
                $product_name = $product_name_en = '';
                if ($main_model->product_id) {
                    $products        = BudgetObjectProduct::find([
                        'conditions' => ' id =:id:',
                        'bind'       => ['id' => $main_model->product_id],
                    ])->toArray();
                    $products        = array_column($products, null, 'id');
                    $product_name    = isset($products) && isset($products[$main_model->product_id]) ? $products[$main_model->product_id]['name_' . strtolower(substr(static::$language,
                        -2))] : $data['product_name'];
                    $product_name_en = isset($products) && isset($products[$main_model->product_id]) ? $products[$main_model->product_id]['name_en'] : $data['product_name'];
                }
                $data['payment_detail']['cost_category']    = $product_name;
                $data['payment_detail']['cost_category_en'] = $product_name_en;

                //根据各个类型 不全数据
                $data['payment_detail']['cost_store_name'] = empty($main_model->cost_store_name) ? $detail_model->created_department_name : $main_model->cost_store_name;
                if ($detail_model->cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_02) {
                    //总部
                    $data['payment_detail']['cost_store_name'] = $detail_model->cost_department_name;
                }
                $data['payment_detail']['amount_no_tax'] = (string)bcdiv($main_model->amount_no_tax, 1000, 2);

                $data['payment_detail']['amount_have_tax'] = (string)bcdiv($main_model->amount_have_tax, 1000, 2);

                $data['payment_detail']['amount_total_actually'] = (string)bcdiv($data['payment_detail']['amount_total_actually'],
                    1000, 2);
                $data['payment_detail']['amount_vat']            = (string)bcdiv($main_model->amount_vat, 1000, 2);

                $data['payment_detail']['vat_rate'] = !empty($main_model->vat_rate) ? (string)floatval($main_model->vat_rate / 10) . '%' : '0%';
                if ($vat7_rate_list_arr[$main_model->vat_rate / 10]) {
                    $data['payment_detail']['vat_rate'] = !empty($main_model->vat_rate) ? (string)$vat7_rate_list_arr[$main_model->vat_rate / 10] : '0%';
                }
                $data['payment_detail']['wht_rate']        = !empty($main_model->wht_rate) ? $main_model->wht_rate . '%' : '0%';
                $data['payment_detail']['amount_wht']      = (string)bcdiv($main_model->amount_wht, 1000, 2);
                $data['payment_detail']['cost_start_date'] = $main_model->cost_start_date . ' - ' . $main_model->cost_end_date;//费用发生日期
                break;
            case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                //普通付款
                $product_name = $product_name_en = '';
                if ($main_model->product_id) {
                    $products        = BudgetObjectProduct::find([
                        'conditions' => ' id =:id:',
                        'bind'       => ['id' => $main_model->product_id],
                    ])->toArray();
                    $products        = array_column($products, null, 'id');
                    $product_name    = isset($products) && isset($products[$main_model->product_id]) ? $products[$main_model->product_id]['name_' . strtolower(substr(static::$language,
                        -2))] : $data['product_name'];
                    $product_name_en = isset($products) && isset($products[$main_model->product_id]) ? $products[$main_model->product_id]['name_en'] : $data['product_name'];
                }
                $data['payment_detail']['cost_store_name']  = $main_model->cost_store_name;
                $data['payment_detail']['cost_category']    = $product_name;
                $data['payment_detail']['cost_category_en'] = $product_name_en;
                break;
            case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                //采购
                $data['payment_detail']['cost_store_name'] = $main_model->cost_store_name;
                $data['payment_detail']['amount_no_tax']   = (string)bcdiv($main_model->amount_no_tax, 1000, 2);
                $data['payment_detail']['amount_have_tax'] = (string)bcdiv($main_model->amount_have_tax, 1000, 2);
                $data['payment_detail']['amount_vat']      = (string)bcdiv($data['payment_detail']['amount_vat'], 1000,
                    2);
                $data['payment_detail']['vat_rate']        = !empty($main_model->vat_rate) ? (string)floatval($main_model->vat_rate) . '%' : '0%';
                if ($vat7_rate_list_arr[$main_model->vat_rate]) {
                    $data['payment_detail']['vat_rate'] = !empty($main_model->vat_rate) ? (string)$vat7_rate_list_arr[$main_model->vat_rate] : '0%';
                }
                $data['payment_detail']['wht_rate']              = !empty($main_model->wht_rate) ? $main_model->wht_rate . '%' : '0%';
                $data['payment_detail']['amount_wht']            = (string)bcdiv($main_model->amount_wht, 1000, 2);
                $data['payment_detail']['amount_total_actually'] = bcdiv(bcsub($main_model->amount_have_tax,
                    $main_model->amount_wht, 2), 1000, 2);
                $data['payment_detail']['cost_start_date']       = '';                       //费用发生日期
                $data['payment_detail']['cost_category']         = $main_model->product_name;//明细分类
                break;
            case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                //租房
                $data['payment_detail']['cost_store_name'] = $main_model->store_name ?? '';
                $data['payment_detail']['vat_rate']        = !empty($main_model->vat_rate) ? (string)$main_model->vat_rate . '%' : '0%';
                if ($vat7_rate_list_arr[$main_model->vat_rate]) {
                    $data['payment_detail']['vat_rate'] = !empty($main_model->vat_rate) ? (string)$vat7_rate_list_arr[$main_model->vat_rate] : '0%';
                }
                $data['payment_detail']['amount_vat']   = empty($main_model->amount_vat) ? '0' : (string)floatval($main_model->amount_vat);
                $data['payment_detail']['wht_category'] = $main_model->wht_category ?? '';                                   //wht类别
                $data['payment_detail']['wht_rate']     = !empty($main_model->wht_rate) ? $main_model->wht_rate . '%' : '0%';//wht税率
                $data['payment_detail']['amount_wht']   = !empty($main_model->amount_wht) ? $main_model->amount_wht : '0.00';//wht税额
                $data['payment_detail']['budget_name']  = static::$t[$store_rent_payment_cost_enums[$main_model->budget_id]] ?? '';
                break;
        }
        if (!empty($deposit_model)) {
            $depositReturnArr = $deposit_model->getDepositReturn() ?? [];

            $attachments = AttachModel::find(
                [
                    'oss_bucket_key =:oss_bucket_key: and oss_bucket_type=:oss_bucket_type:',
                    'bind' => [
                        'oss_bucket_key'  => $deposit_model->id,
                        'oss_bucket_type' => Enums::$oss_deposit_type_add[$params['type']]['mian'],
                    ],
                ]
            )->toArray();

            $data['deposit']   = [
                'deposit_money'      => (string)$data['payment_detail']['amount_total_actually'],
                'return_money'       => (string)bcdiv($deposit_model->return_money, 1000, 2),
                'loss_money'         => (string)bcdiv($deposit_model->loss_money, 1000, 2),
                'return_info'        => $depositReturnArr->return_info,
                'bank_flow_date'     => $depositReturnArr->bank_flow_date,
                'other_return_money' => (string)bcdiv($depositReturnArr->other_return_money, 1000, 2),
                'other_return_info'  => $depositReturnArr->other_return_info ?? '',
                'attachment'         => $attachments,
                'deposit_return_id'  => $depositReturnArr->id,
            ];
            $depositEditLogArr = $deposit_model->getDepositEditLog()->toArray();
            if (!empty($depositEditLogArr)) {
                $attach_arr      = AttachModel::find(
                    [
                        'oss_bucket_key in({oss_bucket_key:array}) and oss_bucket_type=:oss_bucket_type:',
                        'bind' => [
                            'oss_bucket_key'  => array_column($depositEditLogArr, 'id'),
                            'oss_bucket_type' => Enums::$oss_deposit_type_add[$params['type']]['log'],
                        ],
                    ]
                )->toArray();
                $attach_arr_list = [];
                if (!empty($attach_arr)) {
                    $rs = [];
                    foreach ($attach_arr as $item_att) {
                        if (in_array($item_att['oss_bucket_key'], $rs)) {
                            $rs[$item_att['oss_bucket_key']][] = $item_att;
                        } else {
                            $rs[$item_att['oss_bucket_key']][] = $item_att;
                        }
                    }
                    $attach_arr_list = $rs;
                }
                foreach ($depositEditLogArr as &$log_arr) {
                    if ($log_arr['type'] == DepositEnums::DEPOSIT_EDIT_LOG_STATUS) {
                        $log_arr['before_data'] = !empty($log_arr['before_data']) ? static::$t[DepositEnums::$contract_return_list[$log_arr['before_data']]] : '';
                        $log_arr['after_data']  = !empty($log_arr['after_data']) ? static::$t[DepositEnums::$contract_return_list[$log_arr['after_data']]] : '';
                    }
                    $log_arr['type']       = static::$t[DepositEnums::$DEPOSIT_EDIT_LOG[$log_arr['type']]];
                    $log_arr['attachment'] = $attach_arr_list[$log_arr['id']] ?? [];
                    $log_arr['apply_id']   = $log_arr['created_id'] ?? '';
                    $log_arr['apply_name'] = $log_arr['created_name'] ?? '';
                }
            }
            $data['deposit_log'] = !empty($depositEditLogArr) ? $depositEditLogArr : [];

            $depositLossArr = DepositLossModel::find([
                'conditions' => 'deposit_return_id = :deposit_return_id:',
                'bind'       => ['deposit_return_id' => $depositReturnArr->id],
                'columns'    => 'id,loss_money,loss_bear_id,loss_department_id,loss_organization_id,loss_budget_id',
            ])->toArray();

            $deposit_loss_arr = !empty($depositLossArr) ? $depositLossArr : [];
            if (!empty($deposit_loss_arr)) {
                $loss_budget_ids        = array_values(array_filter(array_unique(array_column($deposit_loss_arr,
                    'loss_budget_id'))));
                $loss_budget_id_by_name = $this->getBudgetIdsByname($loss_budget_ids);
                foreach ($deposit_loss_arr as &$item_loss) {
                    $item_loss['loss_bear_id']         = $item_loss['loss_bear_id'];
                    $item_loss['loss_department_id']   = (int)$item_loss['loss_department_id'];
                    $item_loss['loss_organization_id'] = (int)$item_loss['loss_organization_id'];
                    $item_loss['loss_budget_id']       = (int)$item_loss['loss_budget_id'];
                    $item_loss['loss_budget_name']     = $loss_budget_id_by_name[$item_loss['loss_budget_id']] ?? '';
                    $item_loss['loss_money']           = (string)bcdiv($item_loss['loss_money'], 1000, 2);
                }
            }
            $data['deposit_loss'] = $deposit_loss_arr;
        } else {
            $data['deposit']      = [
                'deposit_money'      => (string)$data['payment_detail']['amount_total_actually'],
                'return_money'       => "",
                'loss_money'         => "",
                'return_info'        => "",
                'bank_flow_date'     => "",
                'other_return_money' => '0.00',
                'other_return_info'  => '',
                'attachment'         => [],
            ];
            $data['deposit_log']  = [];
            $data['deposit_loss'] = [];
        }

        if (!empty($data['deposit']['deposit_return_id'])) {
            $req               = $this->getRequest($data['deposit']['deposit_return_id']);
            $data['auth_logs'] = [];
            if (!empty($req)) {
                $data['auth_logs'] = (new WorkflowServiceV2())->getAuditLogs($req);
            }

            $data['can_edit']        = false;
            $data['can_edit_fields'] = (object)[];
            $can_edit_data           = (new OrdinaryPaymentFlowService())->getCanEditFieldByReq(
                $req,
                $params['uid']
            );
            $data['can_edit']        = $can_edit_data === false ? false : true;
            $data['can_edit_fields'] = $can_edit_data === false ? (object)[] : $can_edit_data;//待回复征询ID
            $ask                     = (new FYRService())->getRequestToByReplyAsk($req, $params['uid']);
            $data['ask_id']          = $ask ? $ask->id : '';
        }
        return $data;
    }

    /**
     * 编辑共用方法
     *
     * @Date: 8/6/22 3:53 PM
     * @param array $params 条件数据
     * @param object $detail_obj 明细行对象
     * @param object $main_obj 主表对象
     * @param array $user 用户数据
     * @return array
     * @author: peak pan
     */
    public function depositEdit(array $params, object $detail_obj, object $main_obj, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');

        $db->begin();
        try {
            //获取押金单信息
            $deposit = DepositModel::findFirst([
                'conditions' => 'detail_id = :detail_id:',
                'bind'       => ['detail_id' => $params['id']],
            ]);

            if (!empty($deposit)) {
                if (!in_array($deposit->return_status, [
                    DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
                    DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION,
                    DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE,
                ])) {
                    throw new ValidationException(static::$t->_('deposit_return_status_in_save_status'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            $this->logger->info([
                'deposit_edit_before_data' => $deposit ? $deposit->toArray() : [],
                'params'                   => $params,
            ]);

            $current_time = date('Y-m-d H:i:s', time());
            if ($params['edit_type'] == DepositEnums::DEPOSIT_EDIT_LOG_NO) {
                $data['contract_no'] = $params['contract_no'];
            } else {
                if ($params['edit_type'] == DepositEnums::DEPOSIT_EDIT_LOG_STATUS) {
                    if (!empty($deposit) && !in_array($params['return_status_id'], [
                            DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION,
                            DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE,
                        ])) {
                        throw new ValidationException(static::$t->_('deposit_return_status_in_save_intervention'),
                            ErrCode::$VALIDATE_ERROR);
                    }
                    $data['return_status'] = $params['return_status_id'];
                } else {
                    if ($params['edit_type'] == DepositEnums::DEPOSIT_EDIT_LOG_APPLY) {
                        $deposit_staff = HrStaffInfoByModel::findFirst([
                            'conditions' => 'staff_info_id =:staff_info_id: and  formal=:formal:',
                            'bind'       => [
                                'staff_info_id' => $params['new_apply_id'],
                                'formal'        => StaffInfoEnums::FORMAL_IN,
                            ],
                            'columns'    => 'staff_info_id,state,name,node_department_id',
                        ]);

                        if (empty($deposit_staff)) {
                            //数据库无此人员
                            throw new ValidationException(self::$t['deposit_staff_not_user_info'],
                                ErrCode::$VALIDATE_ERROR);
                        }

                        if ($deposit_staff->state != StaffInfoEnums::STAFF_STATE_IN) {
                            //该账号的人员已经离职或是停职
                            throw new ValidationException(self::$t['deposit_staff_job_stop_duty'],
                                ErrCode::$VALIDATE_ERROR);
                        }

                        $data['apply_id']   = $params['new_apply_id'];
                        $data['apply_name'] = $params['apply_name'];

                        // 新的押金负责人直属部门信息
                        $apply_node_department_id           = $deposit_staff->node_department_id ?? 0;
                        $deposit_node_department_info       = (new DepartmentRepository())->getDepartmentDetail($apply_node_department_id);
                        $data['apply_node_department_id']   = $apply_node_department_id;
                        $data['apply_node_department_name'] = $deposit_node_department_info['name'] ?? '';
                    }
                }
            }

            if (empty($deposit)) {
                //不存在添加
                $old_contract_no   = $detail_obj->contract_no ?? '';
                $old_return_status = DepositEnums::DEPOSIT_RETURN_STATUS_NOT;
                $old_apply_id      = $main_obj->biz_apply_id;
                $old_apply_name    = $main_obj->biz_apply_name;
                if ($params['edit_type'] == DepositEnums::DEPOSIT_EDIT_LOG_STATUS) {
                    if (!in_array($params['return_status_id'], [
                        DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION,
                        DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE,
                    ])) {
                        throw new ValidationException(static::$t->_('deposit_return_status_in_save_intervention'),
                            ErrCode::$VALIDATE_ERROR);
                    }
                }

                if ($params['type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT) {
                    $old_contract_no = $main_obj->contract_no;
                }

                $deposit               = new DepositModel();
                $data['deposit_type']  = $params['type'];
                $data['business_no']   = $main_obj->biz_apply_no;
                $data['detail_id']     = $detail_obj->id;
                $data['apply_id']      = $data['apply_id'] ? $data['apply_id'] : $old_apply_id;
                $data['return_status'] = $data['return_status'] ? $data['return_status'] : DepositEnums::DEPOSIT_RETURN_STATUS_NOT;
                $data['contract_no']   = $data['contract_no'] ? $data['contract_no'] : $old_contract_no;
                $data['apply_name']    = $data['apply_name'] ? $data['apply_name'] : $old_apply_name;
                $data['deposit_money'] = 0;
                $data['loss_money']    = 0;
                $data['return_money']  = 0;

                // 如果不是转交, 则将业务侧申请人部门ID和部门名称固化到押金侧
                if ($params['edit_type'] != DepositEnums::DEPOSIT_EDIT_LOG_APPLY) {
                    $data['apply_node_department_id']   = $main_obj->biz_apply_department_id ?? 0;
                    $data['apply_node_department_name'] = $main_obj->biz_apply_department_name ?? '';
                }

                $bool = $deposit->save($data);
                if ($bool === false) {
                    throw new BusinessException('押金管理-列表-编辑-不存在押金数据添加创建失败 = ' . json_encode(['deposit_data' => $data],
                            JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($deposit),
                        ErrCode::$DEPOSIT_SERVICE_DEPOSIT_EDIT_ERROR);
                }
            } else {
                //存在修改
                $data['updated_at'] = $current_time;
                $old_contract_no    = $deposit->contract_no;
                $old_return_status  = $deposit->return_status;
                $old_apply_id       = $deposit->apply_id;
                $deposit_bool       = $deposit->i_update($data);
                if ($deposit_bool === false) {
                    throw new BusinessException('押金编辑-提交 = ' . json_encode(['data' => $params],
                            JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($deposit),
                        ErrCode::$DEPOSIT_SERVICE_DEPOSIT_EDIT_ERROR);
                }
            }

            $this->logger->info(['deposit_edit_after_data' => $deposit->toArray()]);

            //成功开始添加日志
            $deposit_edit_log_model = new DepositEditLogModel();

            $apply_data['deposit_id'] = $deposit->id;
            $apply_data['type']       = $params['edit_type'];
            if ($apply_data['type'] == DepositEnums::DEPOSIT_EDIT_LOG_NO) {
                $apply_data['before_data'] = $old_contract_no;
                $apply_data['after_data']  = $params['contract_no'];
            } elseif ($apply_data['type'] == DepositEnums::DEPOSIT_EDIT_LOG_STATUS) {
                $apply_data['before_data'] = $old_return_status;
                $apply_data['after_data']  = $params['return_status_id'];
            } else {
                $apply_data['before_data'] = $old_apply_id;
                $apply_data['after_data']  = $params['new_apply_id'];
            }
            $apply_data['created_id']   = $user['id'];        //操作人id
            $apply_data['created_name'] = $user['name'] ?? '';//操作人姓名
            $apply_data['is_deleted']   = DepositEnums::DEPOSIT_IS_DELETED_YES;
            $apply_data['created_at']   = date('Y-m-d H:i:s', time());
            $bool                       = $deposit_edit_log_model->save($apply_data);
            if ($bool === false) {
                throw new BusinessException('押金管理-列表-编辑-添加日志创建失败 = ' . json_encode(['apply_data' => $apply_data],
                        JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($deposit_edit_log_model),
                    ErrCode::$ORDINARY_PAYMENT_CREATE_MAIN_ERROR);
            }

            if (!empty($params['attachment'])) {
                if (count($params['attachment']) > DepositEnums::ATTACHMENT_LAST_NUB_SIZE) {
                    throw new ValidationException(static::$t->_('deposit_up_max_attachment'), ErrCode::$VALIDATE_ERROR);
                }
                $attach_bool = $this->addAttachments($params, Enums::$oss_deposit_type_add[$params['type']]['log'],
                    $deposit_edit_log_model->id);
                if ($attach_bool === false) {
                    throw new BusinessException('押金管理-列表-归还-归还详情附件创建失败： ' . json_encode($params['attachment'],
                            JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($attach_bool),
                        ErrCode::$DEPOSIT_SERVICE_DEPOSIT_EDIT_ATTACHMENT_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('deposit_edit_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS,
        ];
    }


    /**
     * 付款数据处理
     *
     * @Date: 8/11/22 11:44 AM
     * @param array $condition 导出条件
     * @param array $user 条件数据
     * @return array
     * @author: peak pan
     */
    public function exportData(array $condition, array $user)
    {
        $return_data = [];

        try {
            $limit_size            = DepositEnums::DEPOSIT_DOWNLOAD_LIMIT;
            $condition['pageNum']  = DepositEnums::PAGE_NUM;
            $condition['pageSize'] = $limit_size;
            $list                  = $this->getList($condition, $user, DepositEnums::LIST_TYPE_EXPORT_DATA);

            $total_count = $list['data']['pagination']['total_count'] ?? 0;
            if ($total_count > $limit_size) {
                throw new ValidationException(static::$t->_('inventory_check_asset_download_limit_max'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $return_data = $list['data']['items'] ?? [];
        } catch (ValidationException $e) {
            $this->logger->notice('deposit_export_:' . $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error('deposit_export_:' . $e->getMessage() . $e->getTraceAsString());
        }

        return $return_data;
    }

    /**
     * 导出数据到xls
     *
     * @Date: 8/6/22 8:57 PM
     * @param array $row_values data
     * @param int $limit_size 条数据限制
     * @return array
     * @throws GuzzleException
     * @author: peak pan
     */
    public function getDepositExport(array $row_values, $limit_size = 0)
    {
        try {
            if (count($row_values) > $limit_size) {
                throw new ValidationException(static::$t->_('inventory_check_asset_download_limit_max'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $list         = [];
            $handled_keys = [];
            foreach ($row_values as $value) {
                // 同一单据, 仅首行展示具体金额, 其他行展示 0.00
                $handled_key = $value['apply_no'] . '_' . $value['detail_id'];
                if (!in_array($handled_key, $handled_keys)) {
                    $deposit_money      = $value['deposit_money'] ?: '0.00';
                    $return_money       = $value['return_money'] ?: '0.00';
                    $loss_money_return  = $value['loss_money_return'] ?: '0.00';
                    $other_return_money = $value['other_return_money'] ?: '0.00';
                } else {
                    $deposit_money      = '0.00'; // 押金总金额
                    $return_money       = '0.00'; // 转账归还金额
                    $loss_money_return  = '0.00'; // 损失总金额
                    $other_return_money = '0.00'; // 其他退款金额
                }

                $handled_keys[] = $handled_key;

                $list[] = [
                    $value['apply_no'],                    //申请单号
                    $value['apply_id'],                    //申请人工号
                    $value['apply_name'],                  //申请人姓名
                    $value['created_at'],                  //申请日期
                    $value['create_company_name'],         //费用所属公司
                    $value['cost_department_name'],        //费用所属部门
                    $value['cost_store_type'],             //费用所属网点/总部
                    $value['deposit_apply_name'],          //押金负责人
                    $value['deposit_node_department_name'],//押金负责部门
                    $value['contract_no'],                 //相关合同
                    $value['contract_status'],             //合同押金状态
                    $value['currency'],                    //币种
                    $value['budget_id'],                   //预算分类
                    $value['product_id'],                  //明细分类
                    $value['cost_store_name'],             //费用所属网点/总部
                    $value['cost_center_name'],            //费用所属中心
                    $value['cost_start_date'],             //费用发生期间
                    $value['amount_no_tax'],               //不含税金额（含WHT）
                    $value['vat_rate'],                    //SST税率
                    $value['amount_vat'],                  //SST税额
                    $value['amount_have_tax'],             //含税金额
                    $value['wht_category'],                //WHT类别
                    $value['wht_rate'],                    //WHT税率
                    $value['amount_wht'],                  //WHT税额
                    $value['sum_money'],                   //实付金额
                    $deposit_money,                        //押金总金额
                    $value['return_status'],               //押金归还状态
                    $return_money,                         //归还金额
                    $loss_money_return,                    //损失总金额
                    $other_return_money,                   //其他退款金额
                    $value['other_return_info'],           //其他退款金额说明
                    $value['bank_flow_date'],              //银行流水日期
                    $value['return_info'],                 //归还说明
                    $value['return_attachment'],           //归还详情附件
                    $value['loss_bear_id'],                //损失承担方
                    $value['loss_budget_id'],              //损失类型
                    $value['loss_department_id'],          //损失部门名称 网点/总部
                    $value['loss_money'],                  //损失金额
                ];
            }

            $header = [
                static::$t->_('deposit_apply_no'),                 //申请单号
                static::$t->_('deposit_apply_id'),                 //申请人工号
                static::$t->_('deposit_apply_name'),               //申请人姓名
                static::$t->_('deposit_created_at'),               //申请日期
                static::$t->_('deposit_create_company_name'),      //费用所属公司
                static::$t->_('deposit_cost_department_name'),     //费用所属部门
                static::$t->_('deposit_cost_store_type'),          //费用所属网点/总部
                static::$t->_('deposit_apply_name_apply_id'),      //押金负责人
                static::$t->_('deposit_node_department_name'),     //押金负责部门
                static::$t->_('deposit_contract_no'),              //相关合同
                static::$t->_('deposit_contract_no_status'),       //合同押金状态
                static::$t->_('deposit_currency'),                 //币种
                static::$t->_('deposit_budget_id'),                //预算分类
                static::$t->_('deposit_product_id'),               //明细分类
                static::$t->_('deposit_cost_store_type'),          //明细分类
                static::$t->_('deposit_cost_center_name'),         //费用所属中心
                static::$t->_('deposit_cost_start_date'),          //费用发生期间
                static::$t->_('deposit_amount_no_tax'),            //不含税金额（含WHT）
                static::$t->_('deposit_vat_rate'),                 //SST税率
                static::$t->_('deposit_amount_vat'),               //SST税额
                static::$t->_('deposit_amount_have_tax'),          //含税金额
                static::$t->_('deposit_wht_category'),             //WHT类别
                static::$t->_('deposit_wht_rate'),                 //WHT税率
                static::$t->_('deposit_amount_wht'),               //WHT税额
                static::$t->_('deposit_sum_money'),                //实付金额
                static::$t->_('deposit_lan_deposit_money'),        //押金总金额
                static::$t->_('deposit_lan_return_status'),        //押金归还状态
                static::$t->_('deposit_lan_return_money'),         //归还金额
                static::$t->_('deposit_lan_sum_loss_money'),       //损失总金额
                static::$t->_('deposit_other_return_money'),       //其他退款金额
                static::$t->_('deposit_other_return_info'),        //其他退款金额说明
                static::$t->_('deposit_lan_bank_flow_date'),       //银行流水日期
                static::$t->_('deposit_lan_return_info'),          //归还说明
                static::$t->_('deposit_lan_return_attachment'),    //附件
                static::$t->_('deposit_lan_loss_bear_name'),       //损失承担方
                static::$t->_('deposit_lan_loss_budget_name'),     //损失类型
                static::$t->_('deposit_lan_loss_department_name'), //网点/总部
                static::$t->_('deposit_lan_loss_money'),           //损失金额
            ];

            $file_name = 'deposit_export_' . date('YmdHis');
            return $this->exportExcel($header, $list, $file_name);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('deposit_export_:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => '',
        ];
    }

    /**
     * 普通付款审批 - 通过操作
     * @Date: 9/27/22 1:19 PM
     * @param int $id 审批通过的id
     * @param string $note 原因
     * @param array $user 用户数据
     * @param array $amount_detail 批量修改的数据
     * @return array
     * @author: peak pan
     **/
    public function approve($id, $note, $user, $amount_detail = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = DepositReturnModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);
            // 只有待审核的，方可驳回
            if ($main_model->status != Enums::PAYMENT_APPLY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('apply_apply_already_audit'),
                    ErrCode::$ORDINARY_PAYMENT_STATUS_CANCEL_ERROR);
            }
            $update_main_data = [];
            $can_edit         = (new OrdinaryPaymentFlowService())->getCanEditFieldByReq($work_req, $user['id']);

            $this->logger->info('押金规划 - 审批通过 - 主表更新前数据: ' . json_encode($main_model->toArray(),
                    JSON_UNESCAPED_UNICODE) . json_encode(['can_edit' => $can_edit, 'update_data' => $work_req]));

            // 审批
            $ws     = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($main_model, $user), $note);
            // 全部审批通过
            if (!empty($result->approved_at)) {
                $update_main_data['status']      = Enums::PAYMENT_APPLY_STATUS_APPROVAL;
                $update_main_data['approved_at'] = $result->approved_at;
            }
            // 补充业务主表数据
            $main_bool = null;
            if (!empty($update_main_data)) {
                // 更新业务主表数据
                $main_bool = $main_model->i_update($update_main_data);
                if ($main_bool === false) {
                    throw new BusinessException('押金归还 - 审核通过- 主表操作失败' . '; 可能的原因是: ' . get_data_object_error_msg($main_model),
                        ErrCode::$ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR);
                }
                $this->logger->info('押金归还 - 审批通过 - 更新后数据: ' . json_encode($main_model->toArray(),
                        JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($main_model));

                $deposit_model                        = DepositModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $main_model->deposit_id],
                ]);
                $update_deposit_data['return_status'] = DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE;
                $update_deposit_data['updated_at']    = $result->approved_at;
                $deposit_bool                         = $deposit_model->i_update($update_deposit_data);
                if ($deposit_bool === false) {
                    throw new BusinessException('押金归还 - 审核通过- 修改主表状态deposit失败' . '; 可能的原因是: ' . get_data_object_error_msg($deposit_bool),
                        ErrCode::$ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR);
                }
                $this->logger->info('押金归还 - 审批通过 - 主表更新后数据: ' . json_encode($update_deposit_data));
            }
            //批量修改
            if (!empty($amount_detail)) {
                // 更新业务主表数据
                $deposit_loss_up = [];
                foreach ($amount_detail as $deposit_loss) {
                    $deposit_loss_obj                = DepositLossModel::findFirst(
                        [
                            'id = :id:',
                            'bind' => ['id' => $deposit_loss['id']],
                        ]
                    );
                    $deposit_loss_up['loss_bear_id'] = $deposit_loss['loss_bear_id'];
                    $deposit_loss_up['updated_at']   = date('Y-m-d H:i:s', time());
                    $bool                            = $deposit_loss_obj->i_update($deposit_loss_up);
                    if ($bool === false) {
                        $messages = $deposit_loss_obj->getMessages();
                        throw new BusinessException('押金归还 - 审核通过- 批量操作损失承担方失败' . '; 可能的原因是: ' . get_data_object_error_msg($deposit_loss_obj) . $messages,
                            ErrCode::$ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR);
                    }
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('deposit-approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 押金归还申请 - 驳回
     * @Date: 9/27/22 1:23 PM
     * @param int $main_id 驳回 id
     * @param array $user 用户数据
     * @param string $note 驳回原因
     * @return array
     * @author: peak pan
     **/
    public function reject($main_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $work_req = $this->getRequest($main_id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = DepositReturnModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $main_id],
            ]);

            // 只有待审核的，方可驳回
            if ($main_model->status != Enums::PAYMENT_APPLY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('apply_apply_already_audit'),
                    ErrCode::$ORDINARY_PAYMENT_STATUS_CANCEL_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user,
                $this->getWorkflowParams($main_model, $user), $note);
            if ($result === false) {
                throw new BusinessException('押金归还申请-审批流驳回失败', ErrCode::$ORDINARY_PAYMENT_REJECT_ERROR);
            }

            $bool = $main_model->i_update([
                'status'        => Enums::PAYMENT_APPLY_STATUS_REJECTED,
                'updated_at'    => date('Y-m-d H:i:s', time()),
                'rejected_at'   => $result->rejected_at,
                'refuse_reason' => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException('押金归还- 申请驳回操作失败' . '; 可能的原因是: ' . get_data_object_error_msg($main_model),
                    ErrCode::$ORDINARY_PAYMENT_REJECT_MAIN_ERROR);
            }
            $deposit_model = DepositModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $main_model->deposit_id],
            ]);

            $bool_deposit = $deposit_model->i_update([
                'return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
                'updated_at'    => date('Y-m-d H:i:s', time()),
            ]);
            if ($bool_deposit === false) {
                throw new BusinessException('押金归还-主状态更新失败' . '; 可能的原因是: ' . get_data_object_error_msg($deposit_model),
                    ErrCode::$ORDINARY_PAYMENT_REJECT_MAIN_ERROR);
            }
            if ($main_model->loss_money > 0) {
                //释放占用的金额
                $this->freeBudget($main_id, $user);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('deposit-reject:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 获取审批流信息
     * @Date: 9/27/22 1:25 PM
     * @param int $biz_id 创建审批流业务id
     * @return object
     * @author: peak pan
     **/
    public function getRequest(int $biz_id)
    {
        return $this->getRequestByBiz($biz_id, Enums::DEPOSIT_RETURN_BIZ_TYPE);
    }

    /**
     * 获取当前业务审批流信息
     * @Date: 9/27/22 1:25 PM
     * @param int $id 创建审批流业务id去查工作流
     * @param int $bizType 类型
     * @return objec
     * @author: peak pan
     **/
    public function getRequestByBiz($id, $bizType)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id: and is_abandon=:is_abandon:',
                'bind'  => ['type' => $bizType, 'id' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
                'order' => 'id desc',
            ]
        );
    }

    /**
     * 根据语言查询对应的字段
     * @Date: 9/27/22 1:25 PM
     * @param string $lang 语言
     * @return array
     * @author: peak pan
     **/
    public function getLangColumn($lang = 'th')
    {
        $lang_arr = [
            'en'    => 'name_en',
            'th'    => 'name_th',
            'zh-CN' => 'name_cn',
            'zh'    => 'name_cn',
        ];
        return empty($lang_arr[$lang]) ? 'name_en' : $lang_arr[$lang];
    }


    /**
     * 导出数据获取附件并返回键和值
     * @Date: 8/11/22 8:24 PM
     * @param array $ids id集合
     * @param string $oss_bucket_type oss_bucket_type
     * @return array
     **@author: peak pan
     */
    public function getIdByUrlAttach($ids, $oss_bucket_type)
    {
        $attach_arr      = AttachModel::find(
            [
                'oss_bucket_key in({oss_bucket_key:array}) and oss_bucket_type=:oss_bucket_type:',
                'bind' => [
                    'oss_bucket_key'  => $ids,
                    'oss_bucket_type' => $oss_bucket_type,
                ],
            ]
        )->toArray();
        $attach_arr_list = [];
        if (!empty($attach_arr)) {
            $rs = [];
            foreach ($attach_arr as $item_att) {
                if (in_array($item_att['oss_bucket_key'], $rs)) {
                    $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                } else {
                    $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                }
            }
            $attach_arr_list = $rs;
        }

        return $attach_arr_list;
    }


    /**
     * 根据id获取损失类型集合
     * @Token
     * @Date: 8/12/22 8:21 PM
     * @param array $loss_budget_ids id集合
     * @return array
     **@author: peak pan
     */
    public function getBudgetIdsByname($loss_budget_ids)
    {
        $loss_budget_id_by_name = [];
        if (!empty($loss_budget_ids)) {
            $name                   = $this->getLangColumn(static::$language);
            $loss_budget_id_arr     = BudgetObject::find([
                'conditions' => ' id IN ({id:array}) and is_delete = :is_delete:',
                'bind'       => ['id' => $loss_budget_ids, 'is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES],
                'columns'    => "id,{$name} as name",
            ])->toArray();
            $loss_budget_id_by_name = array_column($loss_budget_id_arr, 'name', 'id');
        }
        return $loss_budget_id_by_name;
    }

    /**
     * 根据id获取损失code集合
     * @Token
     * @Date: 8/12/22 8:21 PM
     * @param array $loss_budget_ids id集合
     * @return array
     **@author: peak pan
     */
    public function getBudgetIdsByCode($loss_budget_ids)
    {
        $loss_budget_id_by_code = [];
        if (!empty($loss_budget_ids)) {
            $loss_budget_id_arr     = BudgetObject::find([
                'conditions' => ' id IN ({id:array}) and is_delete = :is_delete:',
                'bind'       => ['id' => $loss_budget_ids, 'is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES],
                'columns'    => 'id,level_code',
            ])->toArray();
            $loss_budget_id_by_code = array_column($loss_budget_id_arr, 'level_code', 'id');
        }
        return $loss_budget_id_by_code;
    }

    /**
     * 损失承担方集合
     * @Date: 8/12/22 8:34 PM
     * @return array
     **@author: peak pan
     */
    public function getCooCostCompany()
    {
        $coo_cost_arr = (new BudgetBaseService())->getCooCostCompany();
        return array_column($coo_cost_arr, 'cost_company_name', 'cost_company_id');
    }

    /**
     * 预算分类 导出数据
     * @Date: 9/27/22 1:29 PM
     * @param array $ids 预算分类id集合
     * @return bool array
     * @author: peak pan
     **/
    public function getBudgetObjectIdByName($ids)
    {
        $name    = $this->getLangColumn(static::$language);
        $results = BudgetObject::find(
            [
                'conditions' => " id IN ({id:array}) and is_delete = :is_delete:",
                'bind'       => ['id' => $ids, 'is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES],
                'columns'    => "id,{$name} as name",
            ])->toArray();
        if (!empty($results)) {
            return array_column($results, 'name', 'id');
        } else {
            return [];
        }
    }

    /**
     * 预算明细 导出数据
     * @Date: 9/27/22 1:29 PM
     * @param array $ids 预算分类id集合
     * @return bool array
     * @author: peak pan
     **/
    public function getBudgetObjectProductIdByName($ids)
    {
        $name    = $this->getLangColumn(static::$language);
        $results = BudgetObjectProduct::find([
            'conditions' => " id IN ({id:array}) and is_delete = :is_delete:",
            'bind'       => ['is_delete' => DepositEnums::DEPOSIT_IS_DELETED_YES, 'id' => $ids],
            'columns'    => "id,{$name} as name",
        ])->toArray();
        if (!empty($results)) {
            return array_column($results, 'name', 'id');
        } else {
            return [];
        }
    }


    /**
     * 脚本数据处理 发送邮件 报销数据查询
     * @Date: 8/22/22 11:16 AM
     * @return array
     * @author: peak pan
     */
    public function getReimbursementToEmailData()
    {
        try {
            $deposit_evn = json_decode(EnvModel::getEnvByCode('deposit_business_budget_object_id_config'), true);
            $columns     = ['re.no as apply_no', 're.cost_department', 'de.return_status'];
            $builder     = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['red' => Detail::class]);
            $builder->leftjoin(Reimbursement::class, 're.id = red.re_id', 're');
            $builder->leftjoin(DepositModel::class, 'de.detail_id = red.id', 'de');
            $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            $builder->inWhere('red.budget_id', $deposit_evn[DepositEnums::DEPOSIT_REIMBURSEMENT]);
            $builder->andWhere('re.pay_status=:pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_PAY]);
            $builder->andWhere('((de.return_status = :return_status:) or (de.return_status is null) or( de.return_status = ""))',
                ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT]);
            if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                $builder->andWhere('re.pay_at >= :pay_at:', ['pay_at' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
            }

            $builder->groupBy('dl.id,red.id');
            $items         = $builder->getQuery()->execute()->toArray();
            $rs_email_send = [];
            if (!empty($items)) {
                $rs = [];
                foreach ($items as $item_info) {
                    if (empty($item_info['return_status']) || $item_info['return_status'] == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                        $rs[$item_info['cost_department']][] = $item_info['apply_no'];
                    }
                }
                $cost_department = [];
                foreach ($rs as $key => $rs_cost_department) {
                    $email = $this->getDepartmentToEmail($key);
                    if (!empty($email)) {
                        $cost_department['department'] = $key;
                        $cost_department['email']      = $email;
                        $cost_department['apply_no']   = $rs_cost_department;
                        $rs_email_send[]               = $cost_department;
                    }
                }
            }
            return $rs_email_send;
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }


    /**
     * 脚本数据处理  根据部门查找负责人的邮箱
     * @Date: 10/12/22 9:30 AM
     * @param int $cost_department_id 部门id
     * @return bool array
     * @author: peak pan
     **/
    public function getDepartmentToEmail($cost_department_id)
    {
        $manager_id_arr = DepartmentModel::findFirst([
            'conditions' => ' id = :id:',
            'bind'       => ['id' => $cost_department_id],
        ]);

        if (empty($manager_id_arr) || empty($manager_id_arr->manager_id)) {
            return '';
        }

        $email_arr = HrStaffInfoByModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id: and  state =:state:',
            'bind'       => ['staff_info_id' => $manager_id_arr->manager_id, 'state' => StaffInfoEnums::STAFF_STATE_IN],
        ]);

        if (empty($email_arr)) {
            return '';
        }
        return $email_arr->email;
    }


    /**
     * 发送邮件 普通付款数据查询封装数据
     * @Date: 8/22/22 11:55 AM
     * @return array
     * @author: peak pan
     */
    public function getOrdinaryPaymentToEmailData()
    {
        try {
            $deposit_evn = json_decode(EnvModel::getEnvByCode('deposit_business_budget_object_id_config'), true);
            $columns     = ['op.apply_no', 'op.cost_department_id', 'de.return_status'];
            $builder     = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['opd' => OrdinaryPaymentDetail::class]);
            $builder->leftjoin(OrdinaryPayment::class, 'op.id = opd.ordinary_payment_id', 'op');
            $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
            $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            $builder->inWhere('opd.budget_id', $deposit_evn[DepositEnums::DEPOSIT_REIMBURSEMENT]);
            $builder->andWhere('op.pay_status=:pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_PAY]);
            $builder->andWhere('((de.return_status = :return_status:) or (de.return_status is null) or( de.return_status = ""))',
                ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT]);
            if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                $builder->leftjoin(OrdinaryPaymentExtend::class, 'op.id = ope.ordinary_payment_id', 'ope');
                $builder->andWhere('ope.pay_bk_flow_date >= :pay_bk_flow_date:',
                    ['pay_bk_flow_date' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
            }
            $builder->groupBy('dl.id,opd.id');
            $items         = $builder->getQuery()->execute()->toArray();
            $rs_email_send = [];
            if (!empty($items)) {
                $rs = [];
                foreach ($items as $item_info) {
                    if (empty($item_info['return_status']) || $item_info['return_status'] == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                        $rs[$item_info['cost_department_id']][] = $item_info['apply_no'];
                    }
                }
                $cost_department = [];
                foreach ($rs as $key => $rs_cost_department) {
                    $email = $this->getDepartmentToEmail($key);
                    if (!empty($email)) {
                        $cost_department['department'] = $key;
                        $cost_department['email']      = $email;
                        $cost_department['apply_no']   = $rs_cost_department;
                        $rs_email_send[]               = $cost_department;
                    }
                }
            }

            return $rs_email_send;
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }


    /**
     * 发送邮件 采购数据查询
     * @Date: 8/22/22 11:55 AM
     * @return  array
     **@author: peak pan
     */
    public function getPurchasePaymentToEmailData()
    {
        try {
            $deposit_evn = json_decode(EnvModel::getEnvByCode('deposit_business_budget_object_id_config'), true);
            $columns     = ['op.ppno', 'op.cost_department', 'de.return_status'];
            $builder     = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['opd' => PurchasePaymentReceipt::class]);
            $builder->leftjoin(PurchasePayment::class, 'op.id = opd.ppid', 'op');
            $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
            $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            $builder->inWhere('opd.budget_id', $deposit_evn[DepositEnums::DEPOSIT_REIMBURSEMENT]);
            $builder->andWhere('op.pay_status=:pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
            $builder->andWhere('((de.return_status = :return_status:) or (de.return_status is null) or( de.return_status = ""))',
                ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT]);
            if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                $builder->andWhere('op.real_pay_at >= :real_pay_at:',
                    ['real_pay_at' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
            }
            $builder->groupBy('dl.id,opd.id');
            $items         = $builder->getQuery()->execute()->toArray();
            $rs_email_send = [];
            if (!empty($items)) {
                $rs = [];
                foreach ($items as $item_info) {
                    if (empty($item_info['return_status']) || $item_info['return_status'] == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                        $rs[$item_info['cost_department']][] = $item_info['ppno'];
                    }
                }
                $cost_department = [];

                foreach ($rs as $key => $rs_cost_department) {
                    $email = $this->getDepartmentToEmail($key);
                    if (!empty($email)) {
                        $cost_department['department'] = $key;
                        $cost_department['email']      = $email;
                        $cost_department['apply_no']   = $rs_cost_department;
                        $rs_email_send[]               = $cost_department;
                    }
                }
            }
            return $rs_email_send;
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }

    /**
     *  发送邮件 租房数据查询
     * @Date: 10/12/22 9:30 AM
     * @param int $status 状态分类 1表示当月查询 其他表示每天查
     * @return bool array
     * @author: peak pan
     **/

    public function getPaymentStoreRentingToEmailData($status = 1)
    {
        try {
            $deposit_evn = json_decode(EnvModel::getEnvByCode('deposit_business_budget_object_id_config'), true);
            if ($status == DepositEnums::DEPOSIT_TASK_TYPE_STATUS_DAY) {
                $columns = ['op.apply_no', 'op.cost_department_id', 'de.return_status'];
                $builder = $this->modelsManager->createBuilder();
                $builder->columns($columns);
                $builder->from(['opd' => PaymentStoreRentingDetail::class]);
                $builder->leftjoin(PaymentStoreRenting::class, 'op.id = opd.store_renting_id', 'op');
                $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
                $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
                $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
                if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                    $builder->leftjoin(PaymentStoreRentingPay::class, 'op.id = srp.store_renting_id', 'srp');
                    $builder->andWhere('srp.pay_date >= :pay_date:', ['pay_date' => DepositEnums::DEPOSIT_PAY_AT]);
                }
                $builder->andWhere('opd.cost_type in({cost_type:array}) and op.pay_status=:pay_status:', [
                    'cost_type'  => $deposit_evn[DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING],
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY,
                ]);
                $builder->andWhere('((de.return_status = :return_status:) or (de.return_status is null) or( de.return_status = ""))',
                    ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT]);
                $builder->groupBy('dl.id,opd.id');
                $items = $builder->getQuery()->execute()->toArray();
            } elseif ($status == DepositEnums::DEPOSIT_TASK_TYPE_STATUS_MONTH) {
                $columns = [
                    'psrd.id as apply_no',
                    'psr.cost_department_id',
                    'de.return_status',
                    'de.business_no',
                    'psrd.contract_no',
                    'de.contract_no as contract_no_b',
                    'csr.contract_end',
                ];
                $builder = $this->modelsManager->createBuilder();
                $builder->columns($columns);
                $builder->from(['psrd' => PaymentStoreRentingDetail::class]);
                $builder->leftjoin(PaymentStoreRenting::class, 'psr.id = psrd.store_renting_id', 'psr');
                $builder->leftjoin(DepositModel::class, 'de.detail_id = psrd.id', 'de');
                $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
                $builder->leftjoin(ContractStoreRentingModel::class,
                    'csr.contract_id = psrd.contract_no and csr .contract_id != "" ', 'csr');
                $builder->leftjoin(ContractArchive::class, 'ca.cno=csr.contract_id', 'ca');
                if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                    $builder->leftjoin(PaymentStoreRentingPay::class, 'psr.id = srp.store_renting_id', 'srp');
                    $builder->andWhere('srp.pay_date >= :pay_date:', ['pay_date' => DepositEnums::DEPOSIT_PAY_AT]);
                }
                $builder->inWhere('psrd.cost_type', $deposit_evn[DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING]);
                $builder->andWhere('(de.return_status = :return_status:) or (de.return_status is null) or( de.return_status = "")',
                    ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT]);
                $builder->andWhere(' psr.pay_status=:pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
                $builder->groupBy('psrd.id');
                $items = $builder->getQuery()->execute()->toArray();
            }

            if (!empty($items)) {
                $rs = [];
                foreach ($items as $item_info) {
                    if (empty($item_info['return_status']) || $item_info['return_status'] == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                        $rs_array[]                             = $item_info;
                        $rs[$item_info['cost_department_id']][] = $item_info['apply_no'];
                    }
                }
                echo date('Ymd H:i:s') . " 未归还数据" . json_encode($rs_array) . PHP_EOL;

                if ($status == DepositEnums::DEPOSIT_TASK_TYPE_STATUS_MONTH) {
                    $contract = [];
                    foreach ($rs_array as $rs_item) {
                        if (!empty($rs_item['contract_no']) || !empty($rs_item['contract_no_b'])) {
                            $contract[] = !empty($rs_item['contract_no_b']) ? $rs_item['contract_no_b'] : $rs_item['contract_no'];
                        }
                    }

                    echo date('Ymd H:i:s') . " 未归还数据对应的合同号" . json_encode($contract) . PHP_EOL;
                    //过期时间
                    $contract_code = [];
                    if (!empty($contract)) {
                        $contract_end_arr = [];
                        $columns_         = ['csr.contract_end,csr.contract_id,ca.status,ca.terminal_at'];
                        $builder_sun      = $this->modelsManager->createBuilder();
                        $builder_sun->columns($columns_);
                        $builder_sun->from(['csr' => ContractStoreRentingModel::class]);
                        $builder_sun->leftjoin(ContractArchive::class, 'csr.contract_id = ca.cno', 'ca');
                        $builder_sun->andWhere('csr.contract_id in({contract_id:array})',
                            ['contract_id' => array_values($contract)]);
                        $contract_items = $builder_sun->getQuery()->execute();

                        if (!empty($contract_items)) {
                            $contract_end_arr = $contract_items->toArray();
                        }
                        $new_date = date('Y-m-d', strtotime(date('Y-m-d', time())) - 86400 * 7);

                        foreach ($contract_end_arr as $contract_end_at) {
                            if ($contract_end_at['contract_end'] <= date('Y-m-d',
                                    time()) || in_array($contract_end_at['status'], [
                                    ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
                                    ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL,
                                ])) {
                                if ((!empty($contract_end_at['contract_end']) && $contract_end_at['contract_end'] <= $new_date) || (!empty($contract_end_at['terminal_at']) && $contract_end_at['terminal_at'] <= $new_date)) {
                                    $contract_code[] = $contract_end_at;
                                }
                            }
                        }

                        echo date('Ymd H:i:s') . " 未归还数据对应的合同号去除符合条件的合同号" . json_encode($contract_code) . PHP_EOL;
                    }

                    $new_contract = array_values(array_filter(array_column($contract_code, 'contract_id')));
                    $rt           = [];
                    foreach ($rs_array as $contract_) {
                        if (!empty($contract_['contract_no']) || !empty($contract_['contract_no_b'])) {
                            $contract_s = !empty($contract_['contract_no_b']) ? $contract_['contract_no_b'] : $contract_['contract_no'];

                            if (in_array($contract_s, $new_contract)) {
                                $email = $this->getDepartmentToEmail($contract_['cost_department_id']);
                                if (!empty($email)) {
                                    $cost_department_no['email']      = $email;
                                    $cost_department_no['apply_no']   = $contract_['apply_no'];
                                    $cost_department_no['department'] = $contract_['cost_department_id'];
                                    $rt[]                             = $cost_department_no;
                                }
                            }
                        }
                    }
                    echo date('Ymd H:i:s') . " 未归还数据对应的合同号去除符合条件的部门数据" . json_encode($rt) . PHP_EOL;
                    return $rt;
                } else {
                    $cost_department = [];
                    $rs_email_send   = [];
                    foreach ($rs as $key => $rs_cost_department) {
                        $email = $this->getDepartmentToEmail($key);
                        if (!empty($email)) {
                            $cost_department['department'] = $key;
                            $cost_department['email']      = $email;
                            $cost_department['apply_no']   = $rs_cost_department;
                            $rs_email_send[]               = $cost_department;
                        }
                    }
                    return $rs_email_send;
                }
            }
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }

    /**
     * 兼容普通付款老数据数据处理
     * @Date: 8/25/22 2:25 PM
     * @param array $params 条件
     * @return array
     **@author: peak pan
     */
    public function getStoreData($params)
    {
        $data = WmsPlanService::getInstance()->getStoreData($params, false);

        if ($params['type'] == 'cost_store' && !empty($data['data'])) {
            foreach ($data['data'] as &$item) {
                $item['id'] = $item['name'];
            }
        }
        if (strpos(strtolower(Enums::PAYMENT_HEADER_STORE_NAME), strtolower($params['name'])) !== false) {
            array_unshift($data['data'], [
                'id'          => Enums::HEAD_OFFICE_STORE_FLAG,
                'name'        => Enums::PAYMENT_HEADER_STORE_NAME,
                'sap_pc_code' => '',
            ]);
        }
        return $data;
    }


    /**
     * 释放报销
     * @param int $id 条件
     * @param array $user 条件
     * @return bool
     * @throws ValidationException
     */
    public function freeBudget($id, $user)
    {
        // 验证默认国家是否开启预算
        $budgetStatus = (new EnumsService())->getBudgetStatus();
        if (!$budgetStatus) {
            return true;
        }
        $reimbursement = $this->depositReturnInfo($id);

        if (isset($reimbursement['detail']) && isset($reimbursement['detail'][0]['loss_budget_id']) && $reimbursement['detail'][0]['loss_budget_id']) {
            // 是 新数据 预算占用
            $freedAmount = [];
            foreach ($reimbursement['detail'] as $detail) {
                if (isset($freedAmount[$detail['level_code']])) {
                    $freedAmount[$detail['level_code']] += 0;
                } else {
                    $freedAmount[$detail['level_code']] = 0;
                }
            }
            $budgetService = new BudgetService();
            $result        = $budgetService->re_back_budget($reimbursement['return_code'], $user,
                DepositEnums::$deposit_reimbursement_mapping_order_type[$reimbursement['deposit_type']], $freedAmount);

            $this->logger->info('deposit_freeBudget  释放预算判断 params ' . json_encode([
                    $reimbursement['return_code'],
                    $user,
                    DepositEnums::$deposit_reimbursement_mapping_order_type[$reimbursement['deposit_type']],
                    $freedAmount,
                ]) . ' results ' . json_encode([$result]));

            if ($result['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($result['message']);
            }
        }
        return true;
    }


    /**
     * 查找押金数据
     * @param int $id 条件
     * @return array
     */
    private function depositReturnInfo($id)
    {
        $reimbursement = DepositReturnModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $id],
        ]);
        $reimbursement = $reimbursement ? $reimbursement->toArray() : [];
        if ($reimbursement) {
            $detail                  = DepositLossModel::find([
                'conditions' => ' deposit_return_id = :deposit_return_id:',
                'bind'       => ['deposit_return_id' => $id],
            ])->toArray();
            $reimbursement['detail'] = $detail;
        }
        return $reimbursement;
    }


    /**
     * 归还数据格式化
     * @Date: 8/5/22 3:36 PM
     * @param object $deposit_model 数据对象
     * @param array $user 用户数据
     * @param array $data 用户数据
     * @return array
     * @author: peak pan
     */
    private function getDepositReturnFormatDataOne($deposit_model, $data, $user)
    {
        $current_time = date('Y-m-d H:i:s', time());
        return [
            'deposit_type'       => $deposit_model->deposit_type,
            'deposit_id'         => $deposit_model->id,
            'create_id'          => $user['id'],
            'return_date'        => date('Y-m-d', time()),
            'status'             => DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
            'loss_money'         => $deposit_model->loss_money,
            'return_money'       => $deposit_model->return_money,
            'deposit_money'      => $deposit_model->deposit_money,
            'bank_flow_date'     => $data['bank_flow_date'],
            'return_info'        => $data['return_info'],
            'other_return_money' => !empty($data['other_return_money']) ? bcmul($data['other_return_money'], 1000) : 0,
            'other_return_info'  => $data['other_return_info'] ?? '',
            'is_deleted'         => GlobalEnums::IS_NO_DELETED,
            'updated_at'         => $current_time,
        ];
    }

    /**
     * 押金列表分页列表 数据
     * @Date: 8/6/22 3:28 PM
     * @param array $condition 查询条件
     * @param int $uid 用户uid
     * @param int $type 分类 2 审核 5 回复
     * @return array
     * @throws Exception
     * @author: peak pan
     */
    public function auditDepositList(array $condition, int $uid, int $type)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            if (isset($condition['sta_return_date']) && isset($condition['end_return_date']) && $condition['sta_return_date'] > $condition['end_return_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }
            $condition['uid'] = $uid;
            $builder          = $this->modelsManager->createBuilder();
            $builder->from(['de' => DepositModel::class]);
            //组合搜索条件
            $builder = $this->getAuditDepositCondition($builder, $condition, $type);
            $count   = (int)$builder->columns('COUNT(DISTINCT dr.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $columns = [
                    'de.business_no as apply_no',
                    'dr.id',
                    'de.contract_no as contract_no_b',
                    'de.return_status',
                    'de.apply_id as deposit_create_id',
                    'de.apply_name as deposit_create_name',
                    'de.deposit_money ',
                    'de.loss_money',
                    'de.return_money',
                    'de.detail_id',
                    'de.deposit_type',
                ];
                $builder->columns($columns);
                $builder->groupBy('dr.id');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDepositItems($items);
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-押金审核-列表数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 押金列表数据处理
     * @Date: 9/27/22 3:27 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     **/
    private function handleDepositItems($items)
    {
        if (empty($items)) {
            return [];
        }
        $data              = $this->handleData($items);
        $cost_company_list = [];

        $cost_company_ids = array_values(array_unique(array_filter(array_column($data, 'cost_company_id'))));
        if ($cost_company_ids) {
            $cost_company_arr  = (new DepartmentService())->getDepartmentInfoByIds($cost_company_ids);
            $cost_company_list = array_column($cost_company_arr, 'name', 'id');
        }
        $cost_department      = [];
        $cost_department_list = [];
        $cost_department_ids  = array_values(array_unique(array_filter(array_column($data, 'cost_department_id'))));
        if (!empty($cost_department_ids)) {
            $cost_department = DepartmentModel::find([
                'conditions' => 'id in ({id:array})',
                'columns'    => 'id,name',
                'bind'       => ['id' => $cost_department_ids],
            ])->toArray();
        }
        if ($cost_department) {
            $cost_department_list = array_column($cost_department, 'name', 'id');
        }

        //租房付款查找合同过期时间
        $contract_end_at_arr        = [];
        $contract_no_status_by_name = [];
        $contract_no_ids            = array_values(array_filter(array_unique(array_column($items, 'contract_no_b'))));
        if (!empty($contract_no_ids)) {
            $contract_no_arr = ContractArchive::find([
                'conditions' => 'cno in ({cno:array})',
                'bind'       => [
                    'cno' => $contract_no_ids,
                ],
                'columns'    => 'cno,status',
            ])->toArray();
            if (!empty($contract_no_arr)) {
                $contract_no_status_by_name = array_column($contract_no_arr, 'status', 'cno');
            }
            //过期时间
            $contract_end_at = ContractStoreRentingModel::find([
                'conditions' => 'contract_id in ({contract_id:array})',
                'bind'       => [
                    'contract_id' => $contract_no_ids,
                ],
                'columns'    => 'contract_end,contract_id',
            ])->toArray();
            if (!empty($contract_end_at)) {
                $contract_end_at_arr = array_column($contract_end_at, 'contract_end', 'contract_id');
            }
        }

        foreach ($items as &$item) {
            $detail_data                  = $data[$item['apply_no'] . '_' . $item['detail_id']];
            $item['id']                   = $item['detail_id'];
            $item['contract_no']          = $item['contract_no_b'] ? $item['contract_no_b'] : $detail_data['contract_no'] ?? '';
            $item['type']                 = (int)$item['deposit_type'];
            $item['return_money']         = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';
            $item['deposit_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';
            $item['status']               = $detail_data['status'] ?? '';
            $item['contract_status']      = $detail_data['status'] ?? '';
            $item['contract_status_name'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
            $item['expiry_date']          = '';
            $item['currency']             = $detail_data['currency'];
            $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$detail_data['currency']]];
            $item['cost_department_name'] = $detail_data['cost_department_name'] ?? '';
            $item['create_company_name']  = !empty($detail_data['cost_company_id']) ? $cost_company_list[$detail_data['cost_company_id']] : '';
            if ($item['deposit_type'] == DepositEnums::DEPOSIT_ORDINARY_PAYMENT) {
                $item['sum_deposit_money']    = bcsub(bcadd($detail_data['amount_no_tax'], $detail_data['amount_vat'],
                    3), $detail_data['amount_wht'], 2);
                $item['cost_store_type_text'] = $detail_data['cost_store_name'];
                $item['create_name']          = empty($item['deposit_create_id']) ? $detail_data['apply_name'] : $item['deposit_create_name'];
            } else {
                if ($item['deposit_type'] == DepositEnums::DEPOSIT_REIMBURSEMENT) {
                    $item['sum_deposit_money'] = bcdiv(bcsub(bcadd($detail_data['amount_no_tax'],
                        $detail_data['amount_vat'], 3), $detail_data['amount_wht']), 1000, 2);
                    $item['create_name']       = empty($item['deposit_create_id']) ? $detail_data['create_name'] : $item['deposit_create_name'];

                    $item['cost_store_type_text'] = $detail_data['cost_store_n_name'];
                    if ($detail_data['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                        $item['cost_store_type_text'] = $detail_data['cost_department_name'];
                    }
                } else {
                    if ($item['deposit_type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT) {
                        $item['sum_deposit_money']    = bcdiv(bcsub($detail_data['ticket_amount'],
                            $detail_data['amount_wht'], 3), 1000, 2);
                        $item['cost_store_type_text'] = $detail_data['cost_store_name'];
                        $item['cost_store_type']      = $detail_data['cost_store_type'] ?? '';
                        $item['create_name']          = empty($item['deposit_create_id']) ? $detail_data['apply_name'] : $item['deposit_create_name'];
                    } else {
                        if ($item['deposit_type'] == DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING) {
                            $item['expiry_date'] = '';
                            if (!empty($item['contract_no_b'])) {
                                $item['contract_status']      = $contract_no_status_by_name[$item['contract_no_b']];
                                $item['contract_status_name'] = empty($item['contract_no_b']) ? '' : static::$t['contract_status_name_normal'];//正常
                                if (in_array($item['contract_status'], [
                                        ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
                                        ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL,
                                    ]) || (!empty($contract_end_at_arr[$item['contract_no_b']])) && $contract_end_at_arr[$item['contract_no_b']] <= date('Y-m-d',
                                        time())) {
                                    $item['contract_status_name'] = static::$t['contract_status_name_be_overdue'];//已过期
                                }
                                $item['expiry_date'] = $contract_end_at_arr[$item['contract_no_b']] ?? '';
                            }
                            $item['sum_deposit_money']    = (string)bcsub(bcadd($detail_data['amount_no_tax'],
                                $detail_data['amount_vat'], 2), $detail_data['amount_wht'], 2);
                            $item['cost_store_type_text'] = $detail_data['store_name'];
                            $item['cost_store_type']      = $detail_data['cost_store_type'] ?? '';

                            $item['create_company_name'] = !empty($detail_data['cost_company_id']) ? $cost_company_list[$detail_data['cost_company_id']] : '';

                            // 费用所属部门
                            $item['cost_department_name'] = $detail_data['cost_department_name'] ?? '';
                            if (!empty($detail_data['cost_department_id'])) {
                                $item['cost_department_name'] = $cost_department_list[$detail_data['cost_department_id']] ?? '';
                            }
                            $item['create_name'] = empty($item['deposit_create_id']) ? $detail_data['create_name'] : $item['deposit_create_name'];
                        }
                    }
                }
            }
            $item['cost_store_id']           = $detail_data['cost_store_id'] ?? '';
            $item['cost_store_name']         = $detail_data['cost_store_name'] ?? '';
            $item['return_status_id']        = $item['return_status'] ?? '1';
            $item['return_status']           = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
            $item['created_at']              = show_time_zone($detail_data['created_at'], 'Y-m-d');
            $item['create_id']               = empty($item['deposit_create_id']) ? $item['apply_id'] : $item['deposit_create_id'];
            $item['apply_id']                = $detail_data['apply_id'] ?? '';
            $item['apply_name']              = $detail_data['apply_name'] ?? '';
            $item['created_department_name'] = $detail_data['created_department_name'] ?? '';
            unset($item['cost_store_n_name'], $item['cost_company_id'], $item['deposit_type']);
        }
        return $items;
    }


    /**
     * 处理列表数据 从四个模块查询并合并
     * @Date: 11/7/22 2:46 PM
     * @param array $items 数据
     * @return  array
     **/
    public function handleData($items)
    {
        try {
            $reimbursement_arr    = [];
            $ordinary_payment_arr = [];
            $purchase_arr         = [];
            $payment_store_arr    = [];
            foreach ($items as $handle_value) {
                if ($handle_value['deposit_type'] == DepositEnums::DEPOSIT_REIMBURSEMENT) {
                    $reimbursement['detail_id'] = $handle_value['detail_id'];
                    $reimbursement_arr[]        = $reimbursement;
                } else {
                    if ($handle_value['deposit_type'] == DepositEnums::DEPOSIT_ORDINARY_PAYMENT) {
                        $ordinary['detail_id']  = $handle_value['detail_id'];
                        $ordinary_payment_arr[] = $ordinary;
                    } else {
                        if ($handle_value['deposit_type'] == DepositEnums::DEPOSIT_PURCHASE_PAYMENT) {
                            $purchase['detail_id'] = $handle_value['detail_id'];
                            $purchase_arr[]        = $purchase;
                        } else {
                            if ($handle_value['deposit_type'] == DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING) {
                                $payment_store['detail_id'] = $handle_value['detail_id'];
                                $payment_store_arr[]        = $payment_store;
                            }
                        }
                    }
                }
            }

            $reimbursement_arr_list = [];
            if (!empty($reimbursement_arr)) {
                //列表中如果有报销 查找报销数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('de.id, de.tax_not as amount_no_tax, concat(rs.no,"_", de.id) as fid, de.tax as amount_vat, de.wht_tax_amount as amount_wht, de.cost_store_n_name, rs.currency, rs.cost_company_id, rs.cost_department_name, rs.apply_id, rs.apply_name, rs.apply_date as created_at, rs.created_name as create_name, rs.created_department_name,rs.cost_store_type');
                $builder->from(['de' => Detail::class]);
                $builder->leftjoin(Reimbursement::class, 'de.re_id = rs.id', 'rs');
                $builder->inWhere('de.id', array_column($reimbursement_arr, 'detail_id'));
                $reimbursement_arr_list = $builder->getQuery()->execute()->toArray();
            }
            $ordinary_payment_arr_list = [];
            if (!empty($ordinary_payment_arr)) {
                //列表中如果有普通付款 查找普通付款数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('opd.id, opd.amount_no_tax, concat(op.apply_no,"_", opd.id) as fid, opd.amount_vat, opd.amount_wht, op.currency, op.cost_company_id, op.cost_department_name, op.apply_id, op.apply_name, op.created_at, op.create_name, opd.cost_store_name, opd.cost_store_id, op.cost_store_type, ca.status');
                $builder->from(['opd' => OrdinaryPaymentDetail::class]);
                $builder->leftjoin(OrdinaryPayment::class, 'op.id = opd.ordinary_payment_id', 'op');
                $builder->leftjoin(Contract::class, 'co.cno = opd.contract_no', 'co');
                $builder->leftjoin(ContractArchive::class, 'ca.cno=co.cno', 'ca');
                $builder->inWhere('opd.id', array_column($ordinary_payment_arr, 'detail_id'));
                $ordinary_payment_arr_list = $builder->getQuery()->execute()->toArray();
            }
            $purchase_arr_list = [];
            if (!empty($purchase_arr)) {
                //列表中如果有采购 查找采购数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('ppr.id, concat(pp.ppno,"_", ppr.id) as fid, ppr.ticket_amount_not_tax as amount_no_tax, ppr.ticket_tax as amount_vat, ppr.wht_amount as amount_wht, 
                pp.currency, pp.cost_company_id, pp.cost_department_name, pp.create_id as apply_id, pp.create_name as apply_name, pp.apply_date as created_at, pp.create_name, ppr.cost_store_name, pp.cost_store as cost_store_type, pp.create_id, ppr.ticket_amount, pp.cost_store as cost_store_type, ca.status');
                $builder->from(['ppr' => PurchasePaymentReceipt::class]);
                $builder->leftjoin(PurchasePayment::class, 'pp.id = ppr.ppid', 'pp');
                $builder->leftjoin(Contract::class, 'co.cno = pp.contract_no', 'co');
                $builder->leftjoin(ContractArchive::class, 'ca.cno=co.cno', 'ca');
                $builder->inWhere('ppr.id', array_column($purchase_arr, 'detail_id'));
                $purchase_arr_list = $builder->getQuery()->execute()->toArray();
            }

            $payment_store_list = [];
            if (!empty($payment_store_arr)) {
                //列表中如果有租房 查找租房数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('psr.apply_no, psr_d.id, concat(psr.apply_no,"_", psr_d.id) as fid, psr.create_id as apply_id, psr.create_name as apply_name, psr.create_date as created_at, psr.cost_company_id, psr.cost_center_department_name as cost_department_name, psr.cost_department_id, psr.cost_store_type, psr.create_id, psr.create_name, psr.cost_company_id, psr_d.store_name, psr_d.amount as amount_no_tax, psr_d.vat_amount as amount_vat, psr_d.wht_amount as amount_wht, psr.currency, ca.status, psr_d.contract_no as contract_no_b, csr.contract_end as expiry_date');
                $builder->from(['psr_d' => PaymentStoreRentingDetail::class]);
                $builder->leftjoin(PaymentStoreRenting::class, 'psr.id = psr_d.store_renting_id', 'psr');
                $builder->leftjoin(ContractStoreRentingModel::class, 'csr.contract_id = psr_d.contract_no', 'csr');
                $builder->leftjoin(ContractArchive::class, 'ca.cno=csr.contract_id', 'ca');
                $builder->inWhere('psr_d.id', array_column($payment_store_arr, 'detail_id'));
                $payment_store_list = $builder->getQuery()->execute()->toArray();
            }
            return array_column(array_merge($reimbursement_arr_list, $ordinary_payment_arr_list, $purchase_arr_list,
                $payment_store_list), null, 'fid');
        } catch (Exception $e) {
            $this->logger->warning('押金管理-押金审核-列表数据:' . $e->getMessage() . $e->getTraceAsString());
        }
    }

    /**
     * 列表复合搜索条件  押金
     * @Date: 9/27/22 3:27 PM
     * @param object $builder 对象
     * @param array $condition 条件
     * @param int $type 分类 2 审核 5 回复
     * @return  array
     * @author: peak pan
     **/
    private function getAuditDepositCondition(object $builder, array $condition, int $type)
    {
        $apply_no          = $condition['apply_no'] ?? '';
        $create_id         = $condition['create_name'] ?? '';
        $start_return_date = $condition['sta_return_date'] ?? '';
        $end_return_date   = $condition['end_return_date'] ?? '';
        $return_status     = $condition['return_status'] ?? '';
        $contract_no       = $condition['contract_no'] ?? '';

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;


        $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');

        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag,
                [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], 'dr');
        } else {
            if ($type == self::LIST_TYPE_CONSULTED_REPLY) {
                $biz_table_info = ['table_alias' => 'dr'];
                $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder,
                    $condition['is_reply'], [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], $biz_table_info);
            }
        }

        //申请编号
        if (!empty($apply_no)) {
            $builder->andWhere('de.business_no = :business_no:', ['business_no' => $apply_no]);
        }

        if (!empty($contract_no)) {
            $builder->andWhere('de.contract_no = :contract_no:', ['contract_no' => $contract_no]);
        }

        //归还-截止日期
        if (!empty($end_return_date) && !empty($start_return_date)) {
            $start_return_date .= ' 00:00:00';
            $end_return_date   .= ' 23:59:59';
            $builder->betweenWhere('dr.return_date', $start_return_date, $end_return_date);

            //如果有归还时间 且没有归还状态的时候 默认为已归还
            if (empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:',
                    ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE]);
            } else {
                if (in_array($return_status, [
                    DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
                    DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION,
                    DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE,
                ])) {
                    //如果有归还时间 且归还状态为 未归还、法务介入中、法务已确定的时候 为默认状态0
                    $builder->andWhere('de.return_status = :return_status:',
                        ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_DEFAULT]);
                } else {
                    //如果有归还时间 且归还状态不为未归还、法务介入中、法务已确定，空的时候 状态为当前选择的状态
                    $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
                }
            }
        } else {
            //如果有归还时间为空 且归还状态不为空按照选择的归还状态查询
            if (!empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
            }
        }

        //押金负责人
        if (!empty($create_id)) {
            $builder->andWhere('de.apply_id = :created_id: or de.apply_name = :created_id:',
                ['created_id' => $create_id]);
        }

        return $builder;
    }

    /**
     * 批量转交
     * @param array $param 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchForward($param, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $bool = ImportCenterService::getInstance()->addImportCenter($user, '',
                ImportCenterEnums::TYPE_DEPOSIT_BATCH_FORWARD, $param);
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage() == static::$t->_('import_center_error_task_exist') ? static::$t->_('import_center_deposit_batch_forward_task_exist') : $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('deposit_apply_id_by_name:' . $real_message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $bool ?? false,
        ];
    }
}
