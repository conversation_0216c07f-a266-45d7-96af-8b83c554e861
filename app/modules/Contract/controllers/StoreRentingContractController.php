<?php
/**
 *StoreRentingContractController.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/9 0009 20:38
 */

namespace App\Modules\Contract\Controllers;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Services\ContractStoreRentingExportService;
use App\Modules\Contract\Services\CsrfTokenServer;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Contract\Services\BaseService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StoreRentingContractController extends BaseController
{
    /**
     * 初始化获取网点类别--合同详情( 我申请的)
     * 说明:
     * 1. 权限粒度, 原 Permission(action='storeRentingContract.getInit')
     * 2. 因老接口涉及跨模块的多个子菜单调用, 当前权限控制粒度不足以通用; 若在租房付款模块每个子菜单下各增新接口, 太散难以维护, 故调整为Token级别
     *
     * @Token
     * Created by: Lqz.
     * CreateTime: 2020/9/11 0011 10:56
     */
    public function getInitAction()
    {
        $res = $this->getInitData();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 初始化获取网点类别--合同详情( 数据查询)
     * @Token
     * 抄送管理的查看调用此接口,使用token权限
     */
    public function getDataDetailAction()
    {
        $res = $this->getInitData(ContractStoreRentingService::LIST_TYPE_SEARCH);
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }


    /**
     * @param int $sub_menu_type 取数来源类型(子菜单类型, 申请/审批/归档/数据查询/征询回复)
     * @return array
     * @throws BusinessException
     */
    public function getInitData($sub_menu_type = 0)
    {
        $id = $this->request->get('id');
        return ContractStoreRentingService::getInstance()->getDetail($id, $this->user, $sub_menu_type);

    }

    /**
     * 获取网点列表
     *
     * @Token
     * Created by: Lqz.
     *
     * @return Response|ResponseInterface
     * CreateTime: 2020/9/10 0010 16:45
     * @throws ValidationException
     */
    public function getStoreListAction()
    {
        $cate          = $this->request->get('store_cate');
        $storeList = ContractStoreRentingService::getInstance()->getStoreList($cate);
        if (isset($storeList['msg'])) {
            throw new ValidationException($storeList['msg'], ErrCode::$VALIDATE_ERROR);
        }

        $res = [
            'store_list' => $storeList
        ];
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 获取主合同编号列表
     * @Permission(action='storeRentingContract.getInit')
     * Created by: Lqz.
     *
     * @return Response|ResponseInterface
     * CreateTime: 2020/9/10 0010 17:15
     */
    public function getMainContractListAction()
    {
        $loginUser    = $this->user;
        $contractId   = $this->request->get('contract_id');
        $contractList = ContractStoreRentingService::getInstance()->getMainContractList($loginUser, $contractId);
        if (isset($contractList['msg'])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $contractList['msg']);
        }
        $res = [
            'contract_list' => $contractList
        ];
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 保存合同
     * @Permission(action='storeRentingContract.saveContract')
     * Created by: Lqz.
     *
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     * @throws BusinessException
     */
    public function saveContractAction()
    {
        if (!$this->request->isPost()) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'request method must been post !');
        }

        $loginUser           = $this->user;
        $params              = $this->request->get();
        $params['manage_id'] = $loginUser['id'];

        // 是否是编辑
        $is_edit = isset($params['id']) && !empty($params['id']);

        $country = get_country_code();

        // 参数验证
        try {
            $validateContract = ContractStoreRentingService::$validate_contract;
            if (isset($params['is_main']) && $params['is_main'] == Enums::CONTRACT_IS_MASTER_NO) {
                $validateContract['main_contract_id'] = 'Required|Str';
            }
            if (isset($params['contract_lease_type']) && $params['contract_lease_type'] == Enums::RENT_BY_MONTH) {
           //     $validateContract['monthly_payment_type'] = 'Required|Int';
            }

            if ($is_edit) {
                $validateContract['contract_id'] = 'Required|StrLenGeLe:0,50'; //合同编号
            }

            if(!empty($params['exempted_amount_months'])){
                $validateContract['exempted_amount_months'] = 'Required|StrLenGeLe:0,500'; //免缴金额月数
            }
            if(!empty($params['contract_benefits'])){
                $validateContract['contract_benefits'] = 'FloatGeLe:0,**********';  // 保证金
            }
            if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
                $validateContract = array_merge($validateContract, ContractStoreRentingService::$validate_not_loi_contract_list);
            }
            if (isset($params['notice_renewal_days']) && !empty($params['notice_renewal_days'])) {
                $validateContract['notice_renewal_days'] = 'Int';  // 需提前通知续租/不续签的天数
            }
            if (isset($params['renovation_days']) && !empty($params['renovation_days'])) {
                $validateContract['renovation_days'] = 'Int';  // 装修所需天数
            }
            // 只针对泰国校验
            if (GlobalEnums::TH_COUNTRY_CODE == $country) {
                // 地契类型
                if (!isset($params['land_type']) || !in_array($params['land_type'], array_values(array_keys(ContractEnums::$land_type_item)))) {
                    $validateContract['land_type'] = 'Required|Arr'; //地契数组类型
                    $validateContract['land_type[*]'] = 'IntIn:' . implode(',', array_keys(ContractEnums::$land_type_item)); //地契类型值
                }

                // 出租人
                if (!isset($params['leaser_type']) || !in_array($params['leaser_type'], array_values(array_keys(ContractEnums::$leaser_type_item)))) {
                    $validateContract['leaser_type'] = 'Required|IntIn:' . implode(',', array_keys(ContractEnums::$leaser_type_item)); //出租人类型
                }
            }

            Validation::validate($params, $validateContract);

            //区域信息不能超过100条
            if (!empty($params['areaInfo']) && count($params['areaInfo']) > 100) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, '区域信息不能超过100条记录');
            }

            if (GlobalEnums::PH_COUNTRY_CODE == $country) {
                Validation::validate($params, ContractStoreRentingService::$ph_contract_validate);
            } else{
                //合同总金额（不含VAT含WHT） 其他国家非必填
                Validation::validate($params, ['contract_total_amount_contain_wht' => 'FloatGeLe:0,**********.99']);
            }

            //收款信息
            $pay_amount      = 0;
            if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
                $bank_collection = $params['bank_collection'];
                foreach ($bank_collection as $_bank) {
                    Validation::validate($_bank, ContractStoreRentingService::$validate_bank_collection);
                }
            }

            if (in_array($params['is_main'], [1, 2])) {
                foreach ($params['amount_detail'] as $amount_detail) {
                    Validation::validate($amount_detail,ContractStoreRentingService::$amount_detail_validate);
                }

                foreach ($params['areaInfo'] as $areaInfo) {
                    Validation::validate($areaInfo, ContractStoreRentingService::$area_info_validate);
                }
            }

            // 税率严格校验: 在合同编辑时
            if ($is_edit) {
                $vat_config = EnumsService::getInstance()->getVatRateValueItem();
                $wht_config = EnumsService::getInstance()->getWhtRateMap();
                $all_wht_config = EnumsService::getInstance()->getAllWhtRateMap();

                // 合同信息
                // LOI: wht 类别非必填
                // 主合同、附属合同: vat/sst/wht类别/税率 必填
                foreach ($params['amount_detail'] as $detail) {
                    if (!in_array($detail['vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    $_wht_info = $wht_config[$detail['wht_category']] ?? [];

                    // LOI: 税率不空则校验
                    if ($params['is_main'] == 3 && $detail['wht_category'] != '' && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == 3 && empty($all_wht_config[$detail['wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [1, 2]) && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [1, 2]) && empty($_wht_info['rate_list'][$detail['wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                // 税费信息
                // LOI: vat/sst、wht类别/税率非必填
                // 主合同、附属合同: vat/sst、wht类别/税率 必填
                foreach ($params['areaInfo'] as $area) {
                    $_wht_info = $wht_config[$area['area_wht_category']] ?? [];

                    if ($params['is_main'] == 3 && $area['area_vat_rate'] != '' && !in_array($area['area_vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == 3 && $area['area_wht_category'] != '' && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == 3 && $area['area_wht_rate'] != '' && empty($all_wht_config[$area['area_wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [1, 2]) && !in_array($area['area_vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [1, 2]) && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [1, 2]) && empty($_wht_info['rate_list'][$area['area_wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            /**
             * 14090【PH|OA|租房合同】 自动计算印花税
             * https://flashexpress.feishu.cn/docx/doxcnUSfhqkz48zueEe4EmwiW3O
             * 增加印花税金额自动计算逻辑【菲律宾】
             */
            $params = ContractStoreRentingService::getInstance()->extendValidation($params);
            $return_data = [
                'save_status' => 0,
                'is_need_confirm' => $params['is_need_confirm'],
                'detail_total_amount_has_tax' => $params['detail_total_amount_has_tax'],
                'detail_total_amount_no_tax' => $params['detail_total_amount_no_tax'],
            ];

            if ($params['is_need_confirm']) {
                return $this->returnJson(ErrCode::$SUCCESS, '', $return_data);
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //保存合同信息
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        if (!isset($params['id']) || empty($params['id'])) {
            $csrfTokenCheck = (new CsrfTokenServer())->checkCsrfToken($params['csrf_token'] ?? '');
            if (isset($csrfTokenCheck['msg'])) {
                $db->rollback();
                return $this->returnJson(ErrCode::$VALIDATE_ERROR,  $csrfTokenCheck['msg']);
            }
        }

        try {
            $contract = ContractStoreRentingService::getInstance()->saveContract($loginUser, $params);

            if (is_array($contract) && isset($contract['error_msg'])) {
                $db->rollback();
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Save contract error,  Please refresh and try again ' ,['data' => $contract['error_msg']]);
            }

            ContractStoreRentingService::getInstance()->saveRelationContract($contract, $loginUser);

            //新创建合同，去创建审批流
            if (!isset($params['id']) && empty($params['id'])) {
                $flow_bool = ContractStoreRentingService::getInstance()->saveWkFlow($contract, $loginUser);
                if ($flow_bool === false || isset($flow_bool['error_msg'])) {
                    $db->rollback();
                    return $this->returnJson(ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR, 'contract create work flow failed', [$flow_bool]);
                }
            }

            $return_data['save_status'] = 1;

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            return $this->returnJson($e->getCode(), $e->getMessage(), ['error' => $e->getMessage()]);

        } catch (BusinessException $e) {
            $db->rollback();
            $this->logger->warning(
                "function =>newSaveContract" .
                ' message=>' . $e->getMessage() .
                ' params=>' . json_encode($params, JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], ['error' => $this->t['retry_later']]);
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->error(
                "function =>newSaveContract" .
                ' message=>' . $e->getMessage() .
                ' params=>' . json_encode($params, JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], ['error' => $this->t['retry_later']]);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $return_data);
    }


    /**
     * 合同列表-我申请的
     * @Permission(action='storeRentingContract.getMyList')
     * Created by: Lqz.
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     */
    public function getMyListAction()
    {
        return $this->_getList(false, false, true);
    }

    /**
     * 合同列表-待我审核的
     * @Permission(action='storeRentingContract.getAuditList')
     * Created by: Lqz.
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     */
    public function getAuditListAction()
    {
        return $this->_getList(true);
    }

    private function _getList($audit = false, $is_fyr = false, $apply = false)
    {
        $params    = $this->request->get();

        $contract_list = ContractStoreRentingService::getInstance()->getMyList($this->user, $params, $audit, $is_fyr, $apply);
        if (isset($contract_list['error_msg'])) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'system error !', $contract_list['error_msg']);
        }

        // v18165 列表网点类型筛选项 展示全部网点类型
        $store_cate_list = ContractStoreRentingService::getInstance()->getAllStoreCate();

        $res = [
            'contract_list'   => $contract_list,
            'store_cate_list' => $store_cate_list,
        ];
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 初始化获取网点类别--合同详情( 待审批的)
     * @Permission(action='storeRentingContract.getAuditInit')
     * Created by: Lqz.
     * CreateTime: 2020/9/11 0011 10:56
     */
    public function getAuditInitAction()
    {
        $res = $this->getInitData();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }
    /**
     * 撤回
     * @Permission(action='storeRentingContract.cancel')
     * Created by: Lqz.
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     */

    public function cancelAction()
    {
        $params    = $this->request->get();

        $this->logger->info('租房合同撤回, 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));

        $code = ErrCode::$SUCCESS;
        $real_message = '';
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $cancel_validate = ContractStoreRentingService::$cancel_validate;
            Validation::validate($params, $cancel_validate);

            $lock_key = md5('store_renting_contract_cancel_' . $params['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ContractStoreRentingService::getInstance()->cancel($this->user, $params);
            }, $lock_key, 20);
            if ($res === false) {
                throw new ValidationException($this->t['sys_processing'], ErrCode::$VALIDATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('租房合同撤回异常(BusinessException):  message => ' . $real_message);

        } catch (Exception $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('租房合同撤回异常(Exception):  message => ' . $real_message);
        }

        return $this->returnJson($code, $message, ['error' => $real_message]);
    }


    /**
     * 审核通过
     * @Permission(action='storeRentingContract.audit')
     * Created by: Lqz.
     *
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     * @throws ValidationException
     */
    public function approveAction()
    {
        $params    = $this->request->get();

        $this->logger->info('租房合同审批通过, 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));

        $approve_validate = ContractStoreRentingService::$approve_validate;

        /**
         * 14090【PH|OA|租房合同】 自动计算印花税
         * 菲律宾国家ap（菲律宾）节点可以修改印花税金额必填，其他国家非必填
         */
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $approve_validate['update_data.total_amount'] = 'Required|FloatGeLe:0,**********';
        }
        Validation::validate($params, $approve_validate);

        $lock_key = md5('store_renting_contract_approve_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ContractStoreRentingService::getInstance()->auditMain($params, $this->user, 'approve');
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 审核驳回
     * @Permission(action='storeRentingContract.audit')
     * Created by: Lqz.
     * @return mixed
     * CreateTime: 2020/9/11 0011 14:33
     */

    public function rejectAction()
    {
        $params    = $this->request->get();

        $this->logger->info('租房合同驳回, 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));

        $code = ErrCode::$SUCCESS;
        $real_message = '';
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $cancel_validate = ContractStoreRentingService::$reject_validate;
            Validation::validate($params, $cancel_validate);

            $lock_key = md5('store_renting_contract_reject_' . $params['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ContractStoreRentingService::getInstance()->auditMain($params, $this->user, 'reject');
            }, $lock_key, 20);

            if ($res === false) {
                throw new ValidationException($this->t['sys_processing'], ErrCode::$VALIDATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('租房合同驳回异常(BusinessException):  message => ' . $real_message);

        } catch (Exception $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('租房合同驳回异常(Exception):  message => ' . $real_message);
        }

        return $this->returnJson($code, $message, ['error' => $real_message]);
    }

    /**
     * 保存合同归档
     * @Permission(action='storeRentingContract.archiveAdd')
     * Created by: Lqz.
     * CreateTime: 2020/9/16 0016 21:44
     */
//    public function archiveAddAction()
//    {
//        $data        = $this->request->get();
//        $contract_id = $this->request->get('id', 'int');
//        try {
//            Validation::validate($data, []);
//        } catch (ValidationException $e) {
//            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
//        }
//        $res = ContractStoreRentingService::getInstance()->saveArchive($contract_id, $this->user);
//        if (isset($res['error_msg'])) {
//            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $res['error_msg'], $res);
//        }
//        return $this->returnJson(ErrCode::$SUCCESS, 'ok');
//    }


    /**
     * 合同归档回显
     *
     * 说明:
     * 1. 权限粒度, 原 Permission(action='storeRentingContract.archiveDetail')
     * 2. 因涉及跨业务模块的多个子菜单调用, 当前权限控制粒度不足以通用; 若在租房付款模块每个子菜单下各增新接口, 太散难以维护, 故调整为Token级别
     * 3. 已与前端同步, 该接口对应页面的关联接口, 后续统一重构优化(接口杂乱; 数据结构重复 - 维护复杂; 接口响应慢)
     *
     * @Token
     * Created by: Lqz.
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function archiveDetailAction()
    {
        //合同ID
        $data['id'] = $this->request->get('id', 'int');
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractStoreRentingService::getInstance()->getArchiveDetail($data['id'], $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }


    /**
     * 合同归档操作(上传合同，变更归档)
     * @Permission(action='storeRentingContract.archivePerform')
     * Created by: Lqz.
     *
     * @return Response|ResponseInterface
     */
    public function archivePerformAction()
    {
        $data       = $this->request->get();
        $archive_id = $this->request->get('archive_id', 'int');
        try {
            Validation::validate($data, ContractStoreRentingService::$validate_archive);
            // 签字文件和盖章文件至少上传一个
            if (empty($data['contract_file_arr']) && empty($data['contract_signature_file_arr'])) {
                throw new ValidationException('contract_signature_and_file_is_required', ErrCode::$VALIDATE_ERROR);
            }
            if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
                Validation::validate($data, ['fair_date'=>'Required|Date|DateTo:'.date('Y-m-d').'|>>>:'.$this->t->_('contract_archive_fair_date_error')]);
            } elseif (!empty($data['fair_date'])) {
                //其他国家非必填，但如果传递了公正日期，必须是日期格式
                Validation::validate($data, ['fair_date'=>'Date|DateTo:'.date('Y-m-d').'|>>>:'.$this->t->_('contract_archive_fair_date_error')]);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ContractStoreRentingService::getInstance()->archivePerform($archive_id, $data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message']);
        } else {
            return $this->returnJson($res['code'], $res['message']);
        }
    }

    /**
     * 合同归档编辑详情
     * @Permission(action='storeRentingContract.archiveEditDetail')
     * Created by: Wq
     *
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function archiveEditDetailAction()
    {
        //合同ID
        $data['id']   = $this->request->get('id', 'int');
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractStoreRentingService::getInstance()->getArchiveDetail($data['id'], $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * 编辑归档合同
     * @Permission(action='storeRentingContract.archiveEditDetail')
     * Created by: Wq
     *
     * @return mixed
     * CreateTime: 2021/09/04 0011 14:33
     * @throws BusinessException
     * @throws ValidationException
     */
    public function editContractArchiveAction()
    {
        $params = $this->request->get();

        // 请求参数
        $this->logger->info('params=>' . json_encode($params, JSON_UNESCAPED_UNICODE));

        if (empty($params['id'])) {
            throw new ValidationException($this->t->_('params_error', ['param' => 'id']), ErrCode::$VALIDATE_ERROR);
        }

        // 参数验证
        $res = ContractStoreRentingService::getInstance()->getArchiveDetail($params['id'], $this->user);
        if (isset($res['error_msg'])) {
            throw new ValidationException($res['error_msg'], ErrCode::$SYSTEM_ERROR);
        }

        $archiveStatus = isset($res['archive']['status']) ? $res['archive']['status'] : ContractEnums::CONTRACT_ARCHIVE_STATUS_DEFAULF;
        // 未归档校验
        if ($archiveStatus == ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING) {
            Validation::validate($params, ContractStoreRentingService::$validate_not_achieve_collection);

            // 归档校验
        } elseif ($archiveStatus == ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL) {
            Validation::validate($params, ContractStoreRentingService::$validate_achieve_collection);

        } elseif (in_array($archiveStatus, [
            ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING
            ])
        ) {
            throw new ValidationException($this->t->_('contract_archive_status_error'), ErrCode::$VALIDATE_ERROR);
        }

        // 其他校验
        $params = ContractStoreRentingService::getInstance()->extendValidation($params);
        $return_data = [
            'save_status' => 0,
            'is_need_confirm' => $params['is_need_confirm'],
            'detail_total_amount_has_tax' => $params['detail_total_amount_has_tax'],
            'detail_total_amount_no_tax' => $params['detail_total_amount_no_tax'],
        ];

        if ($params['is_need_confirm']) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $return_data);
        }

        // 归档编辑保存
        // redis锁优化
        $lock_key = md5(RedisKey::CONTRACT_RENT_ARCHIVE_EDIT_SAVE_KEY . $this->user['id']);
        $result = $this->atomicLock(function () use ($params) {
            return ContractStoreRentingService::getInstance()->editContract($this->user, $params);
        }, $lock_key, 10);

        if (isset($result['code'])) {
            $code = $result['code'];
            $message = $result['message'];
            $return_data['save_status'] = $result['code'] == ErrCode::$SUCCESS ? 1 : 0;
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $return_data['save_status'] = 0;
        }

        return $this->returnJson($code, $message, $return_data);
    }

    /**
     * 编辑归档合同日志记录
     *
     * @Token
     * Created by: Wq
     * @return mixed
     * CreateTime: 2021/09/04 0011 14:33
     * @throws ValidationException
     * 抄送管理的查看调用此接口,使用token权限
     */
    public function listContractArchiveLogAction(){
        $data   = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractStoreRentingService::getInstance()->getContractArchieveEditLogs($data);
        return $this->returnJson(ErrCode::$SUCCESS, 'OK', $res);
    }

    /**
     * 合同下载---变更合同为待归档
     * @Permission(action='storeRentingContract.downPdf')
     * Created by: Lqz.
     *
     * @return mixed
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        // 加锁
        $lock_key = md5('store_renting_contract_apply_download_' . $data['id'] . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($data) {
            return ContractStoreRentingService::getInstance()->download($data['id'], $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function outPutAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        // 加锁
        $lock_key = md5('store_renting_contract_output_' . $data['id'] . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($data) {
            return ContractStoreRentingService::getInstance()->genArchiveDownload($data['id']);
        }, $lock_key, 30);

        if (!empty($res['data']['url'])) {
            $this->response->redirect($res['data']['url'], true, 301);
        } else {
            return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
        }
    }


    /**
     * 合同归档列表
     * @Permission(action='storeRentingContract.archiveList')
     * Created by: Lqz.
     */
    public function archiveListAction()
    {
        $params    = $this->request->get();

        $list = ContractStoreRentingService::getInstance()->getArchiveList($params, $this->user, ContractStoreRentingService::LIST_TYPE_SEARCH);
        if (isset($params['export']) && $params['export']) {
            return $this->returnJson(ErrCode::$SUCCESS, 'OK', $list);
        }

        // v18165 列表网点类型筛选项 展示全部网点类型
        $store_cate_list = ContractStoreRentingService::getInstance()->getAllStoreCate();

        $res = [
            'contract_list'   => $list,
            'store_cate_list' => $store_cate_list,
        ];
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }


    /**
     * 全部合同列表 - 数据查询
     * @Permission(action='storeRentingContract.getAllList')
     * Created by: Lqz.
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function getAllListAction()
    {
        $params    = $this->request->get();

        // 加锁处理
        $lock_key = md5('store_renting_contract_all_list_' . '_' . $this->user['id']);
        $list = $this->atomicLock(function() use ($params) {
            return ContractStoreRentingService::getInstance()->getAllList($params, $this->user, ContractStoreRentingService::LIST_TYPE_SEARCH);
        }, $lock_key, 10);

        if (isset($params['export']) && $params['export']) {
            if (isset($list['code']) && $list['code'] == ErrCode::$SUCCESS && !empty($list['data'])) {
                return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
            } else {
                return $this->returnJson(ErrCode::$BUSINESS_ERROR, $this->t->_('retry_later'), []);
            }
        }

        $store_cate_list = ContractStoreRentingService::getInstance()->getAllStoreCate();

        $res = [
            'contract_list'   => $list,
            'store_cate_list' => $store_cate_list,
        ];
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Token
     */
     public function getFyrListAction(){
         return $this->_getList(false,true);
     }


    /**
     * @Token
     */
    public function getFyrDetailAction(){
        $res =  $this->getInitData();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }


    /**
     *
     * @Token
     */
    public function askAction()
    {
        $biz_id = $this->request->get('id', 'int');
        $note = $this->request->get('note','trim');
        $to_staffs = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request = (new ContractStoreRentingService())->_getWkReq($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }


    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id','int');
        $note = $this->request->get('note','trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 网点租房中涉及
     * 公共枚举参数
     * @Token
     * */
    public function getCommonParamsAction()
    {

        $res = ContractStoreRentingService::getInstance()->getCommonParams();

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 归档合同作废
     * @Permission(action='storeRentingContract.invalid')
     * Created by: Wq
     * @return mixed
     * CreateTime: 2021/10/19 0011 14:33
     */
    public function invalidAction()
    {
        $params = $this->request->get();
        $loginUser = $this->user;
        try {
            ContractStoreRentingService::getInstance()->invalidValidate($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ContractStoreRentingService::getInstance()->invalid($params, $loginUser);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归档合同终止
     * @Permission(action='storeRentingContract.terminal')
     * Created by: Wq
     * CreateTime: 2021/10/19 0011 14:33
     */
    public function terminalAction()
    {
        $params         = $this->request->get();
        $loginUser      = $this->user;
        try {
            Validation::validate($params, ContractStoreRentingService::$terminal_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ContractStoreRentingService::getInstance()->terminal($params,$loginUser);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归档合同终止校验
     * @Permission(action='storeRentingContract.terminal')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88265
     */
    public function terminalCheckAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'cno' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'cno']),
            'terminal_date' => 'Required|Date|>>>:' . $this->t->_('params_error', ['param' => 'terminal_date']),
        ]);

        $res = ContractStoreRentingService::getInstance()->terminalCheck($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * 根据主合同返回loi列表数据
     *
     * @Token
     * @Date: 2021-11-09 20:22
     * @return Response|ResponseInterface
     * @author: peak pan
     */
    public function getRelateMainListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ContractStoreRentingService::$get_relate_mainList);

            $res = ContractStoreRentingService::getInstance()->relateMainList($params, $this->getInitData()['store_cate_list']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 根据loi合同编号查出合同的具体支付信息
     *
     * @Token
     * @Date: 2021-11-16 20:18
     * @return Response|ResponseInterface
     * @author: peak pan
     */
    public function getRentingBankCollectionAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ContractStoreRentingService::$get_relate_contract);
            $res = ContractStoreRentingService::getInstance()->rentingBankCollection($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 根据网点名称获取关联其他网点租房合同
     * @Token
     * @Date: 2021-12-06 17:24
     * @author: wangqi
     * @return mixed
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39591
     * 抄送管理的查看调用此接口,使用token权限
     */
    public function getRelationStoreAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, ContractStoreRentingService::$get_relate_store);
            $res = ContractStoreRentingService::getInstance()->getRelationStoreList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 根据房东类型获取wht列
     * @Token
     * @Date: 2022-02-28 17:24
     * @author: wangqi
     **/
    public function getWhtByLandlordAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, ContractStoreRentingService::$get_landlord_type);
            $res = ContractStoreRentingService::getInstance()->getWhtList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 导出房租/区域服务费付款
     * @Permission(action='storeRentingContract.archiveList')
     * @Date: 2022-03-22 17:24
     * @author: wangqi
     **/
    public function exportRentingServiceAction(){
        $loginUser = $this->user;

        // 加锁处理
        $lock_key = md5('store_renting_contract_renting_auto_service' . '_' . $this->user['id']);
        $list = $this->atomicLock(function() use($loginUser) {
            return ContractStoreRentingExportService::getInstance()->exportRentingPayment($loginUser);
        }, $lock_key, 30);

        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 导出其它服务费付款
     * @Permission(action='storeRentingContract.archiveList')
     * @Date: 2022-03-22 17:24
     * @author: wangqi
     **/
    public function exportOtherFeeAction(){
        $loginUser = $this->user;

        // 加锁处理
        $lock_key = md5('store_renting_contract_other_auto_service' . '_' . $this->user['id']);
        $list = $this->atomicLock(function() use($loginUser) {
            return ContractStoreRentingExportService::getInstance()->exportOtherFee($loginUser);
        }, $lock_key, 30);

        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 租房合同-归档-下载
     * @Permission(action='storeRentingContract.archive_download')
     * Created by: Lqz.
     *
     * @return mixed
     * @throws ValidationException
     */
    public function archiveDownloadAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        // 加锁
        $lock_key = md5('store_renting_contract_archive_download_' . $data['id'] . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($data) {
            return ContractStoreRentingService::getInstance()->genArchiveDownload($data['id']);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 归档合同作废-查询参数/枚举
     * @Permission(action='storeRentingContract.invalid')
     *
     * @date 2023/1/9
     * @return Response|ResponseInterface
     */
    public function getInvalidParamsAction()
    {
        $params = $this->request->get();
        $this->user;
        try {
            Validation::validate($params, ContractStoreRentingService::$get_replace_cno_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ContractStoreRentingService::getInstance()->getInvalidParams($params['cno']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    /**
     * 归档合同作废-查询可替换的合同编号
     * @Permission(action='storeRentingContract.invalid')
     */
    public function getReplaceCnoAction()
    {
        $params = $this->request->get();
        $this->user;
        try {
            Validation::validate($params, ContractStoreRentingService::$get_replace_cno_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ContractStoreRentingService::getInstance()->getReplaceCno($params['cno']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 保存合同
     * @Permission(action='storeRentingContract.saveContract')
     *
     * @return mixed
     * CreateTime: 2023/02/27
     * @throws BusinessException
     */
    public function saveRenewalContractAction()
    {

        $loginUser           = $this->user;
        $params              = $this->request->get();
        $params['manage_id'] = $loginUser['id'];

        // 是否是编辑
        $is_edit = isset($params['id']) && !empty($params['id']);

        $country = get_country_code();

        // 参数验证
        try {
            $validateContract = ContractStoreRentingService::$validate_contract;
            if (isset($params['is_main']) && $params['is_main'] == Enums::CONTRACT_IS_MASTER_NO) {
                $validateContract['main_contract_id'] = 'Required|Str';
            }


            if ($is_edit) {
                $validateContract['contract_id'] = 'Required|StrLenGeLe:0,50'; //合同编号
            }

            if (!empty($params['exempted_amount_months'])) {
                $validateContract['exempted_amount_months'] = 'Required|StrLenGeLe:0,500'; //免缴金额月数
            }
            if (!empty($params['contract_benefits'])) {
                $validateContract['contract_benefits'] = 'FloatGeLe:0,**********';  // 保证金
            }
            if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
                $validateContract = array_merge($validateContract, ContractStoreRentingService::$validate_not_loi_contract_list);
            }
            if (isset($params['notice_renewal_days']) && !empty($params['notice_renewal_days'])) {
                $validateContract['notice_renewal_days'] = 'Int';  // 需提前通知续租/不续签的天数
            }
            if (isset($params['renovation_days']) && !empty($params['renovation_days'])) {
                $validateContract['renovation_days'] = 'Int';  // 装修所需天数
            }
            // 只针对泰国校验
            if (GlobalEnums::TH_COUNTRY_CODE == $country) {
                // 地契类型
                if (!isset($params['land_type']) || !in_array($params['land_type'],
                        array_values(array_keys(ContractEnums::$land_type_item)))) {
                    $validateContract['land_type']    = 'Required|Arr'; //地契数组类型
                    $validateContract['land_type[*]'] = 'IntIn:' . implode(',', array_keys(ContractEnums::$land_type_item)); //地契类型值
                }

                // 出租人
                if (!isset($params['leaser_type']) || !in_array($params['leaser_type'],
                        array_values(array_keys(ContractEnums::$leaser_type_item)))) {
                    $validateContract['leaser_type'] = 'Required|IntIn:' . implode(',', array_keys(ContractEnums::$leaser_type_item)); //出租人类型
                }
            }

            Validation::validate($params, $validateContract);

            //区域信息不能超过100条
            if (!empty($params['areaInfo']) && count($params['areaInfo']) > 100) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, '区域信息不能超过100条记录');
            }

            if (GlobalEnums::PH_COUNTRY_CODE == $country) {
                Validation::validate($params, ContractStoreRentingService::$ph_contract_validate);
            } else {
                //合同总金额（不含VAT含WHT） 其他国家非必填
                Validation::validate($params, ['contract_total_amount_contain_wht' => 'FloatGeLe:0,**********.99']);
            }

            //收款信息
            $pay_amount = 0;
            if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
                $bank_collection = $params['bank_collection'];
                foreach ($bank_collection as $_bank) {
                    Validation::validate($_bank, ContractStoreRentingService::$validate_bank_collection);
                }
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO])) {
                foreach ($params['amount_detail'] as $amount_detail) {
                    Validation::validate($amount_detail, ContractStoreRentingService::$amount_detail_validate);
                }

                foreach ($params['areaInfo'] as $areaInfo) {
                    Validation::validate($areaInfo, ContractStoreRentingService::$area_info_validate);
                }
            }

            // 税率严格校验: 在合同编辑时
            if ($is_edit) {
                $vat_config     = EnumsService::getInstance()->getVatRateValueItem();
                $wht_config     = EnumsService::getInstance()->getWhtRateMap();
                $all_wht_config = EnumsService::getInstance()->getAllWhtRateMap();

                // 合同信息
                // LOI: wht 类别非必填
                // 主合同、附属合同: vat/sst/wht类别/税率 必填
                foreach ($params['amount_detail'] as $detail) {
                    if (!in_array($detail['vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    $_wht_info = $wht_config[$detail['wht_category']] ?? [];

                    // LOI: 税率不空则校验
                    if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $detail['wht_category'] != '' && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && empty($all_wht_config[$detail['wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info['rate_list'][$detail['wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                // 税费信息
                // LOI: vat/sst、wht类别/税率非必填
                // 主合同、附属合同: vat/sst、wht类别/税率 必填
                foreach ($params['areaInfo'] as $area) {
                    $_wht_info = $wht_config[$area['area_wht_category']] ?? [];

                    if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_vat_rate'] != '' && !in_array($area['area_vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_wht_category'] != '' && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_wht_rate'] != '' && empty($all_wht_config[$area['area_wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && !in_array($area['area_vat_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info['rate_list'][$area['area_wht_rate']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            /**
             * 14090【PH|OA|租房合同】 自动计算印花税
             * https://flashexpress.feishu.cn/docx/doxcnUSfhqkz48zueEe4EmwiW3O
             * 增加印花税金额自动计算逻辑【菲律宾】
             */
            $params = ContractStoreRentingService::getInstance()->extendValidation($params);
            $return_data = [
                'save_status' => 0,
                'is_need_confirm' => $params['is_need_confirm'],
                'detail_total_amount_has_tax' => $params['detail_total_amount_has_tax'],
                'detail_total_amount_no_tax' => $params['detail_total_amount_no_tax'],
            ];

            if ($params['is_need_confirm']) {
                return $this->returnJson(ErrCode::$SUCCESS, '', $return_data);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        //保存合同信息
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        if (!isset($params['id']) || empty($params['id'])) {
//            $csrfTokenCheck = (new CsrfTokenServer())->checkCsrfToken($params['csrf_token'] ?? '');
            if (isset($csrfTokenCheck['msg'])) {
                $db->rollback();
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $csrfTokenCheck['msg']);
            }
        }

        try {
            $contract = ContractStoreRentingService::getInstance()->saveContract($loginUser, $params, $is_renewal_contract = true);

            if (is_array($contract) && isset($contract['error_msg'])) {
                $db->rollback();
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Save contract error,  Please refresh and try again ', ['data' => $contract['error_msg']]);
            }

            ContractStoreRentingService::getInstance()->saveRelationContract($contract, $loginUser);

            //新创建合同，去创建审批流
            if (!isset($params['id']) && empty($params['id'])) {
                $flow_bool = ContractStoreRentingService::getInstance()->saveWkFlow($contract, $loginUser, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE);
                if ($flow_bool === false || isset($flow_bool['error_msg'])) {
                    $db->rollback();
                    return $this->returnJson(ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR, 'contract create work flow failed', [$flow_bool]);
                }
            }

            $return_data['save_status'] = 1;

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            return $this->returnJson($e->getCode(), $e->getMessage(), ['error' => $e->getMessage()]);

        } catch (BusinessException $e) {
            $db->rollback();
            $this->logger->warning(
                "function =>saveRenewalContract" .
                ' message=>' . $e->getMessage() .
                ' params=>' . json_encode($params, JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], ['error' => $this->t['retry_later']]);
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->error(
                "function =>saveRenewalContract" .
                ' message=>' . $e->getMessage() .
                ' params=>' . json_encode($params, JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], ['error' => $this->t['retry_later']]);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $return_data);
    }

    /**
     * 初始化获取--续签合同详情
     * @Permission(action='storeRentingContract.getInit')
     * CreateTime: 2023/3/01  10:56
     */
    public function renewalDefaultAction()
    {
        $data['id']   = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractStoreRentingService::getInstance()->renewalDefault($data['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *申请- 转交
     * @Permission(action='storeRentingContract.transfer')
     * */
    public function contractTransferAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ContractStoreRentingService::$validate_transfer_param);

        $res = ContractStoreRentingService::getInstance()->contractTransfer($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     *数据查询- 转交
     * @Permission(action='storeRentingContract.data.transfer')
     * */
    public function contractDataTransferAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ContractStoreRentingService::$validate_transfer_param);

        $res = ContractStoreRentingService::getInstance()->contractTransfer($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     *数据查询-批量转交
     * @Permission(action='storeRentingContract.data.batch_transfer')
     * */
    public function batchContractTransferAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ContractStoreRentingService::$validate_batch_transfer_param);

        $res = ContractStoreRentingService::getInstance()->batchContractTransfer($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     * 重新提交
     * @Permission(action='storeRentingContract.saveContract')
     *
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommitAction()
    {
        $loginUser           = $this->user;
        $params              = $this->request->get();
        $params['manage_id'] = $loginUser['id'];

        $country = get_country_code();

        $validateContract = ContractStoreRentingService::$validate_contract;
        if (isset($params['is_main']) && $params['is_main'] == Enums::CONTRACT_IS_MASTER_NO) {
            $validateContract['main_contract_id'] = 'Required|Str';
        }

        $validateContract['contract_id'] = 'Required|StrLenGeLe:0,50'; //合同编号

        if (!empty($params['exempted_amount_months'])) {
            $validateContract['exempted_amount_months'] = 'Required|StrLenGeLe:0,500'; //免缴金额月数
        }
        if (!empty($params['contract_benefits'])) {
            $validateContract['contract_benefits'] = 'FloatGeLe:0,**********';  // 保证金
        }
        if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
            $validateContract = array_merge($validateContract, ContractStoreRentingService::$validate_not_loi_contract_list);
        }
        if (isset($params['notice_renewal_days']) && !empty($params['notice_renewal_days'])) {
            $validateContract['notice_renewal_days'] = 'Int';  // 需提前通知续租/不续签的天数
        }
        if (isset($params['renovation_days']) && !empty($params['renovation_days'])) {
            $validateContract['renovation_days'] = 'Int';  // 装修所需天数
        }
        // 只针对泰国校验
        if (GlobalEnums::TH_COUNTRY_CODE == $country) {
            // 地契类型
            if (!isset($params['land_type']) || !in_array($params['land_type'],
                    array_values(array_keys(ContractEnums::$land_type_item)))) {
                $validateContract['land_type']    = 'Required|Arr'; //地契数组类型
                $validateContract['land_type[*]'] = 'IntIn:' . implode(',', array_keys(ContractEnums::$land_type_item)); //地契类型值
            }

            // 出租人
            if (!isset($params['leaser_type']) || !in_array($params['leaser_type'],
                    array_values(array_keys(ContractEnums::$leaser_type_item)))) {
                $validateContract['leaser_type'] = 'Required|IntIn:' . implode(',', array_keys(ContractEnums::$leaser_type_item)); //出租人类型
            }
        }

        Validation::validate($params, $validateContract);

        //区域信息不能超过100条
        if (!empty($params['areaInfo']) && count($params['areaInfo']) > 100) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, '区域信息不能超过100条记录');
        }

        if (GlobalEnums::PH_COUNTRY_CODE == $country) {
            Validation::validate($params, ContractStoreRentingService::$ph_contract_validate);
        } else {
            //合同总金额（不含VAT含WHT） 其他国家非必填
            Validation::validate($params, ['contract_total_amount_contain_wht' => 'FloatGeLe:0,**********.99']);
        }

        //收款信息
        $pay_amount = 0;
        if (isset($params['is_main']) && $params['is_main'] != Enums::CONTRACT_IS_LOI_YES) {
            $bank_collection = $params['bank_collection'];
            foreach ($bank_collection as $_bank) {
                Validation::validate($_bank, ContractStoreRentingService::$validate_bank_collection);
            }
        }

        if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO])) {
            foreach ($params['amount_detail'] as $amount_detail) {
                Validation::validate($amount_detail, ContractStoreRentingService::$amount_detail_validate);
            }

            foreach ($params['areaInfo'] as $areaInfo) {
                Validation::validate($areaInfo, ContractStoreRentingService::$area_info_validate);
            }
        }

        // 税率严格校验: 在合同编辑时
        $vat_config     = EnumsService::getInstance()->getVatRateValueItem();
        $wht_config     = EnumsService::getInstance()->getWhtRateMap();
        $all_wht_config = EnumsService::getInstance()->getAllWhtRateMap();

        // 合同信息
        // LOI: wht 类别非必填
        // 主合同、附属合同: vat/sst/wht类别/税率 必填
        foreach ($params['amount_detail'] as $detail) {
            if (!in_array($detail['vat_rate'], $vat_config)) {
                throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
            }

            $_wht_info = $wht_config[$detail['wht_category']] ?? [];

            // LOI: 税率不空则校验
            if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $detail['wht_category'] != '' && empty($_wht_info)) {
                throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && empty($all_wht_config[$detail['wht_rate']])) {
                throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info)) {
                throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info['rate_list'][$detail['wht_rate']])) {
                throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 税费信息
        // LOI: vat/sst、wht类别/税率非必填
        // 主合同、附属合同: vat/sst、wht类别/税率 必填
        foreach ($params['areaInfo'] as $area) {
            $_wht_info = $wht_config[$area['area_wht_category']] ?? [];

            if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_vat_rate'] != '' && !in_array($area['area_vat_rate'], $vat_config)) {
                throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_wht_category'] != '' && empty($_wht_info)) {
                throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES && $area['area_wht_rate'] != '' && empty($all_wht_config[$area['area_wht_rate']])) {
                throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && !in_array($area['area_vat_rate'], $vat_config)) {
                throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info)) {
                throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($params['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && empty($_wht_info['rate_list'][$area['area_wht_rate']])) {
                throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
            }
        }

        /**
         * 14090【PH|OA|租房合同】 自动计算印花税
         * https://flashexpress.feishu.cn/docx/doxcnUSfhqkz48zueEe4EmwiW3O
         * 增加印花税金额自动计算逻辑【菲律宾】
         */
        $params = ContractStoreRentingService::getInstance()->extendValidation($params);
        $return_data = [
            'save_status' => 0,
            'is_need_confirm' => $params['is_need_confirm'],
            'detail_total_amount_has_tax' => $params['detail_total_amount_has_tax'],
            'detail_total_amount_no_tax' => $params['detail_total_amount_no_tax'],
        ];

        if ($params['is_need_confirm']) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $return_data);
        }

        $res = ContractStoreRentingService::getInstance()->reCommit($loginUser, $params);

        if ($res['code'] == ErrCode::$SUCCESS) {
            $return_data['save_status'] = 1;
        }

        return $this->returnJson($res['code'], $res['message'], $return_data);
    }

    /**
     * 获取附件配置
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80272
     */
    public function getAttachmentConfigAction()
    {
        $data = ContractStoreRentingService::getInstance()->getAttachmentAllConfig();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取付款明细列表
     * @Permission(action='storeRentingContract.data.payment_detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88226
     */
    public function getPaymentDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'contract_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'contract_no'])
        ]);
        $data = ContractStoreRentingService::getInstance()->getRelatedPaymentDetailList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 导出付款明细
     * @Permission(action='storeRentingContract.data.payment_detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88229
     */
    public function exportPaymentDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'contract_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'contract_no'])
        ]);

        $lock_key = md5('store_renting_contract_data_payment_detail_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractStoreRentingService::getInstance()->exportRelatedPaymentDetailList($params);
        }, $lock_key, 20);

        if (!empty($res)) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
        }

        return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['sys_processing'], '');
    }
}
