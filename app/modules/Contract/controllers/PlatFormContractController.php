<?php
/**
 * Created by PhpStorm.
 * Date: 2023/4/13
 * Time: 15:27
 */

namespace App\Modules\Contract\Controllers;

use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;

use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Modules\Contract\Services\BaseService;
use App\Modules\Contract\Services\ContractPlatFormService;
use App\Modules\Contract\Services\ContractPlatFormFlowService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PlatFormContractController Extends BaseController
{


    /**
     * @Token
     */
    public function getInitAction()
    {
        $res = ContractPlatFormService::getInstance()->getInit();
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * 创建合同
     * @Permission(action='contract_platform.my_list.add')
     *
     * */
    public function addAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractPlatFormService::$validate_contract);

        $res = ContractPlatFormService::getInstance()->add($data, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 编辑合同
     * @Permission(action='contract_platform.my_list.edit')
     * */
    public function editAction()
    {
        $data = $this->request->get();
        ContractPlatFormService::getInstance()->validateAttachments($data);

        $res = ContractPlatFormService::getInstance()->edit($data, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 快速复制回显数据
     * @Permission(action='contract_platform.my_list.list')
     * */

    public function editDetailAction(){
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractPlatFormService::getInstance()->editDetail($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 合同申请列表
     * @Permission(action='contract_platform.my_list.list')
     * @return mixed
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractPlatFormService::$not_must_params);
        Validation::validate($params, ContractPlatFormService::$validate_list_search);

        $list = ContractPlatFormService::getInstance()->getList($params, $this->user['id'], ContractPlatFormService::LIST_TYPE_APPLY);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同审核列表
     * @Permission(action='contract_platform.get_audit_list.list')
     * @return mixed
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractPlatFormService::$not_must_params);
        Validation::validate($params, ContractPlatFormService::$validate_list_search);

        $list = ContractPlatFormService::getInstance()->getList($params, $this->user['id'], ContractPlatFormService::LIST_TYPE_AUDIT);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同数据查询列表
     * @Permission(action='contract_platform.get_all_list.list')
     * @return mixed
     * @throws ValidationException
     */
    public function searchListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractPlatFormService::$not_must_params);
        Validation::validate($params, ContractPlatFormService::$validate_list_search);

        $list = ContractPlatFormService::getInstance()->getList($params, 0, ContractPlatFormService::LIST_TYPE_SEARCH);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同归档查询列表
     * @Permission(action='contract_platform.archive_list.list')
     * @return mixed
     * @throws ValidationException
     */
    public function archiveListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractPlatFormService::$not_must_params);
        Validation::validate($params, ContractPlatFormService::$validate_list_search);

        $list = ContractPlatFormService::getInstance()->getArchiveList($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 上传盖章合同
     * @Permission(action='contract_platform.archive_list.archeive_perform')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function archivePerformAction()
    {
        $data       = $this->request->get();
        $archive_id = $this->request->get('id', 'int');

        Validation::validate($data, ContractPlatFormService::$validate_archive);

        $res = ContractPlatFormService::getInstance()->archivePerform($archive_id, $data, $this->user);
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 申请-查看
     * @Permission(action='contract_platform.my_list.list')
     * */
    public function detailAction()
    {


        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractPlatFormService::getInstance()->getDetail($data, $this->user['id'], ContractEnums::MODULE_TYPE_APPLY);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 合同审核详情
     * @Permission(action='contract_platform.get_audit_list.list')
     *
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractPlatFormService::getInstance()->getDetail($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据查询-详情
     * @Token
     * */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractPlatFormService::getInstance()->getDetail($data, $this->user['id'], ContractEnums::MODULE_TYPE_DATA);

        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }


    /**
     * 合同归档-查看
     * @Permission(action='contract_platform.archive_list.list')
     *
     * @return Response|ResponseInterface
     */
    public function archiveDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = ContractPlatFormService::getInstance()->getDetail($data, 0, ContractEnums::MODULE_TYPE_ARCHIVE);
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }


    /**
     * 合同审核通过
     * @Permission(action='contract_platform.get_audit_list.list')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function approveAction()
    {
        $contract_id = $this->request->get('id', 'int', 0);
        $note        = $this->request->get('note', 'trim', '');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractPlatFormFlowService::$validate_approve);
        $res = (new ContractPlatFormFlowService())->approve($contract_id, $note, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同审核拒绝
     * @Permission(action='contract_platform.get_audit_list.list')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $contract_id = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractPlatFormFlowService::$validate_reject);
        $res = (new ContractPlatFormFlowService())->reject($contract_id, $note, $this->user);

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 合同撤销
     * @Permission(action='contract_platform.my_list.cancel')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $contract_id = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractPlatFormFlowService::$validate_cancel);
        $res = (new ContractPlatFormFlowService())->cancel($contract_id, $note, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 合同下载
     * @Permission(action='contract_platform.my_list.download')
     * @return mixed
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;
        Validation::validate($data, BaseService::$validate_detail);

        // 加锁
        $lock_key = md5('contract_platform_download_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id) {
            return ContractPlatFormService::getInstance()->download($id, $this->user);
        }, $lock_key, 30);

        if (empty($res['url'])) {
            throw new ValidationException('File download failed, please try again', ErrCode::$VALIDATE_ERROR);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 合同归档 - 下载
     * @Permission(action='contract_platform.archive_list.archive_down')
     * @return mixed
     * @throws ValidationException
     */
    public function archiveDownloadAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;
        Validation::validate($data, BaseService::$validate_detail);

        // 加锁
        $lock_key = md5('contract_platform_archive_download_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id) {
            return ContractPlatFormService::getInstance()->genArchiveDownload($id, $this->user['id']);
        }, $lock_key, 30);

        if (empty($res['url'])) {
            throw new ValidationException('File download failed, please try again', ErrCode::$VALIDATE_ERROR);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 合同管理 - 合同申请 - 审批流记录下载
     * @Permission(action='contract_platform.my_list.wf_down')
     * @return mixed
     */
    public function wfDownloadAction()
    {
        $param = $this->request->get();

        try {
            Validation::validate($param, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('contract_platform_wf_download_' . $param['id'] . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($param) {
            return ContractPlatFormService::getInstance()->wfDownload($param, $this->user['id']);
        }, $lock_key, 30);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }


    /**
     *
     * @Token
     */
    public function askAction()
    {
        $biz_id      = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        $to_staffs   = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $biz_id,
            'note'        => $note,
            'to_staff'    => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request   = (new ContractPlatFormFlowService())->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result    = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id      = $this->request->get('ask_id', 'int');
        $note        = $this->request->get('note', 'trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $ask_id,
            'note'        => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 待回复征询列表
     * @Token
     */
    public function replyListAction()
    {
        $params = $this->request->get();
        $list   = ContractPlatFormService::getInstance()->getList($params, $this->user['id'], ContractPlatFormService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 数据查询-导出excel
     * @Permission(action='contract_platform.get_all_list.export_excel')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractPlatFormService::$not_must_params);
        Validation::validate($params, ContractPlatFormService::$validate_list_search);

        $lock_key = md5('export_contract_platform_lock_key_' . $this->user['id']);
        $list     = $this->atomicLock(function () use ($params) {
            return ContractPlatFormService::getInstance()->getDownloadList($params, $this->user['id']);
        }, $lock_key, 120);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list['data']);
    }

    /**
     * 合同管理 - 数据查询 - 审批流记录下载
     * @Permission(action='contract_platform.get_all_list.list')
     * @return mixed
     */
    public function wfSearchDownloadAction()
    {
        $param = $this->request->get();

        try {
            Validation::validate($param, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('contract_platform_search_wf_download_' . $param['id'] . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($param) {
            return ContractPlatFormService::getInstance()->wfDownload($param, $this->user['id']);
        }, $lock_key, 30);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 详情页查看/下载合同文件
     * @Token
     */
    public function viewFileAction()
    {
        $params = $this->request->get();

        $this->logger->info(['contract_platform_view_file_log' => ['params' => $params, 'user' => $this->user,],]);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success']);
    }



}
