<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\ElectronicSignService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ElectronicSignController extends BaseController
{
    protected $sign_info = [];

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();

        // 签字流程, otp code 验证通过后, 后续接口通过 sign_token 鉴权
        $sign_key = $this->request->getSignKey();
        if (!empty($sign_key)) {
            $sign_key_item = explode('-', $sign_key);

            // 获取电子合同信息
            $this->sign_info = ElectronicSignService::getInstance()->getElectronicSignInfo($sign_key_item[0], $sign_key_item[1], $sign_key_item[2]);
        }
    }

    /**
     * 链接验证
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86402
     */
    public function checkLinkAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'emsk' => 'Required|StrLenGeLe:67,68|>>>:' . $this->t->_('params_error', ['param' => 'emsk'])
        ]);

        $res = ElectronicSignService::getInstance()->checkSignKey($params['emsk']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['email' => $res['email']]);
    }

    /**
     * 认证验证码
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86405
     */
    public function sendOptCodeV1Action()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'emsk' => 'Required|StrLenGeLe:67,68|>>>:' . $this->t->_('params_error', ['param' => 'emsk']),
        ]);

        ElectronicSignService::getInstance()->sendOtpCodeV1($params['emsk']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success');
    }

    /**
     * 认证验证码校验
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86420
     */
    public function optCodeCheckV1Action()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'emsk' => 'Required|StrLenGeLe:67,68|>>>:' . $this->t->_('params_error', ['param' => 'emsk']),
            'code' => 'Required|StrLen:' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_LENGTH . '|>>>:' . $this->t->_('params_error', ['param' => 'code']),
        ]);

        $res = ElectronicSignService::getInstance()->optCodeCheckV1($params['emsk'], $params['code']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 电子合同文件流
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86423
     */
    public function getFileContentAction()
    {
        $res = ElectronicSignService::getInstance()->getElectronicContractFileContent($this->sign_info);
        $this->response
//            ->setHeader('Content-Type', 'application/octet-stream; charset=UTF-8')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $res['header_info']['file_name'] . '"')
            ->setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

        if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) || empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
            $this->response->setHeader('Content-Length', $res['header_info']['Content-Length']);
        }

        // 如下设置Content-Type 未生效, 被重置
        $this->response->setFileToSend($res['sign_info']['file_url'], $res['header_info']['file_name']);
        return $this->response;
    }

    /**
     * 去签约-签约详情
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86450
     */
    public function getSignDetailAction()
    {
        $res = ElectronicSignService::getInstance()->getSignPageDetail($this->sign_info);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 异步更新指定字段
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86453
     */
    public function asyncSaveFieldAction()
    {
        $params = trim_array($this->request->get());

        $field_name_item = implode(',', ContractEnums::ELECTRONIC_CONTRACT_CLIENT_CAN_EDIT_FIELDS);
        $validate = [
            'sign_key' => 'Required|StrLen:32|>>>:' . $this->t->_('params_error', ['param' => 'sign_key']),
            'field_name' => 'Required|StrIn:' . $field_name_item . '|>>>:' . $this->t->_('params_error', ['param' => 'field_name']),
        ];

        if ($params['field_name'] == ContractEnums::ELECTRONIC_CONTRACT_FIELD_RELATED_FILE) {
            $validate['field_value'] = 'Required|ArrLen:1|>>>:' . $this->t->_('params_error', ['param' => 'field_value']);
            $validate['field_value[*].action'] = 'Required|StrIn:add,del|>>>:' . $this->t->_('params_error', ['param' => 'field_value.action']);
            $validate['field_value[*].object_key'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'field_value.object_key']);
            $validate['field_value[*].file_name'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'field_value.file_name']);
            $validate['field_value[*].bucket_name'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'field_value.bucket_name']);
        } else {
            $validate['field_value'] = 'Required|StrLenGeLe:0,255|>>>:' . $this->t->_('params_error', ['param' => 'field_value']);
        }

        Validation::validate($params, $validate);

        $lock_key = md5('electronic_contract_client_async_save_field_' . $this->sign_info['electronic_no']);
        $res = $this->atomicLock(function () use ($params) {
            return ElectronicSignService::getInstance()->saveFieldValue($params, $this->sign_info);
        }, $lock_key, 10);

        if ($res) {
            $code = ErrCode::$SUCCESS;
            $message = 'success';
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 商务转POA签字
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86456
     */
    public function transferPOASignAction()
    {
        $params = trim_array($this->request->get());

        Validation::validate($params, [
            'sign_key' => 'Required|StrLen:32|>>>:' . $this->t->_('params_error', ['param' => 'sign_key']),
        ]);

        ElectronicSignService::getInstance()->transferPOASign($params['sign_key'], $this->sign_info);
        return $this->returnJson(ErrCode::$SUCCESS, 'success');
    }

    /**
     * 签字环节-发送验证码
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86459
     */
    public function sendOptCodeV2Action()
    {
        $params = $this->request->get();

        $otp_scences = ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN . ',' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN;
        Validation::validate($params, [
            'sign_key' => 'Required|StrLen:32|>>>:' . $this->t->_('params_error', ['param' => 'sign_key']),
            'otp_scence' => 'Required|IntIn:' . $otp_scences . '|>>>:' . $this->t->_('params_error', ['param' => 'otp_scence']),
        ]);

        ElectronicSignService::getInstance()->sendOtpCode($this->sign_info['electronic_key'], $params['sign_key'], $params['otp_scence'], $this->sign_info);
        return $this->returnJson(ErrCode::$SUCCESS, 'success');
    }

    /**
     * 商务签字-提交上传签字图片
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86462
     */
    public function saveSignImageAction()
    {
        $params = $this->request->get();

        $otp_scences = ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN . ',' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN;
        Validation::validate($params, [
            'sign_key' => 'Required|StrLen:32|>>>:' . $this->t->_('params_error', ['param' => 'sign_key']),
            'otp_scence' => 'Required|IntIn:' . $otp_scences . '|>>>:' . $this->t->_('params_error', ['param' => 'otp_scence']),
            'code' => 'Required|StrLen:6|>>>:' . $this->t->_('params_error', ['param' => 'code']),
            'sign_img' => 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'sign_img']),
        ]);

        $lock_key = md5('electronic_contract_client_save_sign_image_' . $this->sign_info['current_email_key']);
        $res = $this->atomicLock(function () use ($params) {
            return ElectronicSignService::getInstance()->saveSignImage($params, $this->sign_info);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 商务签字-提交完整签字表单
     * @SignToken
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86474
     */
    public function saveSignAction()
    {
        $params = trim_array($this->request->get());
        $params['related_file_list'] = $params['related_file_list'] ?? [];

        $validate = [
            'custom_company_sign_img' => 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'custom_company_sign_img']),
            'custom_poa_item' => 'Required|ArrLenGeLe:1,3|>>>:' . $this->t->_('params_error', ['param' => 'custom_poa_item']),
            'custom_poa_item[*].sign_key' => "Required|StrLen:32|>>>:" . $this->t->_('params_error', ['param' => 'sign_key']),
            'custom_poa_item[*].sign_job_title' => "Required|StrLenGeLe:1,100|>>>:" . $this->t->_('params_error', ['param' => 'sign_job_title']),
            'custom_poa_item[*].sign_img' => "Required|Url|>>>:" . $this->t->_('params_error', ['param' => 'sign_img']),
        ];

        if (!empty($params['return_person_signature_img'])) {
            $validate['return_person_signature_img'] = 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'return_person_signature_img']);
        }

        // 相关资料 Retail 必填
        if (ElectronicSignService::getInstance()->isRetailManagement($this->sign_info['department_id'])) {
            $validate['related_file_list'] = 'Required|ArrLenGeLe:1,20|>>>:' . $this->t->_('params_error', ['param' => 'related_file_list']);
            $validate['related_file_list[*].object_key'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'related_file_list.object_key']);
            $validate['related_file_list[*].file_name'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'related_file_list.file_name']);
            $validate['related_file_list[*].bucket_name'] = 'Required|StrLenGeLe:1,255|>>>:' . $this->t->_('params_error', ['param' => 'related_file_list.bucket_name']);
        }

        Validation::validate($params, $validate);

        $lock_key = md5('electronic_contract_client_save_sign_' . $this->sign_info['electronic_no']);
        $res = $this->atomicLock(function () use ($params) {
            return ElectronicSignService::getInstance()->saveAllSign($params, $this->sign_info);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 上传图片
     *
     * @SignToken
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86477
     */
    public function getUploadTokenAction()
    {
        $filename = $this->request->get('filename');
        $params = [
            'filename' => $filename
        ];

        Validation::validate($params, [
            'filename' => "Required|StrLenGeLe:1,1000|>>>:" . $this->t->_('params_error', ['param' => 'filename'])
        ]);

        $lock_key = md5('electronic_contract_client_get_upload_token_' . $this->sign_info['current_email_key']);
        $res = $this->atomicLock(function () use ($filename) {
            if (env('break_away_from_ms')) {
                return OssHelper::uploadFileHcm($filename);
            } else {
                return OssHelper::uploadFileFle($filename, 'oca');
            }
        }, $lock_key, 10);

        if ($res) {
            $code = ErrCode::$SUCCESS;
            $message = 'success';
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message, $res);
    }

}
