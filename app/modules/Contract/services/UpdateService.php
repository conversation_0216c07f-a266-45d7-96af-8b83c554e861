<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;

class UpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return UpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $id
     * @param $data
     * @param $user
     * @return array
     */
    public function one($id, $data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 编辑更新, 仅能更新自己申请的合同
            $contract = Contract::findFirst([
                'conditions' => 'id = :id: AND cno = :cno: AND create_id = :create_id:',
                'bind' => ['id' => $id, 'cno' => $data['cno'], 'create_id' => $user['id']]
            ]);
            if (empty($contract) || empty($contract->cno) || empty($contract->template_id)) {
                throw new ValidationException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            // 处理公共数据
            $data = $this->handleSaveData($data, $user, false);

            // PMD部门销售合同的校验: 启用国家: 泰国/菲律宾/马来
            $user['is_pmd_department'] = $this->isPmdSubmitSalesContract($data['template_id'], $user['sys_department_id']);
            //校验是否是Flash Home Operation
            $user['is_flash_home_operation'] = $this->isFlashHomeOperation($user['node_department_id']);

            //报价单不在合同主表更新
            $quotation = [];
            if (isset($data['quotation_no'])) {
                $quotation = $data['quotation_no'];
                unset($data['quotation_no']);
            }

            $contract_file_arr = $data['contract_file_arr'];
            $attachment_arr    = $data['attachment_arr'];
            unset($data['contract_file_arr']);
            unset($data['attachment_arr']);

            //修改合同主表信息
            $old_contract_model = clone $contract;
            if ($contract->i_update($data) === false) {
                throw new BusinessException('contract 更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . 'data=' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            //删除历史附件
            $attachment_ids = array_values(array_filter(array_merge(array_column($contract_file_arr, 'id'), array_column($attachment_arr,'id'))));
            $conditions = 'oss_bucket_key = :key: and oss_bucket_type in ({oss_bucket_type:array})';
            $bind = ['key' => $contract->id, 'oss_bucket_type' => [Enums::OSS_BUCKET_TYPE_CONTRACT_FILE, Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT]];
            if (!empty($attachment_ids)) {
                //没有历史附件需要保留，直接删除所有;有历史附件需要保留，则只需删除非保留的附件即可
                $conditions .= ' and id not in({attachment_ids:array})';
                $bind['attachment_ids'] = $attachment_ids;
            }
            $bool = AttachModel::find([
                'conditions' => $conditions,
                'bind'       => $bind
            ])->delete();
            if ($bool === false) {
                throw new BusinessException("合同原附件删除失败, oss_bucket_key={$contract->id}", ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            $attachArr = [];
            if (!empty($contract_file_arr)) {
                foreach ($contract_file_arr as $k => $v) {
                    //有附件id说明是原有的无需处理
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp = [];
                    $tmp['oss_bucket_key'] = $contract->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_CONTRACT_FILE;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $v['bucket_name'];
                    $tmp['object_key'] = $v['object_key'];
                    $tmp['file_name'] = $v['file_name'];
                    $attachArr[] = $tmp;
                }
            }

            if (!empty($attachment_arr)) {
                foreach ($attachment_arr as $k => $v) {
                    //有附件id说明是原有的无需处理
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp = [];
                    $tmp['oss_bucket_key'] = $contract->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $v['bucket_name'];
                    $tmp['object_key'] = $v['object_key'];
                    $tmp['file_name'] = $v['file_name'];
                    $attachArr[] = $tmp;
                }
            }

            if (!empty($attachArr)) {
                if ((new AttachModel())->batchInsert($attachArr) === false) {
                    throw new BusinessException('合同附件创建失败=' . json_encode($attachArr, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //删除关联报价单
            $contract_quotation_model = new ContractQuotationModel();
            $quotation_delete_bool = $contract_quotation_model::find([
                'conditions' => 'contract_id = :contract_id:',
                'bind' => ['contract_id' => $contract->id]
            ])->delete();
            if ($quotation_delete_bool === false) {
                throw new BusinessException('合同关联报价单删除失败, contract_id=' . $contract->id . ' ;可能的原因是: ' . get_data_object_error_msg($contract_quotation_model), ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            //重新关联报价单
            if (!empty($quotation)) {
                $now_time = date('Y-m-d H:i:s');
                $contract_quotation_data = [];
                $apply_data = AddService::getInstance()->checkQuotation($quotation, $data['cno']);
                foreach ($apply_data as $apply_info) {
                    $tmp = [];
                    $tmp['contract_id'] = $contract->id;
                    $tmp['cno'] = $contract->cno;
                    $tmp['quotation_no'] = $apply_info['quoted_price_list_sn'];
                    $tmp['created_at'] = $now_time;
                    $tmp['updated_at'] = $now_time;
                    //KA客户直接使用报价单的客户id, C码客户需要在配置客户ID处配置
                    if ($apply_info['customer_type_category'] == Enums::CONSUMER_TYPE_KA) {
                        $tmp['configure_consumer_id'] = $apply_info['customer_id'];
                        $tmp['configure_consumer_time'] = $now_time;
                    } else {
                        $tmp['configure_consumer_id'] = '';
                        $tmp['configure_consumer_time'] = null;
                    }
                    $contract_quotation_data[] = $tmp;
                }
                //入库
                if (!empty($contract_quotation_data)) {
                    if ($contract_quotation_model->batch_insert($contract_quotation_data) === false) {
                        throw new BusinessException('合同关联报价单更新失败, 数据=' . json_encode($contract_quotation_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_UPDATE_ERROR);
                    }
                }
            }

            // 电子合同表审批状态回写
            if ($contract->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
                $contract_electronic = ContractElectronicModel::findFirst([
                    'conditions' => 'relate_id = :relate_id:',
                    'bind' => ['relate_id' => $id]
                ]);
                if (empty($contract_electronic)) {
                    throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                }

                $contract_electronic->approve_status = Enums::CONTRACT_STATUS_PENDING;
                if ($contract_electronic->save() === false) {
                    throw new BusinessException("关联的电子合同审批状态更新失败, relate_id={$id}, 原因可能是:" . get_data_object_error_msg($contract_electronic), ErrCode::$CONTRACT_UPDATE_ERROR);
                }
            }

            // 新审批流
            $request_bool = (new ContractFlowService())->recommit($contract, $old_contract_model, $user);
            if (!$request_bool) {
                throw new BusinessException('合同编辑提交, 审批流重建失败, biz_id=' . $contract->id, ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (TxFailed $e) {
            //数据库错误，不可对外抛出
            $code = ErrCode::$MYSQL_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

}
