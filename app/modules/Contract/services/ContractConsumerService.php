<?php

namespace App\Modules\Contract\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\CrmQuotationEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\CrmQuotation\Models\CrmQuotationSyncFailedModel;
use App\Modules\CrmQuotation\Services\CrmFlowService;
use App\Modules\User\Models\AttachModel;

class ContractConsumerService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 合同配置用户id
     * @param string $cno 合同编号
     * @param array $params_quotation[] = ['quotation_no'=>xxx,'consumer_id'=>123]
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @date 2021/10/14
     */
    public function addConsumerId($cno, array $params_quotation)
    {
        //验证合同状态(销售合同,审批已通过,关联了报价单,报价单客户类型是C码)
        //查询合同信息
        $contract = Contract::findFirst([
            'conditions' => 'cno = :cno:',
            'bind' => ['cno' => $cno]
        ]);

        //合同不存在
        if (empty($contract)) {
            throw new BusinessException('合同不存在, cno = ' . $cno, ErrCode::$CONTRACT_GET_INFO_ERROR);
        }

        //非销售合同
        if (!$this->isSalesContract($contract->template_id)) {
            throw new ValidationException(static::$t->_('contract_not_sale'), ErrCode::$VALIDATE_ERROR);
        }

        //参数传来的所有单号
        $params_quotation_nos = array_column($params_quotation, 'quotation_no');

        //查询未配置客户的报价单
        $contract_quotation_model = new ContractQuotationModel();
        $contract_quotation_data = $contract_quotation_model::find([
            'conditions' => 'contract_id = :contract_id: and configure_consumer_id = :empty_string:',
            'bind' => ['contract_id' => $contract->id, 'empty_string' => '']
        ]);
        $contract_quotation_arr = $contract_quotation_data->toArray();

        //此销售合同没有关联报价单
        if (empty($contract_quotation_arr)) {
            throw new ValidationException(static::$t->_('contract_no_quotation'), ErrCode::$VALIDATE_ERROR);
        }
        $quotation_ons = array_column($contract_quotation_arr, 'quotation_no');

        //参数传来的单号和需要配置的单号不一致
        if (array_diff($quotation_ons, $params_quotation_nos) || array_diff($params_quotation_nos, $quotation_ons)) {
            throw new ValidationException(static::$t->_('params_quotation_nos_error'), ErrCode::$VALIDATE_ERROR);
        }

        // 报价单状态
        $crm_quotation = CrmQuotationApplyModel::find([
            'conditions' => 'quoted_price_list_sn in ({quotation_nos:array})',
            'bind' => ['quotation_nos' => $quotation_ons]
        ]);
        $crm_quotation_arr = $crm_quotation->toArray();
        $crm_quotation_kv = array_column($crm_quotation_arr, null, 'quoted_price_list_sn');
        foreach ($params_quotation as $v) {
            //验证crm报价单信息
            if (!isset($crm_quotation_kv[$v['quotation_no']])) {
                throw new BusinessException('报价单不存在: no=' . $v, ErrCode::$QUOTATION_NOT_EXIST);
            }
            if ($crm_quotation_kv[$v['quotation_no']]['sync_status'] == CrmQuotationEnums::SYNC_STATUS_YES) {
                throw new BusinessException('报价单已同步过MS: no=' . $v, ErrCode::$QUOTATION_NOT_EXIST);
            }
            //验证客户id
            $ms_consumer = $this->checkConsumerId($v['consumer_id']);
            if (empty($ms_consumer) || $ms_consumer[0]['consumer_id'] != $v['consumer_id'] || $ms_consumer[0]['can_use'] != 1) {
                throw new ValidationException(static::$t->_('contract_consumer_id_error') . ': consumer_id=' . $v['consumer_id'], ErrCode::$VALIDATE_ERROR);
            }
        }

        //验证通过,配置客户id,同步ms
        $code = ErrCode::$SUCCESS;
        $message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $datetime = date('Y-m-d H:i:s');
            //修改合同关联报价单信息
            $params_quotation_kv = array_column($params_quotation, null, 'quotation_no');
            foreach ($contract_quotation_data as $cq_obj) {
                $update_data = [];
                $update_data['configure_consumer_id'] = $params_quotation_kv[$cq_obj->quotation_no]['consumer_id'];
                $update_data['configure_consumer_time'] = $datetime;
                $update_data['updated_at'] = $datetime;
                $bool = $cq_obj->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('配置账户ID失败 contract_no=' . $cno . '; quotation_no=' . $cq_obj->quotation_no, ErrCode::$CONTRACT_CONFIGURE_CONSUMER_ERROR);
                }
            }

            //修改报价单信息
            foreach ($crm_quotation as $quotation_obj) {
                $quotation_bool = $quotation_obj->i_update(['sync_status' => CrmQuotationEnums::SYNC_STATUS_YES]);
                if ($quotation_bool === false) {
                    throw new BusinessException('更新报价单同步状态失败 quotation_no=' . $quotation_obj->quoted_price_list_sn, ErrCode::$QUOTATION_UPDATE_ERROR);
                }
                //同步报价单给MS
                $quotation_obj->customer_id = $params_quotation_kv[$quotation_obj->quoted_price_list_sn]['consumer_id'];
                $sync_status = (new CrmFlowService())->syncMS($quotation_obj, 1, $contract->effective_date ?? '', $contract->expiry_date ?? '');
                if (!$sync_status) {
                    throw new BusinessException('账户ID同步MS失败 quotation_no=' . $quotation_obj->quoted_price_list_sn, ErrCode::$CONTRACT_SYNC_MS_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('contract-configure-consumer-error: params_quotation=' . json_encode($params_quotation) . '; cno=' . $cno . '; message=' . $e->getMessage() . '; Trace=' . $e->getTraceAsString());

        } catch (\Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('contract-configure-consumer-error: params_quotation=' . json_encode($params_quotation) . '; cno=' . $cno . '; message=' . $e->getMessage() . '; Trace=' . $e->getTraceAsString());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 验证用户id
     * @param $consumer_id
     * @date 2021/10/14
     * @return array
     * @throws BusinessException
     */
    public function checkConsumerId($consumer_id){
        $fle_rpc = new ApiClient('fle','com.flashexpress.fle.svc.api.KaProfileSvc','getKaProfileInfo');
        $fle_rpc->setParams([
            ['id'=>$consumer_id]
        ]);
        $fle_return = $fle_rpc->execute();
        if (!isset($fle_return['result'])) {
            $this->logger->error('getKaProfileInfoRequest:' . json_encode(['id'=>$consumer_id]) . ":freightDiscountresponse:" . json_encode($fle_return));
            throw new BusinessException('get ms consumer failed',ErrCode::$CONTRACT_GET_CONSUMER_ERROR);
        }
        $this->logger->info('getKaProfileInfoRequest:' . json_encode(['id'=>$consumer_id]) . ":freightDiscountresponse:" . json_encode($fle_return));
        $consumer_info = [];
        if(!empty($fle_return['result']['id'])){
            $consumer_info[0]['consumer_id'] = $fle_return['result']['id'];
            $consumer_info[0]['can_use'] = 2;
            //state 是否禁用 true(禁用) false(可用)
            if ($fle_return['result']['type'] == Enums::MS_CONSUMER_TYPE_KA && !$fle_return['result']['state']){
                $consumer_info[0]['can_use'] = 1;
            }
        }
        return $consumer_info;
    }

    /**
     * 搜索报价单信息 (已通过审批,C码和KA类型,未同步过MS)
     * @param $quotation_no
     * @date 2021/10/15
     * @return array
     */
    public function searchQuotation($quotation_no)
    {
        try {
            //客户类型过滤
            $country_code = get_country_code();
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $consumer_type = [Enums::CONSUMER_TYPE_CRM, Enums::CONSUMER_TYPE_KA];
                $conditions = 'quoted_price_list_sn LIKE :quoted_price_list_sn: and state = :state: and sync_status = :sync_status: and customer_type_category in ({customer_type_category:array})';
                $bind = [
                    'quoted_price_list_sn' => '%' . $quotation_no . '%',
                    'state' => Enums::WF_STATE_APPROVED,
                    'sync_status' => CrmQuotationEnums::MS_SYNC_STATUS_NO,
                    'customer_type_category' => $consumer_type
                ];
            } else {
                $conditions = 'quoted_price_list_sn LIKE :quoted_price_list_sn: and state = :state:';
                $bind = [
                    'quoted_price_list_sn' => '%' . $quotation_no . '%',
                    'state' => Enums::WF_STATE_APPROVED,
                ];
            }
            $list = CrmQuotationApplyModel::find([
                'columns' => 'quoted_price_list_sn',
                'conditions' => $conditions,
                'bind' => $bind
            ]);
            $list = empty($list) ? [] : $list->toArray();
            return [
                'code' => ErrCode::$SUCCESS,
                'message' => '',
                'data' => $list
            ];
        } catch (\Exception $e) {
            $this->logger->warning('search_quotation_exception:message=' . $e->getMessage() . ',trace=' . $e->getTraceAsString());
            return [
                'code' => ErrCode::$SYSTEM_ERROR,
                'message' => static::$t->_('retry_later'),
                'data' => $e->getMessage()
            ];
        }
    }

    /**
     * 通过报价单号查询合同
     * @param $quotation_no
     * @return array
     * @date 2021/10/16
     */
    public function getContractByQuotation($quotation_no)
    {
        //查询报价单关联的合同
        $contract_quotation_info = ContractQuotationModel::findFirst([
            'conditions' => 'quotation_no = :quotation_no:',
            'bind' => ['quotation_no' => $quotation_no]
        ]);
        $contract_info = [];
        if ($contract_quotation_info) {
            $contract_info = Contract::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $contract_quotation_info->contract_id]
            ]);
        }
        return $contract_info = empty($contract_info) ? [] : $contract_info->toArray();
    }

    /**
     * 是否能访问该业务表，===true可以，其他不行
     * @param $request
     * @return bool|string
     */
    public function isCanView($request){
        //如果user_id为null，代表数据查询，可以访问
        if(empty($user_id)){
            return true;
        }

        //都没审批流，不让访问
        if(empty($request)){
            return self::$t->_("no_permission_to_view");
        }

        $viewer_ids = $request->viewer_ids;
        if(empty($viewer_ids) || !in_array($user_id,explode(",",$viewer_ids))){
            return self::$t->_("no_permission_to_view");
        }
        return true;
    }

    /**
     * 记录同步MS失败的报价单,等待定时任务处理
     * @date 2021/10/16
     * @param $quotation_no
     * @param $source
     * @return mixed
     */
    public function retry($quotation_no,$source){
        $insert = [
            'quotation_no' => $quotation_no,
            'sync_source' => $source,
            'sync_time' => gmdate('Y-m-d H:i:s'),
            'retry_counter' => 0,
            'retry_result' => 0,
        ];
        return (new CrmQuotationSyncFailedModel())->i_create($insert);
    }
}
