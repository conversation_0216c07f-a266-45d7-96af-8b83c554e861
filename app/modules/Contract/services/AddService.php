<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\CrmQuotationEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Enums\ContractEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Util\RedisKey;

class AddService extends BaseService
{
    public static $not_must_params = [
        'attachment_arr',
        'effective_date',
        'expiry_date'
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function one($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = Contract::findFirst([
                'conditions' => 'cno = :cno:',
                'bind' => ['cno' => $data['cno']],
                'columns' => 'id'
            ]);

            // 合同已存在
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('contract_number_has_been_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 处理公共数据
            $data = $this->handleSaveData($data, $user, true);

            // 销售合同校验编号
            $is_sales_contract = $this->isSalesContract($data['template_id']);
            if ($is_sales_contract && empty($data['sell_cno'])) {
                throw new ValidationException(static::$t->_('contract_number_is_null'), ErrCode::$VALIDATE_ERROR);
            }

            // 销售合同校验
            if ($is_sales_contract) {
                // FlashSupplyChainManagement的条件
                $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
                $deptId   = !empty($user['node_department_id']) ? $user['node_department_id'] : $user['sys_department_id'];
                $department_info = (new DepartmentService())->getDepartmentDetail($deptId);

                // 销售合同,且是FlashSupplyChainManagement
                if ((string)$department_info['company_id'] == $company_ids['FlashSupplyChainManagement'] || $data['company_code'] == ContractEnums::CONTRACT_COMPANY_FLASH_SUPPLY_CHAIN_MANAGEMENT) {
                    // 结算方式校验
                    if (!is_numeric($data['pay_method']) || !key_exists($data['pay_method'], Enums::$pay_method)) {
                        throw new ValidationException(static::$t->_('pay_method_is_null'), ErrCode::$VALIDATE_ERROR);
                    }

                    // 结算天数校验(当“结算方式”=特殊条款时，非必填)
                    if ($data['pay_method'] == Enums::PAY_METHOD_SPECIAL) {
                        if (!key_exists($data['balance_days'], Enums::$contract_balance_days)) {
                            unset($data['balance_days']);
                        }

                    } else {
                        if (!key_exists($data['balance_days'], Enums::$contract_balance_days)) {
                            throw new ValidationException(static::$t->_('balance_days_error'), ErrCode::$VALIDATE_ERROR);
                        }
                    }

                    // 计价方式校验
                    if (!key_exists($data['valuation_type'], Enums::$contract_valuation_type)) {
                        throw new ValidationException(static::$t->_('valuation_type_error'), ErrCode::$VALIDATE_ERROR);
                    }

                    // 折扣比例校验
                    if (empty($data['discount'])) {
                        $data['discount'] = 0;
                    }

                } else {
                    // 结算方式校验
                    if (!is_numeric($data['pay_method']) || !in_array($data['pay_method'], [Enums::PAY_METHOD_FIXED, Enums::PAY_METHOD_NOT_FIXED])) {
                        throw new ValidationException(static::$t->_('pay_method_is_null'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // PMD部门销售合同的校验: 启用国家: 泰国/菲律宾/马来
            $user['is_pmd_department'] = $this->isPmdSubmitSalesContract($data['template_id'], $user['sys_department_id']);
            //校验是否是Flash Home Operation
            $user['is_flash_home_operation'] = $this->isFlashHomeOperation($user['node_department_id']);

            // 授权范围校验
            if ($is_sales_contract && Enums::CONTRACT_IS_MASTER_NO == $data['is_master']
                &&
                (!is_numeric($data['in_scope']) || !in_array($data['in_scope'], [Enums::IN_SCOPE_YES, Enums::IN_SCOPE_NO]))
            ) {
                throw new ValidationException(static::$t->_('in_scope_is_null'), ErrCode::$VALIDATE_ERROR);
            }
            //合同报价单不存储主表
            $quotation = [];
            if (isset($data['quotation_no'])) {
                $quotation = $data['quotation_no'];
                unset($data['quotation_no']);
            }
            $contract_file_arr = $data['contract_file_arr'];
            $attachment_arr    = $data['attachment_arr'] ?? [];
            unset($data['contract_file_arr']);
            unset($data['attachment_arr']);

            if (isset($data['rejected_at'])) {
                unset($data['rejected_at']);
            }
            if (isset($data['refuse_reason'])) {
                unset($data['refuse_reason']);
            }
            if (isset($data['approved_at'])) {
                unset($data['approved_at']);
            }

            $model = new Contract();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('合同创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //电子合同表审批状态
            if ($data['contract_storage_type'] == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
                $contract_electronic = ContractElectronicModel::findFirst([
                    'conditions' => 'id =:id:',
                    'bind'       => ['id' => $data['electronic_id']]
                ]);
                if (empty($contract_electronic)) {
                    throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                }

                if (!empty($contract_electronic->relate_id)) {
                    throw new ValidationException(static::$t->_('contract_electronic_has_create_contract'), ErrCode::$VALIDATE_ERROR);
                }
                $contract_electronic->approve_status = Enums::CONTRACT_STATUS_PENDING;
                $contract_electronic->status         = ContractEnums::CONTRACT_ELECTRONIC_STATUS_3;
                $contract_electronic->relate_id      = $model->id;

                if ($contract_electronic->save() === false) {
                    //电子合同审批状态更新失败了
                    throw new BusinessException('电子合同表状态更新失败：' . get_data_object_error_msg($contract_electronic) . '数据：relate_id' . $model->id, ErrCode::$CONTRACT_UPDATE_ERROR);
                }
            }

            // 合同相关附件/文件
            $attachArr = [];

            //必填
            if (!empty($contract_file_arr)) {
                foreach ($contract_file_arr as $k => $v) {
                    $tmp = [];
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_CONTRACT_FILE;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $v['bucket_name'];
                    $tmp['object_key'] = $v['object_key'];
                    $tmp['file_name'] = $v['file_name'];
                    $attachArr[] = $tmp;
                }
            }

            //也必填
            if (!empty($attachment_arr)) {
                foreach ($attachment_arr as $k => $v) {
                    $tmp = [];
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $v['bucket_name'];
                    $tmp['object_key'] = $v['object_key'];
                    $tmp['file_name'] = $v['file_name'];
                    $attachArr[] = $tmp;
                }
            }

            if (!empty($attachArr)) {
                if ((new AttachModel())->batchInsert($attachArr) === false) {
                    throw new BusinessException('合同附件创建失败, 数据: ' . json_encode($attachArr, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //关联报价单
            if (!empty($quotation)) {
                $now_time = date('Y-m-d H:i:s');
                $contract_quotation_data = [];
                $apply_data = $this->checkQuotation($quotation);
                foreach ($apply_data as $apply_info) {
                    $tmp = [];
                    $tmp['contract_id'] = $model->id;
                    $tmp['cno'] = $model->cno;
                    $tmp['quotation_no'] = $apply_info['quoted_price_list_sn'];
                    $tmp['created_at'] = $now_time;
                    $tmp['updated_at'] = $now_time;
                    //KA客户直接使用报价单的客户id, C码客户需要在配置客户ID处配置
                    if ($apply_info['customer_type_category'] == Enums::CONSUMER_TYPE_KA) {
                        $tmp['configure_consumer_id'] = $apply_info['customer_id'];
                        $tmp['configure_consumer_time'] = $now_time;
                    } else {
                        $tmp['configure_consumer_id'] = '';
                        $tmp['configure_consumer_time'] = null;
                    }
                    $contract_quotation_data[] = $tmp;
                }
                //入库
                if (!empty($contract_quotation_data)) {
                    $contract_quotation_model = new ContractQuotationModel();
                    if ($contract_quotation_model->batch_insert($contract_quotation_data) === false) {
                        throw new BusinessException('合同关联报价单创建失败, 数据=' . json_encode($contract_quotation_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
            }

            $flow_bool = (new ContractFlowService())->createRequest($model->id, $user);
            if (!$flow_bool) {
                throw new BusinessException('合同审批流生成失败, biz_id = ' . $model->id, ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => [
                'id' => !empty($model->id) ? $model->id : 0
            ]
        ];
    }

    /**
     * 验证报价单是否允许在合同关联
     * @param $quotation_no
     * @param string $foreclose
     * @return array
     * @throws ValidationException
     * @date 2021/10/21
     */
    public function checkQuotation(array $quotation_no, $foreclose = '')
    {
        $quotation_no = array_values(array_unique($quotation_no));
        $apply_data = CrmQuotationApplyModel::find([
            'conditions' => ' quoted_price_list_sn in ({quotation_no:array}) ',
            'bind' => [
                'quotation_no' => $quotation_no
            ]
        ])->toArray();
        if (empty($apply_data)) {
            throw new ValidationException(static::$t->_('quotation_not_exist'), ErrCode::$QUOTATION_NOT_EXIST);
        }
        //是否已被申请
        $conditions = 'cq.quotation_no in ({quotation_no:array})';
        $bind = ['quotation_no' => $quotation_no];
        //编辑时排除当前合同号
        if (!empty($foreclose)) {
            $conditions = 'cq.quotation_no in ({quotation_no:array}) and c.cno != :cno:';
            $bind = ['quotation_no' => $quotation_no, 'cno' => $foreclose];
        }
        //查询报价单号是否被占用
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => Contract::class]);
        $builder->columns(['c.status']);
        $builder->leftJoin(ContractQuotationModel::class, 'c.id = cq.contract_id', 'cq');
        $builder->Where($conditions, $bind);
        $exists = $builder->getQuery()->execute()->toArray();
        foreach ($exists as $exists_k => $exists_v) {
            // 报价单号已被关联
            if (in_array($exists_v['status'], [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL])) {
                throw new ValidationException(static::$t->_('quotation_no_has_been_exist'), ErrCode::$VALIDATE_ERROR);
            }
        }
        foreach ($apply_data as $apply_info) {
            //只有泰国校验这些
            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                //可以在合同审批时同步的情况 (1.ka客户且(pmd部门/bulky_business部门/sales部门定结/shop部门定结) 2.crm客户)
                $contract_condition = $apply_info['apply_user_department_type'] == Enums::DEPARTMENT_TYPE_PMD
                    || $apply_info['apply_user_department_type'] == Enums::DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT
                    || ($apply_info['apply_user_department_type'] == Enums::DEPARTMENT_TYPE_SALES && $apply_info['settlement_type'] == Enums::SETTLEMENT_TYPE_REGULAR)
                    || ($apply_info['apply_user_department_type'] == Enums::DEPARTMENT_TYPE_SHOP && $apply_info['settlement_type'] == Enums::SETTLEMENT_TYPE_REGULAR)
                    || $apply_info['apply_user_department_type'] == Enums::DEPARTMENT_TYPE_JVB;

                $contract_customer_ka = $apply_info['customer_type_category'] == Enums::CONSUMER_TYPE_KA;
                $contract_customer_crm = $apply_info['customer_type_category'] == Enums::CONSUMER_TYPE_CRM;

                $can_sync = ($contract_condition && $contract_customer_ka) || $contract_customer_crm;
                //基本校验,审批不通过 或 已同步过ms
                if ($apply_info['state'] != Enums::CONTRACT_STATUS_APPROVAL || $apply_info['sync_status'] == CrmQuotationEnums::MS_SYNC_STATUS_YES) {
                    throw new ValidationException(static::$t->_('quotation_state_error'), ErrCode::$QUOTATION_STATE_ERROR);
                }
                //不可以在合同审批时同步
                if (!$can_sync) {
                    throw new ValidationException(static::$t->_('quotation_state_error'), ErrCode::$QUOTATION_STATE_ERROR);
                }
            }
            //通用校验
            if ($apply_info['state'] != Enums::CONTRACT_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('quotation_state_not_pass_error'), ErrCode::$QUOTATION_STATE_ERROR);
            }
        }
        return $apply_data;
    }

    /**
     * 新增初始化接口
     * @param $user
     * @return array
     */
    public function getDefault($user)
    {
        return [
            'cno'           => self::genSerialNo('FEX', RedisKey::CONTRACT_CREATE_COUNTER),
            'apply_staff_department' => ($this->isFlashHomeOperation($user['node_department_id'])) ? ContractEnums::FLASH_HOME_OPERATION : 0
        ];

    }

}
