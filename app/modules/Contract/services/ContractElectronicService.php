<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/18
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\RestClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\ContractElectronicFormdataModel;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractElectronicReviewSignLogModel;
use App\Models\oa\ContractElectronicSignInfoModel;
use App\Models\oa\ContractElectronicSignLogModel;
use App\Models\oa\ContractElectronicStaffSignConfigModel;
use App\Models\oa\ContractTemplateVersionModel;
use App\Modules\Common\Services\AttachmentService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ContractElectronicFormdataRepository;
use App\Repository\oa\ContractElectronicRepository;
use App\Repository\oa\ContractElectronicReviewSignLogRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class ContractElectronicService extends BaseService
{
    private static $instance;

    /**
     * 有继承不可以改成私有化
     */
    public function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $validate_approve = [
        'id' => 'Required|IntGe:1',
        'note' => 'StrLenGeLe:0,1000',
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',   //每页条数
        'pageNum' => 'Required|IntGt:0',  //页码
        'create_start_date' => 'Required',          //创建开始日期
        'create_end_date' => 'Required',          //创建结束日期
        'contract_status' => 'Required', //合同状态
        'contract_type' => 'Required', //适用合同类型
        'customer_name' => 'Required',//客户名称
        'approve_status' => 'Required', //合同审批状态
        'sign_status' => 'Required', //合同签约状态
    ];

    // v20899 去掉业务表单项的校验
    public static $validate_save = [
//        'contract_name' => 'Required|StrLenGeLe:1,100',
        'template_no' => 'Required|StrLenGeLe:1,20',
        'department_id' => 'Required|IntGe:1',
        'lang' => 'Required|StrLenGeLe:1,10',
        'ver' => 'Required|StrLenGeLe:1,100',
        'template_name' => 'Required|StrLenGeLe:1,100',
        'file_url' => 'Required|StrLenGeLe:1,300',
        'is_completed' => 'Required|IntIn:1,2',
//        'start_date' => 'Required|Date',
//        'end_date' => 'Required|Date',
        'form_rule' => 'Required|Obj',
    ];

    /**
     * v20899 去掉业务表单项的校验
     * 电子合同提交校验
     *
     * @param array $params
     * @param bool $is_update
     * @throws ValidationException
     */
    public static function validateSaveParams(array $params, bool $is_update = false)
    {
        if ($is_update) {
            self::$validate_save['id'] = 'Required|IntGe:1';
        }

        // 合同模板未配置开始日期, 则不校验开始/结束日期的必填规则(实际上, 现结main 或 仅提交补充协议的情况: 不需要校验合同名称/日期等必填项)
//        if (
//            (isset($params['form_rule']['termInfo']) && !in_array('contract_start_date', array_column($params['form_rule']['termInfo'], 'field')))
//            ||
//            (isset($params['form_rule']['supplyAgreement']) && count($params['form_rule']) == 1)
//        ) {
//            self::$validate_save['contract_name'] = 'Required|StrLenGeLe:0,100';
//            unset(self::$validate_save['start_date']);
//            unset(self::$validate_save['end_date']);
//        }

        Validation::validate($params, self::$validate_save);
    }

    /**
     * 表单参数验证
     *
     * @param $data
     * @return array
     */
    public function validParam($data)
    {
        // only_number 只能输入数字 验证
        $valid_number_field = [
            'custom_phone',
            'contract_early_termination',
            'delayed_credit_reduction2',
            'delayed_credit_reduction3',
            'delayed_credit_reduction4',
        ];

        // 字符长度校验
        $valid_length_field = [
            'contract_name',
            'contract_no',
            'custom_company_name_th',
            'custom_company_name_en',
            'custom_legal_name_th',
            'custom_legal_name_en',
            'custom_contacts_name_th',
            'custom_contacts_name_en',
            'custom_address_th',
            'custom_address_en',
            'contract_early_termination',
        ];

        // date 校验
        $valid_date_field = [
            'contract_sign_date',
            'contract_start_date',
            'contract_end_date',
            'pdpa_sign_date',
            'agreement_start_date',
            'agreement_end_date',
            'agreement_sign_date',
        ];

        // 合同编号
        $contract_no_item = [
            'contract_no',// 合同编号
            'agreement_contract_no',// 协议关联的主合同编号
        ];

        //邮箱验证email custom_email  flash_email
        $valid_email_field = [];

        $this->logger->info('create_electronic_valid_before' . json_encode($data, JSON_UNESCAPED_UNICODE));

        foreach ($data['form_rule'] as &$item) {
            foreach ($item as $k_1 => &$v_1) {
//                if (in_array($v_1['field'], $valid_number_field)) {
//                    if (!preg_match('/^[0-9]*$/', $v_1['value'])) {
//                        $v_1['value'] = '';
//                    }
//                }

//                if (in_array($v_1['field'], $valid_length_field)) {
//                    if (!preg_match('/^([0-9]\d{0,1}|100$)(\.\d{1,2})?$/', mb_strlen($v_1['value']))) {
//                        $v_1['value'] = '';
//                    }
//                }

//                if (in_array($v_1['field'], $valid_date_field)) {
//                    if (!preg_match('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/', $v_1['value'])) {
//                        $v_1['value'] = '';
//                    }
//                }

//                if (in_array($v_1['field'], $valid_email_field)) {
//                    if (!preg_match('/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/i', $v_1['value'])) {
//                        $v_1['value'] = '';
//                    }
//                }

                // 将合同编号提取出来, 存储到电子合同主表
                if (in_array($v_1['field'], $contract_no_item) && !empty($v_1['value'])) {
                    $data['contract_no'] = $v_1['value'];
                }
            }
        }

        $this->logger->info('create_electronic_valid_after' . json_encode($data, JSON_UNESCAPED_UNICODE));

        return $data;
    }


    /**
     * 新增
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function add($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $contract_electronic_info = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 电子合同表单数据处理
            if (isCountry('MY')) {
                $data['created_id'] = $user['id'] ?? 0;
            }

            $template_data = $this->templateData($data);//合同制作增加

            // 补充待入库的电子合同数据
            $data = $this->getElectronicContractData($data, $template_data['data']);

            // 是否是主合同组合补充协议
            $version_info = ContractTemplateVersionModel::findFirst([
                'conditions' => 'template_no = :template_no:',
                'bind'       => ['template_no' => $data['template_no']],
                'columns'    => ['is_combination_supplemental_agreement', 'is_show_return_person_signature'],
            ]);

            $insert_data = [
                'no' => BaseService::genSerialNo('EC', RedisKey::CONTRACT_ELECTRONIC_COUNTER),
                'is_completed' => $data['is_completed'],
                'create_node_department_id' => $user['node_department_id'],
                'department_id' => $data['department_id'],
                'lang' => $data['lang'],
                'contract_no' => $data['contract_no'],
                'relate_template_no' => $data['template_no'],
                'relate_version_no' => $data['ver'],
                'contract_name' => $data['contract_name'],
                'template_type' => $data['contract_type'],
                'template_name' => $data['template_name'],
                'start_date' => empty($data['start_date']) ? null : $data['start_date'],
                'end_date' => empty($data['end_date']) ? null : $data['end_date'],
                'created_date' => date('Y-m-d'),
                'business_review' => $data['business_review'] ?? ContractEnums::BUSINESS_REVIEW_NO,
                'contract_status' => ContractEnums::CONTRACT_STATUS_1,
                'file_url' => $template_data['pdf_info']['object_key'] ?? '',
                'bucket_name' => $template_data['pdf_info']['bucket_name'] ?? '',
                'ftl_file_url' => $data['file_url'] ?? '',
                'customer_name' => $data['customer_name'] ?? '',
                'is_combination_supplemental_agreement' => $version_info->is_combination_supplemental_agreement ?? 0,
                'contract_content' => json_encode($data['form_rule'], JSON_UNESCAPED_UNICODE),
                'created_id' => $user['id'],
                'created_name' => $this->getNameAndNickName($user['name'] ?? '', $user['nick_name']),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_id' => $user['id'],
                'is_show_return_person_signature' => $version_info->is_show_return_person_signature ?? 0,
            ];

            $contract_electronic_model = new ContractElectronicModel();
            if ($contract_electronic_model->i_create($insert_data) === false) {
                throw new BusinessException('电子合同创建失败, ' . get_data_object_error_msg($contract_electronic_model) . '; 数据: ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // 写入电子合同->表单数据
            $this->saveFormData($insert_data['no'], $template_data['data'], $user);

            $db->commit();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-add-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $contract_electronic_info,
        ];
    }

    /**
     * 编辑
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function edit($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $detail = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']],
            ]);

            if (empty($detail)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同表单数据处理

            if (isCountry('MY')) {
                $data['created_id'] = $detail->created_id ?? 0;
            }

            $template_data = $this->templateData($data);

            // 补充待入库的电子合同数据
            $data = $this->getElectronicContractData($data, $template_data['data']); //合同制作编辑

            // 是否是主合同组合补充协议
            $version_info = ContractTemplateVersionModel::findFirst([
                'conditions' => 'template_no = :template_no:',
                'bind'       => ['template_no' => $data['template_no']],
                'columns'    => ['is_combination_supplemental_agreement', 'is_show_return_person_signature'],
            ]);

            $update_data = [
                'contract_name' => $data['contract_name'],
                'contract_no' => $data['contract_no'],
                'template_name' => $data['template_name'],
                'is_completed' => $data['is_completed'],
                'start_date' => empty($data['start_date']) ? null : $data['start_date'],
                'end_date' => empty($data['end_date']) ? null : $data['end_date'],
                'contract_status' => ContractEnums::CONTRACT_STATUS_1,
                'file_url' => $template_data['pdf_info']['object_key'] ?? '',
                'bucket_name' => $template_data['pdf_info']['bucket_name'] ?? '',
                'ftl_file_url' => $data['file_url'] ?? '',
                'customer_name' => $data['customer_name'] ?? '',
                'contract_content' => json_encode($data['form_rule'], JSON_UNESCAPED_UNICODE),
                'is_combination_supplemental_agreement' => $version_info->is_combination_supplemental_agreement ?? 0,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
                'is_show_return_person_signature' => $version_info->is_show_return_person_signature ?? 0,
            ];

            if ($detail->save($update_data) === false) {
                throw new BusinessException('电子合同更新失败, ' . get_data_object_error_msg($detail) . '; 数据: ' . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // 更新电子合同->表单数据
            $this->saveFormData($detail->no, $template_data['data'], $user);

            $db->commit();

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 存储表单数据
     *
     * @param string $electronic_no
     * @param array $form_data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function saveFormData(string $electronic_no, array $form_data, array $user)
    {
        $form_data_model = ContractElectronicFormdataModel::findFirst([
            'conditions' => 'electronic_no = :electronic_no:',
            'bind' => ['electronic_no' => $electronic_no],
        ]);

        $save_data = [
            'form_data' => json_encode($form_data, JSON_UNESCAPED_UNICODE),
            'updated_id' => $user['id'],
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        if (empty($form_data_model)) {
            $form_data_model = new ContractElectronicFormdataModel();
            $save_data['electronic_no'] = $electronic_no;
            $save_data['created_id'] = $user['id'];
            $save_data['created_at'] = date('Y-m-d H:i:s');
        }

        if ($form_data_model->save($save_data) === false) {
            throw new BusinessException('电子合同表单数据save失败, ' . get_data_object_error_msg($form_data_model) . '; 数据: ' . json_encode($save_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取电子合同的初始化数据
     *
     * @param array $params_data 请求参数
     * @param array $field_data 电子合同表单字段对应的数据
     * @return mixed
     */
    protected function getElectronicContractData(array $params_data, array $field_data = [])
    {
        // 合同编号
        $params_data['contract_no'] = $field_data['contract_no'] ?? '';

        $params_data['contract_name'] = !empty($params_data['contract_name']) ? $params_data['contract_name'] : ($field_data['contract_name'] ?? '');

        // 合同开始日期 / 结束日期
        if (empty($params_data['start_date'])) {
            if (!empty($field_data['contract_start_date'])) {
                $params_data['start_date'] = $field_data['contract_start_date'];
            } else if (!empty($field_data['issuing_date'])) {
                $params_data['start_date'] = $field_data['issuing_date'];

            } else if (!empty($field_data['agreement_start_date'])) {
                $params_data['start_date'] = $field_data['agreement_start_date'];
            }
        }

        if (empty($params_data['end_date'])) {
            if (!empty($field_data['contract_end_date'])) {
                $params_data['end_date'] = $field_data['contract_end_date'];
            } else if (!empty($field_data['validity_date'])) {
                $params_data['end_date'] = $field_data['validity_date'];
            } else if (!empty($field_data['agreement_end_date'])) {
                $params_data['end_date'] = $field_data['agreement_end_date'];
            }
        }

        return $params_data;
    }

    /**
     * 列表
     * @param $condition
     * @param $user
     * @param $type
     * @param bool $export 是否导出
     * @return array
     */
    public function getList($condition, $user, $type = 0, $export = false)
    {
        $condition['uid'] = $user['id'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $count = $export ? ContractEnums::CONTRACT_ELECTRONIC_EXPORT_MAX : $this->getTotal($condition, $user, $type);
        if ($count > 0) {
            $columns   = 'c.id,c.relate_id,c.no,c.lang,c.relate_version_no,c.contract_name,c.template_name,c.created_date,c.start_date,c.end_date,c.file_url,c.business_review,c.sign_status,c.status,c.approve_status,c.contract_status,c.initiator_type,c.customer_name,c.is_completed,c.created_id,c.created_name,c.created_at,c.department_id,c.business_approve_status';
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $columns .= ',log.audit_at';
            }

            // 其他合同编号
            if ($type == self::LIST_TYPE_SEARCH) {
                $columns .= ',contract.cno AS other_contract_no';
            }

            // 签约信息, 签约客户类型
            if ($type == self::LIST_TYPE_APPLY) {
                $columns .= ',sign_info.customer_type AS sign_customer_type';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => ContractElectronicModel::class]);
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $builder->columns($columns);
            if ($export === false) {
                $builder->limit($page_size, $offset);
            }

            if ($type == self::LIST_TYPE_AUDIT) {
                $builder->groupBy('c.id');
            }

            $builder->orderby('c.id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handelItems($items,$export);
        }

        return [
            'items'      => $items ?? [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ],
        ];
    }

    /**
     * 获取导出的总记录数
     * @param array $condition 请求参数组
     * @param array $user 当前登录着信息组
     * @param int $type 列表类型
     * @return int
     */
    public function getTotal($condition, $user, $type = 0)
    {
        $condition['uid'] = $user['id'];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractElectronicModel::class]);
        $builder = $this->getCondition($builder, $condition, $type, $user);
        return (int)$builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;
    }

    public function getCondition($builder, $condition, $type, $user = [])
    {
        $contract_type   = $condition['contract_type'] ?? '';
        $contract_status = $condition['contract_status'] ?? '';
        $start_time      = $condition['create_start_date'] ?? '';
        $end_time        = $condition['create_end_date'] ?? '';
        $customer_name   = $condition['customer_name'] ?? '';
        $approve_status  = $condition['approve_status'] ?? '';
        $sign_status     = $condition['sign_status'] ?? '';
        $created_id      = $condition['created_id'] ?? '';
        $no              = $condition['no'] ?? '';
        $contract_name   = $condition['template_name'] ?? '';
        $store_id        = $condition['store_id'] ?? '';

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        if ($type == self::LIST_TYPE_APPLY) {
            // 签约信息表
            $builder->leftJoin(ContractElectronicSignInfoModel::class, 'sign_info.electronic_no = c.no', 'sign_info');

            $builder->andWhere('c.created_id = :uid:', ['uid' => $condition['uid']]);

        } else if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE], $condition['uid'], 'c');
        }
        if (!empty($created_id)) {
            $builder->andWhere('c.created_id = :created_id:', ['created_id' => $created_id]);
        }
        if (!empty($no)) {
            $builder->andWhere('c.no = :no:', ['no' => $no]);
        }

        if (!empty($contract_type)) {
            $builder->andWhere('c.template_type = :contract_type:', ['contract_type' => $contract_type]);
        }

        if (!empty($contract_status)) {
            $builder->andWhere('c.contract_status = :contract_status:', ['contract_status' => $contract_status]);
        }
        if (!empty($customer_name)) {
            $builder->andWhere('c.customer_name = :customer_name:', ['customer_name' => $customer_name]);
        }
        if (!empty($approve_status)) {
            $builder->andWhere('c.approve_status = :approve_status:', ['approve_status' => $approve_status]);
        }
        if (!empty($sign_status)) {
            $builder->andWhere('c.sign_status = :sign_status:', ['sign_status' => $sign_status]);
        }

        if (!empty($start_time)) {
            $builder->andWhere('c.created_at >= :create_start_date:', ['create_start_date' => $start_time]);
        }

        if (!empty($end_time)) {
            $builder->andWhere('c.created_at < :create_end_date:', ['create_end_date' => $end_time]);
        }

        if (!empty($contract_name)) {
            $builder->andWhere('c.contract_name  like :contract_name:', ['contract_name' => $contract_name . '%']);
        }

        if (!empty($store_id)) {
            $builder->andWhere('c.store_id = :store_id:', ['store_id' => $store_id]);
        }

        // 电子合同编号
        if (!empty($condition['electronic_no'])) {
            $builder->andWhere('c.no = :electronic_no:', ['electronic_no' => $condition['electronic_no']]);
        }

        if ($type == self::LIST_TYPE_SEARCH) {
            // 其他合同编号
            $builder->leftJoin(Contract::class, 'contract.id = c.relate_id', 'contract');
            if (!empty($condition['other_contract_no'])) {
                $builder->andWhere('contract.cno = :other_contract_no:', ['other_contract_no' => $condition['other_contract_no']]);
            }

            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name'                => 'c',
                'create_id_field'                 => 'created_id',
                'create_node_department_id_filed' => 'create_node_department_id',
            ];
            $builder      = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_ELECTRONIC_CONTRACT, $table_params);
        }

        return $builder;
    }

    public function handelItems($items,$export=false)
    {
        $status             = ContractEnums::$contract_electronic_status;
        $approve_status     = Enums::$contract_status;
        $sign_status        = ContractEnums::$contract_sign_status;
        $contract_status    = ContractEnums::$contract_status;
        $contract_initiator = ContractEnums::$contract_initiator_type;

        $export_file_url_time_limit_second = (int)EnumsService::getInstance()->getSettingEnvValue('export_file_url_time_limit_second', 600);

        foreach ($items as &$item) {
            $item['status_text']          = static::$t->_($status[$item['status']]);
            $item['sign_status_text']     = static::$t->_($sign_status[$item['sign_status']]);
            $item['contract_status_text'] = !empty($item['contract_status']) ? static::$t->_($contract_status[$item['contract_status']]) : '';
            $item['approve_status_text']  = !empty($approve_status[$item['approve_status']]) ? static::$t->_($approve_status[$item['approve_status']]) : '';
            $item['initiator_type_text']  = static::$t->_($contract_initiator[$item['initiator_type']]);
            if ($export){
                $path = !empty($item['file_url']) ? OssHelper::downloadFileHcm($item['file_url'],$export_file_url_time_limit_second) : '';
                $item['file_url']             = $path['file_url'] ?? '';
            }else{
                $item['business_type'] = AttachmentService::BUSINESS_TYPE_ELECTRONIC_1;
                $item['business_id'] = $item['file_url'] ?? '';
            }
            $item['relate_id']            = empty($item['relate_id']) ? '' : $item['relate_id'];

            // 相关按钮展示逻辑: 1-展示; 2-不展示
            $item['is_sign_show'] = 2; // 发起签约
            $item['is_resign_show'] = 2; // 重新发起签约
            $item['is_review_sign_show'] = 2; // 复核客户签字
            if ($item['approve_status'] == Enums::CONTRACT_STATUS_APPROVAL) {
                $item['is_sign_show'] = $item['sign_status'] == ContractEnums::CONTRACT_SIGN_STATUS_1 ? 1 : 2;
                $item['is_resign_show'] = $item['sign_status'] == ContractEnums::CONTRACT_SIGN_STATUS_9 ? 1 : 2;
                $item['is_review_sign_show'] = $item['sign_status'] == ContractEnums::CONTRACT_SIGN_STATUS_8 ? 1 : 2;
            }

            // 是否显示合同内容审核撤回按钮 1-显示； 2-不显示
            $item['is_show_cancel'] = 2;
            if ($item['contract_status'] == ContractEnums::CONTRACT_STATUS_3 && $item['business_approve_status'] == Enums::CONTRACT_STATUS_PENDING) {
                $item['is_show_cancel'] = 1;
            }

            // 是否展示签约邮箱
            $item['is_show_sign_email'] = 2;
            if (isset($item['sign_customer_type']) && $item['sign_customer_type'] == ContractEnums::SIGN_CUSTOMER_TYPE_100 && in_array($item['sign_status'],
                    [
                        ContractEnums::CONTRACT_SIGN_STATUS_2,
                        ContractEnums::CONTRACT_SIGN_STATUS_3,
                        ContractEnums::CONTRACT_SIGN_STATUS_5,
                        ContractEnums::CONTRACT_SIGN_STATUS_6,
                        ContractEnums::CONTRACT_SIGN_STATUS_8,
                        ContractEnums::CONTRACT_SIGN_STATUS_9,
                    ])
            ) {
                $item['is_show_sign_email'] = 1;
            }

            unset($item['contract_content'], $item['business_approve_status'], $item['sign_customer_type']);
        }

        return $items;
    }

    /**
     * 电子合同制作列表 - 导出
     *
     * @param array $condition 请求参数组
     * @param array $user 当前登录着信息组
     * @param int $type 列表类型
     * @return array
     * @throws GuzzleException
     */
    public function export($condition, $user, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $data = '';
        try {
            $list = $this->getList($condition,$user, $type, true);
            $export_data = $this->getExportExcelHeaderFields($type, $list['items']);
            $result = $this->exportExcel($export_data['header'], $export_data['row_values'], $export_data['file_name']);
            $data = $result['data'] ?? '';
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('download-contract-electronic-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 电子合同列表 - 导出表头
     * @param int $type 列表类型
     * @param array $list 列表
     * @return array
     */
    public function getExportExcelHeaderFields($type, $list)
    {
        $file_name = '';
        $header = $row_values = [];
        if ($type == self::LIST_TYPE_APPLY) {
            $file_name = 'contract_electronic_list_' . date('YmdHis');
            $header = [
                static::$t->_('global.no'),//序号
                static::$t->_('contract.no'),//电子合同编号
                static::$t->_('contract.template_name'),//合同模板名称
                static::$t->_('csr_field_contract_lang'),//合同语言
                static::$t->_('contract.start_date'),//开始生效日期
                static::$t->_('cc_summary_contract_rent_contract_end'),//合同结束日期
                static::$t->_('contract.created_date'),//合同创建日期
                static::$t->_('contract.customer_name'),//客户名称
                static::$t->_('contract.cname'),//合同名称
                static::$t->_('contract.approve_status_text'),//审批流状态
                static::$t->_('contract.sign_status_text'),//合同签约状态
                static::$t->_('contract.status'),//合同状态
                static::$t->_('contract.file_url'),//合同附件
            ];
            foreach ($list as $key => $item) {
                $row_values[] = [
                    $key+1,
                    $item['no'],
                    $item['template_name'],
                    $item['lang'],
                    $item['start_date'],
                    $item['end_date'],
                    $item['created_date'],
                    $item['customer_name'],
                    $item['contract_name'],
                    $item['approve_status_text'],
                    $item['sign_status_text'],
                    $item['contract_status_text'],
                    $item['file_url'],
                ];
            }
        } elseif ($type == self::LIST_TYPE_SEARCH) {
            $file_name = 'contract_electronic_all_list_' . date('YmdHis');
            $header = [
                static::$t->_('global.no'),//序号
                static::$t->_('contract.no'),//电子合同编号
                static::$t->_('other_contract.cno'),//其他合同编号
                static::$t->_('contract.template'),//合同分类
                static::$t->_('csr_field_contract_lang'),//合同语言
                static::$t->_('contract.start_date'),//开始生效日期
                static::$t->_('cc_summary_contract_rent_contract_end'),//合同结束日期
                static::$t->_('contract.created_date'),//合同创建日期
                static::$t->_('contract.customer_name'),//客户名称
                static::$t->_('contract.cname'),//合同名称
                static::$t->_('contract.approve_status_text'),//审批流状态
                static::$t->_('contract.sign_status_text'),//合同签约状态
                static::$t->_('sample_apply_id'),//创建人工号
                static::$t->_('sample_apply_name'),//创建人姓名
                static::$t->_('contract.file_url'),//合同附件
            ];
            foreach ($list as $key => $item) {
                $row_values[] = [
                    $key+1,
                    $item['no'],
                    $item['other_contract_no'],
                    $item['template_name'],
                    $item['lang'],
                    $item['start_date'],
                    $item['end_date'],
                    $item['created_date'],
                    $item['customer_name'],
                    $item['contract_name'],
                    $item['approve_status_text'],
                    $item['sign_status_text'],
                    $item['created_id'],
                    $item['created_name'],
                    $item['file_url'],
                ];
            }
        }

        return [
            'header' => $header,
            'row_values' => $row_values,
            'file_name' => $file_name,
        ];
    }

    /**
     * 处理合同表单数据
     *
     * @param $data
     * @param $lang
     * @return array
     */
    public function contractParams($data, $lang)
    {
        $field_data = [];

        // 一维结构上的合同编号
        $field_data['contract_no'] = $data['contract_no'] ?? '';
        $field_data['contract_name'] = $data['contract_name'] ?? '';

        // 是否有COD服务
        $term_info_map = !empty($data['form_rule']['termInfo']) ? array_column($data['form_rule']['termInfo'], 'value', 'field') : [];
        $has_cod_service = $term_info_map['has_cod_service'] ?? 1; // 默认是
        $no_cod_service_default_value = 'NA';// 无COD服务时的默认值

        // 多语言获取
        $lang_item = explode('-', $lang);
        if (isset($lang_item[0])) {
            self::setLanguage($lang_item[0]);
            $language_1 = static::$t;
            $enums_data_1 = $this->formSelectEnums($data['department_id']);
        }

        if (isset($lang_item[1])) {
            self::setLanguage($lang_item[1]);
            $language_2 = static::$t;
            $enums_data_2 = $this->formSelectEnums($data['department_id']);
        }

        $cod_pay_method_enums = array_column($enums_data_1['standard_cod_pay_method'] ?? [], 'name', 'id');
        $cod_pay_method_enums_2 = array_column($enums_data_2['standard_cod_pay_method'] ?? [], 'name', 'id');

        //获取特殊字段 翻译配置
        foreach ($data['form_rule'] as $k => $v) {
            $i = 1;

            foreach ($v as $k1 => $v1) {
                if (!in_array($v1['field'], self::$special_field)) {
                    $field_data[$v1['field']] = $v1['value'];

                    if ($v1['field'] == 'flash_authorized_person') {
                        $field_data[$v1['field'] . '_' . $i] = $v1['value'];
                        $i++;
                    }

                    if ($v1['field'] == 'price_scheme') {
                        $field_data['price_scheme_description'] = $v1['option_desc'];
                    }

                } else {
                    // 标准件
                    if ($v1['field'] == 'standard_settlement_type') {
                        $field_data['standard_settlement_type_key'] = $v1['value'];

                        // 固定折扣
                        if ($v1['value'] == 1) {
                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['standard_settlement_type_item'] = [
                                    'standard_cod_ratio' => $no_cod_service_default_value,
                                    'standard_shipping_discount' => $no_cod_service_default_value,
                                    'standard_cod_pay_method' => $no_cod_service_default_value,
                                ];
                            } else {
                                $field_data['standard_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');

                                // COD手续费计算方法
                                $standard_cod_pay_method_0 = $cod_pay_method_enums[$field_data['standard_settlement_type_item']['standard_cod_pay_method']] ?? '';
                                $standard_cod_pay_method_1 = $cod_pay_method_enums_2[$field_data['standard_settlement_type_item']['standard_cod_pay_method']] ?? '';
                                $field_data['standard_settlement_type_item']['standard_cod_pay_method'] = $standard_cod_pay_method_0 . $standard_cod_pay_method_1;

                                // COD服务费率/运费折扣带 %
                                $field_data['standard_settlement_type_item']['standard_cod_ratio'] .= '%';
                                $field_data['standard_settlement_type_item']['standard_shipping_discount'] .= '%';
                            }
                        }

                        // 阶梯折扣
                        if ($v1['value'] == 2) {
                            $field_data['standard_cod_pay_method'] = $v1['condition'][2][0]['value'];

                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['standard_settlement_type_item'] = [[]];
                                foreach ($field_data['standard_settlement_type_item'] as $k3 => &$v3) {
                                    $v3['standard_daily_order_quantity'] = $no_cod_service_default_value;
                                    $v3['standard_cod_ratio'] = $no_cod_service_default_value;
                                    $v3['standard_shipping_discount'] = $no_cod_service_default_value;
                                }
                            } else {
                                // 有COD服务
                                foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                    $field_data['standard_settlement_type_item'][] = array_column($v2, 'value', 'field');
                                }

                                $count = count($field_data['standard_settlement_type_item']);
                                $standard_daily_order_quantity = 0;
                                foreach ($field_data['standard_settlement_type_item'] as $k3 => &$v3) {
                                    // 提交的最后一组
                                    if ($k3 < ($count - 1)) {
                                        // 提交的倒数第二组
                                        if ($k3 == $count - 2) {
                                            $standard_daily_order_quantity = $v3['standard_daily_order_quantity'];
                                        }

                                        $v3['standard_daily_order_quantity'] = '< ' . $v3['standard_daily_order_quantity'];
                                        $v3['standard_cod_ratio']            .= '%';
                                        $v3['standard_shipping_discount']    .= '%';
                                    }

                                    // 构造提交后的最后一组
                                    if ($k3 == ($count - 1)) {
                                        $v3['standard_daily_order_quantity'] = '>= ' . $standard_daily_order_quantity;
                                        $v3['standard_cod_ratio']            .= '%';
                                        $v3['standard_shipping_discount']    .= '%';
                                    }
                                }
                            }
                        }
                    }

                    // 大件
                    if ($v1['field'] == 'bulky_item_settlement_type') {
                        $field_data['bulky_item_settlement_type_key'] = $v1['value'];

                        // 固定折扣
                        if ($v1['value'] == 1) {
                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['bulky_item_settlement_type_item'] = [
                                    'bulky_item_cod_ratio' => $no_cod_service_default_value,
                                    'bulky_item_shipping_discount' => $no_cod_service_default_value,
                                    'bulky_item_cod_pay_method' => $no_cod_service_default_value,
                                ];
                            } else {
                                $field_data['bulky_item_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');

                                // COD手续费计算方法
                                $bulky_item_cod_pay_method_0 = $cod_pay_method_enums[$field_data['bulky_item_settlement_type_item']['bulky_item_cod_pay_method']] ?? '';
                                $bulky_item_cod_pay_method_1 = $cod_pay_method_enums_2[$field_data['bulky_item_settlement_type_item']['bulky_item_cod_pay_method']] ?? '';
                                $field_data['bulky_item_settlement_type_item']['bulky_item_cod_pay_method'] = $bulky_item_cod_pay_method_0 . $bulky_item_cod_pay_method_1;

                                // COD服务费率/运费折扣带 %
                                $field_data['bulky_item_settlement_type_item']['bulky_item_cod_ratio'] .= '%';
                                $field_data['bulky_item_settlement_type_item']['bulky_item_shipping_discount'] .= '%';
                            }
                        }

                        // 阶梯折扣
                        if ($v1['value'] == 2) {
                            $field_data['bulky_item_cod_pay_method'] = $v1['condition'][2][0]['value'];

                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['bulky_item_settlement_type_item'] = [[]];
                                foreach ($field_data['bulky_item_settlement_type_item'] as $k3 => &$v3) {
                                    $v3['bulky_item_daily_order_quantity'] = $no_cod_service_default_value;
                                    $v3['bulky_item_cod_ratio'] = $no_cod_service_default_value;
                                    $v3['bulky_item_shipping_discount'] = $no_cod_service_default_value;
                                }
                            } else {
                                // 有COD服务
                                foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                    $field_data['bulky_item_settlement_type_item'][] = array_column($v2, 'value', 'field');
                                }

                                $count = count($field_data['bulky_item_settlement_type_item']);
                                $bulky_daily_order_quantity = 0;
                                foreach ($field_data['bulky_item_settlement_type_item'] as $k4 => &$v4) {
                                    if ($k4 < ($count - 1)) {
                                        if ($k4 == $count - 2) {
                                            $bulky_daily_order_quantity = $v4['bulky_item_daily_order_quantity'];
                                        }
                                        $v4['bulky_item_daily_order_quantity'] = '< ' . $v4['bulky_item_daily_order_quantity'];
                                        $v4['bulky_item_cod_ratio']            .= '%';
                                        $v4['bulky_item_shipping_discount']    .= '%';
                                    }

                                    if ($k4 == ($count - 1)) {
                                        $v4['bulky_item_daily_order_quantity'] = '>= ' . $bulky_daily_order_quantity;
                                        $v4['bulky_item_cod_ratio']            .= '%';
                                        $v4['bulky_item_shipping_discount']    .= '%';
                                    }
                                }
                            }
                        }
                    }

                    // 水果件
                    if ($v1['field'] == 'fruit_item_settlement_type') {
                        $field_data['fruit_item_settlement_type_key'] = $v1['value'];

                        // 固定折扣
                        if ($v1['value'] == 1) {
                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['fruit_item_settlement_type_item'] = [
                                    'fruit_item_cod_ratio' => $no_cod_service_default_value,
                                    'fruit_item_shipping_discount' => $no_cod_service_default_value,
                                    'fruit_item_cod_pay_method' => $no_cod_service_default_value,
                                ];
                            } else {
                                $field_data['fruit_item_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');

                                // COD手续费计算方法
                                $fruit_item_cod_pay_method_0 = $cod_pay_method_enums[$field_data['fruit_item_settlement_type_item']['fruit_item_cod_pay_method']] ?? '';
                                $fruit_item_cod_pay_method_1 = $cod_pay_method_enums_2[$field_data['fruit_item_settlement_type_item']['fruit_item_cod_pay_method']] ?? '';
                                $field_data['fruit_item_settlement_type_item']['fruit_item_cod_pay_method'] = $fruit_item_cod_pay_method_0 . $fruit_item_cod_pay_method_1;

                                // COD服务费率/运费折扣带 %
                                $field_data['fruit_item_settlement_type_item']['fruit_item_cod_ratio'] .= '%';
                                $field_data['fruit_item_settlement_type_item']['fruit_item_shipping_discount'] .= '%';
                            }
                        }

                        // 阶梯折扣
                        if ($v1['value'] == 2) {
                            $field_data['fruit_item_cod_pay_method'] = $v1['condition'][2][0]['value'];

                            // 无COD服务
                            if ($has_cod_service == 2) {
                                $field_data['fruit_item_settlement_type_item'] = [[]];
                                foreach ($field_data['fruit_item_settlement_type_item'] as $k3 => &$v3) {
                                    $v3['fruit_item_daily_order_quantity'] = $no_cod_service_default_value;
                                    $v3['fruit_item_cod_ratio'] = $no_cod_service_default_value;
                                    $v3['fruit_item_shipping_discount'] = $no_cod_service_default_value;
                                }
                            } else {
                                // 有COD服务
                                foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                    $field_data['fruit_item_settlement_type_item'][] = array_column($v2, 'value', 'field');
                                }

                                $count = count($field_data['fruit_item_settlement_type_item']);
                                $fruit_item_daily_order_quantity = 0;
                                foreach ($field_data['fruit_item_settlement_type_item'] as $k3 => &$v3) {
                                    // 提交的最后一组
                                    if ($k3 < ($count - 1)) {
                                        // 提交的倒数第二组
                                        if ($k3 == $count - 2) {
                                            $fruit_item_daily_order_quantity = $v3['fruit_item_daily_order_quantity'];
                                        }

                                        $v3['fruit_item_daily_order_quantity'] = '< ' . $v3['fruit_item_daily_order_quantity'];
                                        $v3['fruit_item_cod_ratio']            .= '%';
                                        $v3['fruit_item_shipping_discount']    .= '%';
                                    }

                                    // 构造提交后的最后一组
                                    if ($k3 == ($count - 1)) {
                                        $v3['fruit_item_daily_order_quantity'] = '>= ' . $fruit_item_daily_order_quantity;
                                        $v3['fruit_item_cod_ratio']            .= '%';
                                        $v3['fruit_item_shipping_discount']    .= '%';
                                    }
                                }
                            }
                        }
                    }
                }

                // 甲方授权人
                if ($v1['field'] == 'custom_authorized_person_info') {
                    $field_data['custom_authorized_person_info'] = [];
                    foreach ($v1['detail'] as $k2 => $v2) {
                        $field_data['custom_authorized_person_info'][] = array_column($v2, 'value', 'field');
                    }
                }
            }
        }

        // 多语言字段配置
        $enums_form_rule_lang = EnumsService::getInstance()->getSettingEnvValueMap('pmd_enums_form_rule_lang');

        // 价格调整和条件 - PMD
        if (isset($field_data['price_scheme'])) {
            $field_data['price_scheme_description'] = $enums_form_rule_lang['price_scheme_' . $lang][$field_data['price_scheme']] ?? '';

            // en-zh -> en; en-th -> en
            $field_data['price_scheme_description_0'] = $enums_form_rule_lang['price_scheme_' . $lang . '_' . $lang_item[0]][$field_data['price_scheme']] ?? '';

            // en-zh -> zh; en-th -> th
            $field_data['price_scheme_description_1'] = $enums_form_rule_lang['price_scheme_' . $lang . '_' . $lang_item[1]][$field_data['price_scheme']] ?? '';

            $field_data['price_scheme'] = array_column($enums_data_1['price_scheme_type'] ?? [], 'name', 'id')[$field_data['price_scheme']] ?? '';
        }

        // 大件计费标准
        if (isset($field_data['bulky_item_charge_type'])) {
            $bulky_item_charge_type = array_column($enums_data_1['bulky_item_charge_type'] ?? [], 'name', 'id');
            $field_data['bulky_item_charge_type_value'] = $bulky_item_charge_type[$field_data['bulky_item_charge_type']] ?? '';
        }

        // 标准件计费标准: 第4项有二级明细的描述及内嵌变量
        if (isset($field_data['standard_charge_type'])) {
            $bulky_item_charge_type_desc_key = 'standard_charge_type_desc_' . $field_data['standard_charge_type'];

            $standard_charge_type_desc_field_0 = 'standard_charge_type_desc_' . $lang_item[0];
            $standard_charge_type_desc_field_1 = 'standard_charge_type_desc_' . $lang_item[1];

            if ($field_data['standard_charge_type'] == 4 && !empty($field_data['standard_limit_type'])) {
                $bulky_item_charge_type_desc_key .= '_' . $field_data['standard_limit_type'];
                $standard_limit_type_values = [
                    'limit_weight' => $field_data['limit_weight'] ?? '',
                    'limit_size' => $field_data['limit_size'] ?? '',
                ];

                $field_data[$standard_charge_type_desc_field_0] = $language_1->_($bulky_item_charge_type_desc_key, $standard_limit_type_values);
                $field_data[$standard_charge_type_desc_field_1] = $language_2->_($bulky_item_charge_type_desc_key, $standard_limit_type_values);
            } else {
                $field_data[$standard_charge_type_desc_field_0] = $language_1->_($bulky_item_charge_type_desc_key);
                $field_data[$standard_charge_type_desc_field_1] = $language_2->_($bulky_item_charge_type_desc_key);
            }
        }

        // PMD 特有派生变量处理
        if ($this->isGroupProjectManagement($data['department_id'])) {
            if (isset($field_data['settlement_type'])) {
                $field_data['settlement_type_1'] = array_column($enums_data_1['settlement_type'] ?? [], 'name', 'id')[$field_data['settlement_type']] ?? '';
                $field_data['settlement_type_2'] = array_column($enums_data_2['settlement_type'] ?? [], 'name', 'id')[$field_data['settlement_type']] ?? '';
            }

            if (isset($field_data['settlement_circle'])) {
                $field_data['settlement_circle_1'] = array_column($enums_data_1['account_period'] ?? [], 'name', 'id')[$field_data['settlement_circle']] ?? '';
                $field_data['settlement_circle_2'] = array_column($enums_data_2['account_period'] ?? [], 'name', 'id')[$field_data['settlement_circle']] ?? '';
            }
        }

        // 退件运费-价格参照(水果件无该项)
        // 标准件
        if (isset($field_data['standard_item_refund_refer'])) {
            $field_data['standard_item_refund_refer_1'] = array_column($enums_data_1['standard_return_price'] ?? [], 'name', 'id')[$field_data['standard_item_refund_refer']] ?? '';
            $field_data['standard_item_refund_refer_2'] = array_column($enums_data_2['standard_return_price'] ?? [], 'name', 'id')[$field_data['standard_item_refund_refer']] ?? '';
        }

        // 大件
        if (isset($field_data['bulky_item_refund_refer'])) {
            $field_data['bulky_item_refund_refer_1'] = array_column($enums_data_1['big_return_price'] ?? [], 'name', 'id')[$field_data['bulky_item_refund_refer']] ?? '';
            $field_data['bulky_item_refund_refer_2'] = array_column($enums_data_2['big_return_price'] ?? [], 'name', 'id')[$field_data['bulky_item_refund_refer']] ?? '';
        }

        // 退件运费-费率
        // 标准件
        if (isset($field_data['standard_item_refund_ratio'])) {
            $field_data['standard_item_refund_ratio'] = array_column($enums_data_1['standard_return_rate'] ?? [], 'name', 'id')[$field_data['standard_item_refund_ratio']] ?? '';
        }

        // 大件
        if (isset($field_data['bulky_item_refund_ratio'])) {
            $field_data['bulky_item_refund_ratio'] = array_column($enums_data_1['big_return_rate'] ?? [], 'name', 'id')[$field_data['bulky_item_refund_ratio']] ?? '';
        }

        // 水果件
        if (isset($field_data['fruit_item_refund_ratio'])) {
            $field_data['fruit_item_refund_ratio'] = array_column($enums_data_1['fruit_return_rate'] ?? [], 'name', 'id')[$field_data['fruit_item_refund_ratio']] ?? '';
        }

        // 无COD服务
        if ($has_cod_service == 2) {
            // COD支付约定-币种
            $field_data['cod_pay_currency_1'] = $no_cod_service_default_value;
            $field_data['cod_pay_currency_2'] = $no_cod_service_default_value;

            // COD支付约定-账期
            $field_data['cod_pay_date'] = $no_cod_service_default_value;

            // 阶梯费率用到: COD手续费计算方法
            $field_data['standard_cod_pay_method'] = $no_cod_service_default_value;
            $field_data['bulky_item_cod_pay_method'] = $no_cod_service_default_value;
            $field_data['fruit_item_cod_pay_method'] = $no_cod_service_default_value;

        } else {
            // COD 币种
            if (isset($field_data['cod_pay_currency'])) {
                $field_data['cod_pay_currency_1'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[0]][$field_data['cod_pay_currency']] ?? '';
                $field_data['cod_pay_currency_2'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[1]][$field_data['cod_pay_currency']] ?? '';
            }

            // 阶梯费率用到: COD手续费计算方法
            if (isset($field_data['standard_cod_pay_method'])) {
                $field_data['standard_cod_pay_method'] = $cod_pay_method_enums[$field_data['standard_cod_pay_method']] . $cod_pay_method_enums_2[$field_data['standard_cod_pay_method']];
            }

            if (isset($field_data['bulky_item_cod_pay_method'])) {
                $field_data['bulky_item_cod_pay_method'] = $cod_pay_method_enums[$field_data['bulky_item_cod_pay_method']] . $cod_pay_method_enums_2[$field_data['bulky_item_cod_pay_method']];
            }

            if (isset($field_data['fruit_item_cod_pay_method'])) {
                $field_data['fruit_item_cod_pay_method'] = $cod_pay_method_enums[$field_data['fruit_item_cod_pay_method']] . $cod_pay_method_enums_2[$field_data['fruit_item_cod_pay_method']];
            }
        }

        if (isset($field_data['custom_bank_currency'])) {
            $field_data['custom_bank_currency_1'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[0]][$field_data['custom_bank_currency']] ?? '';
            $field_data['custom_bank_currency_2'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[1]][$field_data['custom_bank_currency']] ?? '';
        }

        if (isset($field_data['flash_bank_currency'])) {
            $field_data['flash_bank_currency_1'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[0]][$field_data['flash_bank_currency']] ?? '';
            $field_data['flash_bank_currency_2'] = $enums_form_rule_lang['cod_pay_currency'][$lang_item[1]][$field_data['flash_bank_currency']] ?? '';
        }

        if (isset($field_data['pdpa_custom_country'])) {
            $field_data['pdpa_custom_country_1'] = array_column($enums_data_1['customer_country_registerer'] ?? [], 'name', 'id')[$field_data['pdpa_custom_country']] ?? '';
            $field_data['pdpa_custom_country_2'] = array_column($enums_data_2['customer_country_registerer'] ?? [], 'name', 'id')[$field_data['pdpa_custom_country']] ?? '';
        }

        if (isset($field_data['flash_authorized_person_1'])) {
            $field_data['flash_authorized_person_1'] = array_column($enums_data_1['staff_list'] ?? [], null, 'id')[$field_data['flash_authorized_person_1']]['name_' . $lang_item[0]] ?? '';
        }

        if (isset($field_data['flash_authorized_person_2'])) {
            $field_data['flash_authorized_person_2'] = array_column($enums_data_1['staff_list'] ?? [], null, 'id')[$field_data['flash_authorized_person_2']]['name_' . $lang_item[1]] ?? '';
        }

        if (isset($field_data['custom_company_name_en'])) {
            $field_data['contract_name'] = $field_data['custom_company_name_en'];
        }

        // 有效期特殊格式处理
        if (isset($field_data['contract_sign_date'])) {
            $contract_sign_date                 = $this->getDateFormat($lang_item, $field_data['contract_sign_date']);
            $field_data['contract_sign_date_0'] = $contract_sign_date[0] ?? '';
            $field_data['contract_sign_date_1'] = $contract_sign_date[1] ?? '';
        }

        if (isset($field_data['contract_start_date'])) {
            $contract_start_date                 = $this->getDateFormat($lang_item, $field_data['contract_start_date']);
            $field_data['contract_start_date_0'] = $contract_start_date[0] ?? '';
            $field_data['contract_start_date_1'] = $contract_start_date[1] ?? '';
        }

        if (isset($field_data['contract_end_date'])) {
            $contract_end_date                 = $this->getDateFormat($lang_item, $field_data['contract_end_date']);
            $field_data['contract_end_date_0'] = $contract_end_date[0] ?? '';
            $field_data['contract_end_date_1'] = $contract_end_date[1] ?? '';
        }

        if (isset($field_data['pdpa_sign_date'])) {
            $pdpa_sign_date                 = $this->getDateFormat($lang_item, $field_data['pdpa_sign_date']);
            $field_data['pdpa_sign_date_0'] = $pdpa_sign_date[0] ?? '';
            $field_data['pdpa_sign_date_1'] = $pdpa_sign_date[1] ?? '';
        }

        // 补充协议: 协议开始日期
        if (isset($field_data['agreement_start_date'])) {
            $contract_sign_date                 = $this->getDateFormat($lang_item, $field_data['agreement_start_date']);
            $field_data['agreement_start_date_0'] = $contract_sign_date[0] ?? '';
            $field_data['agreement_start_date_1'] = $contract_sign_date[1] ?? '';
        }

        // 补充协议: 协议结束日期
        if (isset($field_data['agreement_end_date'])) {
            $contract_sign_date                 = $this->getDateFormat($lang_item, $field_data['agreement_end_date']);
            $field_data['agreement_end_date_0'] = $contract_sign_date[0] ?? '';
            $field_data['agreement_end_date_1'] = $contract_sign_date[1] ?? '';
        }

        // 补充协议: 签约日期
        if (isset($field_data['agreement_sign_date'])) {
            $contract_sign_date                 = $this->getDateFormat($lang_item, $field_data['agreement_sign_date']);
            $field_data['agreement_sign_date_0'] = $contract_sign_date[0] ?? '';
            $field_data['agreement_sign_date_1'] = $contract_sign_date[1] ?? '';
        }

        // 现结主: 发行日期
        if (isset($field_data['issuing_date'])) {
            $issuing_date                 = $this->getDateFormat($lang_item, $field_data['issuing_date']);
            $field_data['issuing_date_0'] = $issuing_date[0] ?? '';
            $field_data['issuing_date_1'] = $issuing_date[1] ?? '';
        }

        // 现结主: 有效期至
        if (isset($field_data['validity_date'])) {
            $validity_date                 = $this->getDateFormat($lang_item, $field_data['validity_date']);
            $field_data['validity_date_0'] = $validity_date[0] ?? '';
            $field_data['validity_date_1'] = $validity_date[1] ?? '';
        }

        // 如果合同名称为空 且 为补充协议, 则根据关联的主合同编号取主合同名称
        if (!empty($field_data['agreement_contract_no']) && empty($field_data['contract_name'])) {
            $field_data['contract_name'] = ContractElectronicModel::findFirst([
                'conditions' => 'contract_no = :contract_no: AND template_type != 11',
                'bind' => ['contract_no' => $field_data['agreement_contract_no']],
                'columns' => ['contract_name'],
            ])->contract_name ?? '';
        }

        return $this->getElectronicContractSignData($field_data, 0);
    }

    /**
     * 取电子合同某表单项的值
     *
     * @param $contract_content
     * @param string $form_block_key 示例: contractBasic
     * @param string $form_field 示例: contract_no
     * @return mixed|string
     */
    public function getFormItemValue($contract_content, string $form_block_key, string $form_field)
    {
        $form_field_value = '';

        $form_data = json_decode($contract_content, true);
        $form_block_item = $form_data[$form_block_key] ?? [];
        foreach ($form_block_item as $item) {
            if ($item['field'] == $form_field) {
                $form_field_value = $item['value'];
                break;
            }
        }

        return $form_field_value;
    }

    /**
     * 电子合同内容审核日志
     *
     * @param int $id
     * @return array
     * @throws BusinessException
     */
    public function getContentAuditLog(int $id)
    {
        if (empty($id)) {
            return [];
        }

        $contract_req = $this->getRequest($id);
        if (empty($contract_req->id)) {
            throw new BusinessException(static::$t->_('workflow_is_empty'), ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
        }

        return (new WorkflowServiceV2())->getAuditLogs($contract_req);
    }

    /**
     * 合同地址
     *
     * @param $id
     * @param bool $log
     * @param bool $has_form_rule
     * @return array
     */
    public function getDetail($id, $log = false, $has_form_rule = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $detail = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
            ]);

            if (empty($detail)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            $detail = $detail->toArray();

            if ($log && $detail['business_approve_status'] != 0) {
                $detail['auth_logs'] = $this->getContentAuditLog($id);
            }

            if (!$has_form_rule) {
                unset($detail['contract_content']);
            }

            // 电子合同信息: 创建人姓名、英文名、昵称
            $created_info = (new HrStaffRepository())->getStaffById($detail['created_id']);

            if (empty($created_info)) {
                $tool_staff_info           = (new HrStaffInfoService())->getToolStaffInfo($detail['created_id']);
                $created_info['name']      = $tool_staff_info['name'] ?? '';
                $created_info['name_en']   = $tool_staff_info['name_en'] ?? '';
                $created_info['nick_name'] = $tool_staff_info['nick_name'] ?? '';
            }

            $detail['created_name'] = $created_info['name'] ?? '';
            $detail['created_name_en'] = $created_info['name_en'] ?? '';
            $detail['created_nick_name'] = $created_info['nick_name'] ?? '';

            $path = !empty($detail['file_url']) ? OssHelper::downloadFileHcm($detail['file_url']) : '';
            $detail['pdf_url'] = $path['file_url'] ?? '';

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('电子合同获取详情失败' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $detail ?? [],
        ];
    }

    /**
     * 电子合同-客户签字审核详情(信息表->主体)
     *
     * @param $id
     * @param bool $has_form_rule
     * @return array
     */
    public function getSignInfoAuditDetail($id, $has_form_rule = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            // 电子合同签字信息
            $sign_info_model = ContractElectronicSignInfoModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同的主表信息
            $contract_electronic_model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($sign_info_model->electronic_no);
            if (empty($contract_electronic_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $sign_info_model->electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同基本信息
            $data['id'] = $sign_info_model->id;
            $data['no'] = $sign_info_model->electronic_no;
            $data['lang'] = $contract_electronic_model->lang;
            $data['department_id'] = $contract_electronic_model->department_id;

            // 电子合同信息: 创建人姓名、英文名、昵称
            $created_info = (new HrStaffRepository())->getStaffById($contract_electronic_model->created_id);
            $data['created_name'] = $created_info['name'] ?? '';
            $data['created_name_en'] = $created_info['name_en'] ?? '';
            $data['created_nick_name'] = $created_info['nick_name'] ?? '';

            $data['customer_name'] = $contract_electronic_model->customer_name;

            $path = !empty($contract_electronic_model->file_url) ? OssHelper::downloadFileHcm($contract_electronic_model->file_url) : '';
            $data['pdf_url'] = $path['file_url'] ?? '';

            $data['auth_logs'] = ContractElectronicFlowService::getInstance()->getAuditLogs($sign_info_model);

            if ($has_form_rule) {
                $data['contract_content'] = $contract_electronic_model->contract_content;
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('电子合同审核-客户签字审核-获取签字审批记录/详情失败' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 电子合同-客户签字审核详情(电子合同表->主体)
     *
     * @param $id
     * @param bool $has_form_rule
     * @return array
     */
    public function getSignAuditDetail($id, $has_form_rule = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $model = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同基本信息
            $data['id'] = $model->id;
            $data['no'] = $model->no;
            $data['lang'] = $model->lang;
            $data['department_id'] = $model->department_id;

            // 电子合同信息: 创建人姓名、英文名、昵称
            $created_info = (new HrStaffRepository())->getStaffById($model->created_id);
            $data['created_name'] = $created_info['name'] ?? '';
            $data['created_name_en'] = $created_info['name_en'] ?? '';
            $data['created_nick_name'] = $created_info['nick_name'] ?? '';

            $data['customer_name'] = $model->customer_name;

            $_pdf_url = !empty($model->file_url) ? \App\Library\OssHelper::downloadFileHcm($model->file_url) : '';
            $data['pdf_url'] = $_pdf_url['file_url'] ?? '';

            $sign_info_model = $model->getSignInfo();
            $data['auth_logs'] = ContractElectronicFlowService::getInstance()->getAuditLogs($sign_info_model);

            if ($has_form_rule) {
                $data['contract_content'] = $model->contract_content;
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('电子合同-获取乐乎签字审批记录/详情失败' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 编辑回显特殊处理字段
     *
     * @param $id
     * @return array
     * @throws ValidationException
     */
    public function editDetail($id)
    {
        $data = $this->getDetail($id, false, true);

        //基础模版ftl
        $contract = ContractTemplateVersionModel::findFirst([
            'conditions' => 'template_no = :template_no:',
            'bind'       => ['template_no' => $data['data']['relate_template_no']],
        ]);
        if (empty($contract)) {
            throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$CONTRACT_GET_INFO_ERROR);
        }
        $_pdf_url = !empty($data['data']['file_url']) ? \App\Library\OssHelper::downloadFileHcm($data['data']['file_url']) : '';
        $data['data'] = [
            'id'              => $id,
            'lang'            => $data['data']['lang'] ?? '',
            'template_no'     => $data['data']['relate_template_no'] ?? '',
            'ver'             => $data['data']['relate_version_no'] ?? '',
            'contract_name'   => $data['data']['contract_name'] ?? '',
            'template_name'   => $data['data']['template_name'] ?? '',
            'business_review' => $data['data']['business_review'] ?? '',
            'start_date'      => $data['data']['start_date'] ?? '',
            'end_date'        => $data['data']['end_date'] ?? '',
            'customer_name'   => $data['data']['customer_name'] ?? '',
            'department_id'   => $data['data']['department_id'] ?? '',
            'contract_type'   => $data['data']['template_type'] ?? '',
            'file_url'        => $contract->file_url,
            'pdf_url'         => $_pdf_url['file_url'] ?? '',
            'form_rule'       => json_decode($data['data']['contract_content'] ?? [], true),
        ];


        return $data;
    }

    /**
     * 发起商务审核
     *
     * @param $id
     * @param $user
     * @return array
     */
    public function createBusiness($id, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $contract = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            // 判断是否完成合同表单项填写
            if ($contract->is_completed != ContractEnums::CONTRACT_IS_COMPLETED) {
                throw new ValidationException(static::$t->_('electronic_contract_content_audit_error'), ErrCode::$VALIDATE_ERROR);
            }

            $ka_account                        = parse_contract_number($contract->contract_no);
            $contract->business_staff_id       = $user['id'];
            $contract->contract_status         = ContractEnums::CONTRACT_STATUS_3;
            $contract->business_approve_status = Enums::CONTRACT_STATUS_PENDING;
            $contract->updated_at              = date('Y-m-d H:i:s');
            $contract->ka_account              = $ka_account;
            if ($contract->save() === false) {
                throw new BusinessException('电子合同发起商务审核, ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $flow_bool = $this->recommitWorkFlow($contract, $user);

            if ($flow_bool === false) {
                throw new BusinessException('电子合同商务审核审批流生成失败, biz_id = ' . $contract->id, ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('electronic-business-create-request' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }


    /**
     * 商务审核撤回
     *
     * @param $id
     * @param $user
     * @return array
     */
    public function businessAuditCancel($id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 电子合同
            $contract = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id: AND created_id = :created_id:',
                'bind' => ['id' => $id, 'created_id' => $user['id']],
            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 商务审批非待审核状态, 不可撤回
            if ($contract->business_approve_status != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 关联的审批流
            $request = $this->getRequest($id);
            if (empty($request->id)) {
                throw new BusinessException('审批流不存在, id=' . $id, ErrCode::$BUSINESS_ERROR);
            }

            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($request, $user, $this->getWorkflowParams($contract), '');
            if ($result === false) {
                throw new BusinessException('审批流撤回失败, id=' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $bool = $contract->i_update([
                'business_approve_status' => Enums::CONTRACT_STATUS_CANCEL,
                'contract_status' => ContractEnums::CONTRACT_STATUS_4,
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_id' => $user['id'],
            ]);
            if ($bool === false) {
                throw new BusinessException('商务审核-撤回-主数据更新失败,id=' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (Exception $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-approve-cancel-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     *商务审核
     *
     * @param $id
     * @param $user
     * @param $note
     * @return array
     */
    public function businessApprove($id, $user, $note)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $request = $this->getRequest($id);
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if (empty($request->id)) {
                throw new BusinessException(static::$t->_('workflow_is_empty'), ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }


            $contract = ContractElectronicModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            $result   = (new WorkflowServiceV2())->doApprove($request, $user, $this->getWorkflowParams($contract), $note);
            if ($result === false) {
                throw new BusinessException('审批流通过失败' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $update_data = [
                'business_approve_status' => Enums::CONTRACT_STATUS_APPROVAL,
                'updated_at'              => date('Y-m-d H:i:s'),
                'contract_status'         => ContractEnums::CONTRACT_STATUS_2,
            ];

            if (!empty($result->approved_at)) {
                if (isCountry('MY')) {

                    $template_version = ContractTemplateVersionModel::findFirst([
                        'conditions' => 'template_no = :template_no:',
                        'bind'       => ['template_no' => $contract->relate_template_no],
                    ]);

                    if (empty($template_version)) {
                        throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$VALIDATE_ERROR);
                    }

                    $data = [
                        'form_rule'               => json_decode($contract->contract_content, true),
                        'file_url'                => $template_version->file_url,
                        'department_id'           => $contract->department_id,
                        'business_approve_status' => Enums::CONTRACT_STATUS_APPROVAL,
                        'created_id'              => $contract->created_id ?? '',
                    ];

                    //更新模版
                    $service       = reBuildCountryInstance(new ContractElectronicService());
                    $template_data = $service->templateData($data);

                    //再次查询编号
                    $contractRel = ContractElectronicModel::findFirst([
                        'contract_no = :contract_no:',
                        'bind' => ['contract_no' => $template_data['data']['contract_no']]
                    ]);

                    //编号已存在，请重试
                    if ($contractRel) {
                        throw new BusinessException(static::$t->_('retry_later'), ErrCode::$BUSINESS_ERROR);
                    }

                    // 重置合同编号
                    foreach ($data['form_rule'] as $key => &$value) {
                        foreach ($value as $k => &$v) {
                            if ($v['field'] == 'contract_no') {
                                $v['value'] = $template_data['data']['contract_no'];
                            }
                        }
                    }

                    $update_data['contract_content'] = json_encode($data['form_rule'], JSON_UNESCAPED_UNICODE);
                    $update_data['file_url']         = $template_data['pdf_info']['object_key'] ?? '';
                    $update_data['bucket_name']      = $template_data['pdf_info']['bucket_name'] ?? '';
                    $update_data['updated_id']       = $user['id'];
                    $update_data['contract_no']      = $template_data['data']['contract_no'] ?? null;
                    $update_data['updated_at']       = date('Y-m-d H:i:s');

                    // 更新电子合同->表单数据
                    $this->saveFormData($contract->no, $template_data['data'], $user);
                }

                $bool = $contract->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('商务审核失败id' . $id, ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $item
     * @return array
     */
    private function getWorkflowParams($item)
    {
        return [
            'submitter_id' => $item->created_id,
            'is_combination_supplemental_agreement' => $item->is_combination_supplemental_agreement,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function businessReject($id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $request = $this->getRequest($id);
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }

            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }
            $contract = ContractElectronicModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);
            $result   = (new WorkflowServiceV2())->doReject($request, $user, $this->getWorkflowParams($contract), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $bool = $contract->i_update([
                'business_approve_status' => Enums::CONTRACT_STATUS_REJECTED,
                'contract_status'         => ContractEnums::CONTRACT_STATUS_4,
                'updated_at'              => date('Y-m-d H:i:s'),
            ]);
            if ($bool === false) {
                throw new BusinessException('商务审核驳回失败' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-reject-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 发起审批提交合同
     * 初始化数据
     *
     * @param $data
     * @return array
     */
    public function contractDefault($data)
    {
        $data                   = $this->getDetail($data['id']);
        $data                   = $data['data'];
        $contract_type_enums    = ($this->getEnums())['lang'] ?? [];
        $contract_lang          = array_column($contract_type_enums, 'name', 'id');
        $contract_category_map  = EnumsService::getInstance()->getContractCategorysMap();
        $apply_staff_department = '';

        if ($this->isFlashHomeOperationV2($data['department_id'])) {
            $template_id            = Enums::WF_CONTRACT_TYPE7;
            $apply_staff_department = ContractEnums::FLASH_HOME_OPERATION;
            $company_code           = ContractEnums::CONTRACT_COMPANY_FLASH_HOME_OPERATION;
            $is_vendor              = ContractEnums::IS_VENDOR_NO;
            $is_group_contract      = ContractEnums::IS_GROUP_CONTRACT_NO;
            $franchisee_type        = ContractEnums::FRANCHISEE_TYPE_1;

        } else {
            $company_code      = '';

            $template_id = Enums::CONTRACT_TEMPLATE_SALES_STANDARD;
            if ($data['is_combination_supplemental_agreement'] == ContractEnums::IS_COMBINATION_SUPPLEMENTAL_AGREEMENT_YES) {
                $template_id = Enums::CONTRACT_TEMPLATE_SALES_NOT_STANDARD;
            }

            $is_vendor         = ContractEnums::IS_VENDOR_NO;
            $is_group_contract = ContractEnums::IS_GROUP_CONTRACT_NO;
            $franchisee_type   = '';
        }

        $contract_category_info = $contract_category_map[$template_id] ?? [];
        $template_title         = $contract_category_info['label'] ?? '';

        // 若直属分类是二级分类, 则需拼接一级分类
        if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
            $template_title = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $template_title;
        }
        $is_master = Enums::CONTRACT_IS_MASTER_YES;
        //file_name的后缀
        $extension = strchr($data['file_url'],'.');

        return [
            'effective_date'         => $data['start_date'],
            'expiry_date'            => $data['end_date'],
            'lang'                   => $data['lang'],
            'lang_label'             => $contract_lang[$data['lang']] ?? '',
            'file_url'               => $data['file_url'],
            'cname'                  => $data['contract_name'],
            'electronic_id'          => $data['id'],
            'template_title'         => $template_title,
            'template_id'            => (string)($template_id),
            'is_master'              => (string)$is_master,
            'is_master_title'        => static::$t->_(Enums::$contract_is_master[$is_master] ?? ''),
            'apply_staff_department' => $apply_staff_department,
            'company_code'           => $company_code,
            'is_vendor'              => (string)$is_vendor,
            'is_group_contract'      => (string)$is_group_contract,
            'franchisee_type'        => $franchisee_type,
            'is_combination_supplemental_agreement' => $data['is_combination_supplemental_agreement'],
            'contract_file'          => [[
                'id' => $data['id'],
                'business_type' => AttachmentService::BUSINESS_TYPE_ELECTRONIC,
                'bucket_name' => $data['bucket_name'],
                'object_key'  => $data['file_url'],
                'file_name'   => $data['contract_name'] . $extension,
            ]],
        ];
    }


    /**
     * 创建审批
     *
     * @param $contract
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveWkFlow($contract, $user)
    {
        $data['flow_id']  = $this->getFlowId($contract);

        $data['id']       = $contract->id;
        $data['name']     = $contract->no . '审批申请';
        $data['biz_type'] = Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE;
        $info             = $this->getWorkflowParams($contract);
        return (new WorkflowServiceV2())->createRequest($data, $user, $info);
    }

    /**
     * 提交审批流
     * 存在审批遗弃新建，不存在新建
     *
     * @param $contract
     * @param $user
     * @return array|Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/2/10
     */
    public function recommitWorkFlow($contract, $user)
    {

        $req = $this->getRequest($contract->id);
        if (!empty($req)) {
            //老的改成被遗弃
            $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
            if ($req->save() === false) {
                throw new BusinessException('电子合同-合同内容审核-审批流save失败,原因:' . get_data_object_error_msg($req) . ';biz_id' . $contract->id, ErrCode::$BUSINESS_ERROR);
            }
        }

        return $this->saveWkFlow($contract, $user);
    }

    private function getFlowId($contract)
    {
        // 默认PMD部门
        $flow_id = Enums::WF_BUSINESS_ELECTRONIC_CONTRACT_FLOW_ID;

        $department_list = $this->getContractApplicableDepartmentByName();

        // Retail部门
        if (in_array($contract->department_id, $department_list['Retail Management'])) {
            $flow_id = Enums::WF_CONTRACT_ELECTRONIC_RETAIL_WF_ID;
        }

        return $flow_id;
    }

    /**
     * 获取当前业务审批流信息
     * @param $id
     * @param $biz_type
     * @return Model
     */
    public function getRequestByBiz($id, $biz_type)
    {
        return WorkflowRequestModel::findFirst([
            'conditions' => 'biz_type = :type: AND biz_value= :id: AND is_abandon = :is_abandon:',
            'bind'       => ['type' => $biz_type, 'id' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
            'order'      => 'id DESC',
        ]);
    }

    /**
     * @param $id
     * @return Model
     */
    public function getRequest($id)
    {
        return $this->getRequestByBiz($id, Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE);
    }

    /**
     * 表单枚举
     *
     * @param int $department_id 电子合同所属一级部门
     * @return array
     */
    public function formSelectEnums(int $department_id = 0)
    {
        $data               = [];
        $enums_data         = EnumsService::getInstance()->getSettingEnvValueMap('pmd_form_rule_select_enums');
        $currency           = GlobalEnums::$currency_item;

        // 乙方授权人
        $data['staff_list'] = $this->getFlashAuthorizedStaffList($department_id);

        $flash_sign_config  = EnumsService::getInstance()->getSettingEnvValueMap('flash_home_contract_sign_name_config');

        //描述
        foreach ($currency as $key => $value) {
            $data['flash_bank_currency'][] = [
                'id'   => $key,
                'name' => static::$t->_($value),
            ];
        }

        foreach ($enums_data['big_return_rate'] as $key => $value) {
            $data['big_return_rate'][] = [
                'id'   => $value['id'],
                'name' => $value['name'],
            ];
        }

        foreach ($enums_data['standard_return_price'] as $key => $value) {
            $data['standard_return_price'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['big_return_price'] as $key => $value) {
            $data['big_return_price'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['standard_return_rate'] as $key => $value) {
            $data['standard_return_rate'][] = [
                'id'   => $value['id'],
                'name' => $value['name'],
            ];
        }

        foreach ($enums_data['settlement_type'] as $key => $value) {
            $data['settlement_type'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['account_period'] as $key => $value) {
            $data['account_period'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['customer_country_registerer'] as $key => $value) {
            $data['customer_country_registerer'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['standard_cod_pay_method'] as $key => $value) {
            $data['standard_cod_pay_method'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['standard_settlement_type'] as $key => $value) {
            $data['standard_settlement_type'][] = [
                'id'   => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        foreach ($enums_data['cod_pay_date'] as $key => $value) {
            $data['cod_pay_date'][] = [
                'id'   => $value['id'],
                'name' => $value['name'],
            ];
        }

        foreach ($enums_data['sign_dot_radius'] as $key => $value) {
            $data['sign_dot_radius'][] = [
                'id'   => $value['id'],
                'name' => $value['name'],
            ];
        }

        // 是否 有cod服务的选项
        foreach ($enums_data['has_cod_service'] as $value) {
            $data['has_cod_service'][] = [
                'id' => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        // 条款信息: 水果件-退件运费-费率
        foreach ($enums_data['fruit_return_rate'] as $value) {
            $data['fruit_return_rate'][] = [
                'id' => $value['id'],
                'name' => $value['name'],
            ];
        }

        // 条款信息 水果件-结算模式
        foreach ($enums_data['fruit_settlement_type'] as $value) {
            $data['fruit_settlement_type'][] = [
                'id' => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        // 补充协议: 修改条款选项
        foreach ($enums_data['agreement_modified_term'] as $value) {
            $data['agreement_modified_term'][] = [
                'id' => $value['id'],
                'name' => $value['name'],
            ];
        }

        // 附件A
        // 标准件计费方式选项
        foreach ($enums_data['standard_charge_type'] as $value) {
            $data['standard_charge_type'][] = [
                'id' => $value['id'],
                'name' => static::$t->_($value['name']),
            ];
        }

        // 上述选项4: 按重量/尺寸（带尺寸限制）的子项
        foreach ($enums_data['standard_limit_type'] as $value) {
            $data['standard_limit_type'][] = [
                'id' => $value['id'],
                'name' => static::$t->_($value['name']),
                'description' => $value['description'],
            ];
        }

        // 大件计费标准选项
        foreach ($enums_data['bulky_item_charge_type'] as $value) {
            $data['bulky_item_charge_type'][] = [
                'id' => $value['id'],
                'name' => $value['name'],
            ];
        }

        foreach (ContractEnums::$franchisee_type_enums as $key => $value) {
            $data['franchisee_type'][] = [
                'id'   => $key,
                'name' => static::$t->_($value),
            ];
        }

        $data['delivery_fh_principal'][] = [
            'id'   => $flash_sign_config['name'] ?? '',
            'name' => $flash_sign_config['name'] ?? '',
        ];

        // 价格调整策略 各选项的描述 按 合同语言分别返回
        $data['price_scheme_type'] = $enums_data['price_scheme_type'];

        // Retail 额外的表单
        // 条款信息
        // 结算周期(天)
        $data['settlement_type_retail'] = $enums_data['settlement_type_retail'];

        // 信用期限(天)
        $data['account_period_retail'] = $enums_data['account_period_retail'];

        // 价格调整和条件
        $data['price_scheme_item_retail'] = $enums_data['price_scheme_item_retail'];

        // 补充协议-偏远地区附加费
        $data['agreement_surcharge_type'] = $enums_data['agreement_surcharge_type'];
        
        return $data;
    }

    /**
     * 查询Project Management - KAM
     * 及子部门下员工信息
     *
     * @param int $staff_info_id
     * @param int $staff_type 员工类型: 1-联系人; 2-销售代表
     * @return object
     */
    public function getStaffById(int $staff_info_id, int $staff_type = 0)
    {
        $conditions = 'staff_info_id = :staff_info_id:';
        $bind = ['staff_info_id' => $staff_info_id];

        // 销售代表 - 全员搜
        if ($staff_type == 2) {

        } else {
            // 联系人 - 按 部门搜
            //Group Project Management  部门下
            $department_info = $this->getContractApplicableDepartmentByName();

            $department_id = $department_info['Group Project Management'];

            $conditions .= ' AND sys_department_id IN ({sys_department_id:array})';
            $bind['sys_department_id'] = $department_id;
        }

        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => 'staff_info_id, name, name_en, email, mobile',
        ]);

        return empty($staff_info) ? (object)[] : $staff_info->toArray();
    }

    /**
     * 确认合同已完成
     * @param $id
     * @return array
     */
    public function confirmCompleted($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $contract = ContractElectronicModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            $bool = $contract->i_update([
                'updated_at'      => date('Y-m-d H:i:s'),
                'contract_status' => ContractEnums::CONTRACT_STATUS_2,
            ]);
            if ($bool === false) {
                throw new BusinessException('确认完成合同失败,id' . $id, ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (BusinessException $e) {                 //业务错误不可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-confirm-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }


    /**
     * 线上签约
     * @param $id
     * @return array
     */
    public function contractSign($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $contract_electronic = ContractElectronicModel::findFirst([
                'id = :id: and sign_status =:sign_status: and approve_status =:approve_status:',
                'bind' => ['id' => $id, 'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_1, 'approve_status' => Enums::CONTRACT_STATUS_APPROVAL],
            ]);
            if (empty($contract_electronic)) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $bool = $contract_electronic->i_update([
                'updated_at'  => date('Y-m-d H:i:s'),
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_4,
                'sign_completed_at' => date('Y-m-d H:i:s'),
            ]);
            if ($bool === false) {
                throw new BusinessException('签约线上合同失败,id' . $id . '原因：' . get_data_object_error_msg($contract_electronic), ErrCode::$BUSINESS_ERROR);
            }

            //关联的合同更新为已签约线下
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $contract_electronic->relate_id],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $contract_bool = $contract->i_update(['sign_type' => ContractEnums::CONTRACT_SIGN_TYPE_1]);

            if ($contract_bool === false) {
                throw new BusinessException('签约线上合同失败,id' . $id . '原因：' . get_data_object_error_msg($contract), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误不可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-confirm-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 有效期格式特殊处理
     * @param $lang
     * @param $date
     * @return array
     */
    public function getDateFormat($lang, $date)
    {
        $date = explode('-', $date);
        //时间格式错误返回空
        if (!isset($date[0]) || !isset($date[1]) || !isset($date[2])) {
            return [];
        }
        if (isset($lang[0]) && $lang[0] == 'en') {
            $date_0 = ltrim($date[2], '0') . ' ' . ContractEnums::$month_en_arr[ltrim($date[1], '0')] . ',' . $date[0];
        }

        if (isset($lang[1]) && $lang[1] == 'th') {
            $date_1 = ltrim($date[2], '0') . ' ' . ContractEnums::$month_th_arr[ltrim($date[1], '0')] . ' ' . ($date[0] + Enums::BUDDHIST_CALENDAR_YEAR);
        }

        if (isset($lang[1]) && $lang[1] == 'zh') {
            $date_1 = $date[0] . '年' . ltrim($date[1], '0') . '月' . ltrim($date[2], '0') . '日';
        }

        return [$date_0 ?? '', $date_1 ?? ''];

    }

    /**
     * 不同部门组装不同模版
     *
     * @param $data
     * @param array $sign_info 签字信息
     * @return array
     * @throws BusinessException
     */
    public function templateData($data, $sign_info = [])
    {
        if ($this->isFlashHomeOperationV2($data['department_id'])) {
            $form_data = $this->fhContractParams($data, $sign_info);
        } else {
            //验证数据
            $form_data = $this->validParam($data);

            //生成合同
            $lang      = $data['lang'];
            if (isCountry('MY')) {
                $service = reBuildCountryInstance(new ContractElectronicService());
                $form_data = $service->contractParams($form_data, $lang);
            } else {
                $form_data = $this->contractParams($form_data, $lang);
            }
            // 重置指定的表单字段值
            $form_data = $this->resetFormDataValue($form_data);
        }
        // 生成PDF
        $pdf_info = $this->generateElectronicContractPdfFile($data['department_id'], $data['file_url'], $form_data);

        return [
            'data' => $form_data,
            'pdf_info' => $pdf_info,
        ];
    }

    /**
     * 处理flash home 合同表单数据
     *
     * @param $data
     * @param array $sign_info
     * @return array
     */
    public function fhContractParams($data, $sign_info = [])
    {
        $field_data = [];

        $form_rule = $data['form_rule'];

        foreach ($form_rule as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $field_data[$v1['field']] = $v1['value'];
            }
        }

        $enums_data = $this->formSelectEnums($data['department_id']);

        if (isset($field_data['sign_account_opening_date'])) {
            $date_format = $this->getDateFormat([1 => 'th'], $field_data['sign_account_opening_date']);
            $field_data['sign_account_opening_date'] = $date_format[1] ?? '';
        }
        if (isset($field_data['sign_dot_radius'])) {
            $sign_dot_radius_kv = array_column($enums_data['sign_dot_radius'] ?? [], 'name', 'id');
            $field_data['sign_dot_radius'] = $sign_dot_radius_kv[$field_data['sign_dot_radius']] ?? '';
        }
        if (isset($field_data['franchisee_type'])) {
            $franchisee_type_kv = array_column($enums_data['franchisee_type'] ?? [], 'name', 'id');
            $field_data['franchisee_type'] = $franchisee_type_kv[$field_data['franchisee_type']] ?? '';
        }
        $field_data['sign_info'][] = ['customer_name' => $field_data['sign_franchisee_name'] ?? ''];
        if (!empty($sign_info['signNameUrl'])) {
            $field_data['sign_info'][0]['sign_name_url'] = $sign_info['signNameUrl'];
        }
        //[送货约定信息-公司名称]可能出现空值, 空值显示NA
        if (empty($field_data['delivery_company_name'])) {
            $field_data['delivery_company_name'] = '-';
        }
        $flash_sign_config               = EnumsService::getInstance()->getSettingEnvValueMap('flash_home_contract_sign_name_config');
        $field_data['fh_sign_name']      = $flash_sign_config['name'];
        $field_data['fh_sign_job_title'] = $flash_sign_config['job_title'];
        $field_data['fh_sign_img_url']   = $flash_sign_config['sign_name_url'];
        return $field_data;
    }

    /**
     * 作废操作
     *
     * @param $id
     * @param array $user
     * @return array
     */
    public function Invalid($id, $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $contract_electronic = ContractElectronicModel::findFirst([
                'id = :id: AND sign_status IN ({sign_status:array})',
                'bind' => ['id' => $id, 'sign_status' => [ContractEnums::CONTRACT_SIGN_STATUS_1, ContractEnums::CONTRACT_SIGN_STATUS_9]],
            ]);
            if (empty($contract_electronic)) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $business_approve_status = $contract_electronic->business_approve_status;

            // 将待审核的合同内容审核(原电子合同商务审核)单据撤回
            $request = $this->getRequest($contract_electronic->id);
            if (!empty($request) && $request->state == Enums::WF_STATE_PENDING) {
                $result = (new WorkflowServiceV2())->doCancel($request, $user, $this->getWorkflowParams($contract_electronic), '');
                if ($result === false) {
                    throw new BusinessException('电子合同-作废-合同内容审核的审批流撤回失败, no=' . $contract_electronic->no, ErrCode::$BUSINESS_ERROR);
                }

                $business_approve_status = Enums::WF_STATE_CANCEL;
            }

            $update_data = [
                'updated_at'  => date('Y-m-d H:i:s'),
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_7,
                'contract_status' => ContractEnums::CONTRACT_STATUS_0,
                'business_approve_status' => $business_approve_status,
            ];
            $bool = $contract_electronic->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('作废线上合同失败,id' . $id . '原因：' . get_data_object_error_msg($contract_electronic), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-sign-invalid-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 撤回并作废操作
     * @param $id
     * @return array
     */
    public function signCancel($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $contract_electronic = ContractElectronicModel::findFirst([
                'id = :id: AND sign_status IN ({sign_status:array})',
                'bind' => ['id' => $id, 'sign_status' => [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_6]],
            ]);
            if (empty($contract_electronic)) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $bool = $contract_electronic->i_update([
                'updated_at'  => date('Y-m-d H:i:s'),
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_7,
            ]);
            if ($bool === false) {
                throw new BusinessException('撤回并作废线上合同失败,id' . $id . '原因：' . get_data_object_error_msg($contract_electronic), ErrCode::$BUSINESS_ERROR);
            }

            //关联的合同更新为已签约线下
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $contract_electronic->relate_id],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $contract_bool = $contract->i_update(['sign_type' => ContractEnums::CONTRACT_SIGN_TYPE_1]);

            if ($contract_bool === false) {
                throw new BusinessException('作废线上合同失败,id' . $id . '原因：' . get_data_object_error_msg($contract), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-sign-cancel-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取网点信息
     * @param $store_id
     * @return array
     */
    public function getStoreById($store_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $api_params = ['storeId' => $store_id];
            $headers[]  = "Content-type: application/json";
            $headers[]  = "Accept: application/json";
            $headers[]  = "Accept-Language: " . $this->getLanguage('zh-CN');
            $client     = new RestClient('fle_fra');
            $result     = $client->execute(RestClient::METHOD_GET, '/svc/franchisee/profile/getFranchiseeStore', $api_params, $headers, false);
            $this->logger->info(['params' => $api_params, 'result' => $result]);

        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-get-store-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => empty($result['data']) ? [] : [$result['data']],
        ];
    }


    public function signList($condition, $uid = 0, $type = 0)
    {

        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $condition = ['store_id' => $condition['storeId'], 'sign_status' => $condition['signStatus']];
        $columns   = 'c.no,c.lang,c.contract_name as contractName,c.start_date as startDate,c.end_date as endDate,c.sign_status as signStatus,c.customer_name as customerName,c.file_url as fileUrl';

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractElectronicModel::class]);

        $builder = $this->getCondition($builder, $condition, $type);
        $count   = (int)$builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;

        if ($count > 0) {
            $builder->columns($columns);
            $builder->limit($page_size, $offset);
            $builder->orderby('c.id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handelSignItems($items);
        }

        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'ok',
            'data'    => [
                'items'      => $items ?? [],
                'pagination' => [
                    'currentPage' => $page_num,
                    'perPage'     => $page_size,
                    'totalCount'  => $count,
                ],
            ],
        ];
    }

    public function handelSignItems($items)
    {
        $contract_sign_status = ContractEnums::$contract_sign_status;
        foreach ($items as &$item) {
            $item['signStatusText'] = static::$t->_($contract_sign_status[$item['signStatus']]);
            $item['fileUrl']        = gen_file_url(['object_key' => $item['fileUrl']]);
            $item['signInfo']       = [
                [
                    'customerName' => '测试名称',
                    'jobTitle'     => '测试职位',
                    'signNameUrl'  => '',
                ],
            ];

        }
        return $items;
    }

    /**
     * 根据单号获取pdf
     * @param $no
     * @return array
     */
    public function getPdfByNo($no)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $detail = ContractElectronicModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $no],
            ]);

            if (empty($detail)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }
            $_pdf_url = !empty($detail->file_url) ? \App\Library\OssHelper::downloadFileHcm($detail->file_url) : '';
            $file_url = $_pdf_url['file_url'] ?? '';

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => ['pdfUrl' => $file_url ?? ''],
        ];

    }


    /**
     * 电子签名
     * @param $params
     * @return array
     */
    public function signElectronic($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $detail = ContractElectronicModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
            ]);

            if (empty($detail)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            // 已完成在线签约, 返回成功
            if ($detail->sign_status == ContractEnums::CONTRACT_SIGN_STATUS_3) {
                throw new ValidationException(static::$t->_(ContractEnums::$contract_sign_status[ContractEnums::CONTRACT_SIGN_STATUS_3]), ErrCode::$SUCCESS);
            }

            if ($detail->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_2) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $template_version = ContractTemplateVersionModel::findFirst([
                'conditions' => 'template_no = :template_no:',
                'bind'       => ['template_no' => $detail->relate_template_no],
            ]);

            if (empty($template_version)) {
                throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            $data = [
                'form_rule'     => json_decode($detail->contract_content, true),
                'sign_info'     => $params['sign_info'][0],
                'file_url'      => $template_version->file_url,
                'department_id' => $detail->department_id,
            ];
            $template_data = $this->templateData($data, $params['sign_info'][0]);

            // 签字后更新电子合同签名
            $update_data = [
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_3,
                'sign_completed_at' => date('Y-m-d H:i:s'),
                'sign_info' => json_encode($params['sign_info'], JSON_UNESCAPED_UNICODE),
            ];

            //签字后的文件替换没有签字的
            $update_data['file_url'] = $template_data['pdf_info']['object_key'];
            $update_data['bucket_name'] = $template_data['pdf_info']['bucket_name'];
            $this->logger->info('fh电子合同签约-签字before =>' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $detail->save($update_data);
            $this->logger->info('fh电子合同签约-签字after =>' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE));
            if ($bool === false) {
                throw new BusinessException('电子合同签名失败, ' . get_data_object_error_msg($detail) . '; 数据: ' . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //自动归档
            ArchiveUpdateService::getInstance()->autoArchive($detail->toArray());

            // log
            $log_data = [
                'no' => $params['no'],
                'operate_id' => $detail->store_id,
                'created_at' => date('Y-m-d H:i:s'),
                'content' => json_encode($params['sign_info'], JSON_UNESCAPED_UNICODE),
            ];

            $this->logger->info('fh电子合同签字信息:' . json_encode($log_data, JSON_UNESCAPED_UNICODE));

            $log_model = new ContractElectronicSignLogModel();
            if ($log_model->i_create($log_data) === false) {
                throw new BusinessException('电子合同签名日志写入失败, ' . get_data_object_error_msg($log_model), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $db->rollback();

            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-fh-sign-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取默认签约用户类型
     *
     * @param array $user
     * @return array
     */
    public function getDefaultCustomerType(array $user)
    {
        // 默认签约类型
        // 登录人是FH部门, 则默认签约用户类型是FH; 否则, 其他
        $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
        return [
            'customer_type' => $is_flashhome ? ContractEnums::SIGN_CUSTOMER_TYPE_3 : ContractEnums::SIGN_CUSTOMER_TYPE_100,
        ];
    }

    /**
     * 发起签约的初始化数据
     *
     * @param string $electronic_no
     * @return array
     * @throws ValidationException
     */
    public function signInitData(string $electronic_no)
    {
        // 获取电子合同信息
        $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        // 合同未终审通过 且 非未发起的签约状态
        if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_1) {
            throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        // 初始化 商务联系人和POA信息的数据结构
        $sign_data = [
            'electronic_no' => $electronic_no,
            'custom_contact_email' => '',
        ];

        // 获取甲方授权人信息
        // 补充协议 获取 主合同的授权人
        if ($model->template_type == ContractEnums::ELECTRONIC_CONTRACT_TYPE_11) {
            $main_info = ContractElectronicRepository::getInstance()->getLatestRelatedByContractNo($model->contract_no);
            $form_data = ContractElectronicFormdataRepository::getInstance()->getFormDataByNo($main_info['no'] ?? '');
            $lang = $main_info['lang'] ?? '';
        } else {
            $form_data = ContractElectronicFormdataRepository::getInstance()->getFormDataByNo($electronic_no);
            $lang = $model->lang;
        }

        // 授权人
        $custom_authorized_person_info = $form_data['custom_authorized_person_info'] ?? [];


        $lang_item = explode('-', $lang);
        foreach ($custom_authorized_person_info as $info) {
            $sign_name_1 = $info['custom_authorized_person_' . $lang_item[0] ?? ''] ?? '';
            $sign_name_2 = $info['custom_authorized_person_' . $lang_item[1] ?? ''] ?? '';
            $sign_name_item = array_filter([
                $sign_name_1 == ContractEnums::ELECTRONIC_CONTRACT_FORM_DATA_EMPTY_VALUE_DEFAULT_CHAR ? '' : $sign_name_1,
                $sign_name_2 == ContractEnums::ELECTRONIC_CONTRACT_FORM_DATA_EMPTY_VALUE_DEFAULT_CHAR ? '' : $sign_name_2,
            ]);

            $sign_name = implode(ContractEnums::ELECTRONIC_CONTRACT_BILINGUAL_SEPARATOR, $sign_name_item);
            $custom_poa_item[] = [
                'sign_name' => $sign_name,
                'sign_email' => '',
            ];
        }

        $sign_data['custom_poa_item'] = $custom_poa_item ?? [];
        return $sign_data;
    }


    /**
     * 发起签约
     * @param $params
     * @return array
     */
    public function signInitiate($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $detail = ContractElectronicModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);

            if (empty($detail)) {
                throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            if ($detail->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_1 || $detail->approve_status != Enums::CONTRACT_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $detail->sign_status = ContractEnums::CONTRACT_SIGN_STATUS_2;
            $detail->initiate_sign_date = date('Y-m-d');
            $detail->store_id = $params['store_id'];
            $detail->updated_at = date('Y-m-d H:i:s');
            $bool = $detail->save();
            if ($bool === false) {
                throw new BusinessException('电子合同发起签约失败, ' . get_data_object_error_msg($detail) . '; 数据: ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //关联的合同更新为已签约线上
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $detail->relate_id],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $contract_bool = $contract->i_update(['sign_type' => ContractEnums::CONTRACT_SIGN_TYPE_2]);

            if ($contract_bool === false) {
                throw new BusinessException('电子合同发起签约更新合同签约类型失败, ' . get_data_object_error_msg($detail) . '; 数据: ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);

            }
            // todo 通知java

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-electronic-sign-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 发起签约的表单提交, 非FH部门的
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function signInitiateV2(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 发起人部门
            $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
            if ($is_flashhome) {
                throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($params['electronic_no']);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 合同未终审通过 且 非未发起的签约状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_1) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 关联的合同
            $contract = Contract::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $model->relate_id],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $model->relate_id]), ErrCode::$VALIDATE_ERROR);
            }

            $custom_poa_item = [];
            foreach ($params['custom_poa_item'] as $item) {
                $custom_poa_item[] = [
                    'sign_name' => $item['sign_name'],
                    'sign_email' => $item['sign_email'],
                    'sign_key' => generate_uuid('poa_sign_key'),
                    'sign_img' => '',
                    'sign_job_title' => '',
                ];
            }

            // 电子合同发起签约信息
            $initiate_sign_date = date('Y-m-d');
            $sign_data = [
                'electronic_no' => $params['electronic_no'],
                'electronic_key' => generate_uuid($params['electronic_no']),
                'sign_version' => 1,
                'initiate_sign_date' => $initiate_sign_date,
                'customer_type' => $params['customer_type'],
                'sign_order_type' => $params['sign_order_type'],
                'custom_contact_email' => $params['custom_contact_email'],
                'custom_contact_email_key' => generate_uuid('custom_contact_sign_key'),
                'custom_poa_item' => json_encode($custom_poa_item, JSON_UNESCAPED_UNICODE),
                'created_id' => $user['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('电子合同-发起签约-表单数据' . json_encode($sign_data, JSON_UNESCAPED_UNICODE));
            $sign_info_model = new ContractElectronicSignInfoModel();
            if ($sign_info_model->save($sign_data) === false) {
                throw new BusinessException('发起签约失败, ' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $model_update = [
                'initiate_sign_date' => $initiate_sign_date,
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_2,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            // 判断签约顺序, 如果是我方先签约, 需要合成乙方签章
            if ($sign_info_model->sign_order_type == ContractEnums::SIGN_ORDER_TYPE_PARTY_B) {
                // 获取乙方授权人签章
                $form_data_model = $model->getFormData();
                $form_data = !empty($form_data_model->form_data) ? json_decode($form_data_model->form_data, true) : [];
                $form_data = $this->getPartyBFormData($form_data, $model);

                $this->logger->info('电子合同-发起签约-我方先签, pdf原文件=' . $model->file_url);

                // 生成签字版pdf
                $pdf_file_info = $this->generateElectronicContractPdfFile($model->department_id, $model->ftl_file_url, $form_data);

                $this->logger->info('电子合同-发起签约-我方先签, pdf新文件(无水印)=' . $pdf_file_info['object_url']);

                // 更新电子合同表单数据
                $this->updateElectronicContractFormData($form_data_model, $form_data);

                // 合成后的PDF更新
                $model_update['bucket_name'] = $pdf_file_info['bucket_name'];
                $model_update['file_url'] = $pdf_file_info['object_key'];
            }

            if ($model->save($model_update) === false) {
                throw new BusinessException('发起签约失败, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 关联的合同更新为已签约线上
            $contract_update = [
                'sign_type' => ContractEnums::CONTRACT_SIGN_TYPE_2,
            ];
            if ($contract->i_update($contract_update) === false) {
                throw new BusinessException('发起签约-更新合同签约类型失败, ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 给商务联系人发送邮件通知
            $email_val = [
                'emails' => [$sign_data['custom_contact_email']],
                'contract_name' => $model->contract_name,
                'contract_lang' => $model->lang,
                'sign_link' => $this->generateEmailSignLink($sign_data['electronic_key'], $sign_data['custom_contact_email_key'], ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
            ];
            $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN, $email_val);

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-signInitiateV2-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-signInitiateV2-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 重新发起签约, 非FH部门的
     *
     * @param string $electronic_no
     * @param array $user
     * @return array
     */
    public function resign(string $electronic_no, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 发起人部门
            $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
            if ($is_flashhome) {
                throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同须是超时未签约
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_9) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 关联的合同
            $contract = Contract::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $model->relate_id],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $model->relate_id]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 重新发起签约日期
            $initiate_sign_date = date('Y-m-d');

            // 电子合同签约信息更新
            $sign_data = [
                'sign_version' => $sign_info_model->sign_version + 1,
                'initiate_sign_date' => $initiate_sign_date,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($sign_info_model->save($sign_data) === false) {
                throw new BusinessException('重新发起签约失败, ' . get_data_object_error_msg($sign_info_model) . '; 数据: ' . json_encode($sign_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-重新发起签约-表单数据' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 电子合同主表更新
            $model_update = [
                'initiate_sign_date' => $initiate_sign_date,
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_2,
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($model->save($model_update) === false) {
                throw new BusinessException('重新发起签约失败, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 给商务联系人发送邮件通知
            $email_val = [
                'emails' => [$sign_info_model->custom_contact_email],
                'contract_name' => $model->contract_name,
                'contract_lang' => $model->lang,
                'sign_link' => $this->generateEmailSignLink($sign_info_model->electronic_key, $sign_info_model->custom_contact_email_key, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
            ];
            $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN, $email_val);

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-resign-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-resign-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 发起签约的初始化数据
     *
     * @param string $electronic_no
     * @param array $user
     * @return array
     * @throws ValidationException
     */
    public function getReviewSignInfo(string $electronic_no, array $user)
    {
        // 发起人部门
        $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
        if ($is_flashhome) {
            throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取电子合同信息
        $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        // 合同终审通过 且 待BD复核签字状态
        if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_8) {
            throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取电子合同签约信息
        $sign_info_model = $model->getSignInfo();
        if (empty($sign_info_model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }
        $_pdf_url = !empty($model->file_url) ? \App\Library\OssHelper::downloadFileHcm($model->file_url) : '';
        $file_url = $_pdf_url['file_url'] ?? '';

        return [
            'electronic_no' => $sign_info_model->electronic_no,
            'file_url' => $file_url,
            'reviewed_page_total' => $sign_info_model->reviewed_page_total
        ];
    }

    /**
     * 获取复核人电子小签(BD)
     *
     * @param array $user
     * @return array
     * @throws ValidationException
     */
    public function getReviewerSignImg(array $user)
    {
        // 发起人部门
        $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
        if ($is_flashhome) {
            throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取BD小签配置
        $sign_img = ContractElectronicStaffSignConfigModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind' => ['staff_id' => $user['id']],
            'columns' => ['sign_img'],
        ])->sign_img ?? '';

        return [
            'sign_img' => $sign_img,
        ];
    }

    /**
     * BD签字复核，逐页提交小签, 非FH部门的
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function submitPageSign(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 发起人部门
            $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
            if ($is_flashhome) {
                throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($params['electronic_no']);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 待BD复核状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_8) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 总页数和首次报告的总页数是否一致
            if (!empty($sign_info_model->pdf_page_total) && $sign_info_model->pdf_page_total != $params['pdf_page_total']) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 当前复核页码 超过了提交的总页数
            if (!empty($sign_info_model->pdf_page_total) && $params['review_page_number'] > $sign_info_model->pdf_page_total) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 当前复核页码 非 上次页码的下一页
            if (($sign_info_model->reviewed_page_total + 1) != $params['review_page_number']) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取该BD已签约页数
            $reviewed_page_total = ContractElectronicReviewSignLogRepository::getInstance()->getReviewedSignPageTotal($sign_info_model->electronic_no, $sign_info_model->sign_version, $user['id']);
            if (($reviewed_page_total + 1) != $params['review_page_number']) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同签约信息更新
            $sign_data = [
                'reviewed_page_total' => $sign_info_model->reviewed_page_total + 1,
                'reviewer_sign_img' => $params['sign_img'],
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            // 初始化总页码
            if (empty($sign_info_model->pdf_page_total)) {
                $sign_data['pdf_page_total'] = $params['pdf_page_total'];
            }

            $this->logger->info('电子合同-BD复核签字-提交每页小签, 更新前=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->logger->info('电子合同-BD复核签字-提交每页小签, 待更新=' . json_encode($sign_data, JSON_UNESCAPED_UNICODE));

            if ($sign_info_model->save($sign_data) === false) {
                throw new BusinessException('BD复核签字-提交小签异常, ' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-BD复核签字-提交每页小签, 更新后=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 写入每页复核日志
            $review_sign_log = [
                'electronic_no' => $sign_info_model->electronic_no,
                'sign_version' => $sign_info_model->sign_version,
                'staff_id' => $user['id'],
                'staff_name' => $user['name'],
                'staff_sign_img' => $params['sign_img'],
                'review_at' => date('Y-m-d H:i:s'),
                'review_page_number' => $params['review_page_number'],
                'review_ip' => get_client_real_ip(),
                'review_status' => ContractEnums::ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_PASS,
            ];

            $this->logger->info('电子合同-BD复核签字-提交每页小签, 复核日志=' . json_encode($review_sign_log, JSON_UNESCAPED_UNICODE));

            $review_sign_model = new ContractElectronicReviewSignLogModel();
            if ($review_sign_model->save($review_sign_log) === false) {
                throw new BusinessException('BD复核签字-提交小签异常, ' . get_data_object_error_msg($review_sign_model), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-submitPageSign-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-submitPageSign-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * BD签字复核 - 退回, 非FH部门的
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function reviewSignReturn(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 发起人部门
            $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
            if ($is_flashhome) {
                throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($params['electronic_no']);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 待BD复核状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_8) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 退回时的签字版本
            $current_sign_version = $sign_info_model->sign_version;

            // 重置发起签约日期
            $initiate_sign_date = date('Y-m-d');

            // 电子合同签约信息: 复核相关数据初始化
            $sign_data = [
                'sign_version' => $current_sign_version + 1,
                'initiate_sign_date' => $initiate_sign_date,
                'reviewer_sign_img' => '',
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('电子合同-BD复核签字-复核退回, 更新前=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->logger->info('电子合同-BD复核签字-复核退回, 待更新=' . json_encode($sign_data, JSON_UNESCAPED_UNICODE));

            if ($sign_info_model->save($sign_data) === false) {
                throw new BusinessException('BD复核签字-退回异常, ' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-BD复核签字-复核退回, 更新后=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 写入每页复核日志
            $review_sign_log = [
                'electronic_no' => $sign_info_model->electronic_no,
                'sign_version' => $current_sign_version,
                'staff_id' => $user['id'],
                'staff_name' => $user['name'],
                'review_at' => date('Y-m-d H:i:s'),
                'review_page_number' => $params['review_page_number'],
                'review_ip' => get_client_real_ip(),
                'review_status' => ContractEnums::ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_RETURN,
                'return_reason' => $params['return_reason'],
            ];

            $this->logger->info('电子合同-BD复核签字-复核退回, 退回日志=' . json_encode($review_sign_log, JSON_UNESCAPED_UNICODE));

            $review_sign_model = new ContractElectronicReviewSignLogModel();
            if ($review_sign_model->save($review_sign_log) === false) {
                throw new BusinessException('BD复核签字-退回异常, ' . get_data_object_error_msg($review_sign_model), ErrCode::$BUSINESS_ERROR);
            }

            // 电子合同更新为待重新签约
            $model_update = [
                'initiate_sign_date' => $initiate_sign_date,
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_6,
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($model->save($model_update) === false) {
                throw new BusinessException('BD复核签字-退回异常, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 给商务联系人发送待重新签约邮件通知
            $email_val = [
                'emails' => [$sign_info_model->custom_contact_email],
                'contract_name' => $model->contract_name,
                'contract_lang' => $model->lang,
                'reason' => $params['return_reason'],
                'sign_link' => $this->generateEmailSignLink($sign_info_model->electronic_key, $sign_info_model->custom_contact_email_key, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
            ];
            $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_RESIGN, $email_val);

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-reviewSignReturn-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-reviewSignReturn-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * BD签字复核 - 完成, 非FH部门的
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function reviewSignCompleted(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 发起人部门
            $is_flashhome = $this->isFlashHomeOperation($user['node_department_id']);
            if ($is_flashhome) {
                throw new ValidationException(static::$t->_('sign_init_submit_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 密码验证
            $user_model = StaffInfoModel::findfirst($user['id']);
            if (empty($user_model)) {
                throw new ValidationException(static::$t->_('staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $password_check_result = UserService::getInstance()->checkUserPassword($user_model, $params['password']);
            if (!$password_check_result) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_005'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($params['electronic_no']);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 待BD复核状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_8) {
                throw new ValidationException(static::$t->_('contract_electronic_sign_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 检测所有页码是否都已复合签字
            $reviewed_page_total = ContractElectronicReviewSignLogRepository::getInstance()->getReviewedSignPageTotal($sign_info_model->electronic_no, $sign_info_model->sign_version, $user['id']);
            if ($reviewed_page_total != $sign_info_model->pdf_page_total) {
                throw new ValidationException(static::$t->_('sign_bd_review_error_004'), ErrCode::$VALIDATE_ERROR);
            }

            // 电子合同签约信息更新
            $sign_data = [
                'review_finsh_staff' => $user['id'],
                'review_finsh_at' => date('Y-m-d H:i:s'),
                'business_audit_status' => Enums::WF_STATE_PENDING,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('电子合同-BD复核签字-完成复核, 更新前=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->logger->info('电子合同-BD复核签字-完成复核, 待更新=' . json_encode($sign_data, JSON_UNESCAPED_UNICODE));

            if ($sign_info_model->save($sign_data) === false) {
                throw new BusinessException('BD复核签字-完成复核异常,' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-BD复核签字-完成复核, 更新后=' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 写入每页复核日志
            $review_sign_log = [
                'electronic_no' => $sign_info_model->electronic_no,
                'sign_version' => $sign_info_model->sign_version,
                'staff_id' => $user['id'],
                'staff_name' => $user['name'],
                'review_at' => date('Y-m-d H:i:s'),
                'review_page_number' => $sign_info_model->pdf_page_total,
                'review_ip' => get_client_real_ip(),
                'review_status' => ContractEnums::ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_COMPLETED,
            ];

            $this->logger->info('电子合同-BD复核签字-完成复核, 复核日志=' . json_encode($review_sign_log, JSON_UNESCAPED_UNICODE));

            $review_sign_model = new ContractElectronicReviewSignLogModel();
            if ($review_sign_model->save($review_sign_log) === false) {
                throw new BusinessException('BD复核签字-完成复核异常, ' . get_data_object_error_msg($review_sign_model), ErrCode::$BUSINESS_ERROR);
            }

            // 生成待BD小签的pdf电子合同
            $form_data_model = $model->getFormData();
            $form_data = !empty($form_data_model->form_data) ? json_decode($form_data_model->form_data, true) : [];
            $form_data['flash_bd_sign_img'] = $sign_info_model->reviewer_sign_img;

            $this->logger->info('电子合同-BD复核签字-完成复核, pdf原文件=' . $model->file_url);

            // 生成pdf文件
            $pdf_file_info = $this->generateElectronicContractPdfFile($model->department_id, $model->ftl_file_url, $form_data);

            $this->logger->info('电子合同-BD复核签字-完成复核, pdf新文件=' . $pdf_file_info['object_url']);

            // 电子合同更新为签字待复核
            $model_update = [
                'file_url' => $pdf_file_info['object_key'],
                'bucket_name' => $pdf_file_info['bucket_name'],
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_5,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($model->save($model_update) === false) {
                throw new BusinessException('BD复核签字-完成复核异常, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 更新电子合同表单数据
            $this->updateElectronicContractFormData($form_data_model, $form_data);

            // 创建商务复核签字审批流
            $flow_res = ContractElectronicFlowService::getInstance()->createRequest($sign_info_model, $user);
            if (!$flow_res) {
                throw new BusinessException('BD复核签字-完成复核异常, 审批流创建失败=' . $sign_info_model->electronic_no, ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-reviewSignCompleted-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-reviewSignCompleted-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 乙方复核签字审批列表
     *
     * @param $condition
     * @param $user
     * @return array
     */
    public function getReviewSignAuditList($condition, $user)
    {
        $condition['uid'] = $user['id'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['sign_info' => ContractElectronicSignInfoModel::class]);
        $builder->leftjoin(ContractElectronicModel::class, "sign_info.electronic_no = c.no", 'c');
        $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE], $condition['uid'], 'sign_info');

        // 电子合同名称
        if (!empty($condition['contract_name'])) {
            $builder->andWhere('c.contract_name  like :contract_name:', ['contract_name' => '%' . $condition['contract_name'] . '%']);
        }

        // 客户名称
        if (!empty($condition['customer_name'])) {
            $builder->andWhere('c.customer_name like :customer_name:', ['customer_name' => '%' . $condition['customer_name'] . '%']);
        }

        // 合同编号
        if (!empty($condition['electronic_no'])) {
            $builder->andWhere('sign_info.electronic_no = :electronic_no:', ['electronic_no' => $condition['electronic_no']]);
        }

        // 创建人
        if (!empty($condition['created_id'])) {
            $builder->andWhere('sign_info.created_id = :created_id:', ['created_id' => $condition['created_id']]);
        }

        $count = (int)$builder->columns('COUNT(DISTINCT sign_info.id) AS total')->getQuery()->getSingleResult()->total;
        if ($count) {
            $columns   = [
                'sign_info.id',
                'sign_info.electronic_no',
                'c.template_name',
                'c.lang',
                'c.start_date',
                'c.end_date',
                'sign_info.custom_sign_submited_at',
                'c.customer_name',
                'c.contract_name',
                'c.sign_status',
                'c.file_url',
                'c.department_id',
            ];

            $builder->columns($columns);
            $builder->limit($page_size, $offset);
            $builder->groupBy('sign_info.id');
            $items = $builder->getQuery()->execute()->toArray();

            // 部门配置
            $department_list = $this->getContractApplicableDepartmentByName();

            foreach ($items as &$item) {
                $item['sign_status_text'] = static::$t->_(ContractEnums::$contract_sign_status[$item['sign_status']]);
                $_pdf_url = !empty($item['file_url']) ? \App\Library\OssHelper::downloadFileHcm($item['file_url']) : '';
                $item['file_url'] = $_pdf_url['file_url'] ?? '';

                // 是否展示下载相关资料按钮 - Retail部门
                $item['is_can_download_file'] = in_array($item['department_id'], $department_list['Retail Management']);
            }
        }

        return [
            'items' => $items ?? [],
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page' => (int)$page_size,
                'total_count' => $count,
            ],
        ];
    }

    /**
     * 更新电子合同表单数据
     *
     * @param object $form_data_model
     * @param array $form_data
     * @return bool
     * @throws BusinessException
     */
    public function updateElectronicContractFormData(object $form_data_model, array $form_data)
    {
        // 更新电子合同表单数据
        $form_data_update = [
            'form_data' => json_encode($form_data, JSON_UNESCAPED_UNICODE),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        if ($form_data_model->save($form_data_update) === false) {
            throw new BusinessException('contract_electronic_formdata save 失败, ' . get_data_object_error_msg($form_data_model) . '; data=' . json_encode($form_data_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取电子合同甲方上传的相关资料-Retail部门
     *
     * @param string $electronic_no
     * @return array
     * @throws ValidationException
     */
    public function getDownloadRelatedFileList(string $electronic_no)
    {
        // 获取电子合同信息
        $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        if (!$this->isRetailManagement($model->department_id)) {
            throw new ValidationException(static::$t->_('sign_audit_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取电子合同签约信息
        $sign_info_model = $model->getSignInfo();
        if (empty($sign_info_model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        // 获取甲方签字时上传的相关资料
        $related_file_list = [];
        $_related_file_list =  $sign_info_model->getRelatedFileList()->toArray();
        foreach ($_related_file_list as $file) {
            $path = !empty($file['object_key']) ? OssHelper::downloadFileHcm($file['object_key']) : '';

            $related_file_list[] = [
                'file_name' => $file['file_name'],
                'object_url' => $path['file_url'] ?? '',
                'created_at' => $file['created_at'],
            ];
        }

        return $related_file_list;
    }

    /**
     * 获取签约邮箱信息
     *
     * @param string $electronic_no
     * @param array $user
     * @return array
     * @throws ValidationException
     */
    public function getSignEmailInfo(string $electronic_no, array $user)
    {
        // 获取电子合同信息
        $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
        if (empty($model) || $model->created_id != $user['id']) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        // 签约状态为非 待签约、已签约-线上、签字待复核、待重新签约、待BD复核签字、超时未签约时, 不可查看
        if (!in_array($model->sign_status, [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_3, ContractEnums::CONTRACT_SIGN_STATUS_5, ContractEnums::CONTRACT_SIGN_STATUS_6, ContractEnums::CONTRACT_SIGN_STATUS_8, ContractEnums::CONTRACT_SIGN_STATUS_9])) {
            throw new ValidationException(static::$t->_('contract_electronic_view_sign_email_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        // 获取电子合同签约信息
        $sign_info_model = $model->getSignInfo();
        if (empty($sign_info_model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
        }

        // 客户类型 非其他, 不可查看
        if ($sign_info_model->customer_type != ContractEnums::SIGN_CUSTOMER_TYPE_100) {
            throw new ValidationException(static::$t->_('contract_electronic_view_sign_email_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        $custom_poa_item = [];
        foreach (json_decode($sign_info_model->custom_poa_item, true) as $poa) {
            $custom_poa_item[] = [
                'sign_name' => $poa['sign_name'],
                'sign_email' => $poa['sign_email'],
                'sign_key' => $poa['sign_key'],
            ];
        }

        return [
            'electronic_no' => $sign_info_model->electronic_no,
            'sign_status' => $model->sign_status,
            'customer_type' => $sign_info_model->customer_type,
            'sign_order_type' => $sign_info_model->sign_order_type,
            'custom_contact_email' => $sign_info_model->custom_contact_email,
            'custom_contact_sign_key' => $sign_info_model->custom_contact_email_key,
            'custom_poa_item' => $custom_poa_item,
        ];
    }

    /**
     * 更新签约邮箱信息
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function saveSignEmailInfo(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($params['electronic_no']);
            if (empty($model) || $model->created_id != $user['id']) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 签约状态为 待签约、待重新签约、超时未签约时, 可修改
            if (!in_array($model->sign_status, [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_6, ContractEnums::CONTRACT_SIGN_STATUS_9])) {
                throw new ValidationException(static::$t->_('contract_electronic_save_sign_email_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['electronic_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 客户类型 非其他, 不可查看
            if ($sign_info_model->customer_type != ContractEnums::SIGN_CUSTOMER_TYPE_100) {
                throw new ValidationException(static::$t->_('contract_electronic_save_sign_email_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info(['update_before_main_data' => $model->toArray()]);
            $this->logger->info(['update_before_sign_info_data' => $sign_info_model->toArray()]);

            // 需要重置的相关信息
            $update_main_data = [];
            $update_sign_info_data = [];
            $is_reset_client_otp_certification = false; // 是否销毁甲方OTP认证
            $is_resend_sign_email_notice = false;// 是否发送签约邮件通知

            // 是否是商务联系人的
            if ($params['sign_key'] == $sign_info_model->custom_contact_email_key) {
                $sign_key_role = ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS;
                // 邮箱是否真正变更
                if (strtolower($params['sign_email']) != strtolower($sign_info_model->custom_contact_email)) {
                    $is_reset_client_otp_certification = true;

                    $update_sign_info_data = [
                        'custom_contact_email' => $params['sign_email'],
                        'updated_id' => $user['id'],
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];

                    // 仅待签约/待重新签约 发邮件, 重置签约日期
                    if (in_array($model->sign_status, [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_6])) {
                        $is_resend_sign_email_notice = true;

                        $initiate_sign_date = date('Y-m-d');

                        $update_sign_info_data['initiate_sign_date'] = $initiate_sign_date;

                        $update_main_data = [
                            'initiate_sign_date' => $initiate_sign_date,
                            'updated_id' => $user['id'],
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                }

            } else {
                // 是否是POA的
                $sign_key_role = ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_POA;

                $custom_poa_item = json_decode($sign_info_model->custom_poa_item, true);
                foreach ($custom_poa_item as $poa_index => $poa) {
                    if ($params['sign_key'] == $poa['sign_key'] && strtolower($params['sign_email']) != strtolower($poa['sign_email'])) {
                        $is_reset_client_otp_certification = true;
                        $custom_poa_item[$poa_index]['sign_email'] = $params['sign_email'];

                        $update_sign_info_data = [
                            'custom_poa_item' => json_encode($custom_poa_item, JSON_UNESCAPED_UNICODE),
                            'updated_id' => $user['id'],
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        break;
                    }
                }
            }

            $this->logger->info(['update_main_data' => $update_main_data]);
            $this->logger->info(['update_sign_info_data' => $update_sign_info_data]);

            if (!empty($update_main_data) && $model->i_update($update_main_data) === false) {
                throw new BusinessException('电子合同主表更新失败, 原因可能是' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($update_sign_info_data) && $sign_info_model->i_update($update_sign_info_data) === false) {
                throw new BusinessException('sign_info表更新失败, 原因可能是' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info(['update_after_main_data' => $model->toArray()]);
            $this->logger->info(['update_after_sign_info_data' => $sign_info_model->toArray()]);

            // 给商务联系人发送邮件通知
            if ($is_resend_sign_email_notice) {
                $email_val = [
                    'emails' => [$sign_info_model->custom_contact_email],
                    'contract_name' => $model->contract_name,
                    'contract_lang' => $model->lang,
                    'sign_link' => $this->generateEmailSignLink($sign_info_model->electronic_key, $sign_info_model->custom_contact_email_key, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
                ];

                $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN, $email_val);
            }

            $db->commit();

            // 重置OTP认证信息
            if ($is_reset_client_otp_certification) {
                $otp_cache_key_item = [
                    // OTP认证缓存清除(sign_token)
                    RedisKey::ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_PREFIX . $sign_info_model->electronic_key . '_' . $params['sign_key'],
                ];

                // 商务角色: 清空商务登录场景OTP
                $client_otp_code_key_prefix = RedisKey::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_PREFIX . $sign_info_model->electronic_key . '_' . $params['sign_key'];
                if ($sign_key_role == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS) {
                    $otp_cache_key_item[] = $client_otp_code_key_prefix . '_' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN;
                } else {
                    // POA角色: 清空POA登录场景OTP, 商务代签场景OTP
                    $otp_cache_key_item[] = $client_otp_code_key_prefix . '_' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN;
                    $otp_cache_key_item[] = $client_otp_code_key_prefix . '_' . ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN;
                }

                $del_cache_log = [
                    'electronic_no' => $sign_info_model->electronic_no,
                    'electronic_key' => $sign_info_model->electronic_key,
                    'sign_key' => $params['sign_key'],
                    'del_res' => [],
                ];

                foreach ($otp_cache_key_item as $cache_key) {
                    $del_res = $this->delCache(md5($cache_key));
                    $del_cache_log['del_res'][] = [
                        $cache_key => $del_res,
                    ];
                }

                $this->logger->info(['save_sign_email_clear_otp_cache' => $del_cache_log]);
            }

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->notice('contract-electronic-saveSignEmailInfo-error:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->error('contract-electronic-saveSignEmailInfo-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取原合同信息并赋值到新合同
     * @param $params
     * @param $user
     * @return array
     * @throws ValidationException
     */
    public function getOriginalContractInfo($params, $user): array
    {
        $original_no = $params['original_no'] ?? '';
        $template_no          = $params['template_no'] ?? '';

        //校验原合同编号是否已填写
        if (empty($original_no)) {
            throw new ValidationException(self::$t->_('contract.original_contract_no_empty'), ErrCode::$VALIDATE_ERROR);
        }

        //校验模版是否选择
        if (empty($template_no)) {
            throw new ValidationException(self::$t->_('contract.template_no_empty'), ErrCode::$VALIDATE_ERROR);
        }

        // 电子合同不存在
        $contract_electronic_info = ContractElectronicRepository::getInstance()->getContractElectronicByNo($original_no);
        if (empty($contract_electronic_info)) {
            throw new ValidationException(static::$t->_('contract.contract_info_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        $contract_electronic_info = $contract_electronic_info->toArray();

        if ($contract_electronic_info['relate_template_no'] != $template_no) {
            throw new ValidationException(static::$t->_('contract.contract_info_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        //创建人
        if ($contract_electronic_info['created_id'] != $user['id']) {
            throw new ValidationException(static::$t->_('contract.contract_info_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        //获取字段值
        $form_data = ContractElectronicFormdataRepository::getInstance()->getFormDataByNo($original_no);

        if (empty($form_data)) {
            throw new ValidationException(static::$t->_('contract.contract_info_not_exist'), ErrCode::$BUSINESS_ERROR);
        }

        foreach ($form_data as $key => $value) {
            if (in_array($key, ContractEnums::$retail_delete_fields)) {
                unset($form_data[$key]);
            }
        }

        return $form_data;
    }

    /**
     * 提取乙方签字数据
     *
     * @param array $form_data 表单数据
     * @param object $model 电子合同主表对象
     * @return array|mixed
     */
    public function getPartyBFormData(array $form_data, object $model)
    {
        // 乙方公司签章
        $flash_company_sign_img = ContractElectronicService::getInstance()->getElectronicContractCompanySignImg($model->department_id);
        $form_data['flash_company_sign_img'] = $flash_company_sign_img;
        $form_data['flash_company_sign_date'] = date('Y-m-d');

        // 乙方授权人签章: 如果合同是仅补充协议, 则乙方授权人从关联的主合同获取
        if ($model->template_type == ContractEnums::ELECTRONIC_CONTRACT_TYPE_11) {
            // 关联主合同固化的授权人
            $main_contract_info = ContractElectronicRepository::getInstance()->getLatestRelatedByContractNo($model->contract_no);
            $main_contract_form_data = ContractElectronicFormdataRepository::getInstance()->getFormDataByNo($main_contract_info['no'] ?? '');
            $form_data['flash_authorized_person_name'] = $main_contract_form_data['flash_authorized_person_1'] ?? '';
            if (!empty($main_contract_form_data['flash_authorized_person_2'])) {
                $form_data['flash_authorized_person_name'] .= ContractEnums::ELECTRONIC_CONTRACT_BILINGUAL_SEPARATOR . $main_contract_form_data['flash_authorized_person_2'];
            }

            $authorized_info = ContractElectronicService::getInstance()->getElectronicContractAuthorizedPersonInfo($model->department_id, $main_contract_form_data['flash_authorized_person']);
            $form_data['flash_authorized_person_sign_img'] = $authorized_info['sign_img'];
            $form_data['flash_authorized_person_job_title'] = $authorized_info['job_title'];
        } else {
            // 有主合同的电子合同
            // 授权人姓名
            $form_data['flash_authorized_person_name'] = $form_data['flash_authorized_person_1'] ?? '';
            if (!empty($form_data['flash_authorized_person_2'])) {
                $form_data['flash_authorized_person_name'] .= ContractEnums::ELECTRONIC_CONTRACT_BILINGUAL_SEPARATOR . $form_data['flash_authorized_person_2'];
            }

            // 授权人签章 和 职位
            $authorized_info = ContractElectronicService::getInstance()->getElectronicContractAuthorizedPersonInfo($model->department_id, $form_data['flash_authorized_person']);
            $form_data['flash_authorized_person_sign_img'] = $authorized_info['sign_img'];
            $form_data['flash_authorized_person_job_title'] = $authorized_info['job_title'];
        }

        return $form_data;
    }
}
