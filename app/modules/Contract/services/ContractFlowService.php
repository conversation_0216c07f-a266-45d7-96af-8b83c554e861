<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Enums\ContractEnums;
use App\Library\Exception\BusinessException;
use App\Models\oa\ContractElectronicModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\CrmQuotation\Services\CrmFlowService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class ContractFlowService extends AbstractFlowService
{

    /**
     * @param $contractId
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($contractId, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $request = $this->getRequest($contractId);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING){
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $contractId]
            ]);
            $result = (new WorkflowServiceV2())->doApprove($request, $user,$this->getWorkflowParams($contract,$user),$note);
            if ($result === false) {
                throw new BusinessException('审批流通过失败', ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($result->approved_at)) {
                $update_data = [
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at
                ];
                if ($contract->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_1) {
                    $update_data['sign_type'] = ContractEnums::CONTRACT_SIGN_TYPE_1;
                }

                $bool = $contract->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
                }

                // 审批状态同步电子合同表
                if ($contract->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
                    $contract_electronic = ContractElectronicModel::findFirst([
                        'conditions' => 'relate_id =:relate_id:',
                        'bind'       => ['relate_id' => $contractId]
                    ]);
                    if (empty($contract_electronic)) {
                        throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                    }

                    $contract_electronic->approve_status = Enums::CONTRACT_STATUS_APPROVAL;
                    if ($contract_electronic->save() === false) {
                        //电子合同审批状态更新失败了
                        throw new BusinessException('合同审批电子合同状态更新失败：' . get_data_object_error_msg($contract_electronic) . '数据：relate_id' . $contractId, ErrCode::$CONTRACT_UPDATE_ERROR);
                    }
                }

                //判断是否需要同步MS
                //判断是否需要同步MS, 只有泰国需要在合同处同步MS
                $contract_quotation = $contract->getContractQuotation()->toArray();
                if (!empty($contract_quotation) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                    $contract_quotation_nos = array_column($contract_quotation, 'quotation_no');
                    $contract_quotation_nos = array_values(array_unique($contract_quotation_nos));
                    //查询报价单
                    $crm_quotation_model = new CrmQuotationApplyModel();
                    $quotation_data = $crm_quotation_model::find([
                        'columns' => ['quoted_price_list_sn', 'customer_type_category'],
                        'quoted_price_list_sn in ({quoted_price_list_sn:array})',
                        'bind' => ['quoted_price_list_sn' => $contract_quotation_nos]
                    ])->toArray();
                    $quotation_data_kv = array_column($quotation_data, 'customer_type_category', 'quoted_price_list_sn');
                    //需要同步的报价单号
                    $sync_quotation_nos = [];
                    foreach ($contract_quotation as $apply_info) {
                        //只有KA需要同步,C码在配置账户功能同步
                        if ($quotation_data_kv[$apply_info['quotation_no']] == Enums::CONSUMER_TYPE_KA) {
                            //记录报价单号
                            $sync_quotation_nos[] = $apply_info['quotation_no'];

                        }
                    }
                    $sync_quotation_nos = array_values(array_unique($sync_quotation_nos));
                    if (!empty($sync_quotation_nos)) {
                        $quotation_data = $crm_quotation_model::find([
                            'quoted_price_list_sn in ({quoted_price_list_sn:array})',
                            'bind' => ['quoted_price_list_sn' => $sync_quotation_nos]
                        ]);
                        $quotation_data_arr = $quotation_data->toArray();
                        //修改报价单同步状态
                        $quotation_ids = array_column($quotation_data_arr, 'id');
                        $quotation_ids_str = implode(',', $quotation_ids);
                        $update_success = $db->updateAsDict(
                            $crm_quotation_model->getSource(),
                            [
                                'sync_status' => Enums\CrmQuotationEnums::SYNC_STATUS_YES
                            ],
                            [
                                'conditions' => "id in ({$quotation_ids_str})",
                            ]
                        );
                        if (!$update_success) {
                            throw new BusinessException('同步报价单信息失败', ErrCode::$BUSINESS_ERROR);
                        }
                        //同步MS折扣信息
                        foreach ($quotation_data as $quotation_one) {
                            $sync_result = (new CrmFlowService())->syncMS($quotation_one, 1, $contract->effective_date ?? '', $contract->expiry_date ?? '');
                            if (!$sync_result) {
                                ContractConsumerService::getInstance()->retry($quotation_one->quoted_price_list_sn, 2);
                            }
                        }
                    }

                }
                //更新待归档
                ArchiveAddService::getInstance()->one($contractId, $user);

            }
            $db->commit();
        }catch (ValidationException $e) {
            //校验错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        }catch (BusinessException $e) {                 //业务错误可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contractId
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($contractId, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $request = $this->getRequest($contractId);
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING){
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $contractId]
            ]);
            $result = (new WorkflowServiceV2())->doReject($request, $user,$this->getWorkflowParams($contract,$user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$BUSINESS_ERROR);
            }

            $bool = $contract->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'rejected_at' => $result->rejected_at,
                'refuse_reason' => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
            }
            //审批状态同步电子合同表

            if ($contract->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
                $contract_electronic = ContractElectronicModel::findFirst([
                    'conditions' => 'relate_id =:relate_id:',
                    'bind'       => ['relate_id' => $contractId]
                ]);
                if (empty($contract_electronic)) {
                    throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                }

                $contract_electronic->approve_status = Enums::CONTRACT_STATUS_REJECTED;
                if ($contract_electronic->save() === false) {
                    //电子合同审批状态更新失败了
                    throw new BusinessException('合同驳回电子合同更新状态失败：原因' . get_data_object_error_msg($contract_electronic) . '数据：relate_id' . $contractId, ErrCode::$CONTRACT_UPDATE_ERROR);
                }

            }

        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contractId
     * @param $note
     * @param $user
     * @return mixed|void
     */
    public function cancel($contractId, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $contract = Contract::findFirst([
                'conditions' => 'id = :id: AND create_id = :create_id: AND status=' . Enums::CONTRACT_STATUS_PENDING,
                'bind' => ['id' => $contractId, 'create_id' => $user['id']]
            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $contractId . '-' . $user['id']]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $request = $this->getRequest($contractId);
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING){
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($request, $user,$this->getWorkflowParams($contract,$user), $note);
            if ($result === false) {
                throw new BusinessException('审批流撤回失败', ErrCode::$BUSINESS_ERROR);
            }
            $bool = $contract->i_update([
                'status' => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'cancel_reason' => $note,
            ]);

            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
            }

            //审批状态同步电子合同表

            if ($contract->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
                $contract_electronic = ContractElectronicModel::findFirst([
                    'conditions' => 'relate_id =:relate_id:',
                    'bind'       => ['relate_id' => $contractId]
                ]);
                if (empty($contract_electronic)) {
                    throw new ValidationException(static::$t->_('contract_electronic_get_fail'), ErrCode::$VALIDATE_ERROR);
                }

                $contract_electronic->approve_status = Enums::CONTRACT_STATUS_CANCEL;
                if ($contract_electronic->save() === false) {
                    //电子合同审批状态更新失败了
                    throw new BusinessException('合同取消电子合同状态更新失败，原因：' . get_data_object_error_msg($contract_electronic) . '数据：id' . $contractId, ErrCode::$CONTRACT_UPDATE_ERROR);
                }

            }

        } catch (ValidationException $e) {
            //校验错误可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        }  catch (BusinessException $e) {                 //业务错误可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-withdrawal-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contractId
     * @return Model
     */
    public function getRequest($contractId)
    {
        $contract = Contract::findFirst($contractId);
        $bizType = $this->getBizType($contract);
        return $this->getRequestByBiz($contractId,$bizType);
    }

    /**
     * @param $contractId
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($contractId, $user)
    {
        $workflowServiceV2 = new WorkflowServiceV2();
        $contract = Contract::findFirst($contractId);
        $data['id'] = $contract->id;
        $data['name'] = $contract->cno . '审批申请';
        $data['biz_type'] = $this->getBizType($contract);
        $data['flow_id'] = $this->getContractFlowId($contract, $workflowServiceV2, $user);

        return $workflowServiceV2->createRequest($data, $user, $this->getWorkflowParams($contract, $user));
    }

    /**
     * 根据业务类型获取审批流ID
     * @param Model $model
     * @return int
     */
    public function getFlowId($model=null)
    {
        return Enums::WF_CONTRACT_TYPE32;  //24改成32
    }

    /**
     * 其他合同更新操作, 创建新审批流
     *
     * @param $current_biz_model
     * @param $old_biz_model
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit($current_biz_model, $old_biz_model, $user)
    {
        // 获取旧数据对应的的原审批流模型
        $old_biz_type = $this->getBizType($old_biz_model);
        $old_request_model = $this->getRequestByBiz($old_biz_model->id, $old_biz_type);
        if (empty($old_request_model)) {
            throw new BusinessException("没有找到原审批流, old_biz_type={$old_biz_type}; biz_id={$old_biz_model->id}", ErrCode::$BUSINESS_ERROR);
        }

        // 原审批流置为废弃状态
        $old_request_model->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        if ($old_request_model->save() === false) {
            throw new BusinessException('原审批流废弃状态更新失败, 原因可能是=' . get_data_object_error_msg($old_request_model) . '; 数据=' . json_encode($old_request_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 创建新的审批流
        return $this->createRequest($current_biz_model->id, $user);
    }

    /**
     * 获取业务类型
     * @param $model
     * @return int
     */
    public function getBizType($model)
    {
        // 标准主合同(销售)
        if (Enums::CONTRACT_TEMPLATE_SALES_STANDARD == $model->template_id && Enums::CONTRACT_IS_MASTER_YES == $model->is_master) {
            // 标准主合同定结
            if (Enums::PAY_METHOD_FIXED == $model->pay_method) {
                $bizType = Enums::WF_CONTRACT_TYPE20;
            } else {
                // 标准主合同现结
                $bizType = Enums::WF_CONTRACT_TYPE21;
            }

        // 非标准主合同(销售)
        } else if (Enums::CONTRACT_TEMPLATE_SALES_NOT_STANDARD == $model->template_id && Enums::CONTRACT_IS_MASTER_YES == $model->is_master) {
            $bizType = Enums::WF_CONTRACT_TYPE22;

        // 销售主合同
        } else if (Enums::CONTRACT_IS_MASTER_NO == $model->is_master && (new BaseService())->isSalesContract($model->template_id)) {
            // 授权内附属合同
            if (Enums::IN_SCOPE_YES == $model->in_scope) {
                $bizType = Enums::WF_CONTRACT_TYPE23;
            } else {
                // 授权外附属合同
                $bizType = Enums::WF_CONTRACT_TYPE24;
            }

        } else {
            $bizType = Enums::WF_CONTRACT_TYPE1;
        }

        return $bizType;
    }

    /**
     * @param $item
     * @param $user
     * @return array
     */
    private function getWorkflowParams($item, $user)
    {
        $staffInfo = (new UserService())->getUserByIdInRbi($item->create_id);
        $exchange_amount = EnumsService::getInstance()->amountExchangeRateCalculation($item->amount, $item->exchange_rate, 2);

        // 设置系统默认币种
        if (is_null(GlobalEnums::$sys_default_currency)) {
            GlobalEnums::setCurrency();
        }

        $lang = [//泰英走泰文审批流，中英走中文审批流，中泰走泰文审批流
            'en-th' => 'th',
            'en-zh' => 'zh-CN',
            'th-zh' => 'th'
        ];

        return [
            'lang' => $lang[$item->lang] ?? $item->lang,
            'amount'   => $exchange_amount, // 合同金额使用合同币种根据汇率转化成该国币种的金额
            'exchange_amount' => $exchange_amount,
            'template_id' => $item->template_id,
            'is_master' => $item->is_master,
            'pay_method' => $item->pay_method,
            'in_scope' => $item->in_scope,
            'currency' => GlobalEnums::$sys_default_currency,// 该国币种枚举值
            'submitter_id' => $staffInfo->staff_info_id,
            'department_id'=> $staffInfo->sys_department_id,
            'node_department_id'=> $staffInfo->node_department_id,
            'store_id' => $staffInfo->sys_store_id,
            'contract_storage_type' => $item->contract_storage_type // 合同介质: 1-纸质合同; 2-电子合同
        ];
    }

    /**
     * 查询合同的flow_id
     * @param Contract $contract
     * @param WorkflowServiceV2 $workflowServiceV2
     * @param $user
     * @return int
     * @throws BusinessException
     * @throws ValidationException
     * @date 2021/10/29
     * v10533需求  改为用费用所属公司判断审批流,作废申请人所属公司的判断
     */
    public function getContractFlowId(Contract $contract, WorkflowServiceV2 $workflowServiceV2, $user)
    {
        $flow_id = 0;
        $biz_type = $this->getBizType($contract);
        $flow_id = $workflowServiceV2->getFlowIdByBizType($biz_type) ?? $this->getFlowId();

        // 国家码
        $country_code = get_country_code();

        // 是否是销售类合同
        $is_sales_contract = (new BaseService())->isSalesContract($contract->template_id);

        //获取公司id
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        $deptId   = !empty($user['node_department_id']) ? $user['node_department_id'] : $user['sys_department_id'];
        if ($user['is_flash_home_operation']) {
            return Enums::WF_FLOW_ID_CONTRACT_FLASH_HOME;
        }

        if ($contract->company_code == ContractEnums::CONTRACT_COMPANY_FLASH_FULLFILMENT) {
            $flow_id = Enums::WF_CONTRACT_TYPE64;
        }

        // 泰国/菲律宾/马来/越南/印尼的Fcommerce走专属审批流 v15597
        if ($contract->company_code == ContractEnums::CONTRACT_COMPANY_F_COMMERCE && in_array($country_code, [
                GlobalEnums::TH_COUNTRY_CODE,
                GlobalEnums::PH_COUNTRY_CODE,
                GlobalEnums::MY_COUNTRY_CODE,
                GlobalEnums::VN_COUNTRY_CODE,
                GlobalEnums::ID_COUNTRY_CODE
            ])
        ) {
            return Enums::WF_FLOW_ID_CONTRACT_FCOMMERCE_COMPANY;
        }

        // 印尼和越南只走一条
        if (in_array($country_code, [GlobalEnums::VN_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE])) {
            return Enums::WF_CONTRACT_TYPE32;
        }

        //9777需求
        $department_info = (new DepartmentService())->getDepartmentDetail($deptId);
        // 1.泰国,马来, 销售合同 FlashFullfillment审批流81 FCommerce审批流82 ,v10065:增加菲律宾 ,v10457增加老挝ffm
        if ($is_sales_contract) {
            if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                if ($contract->company_code == ContractEnums::CONTRACT_COMPANY_FLASH_FULLFILMENT) {
                    $flow_id = Enums::WF_CONTRACT_TYPE81;

                } else if ($contract->company_code == ContractEnums::CONTRACT_COMPANY_F_COMMERCE) {
                    $flow_id = Enums::WF_CONTRACT_TYPE82;
                }

            } else if ($country_code == GlobalEnums::LA_COUNTRY_CODE) {
                if ($contract->company_code == ContractEnums::CONTRACT_COMPANY_FLASH_FULLFILMENT) {
                    $flow_id = Enums::WF_CONTRACT_TYPE81;
                }
            }
        }

        // 2.泰国 非销售合同FlashFullfillment,FlashMoney,FlashPay,FCommerce 审批流64 , 马来非销售合同 FlashFullfillment 审批流64(上方逻辑($is_ffm代码块)已满足,这里不再重复写)
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && !$is_sales_contract) {
            $contract_company = in_array($contract->company_code, [
                ContractEnums::CONTRACT_COMPANY_FLASH_FULLFILMENT,
                ContractEnums::CONTRACT_COMPANY_FLASH_MONEY,
                ContractEnums::CONTRACT_COMPANY_FLASH_PAY,
                ContractEnums::CONTRACT_COMPANY_F_COMMERCE
            ]);

            if ($contract_company) {
                $flow_id = Enums::WF_CONTRACT_TYPE64;
            }
        }

        //v10239需求 销售合同-申请人所属公司或合同所属公司=Flash Supply Chain Management  或 合同所属公司=Flash Supply Chain Management 走单独审批流
        if (
            (isset($company_ids['FlashSupplyChainManagement']) && $department_info['company_id'] == $company_ids['FlashSupplyChainManagement'])
            ||
            $contract->company_code == ContractEnums::CONTRACT_COMPANY_FLASH_SUPPLY_CHAIN_MANAGEMENT
        ) {
            if (!$is_sales_contract) {
                throw new ValidationException(static::$t->_('not_found_spc_flow_id'), ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }

            $flow_id = $this->getFlashSupplyChainManagementFlow($contract);
        }
        if (isset($user['is_pmd_department']) && $user['is_pmd_department'] && !$is_sales_contract && GlobalEnums::MY_COUNTRY_CODE == get_country_code()) {
            throw new ValidationException(static::$t->_('not_found_spc_flow_id'), ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
        }
        // PMD 部门的销售合同审批流: PH/MY/TH
        if (isset($user['is_pmd_department']) && $user['is_pmd_department'] && $is_sales_contract) {
            // 三国通用: 标准 + 主 + 现结
            if ($contract->template_id == Enums::WF_CONTRACT_TYPE21 && $contract->is_master == Enums::CONTRACT_IS_MASTER_YES && $contract->pay_method == Enums::PAY_METHOD_NOT_FIXED) {
                $flow_id = Enums::WF_CONTRACT_TYPE96;

            } else {
                switch ($country_code) {
                    // 泰国: 根据合同主从属性 / 授权范围细分
                    case GlobalEnums::TH_COUNTRY_CODE:
                    case GlobalEnums::MY_COUNTRY_CODE:
                    if ($contract->template_id == Enums::WF_CONTRACT_TYPE21 && $contract->is_master == Enums::CONTRACT_IS_MASTER_YES && $contract->pay_method == Enums::PAY_METHOD_FIXED) {
                            // 标准 - 主 - 定结
                            $flow_id = Enums::WF_SALE_CONTRACT_PMD_TYPE242;

                        } else if ($contract->template_id == Enums::WF_CONTRACT_TYPE22 && $contract->is_master == Enums::CONTRACT_IS_MASTER_YES) {
                            // 非标准 - 主
                            $flow_id = Enums::WF_SALE_CONTRACT_PMD_TYPE243;

                        } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_NO && $contract->in_scope == Enums::IN_SCOPE_YES) {
                            // 附属 - 授权范围内
                            $flow_id = Enums::WF_SALE_CONTRACT_PMD_TYPE244;

                        } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_NO && $contract->in_scope == Enums::IN_SCOPE_NO) {
                            // 附属 - 非授权范围内
                            $flow_id = Enums::WF_SALE_CONTRACT_PMD_TYPE245;
                        }

                        break;

                        // 马来/菲律宾: 其他组合的审批流
                    case GlobalEnums::PH_COUNTRY_CODE:
                        $flow_id = Enums::WF_CONTRACT_TYPE97;
                        break;
                }
            }
        }

        //v14472需求, 一级部门 = retail management 走单独审批流
        //13062需求: 申请人部门=Bulky Business Development,且申请销售合同时走特定审批流
        $sales_not_find = false;
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && $is_sales_contract) {
            $setting_department = EnvModel::getEnvByCode('sys_department_ids', []);
            if (!empty($setting_department)) {
                $setting_department = json_decode($setting_department, true);
            }

            // retail management 部门的销售合同审批流
            if (isset($setting_department['retail_management']) && $user['sys_department_id'] == $setting_department['retail_management']) {
                if ($contract->is_master == Enums::CONTRACT_IS_MASTER_YES && $contract->pay_method == Enums::PAY_METHOD_FIXED) {
                    // 主合同 - 定结
                    $flow_id = Enums::WF_SALE_CONTRACT_RETAIL_TYPE246;

                } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_NO && $contract->in_scope == Enums::IN_SCOPE_YES) {
                    // 附属合同 - 授权范围内
                    $flow_id = Enums::WF_SALE_CONTRACT_RETAIL_TYPE247;

                } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_NO && $contract->in_scope == Enums::IN_SCOPE_NO) {
                    // 附属合同 - 非授权范围内
                    $flow_id = Enums::WF_SALE_CONTRACT_RETAIL_TYPE248;

                } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_YES && $contract->pay_method == Enums::PAY_METHOD_NOT_FIXED) {
                    // 主合同 - 现结
                    throw new ValidationException(static::$t->_('not_found_spc_flow_id'), ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);

                } else {
                    $sales_not_find = true;
                }
            }

            if (isset($setting_department['bulky_business']) && $deptId == $setting_department['bulky_business']) {
                if (in_array($biz_type, [Enums::WF_CONTRACT_TYPE20, Enums::WF_CONTRACT_TYPE22, Enums::WF_CONTRACT_TYPE23, Enums::WF_CONTRACT_TYPE24])) {
                    $flow_id = Enums::WF_CONTRACT_TYPE133;

                } else if ( $biz_type == Enums::WF_CONTRACT_TYPE21) {
                    $flow_id = Enums::WF_CONTRACT_TYPE134;
                }
            }

            if (isset($setting_department['jvb_operations']) && $deptId == $setting_department['jvb_operations']) {
                if (in_array($biz_type, [Enums::WF_CONTRACT_TYPE20, Enums::WF_CONTRACT_TYPE22, Enums::WF_CONTRACT_TYPE23, Enums::WF_CONTRACT_TYPE24])) {
                    $flow_id = Enums::WF_CONTRACT_JVB_409;

                } else if ( $biz_type == Enums::WF_CONTRACT_TYPE21) {
                    $flow_id = Enums::WF_CONTRACT_JVB_408;
                }
            }
        }

        //v13062如果与历史需求重合，优先走历史需求。但是按照历史需求，如果找到审批流id59、60、61、62、63，则报错“找不到对应审批流”
        $default_flow = [Enums::WF_CONTRACT_TYPE59, Enums::WF_CONTRACT_TYPE60, Enums::WF_CONTRACT_TYPE61, Enums::WF_CONTRACT_TYPE62, Enums::WF_CONTRACT_TYPE63];
        if ($sales_not_find && (empty($flow_id) || in_array($flow_id,$default_flow))) {
            throw new ValidationException(static::$t->_('not_found_sales_flow_id'), ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
        }

        return $flow_id;
    }

    /**
     * @param $contract
     * @return int
     */
    public function getFlashSupplyChainManagementFlow($contract)
    {
        $this->logger->info('contract-spc-flow-choose: contract_info = ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE));

        //1.确定合同属性
        // (1) 合同类型
        // 1001.标准合同(主合同,template_id=21)
        // 1002.非标准合同(主合同,template_id=22)
        // 1003.补充协议(附属合同)
        $contract_type = 0;
        if ($contract->is_master == Enums::CONTRACT_IS_MASTER_YES) {
            if ($contract->template_id == Enums::CONTRACT_TEMPLATE_SALES_STANDARD) {
                $contract_type = 1001;
            } else if ($contract->template_id == Enums::CONTRACT_TEMPLATE_SALES_NOT_STANDARD) {
                $contract_type = 1002;
            }

        } else if ($contract->is_master == Enums::CONTRACT_IS_MASTER_NO) {
            $contract_type = 1003;
        }

        // (2) 计价方式(文档中的计算类型)
        // 2001.成本加成
        // 2002.标准折扣
        $valuation_type = 0;
        if ($contract->valuation_type == Enums::CONTRACT_VALUATION_TYPE_COST) {
            $valuation_type = 2001;
        } else if ($contract->valuation_type == Enums::CONTRACT_VALUATION_TYPE_DISCOUNT) {
            $valuation_type = 2002;
        }

        // (3) 结算类型
        // 3001.定结-30天以内
        // 3002.定结-30天以上
        // 3003.现结
        // 3004.预充值
        // 3005.特殊条款
        // 结算方式  1.定结 0.现结 3.预充值 4.特殊条款  结算天数 1:30天(含)以内 2:30天(含)以内
        $pay_type = 0;
        if ($contract->pay_method == Enums::PAY_METHOD_FIXED && $contract->balance_days == Enums::CONTRACT_BALANCE_DAYS_30) {
            $pay_type = 3001;
        } else if ($contract->pay_method == Enums::PAY_METHOD_FIXED && $contract->balance_days == Enums::CONTRACT_BALANCE_DAYS_30_ABOVE) {
            $pay_type = 3002;
        } else if ($contract->pay_method == Enums::PAY_METHOD_NOT_FIXED ) {
            $pay_type = 3003;
        } else if ($contract->pay_method == Enums::PAY_METHOD_PRELOAD) {
            $pay_type = 3004;
        } else if ($contract->pay_method == Enums::PAY_METHOD_SPECIAL) {
            $pay_type = 3005;
        }

        // (4) 折扣比例
        $discount = (string)$contract->discount;
        //2.执行判断 (条件太多,写else逻辑太混乱,这里用优先级方式,命中目标则返回)
        /**
         * 详情见需求中的表格 【10239】: 4.4-03
         * 补充信息: 审批流判断优先级：1. 折扣比例大于20%，则不区分结算类型（标准销售合同和补充协议的）；2.除折扣比例20%以外的，或时，满足结算类型即执行审批流；且时，需要都满足才执行审批流 标准销售合同（主合同）且标准价折扣的特殊条款，直接走CEO的审批流
         */
        //标准合同-标准折扣  折扣>20%
        if ($contract_type == 1001 && $valuation_type == 2002 && bccomp($discount,'20',2) == 1) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //标准合同-标准折扣-定结30天以内
        if ($contract_type == 1001 && $valuation_type == 2002 && $pay_type == 3001) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //标准合同-标准折扣-定结30天以上
        if ($contract_type == 1001 && $valuation_type == 2002 && $pay_type == 3002) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //标准合同-标准折扣-现结/预充值
        if ($contract_type == 1001 && $valuation_type == 2002 && in_array($pay_type, [3003, 3004])) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //标准合同-标准折扣-特殊条款
        //if ($contract_type==1001 && $valuation_type==2002 && $pay_type==3005 ){
        //    return Enums::WF_CONTRACT_TYPE87;
        //}

        //标准合同-成本加成-(定结30天以内 且 折扣比例<5%)
        if ($contract_type == 1001 && $valuation_type == 2001 && ($pay_type == 3001 && bccomp($discount,'5',2) == -1)) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //标准合同-成本加成-(定结30天以内 且 折扣比例>=5%)
        if ($contract_type == 1001 && $valuation_type == 2001 && ($pay_type == 3001 && bccomp($discount,'5',2) != -1)) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //标准合同-成本加成-定结30天以上
        if ($contract_type == 1001 && $valuation_type == 2001 && $pay_type == 3002) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //标准合同-成本加成-特殊条款
        if ($contract_type == 1001 && $valuation_type == 2001 && $pay_type == 3005) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //标准合同-成本加成-现结/预充值
        if ($contract_type == 1001 && $valuation_type == 2001 && in_array($pay_type, [3003, 3004])) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //补充协议-标准折扣-折扣>20%
        if ($contract_type == 1003 && $valuation_type == 2002 && bccomp($discount,'20',2) == 1) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //补充协议-标准折扣-定结30天以内
        if ($contract_type == 1003 && $valuation_type == 2002 && $pay_type == 3001) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //补充协议-标准折扣-定结30天以上
        if ($contract_type == 1003 && $valuation_type == 2002 && $pay_type == 3002) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //补充协议-成本加成-折扣<5%
        if ($contract_type == 1003 && $valuation_type == 2001 && bccomp($discount,'5',2) == -1) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //补充协议-成本加成-定结30天以内
        if ($contract_type == 1003 && $valuation_type == 2001 && $pay_type == 3001) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //补充协议-成本加成-定结30天以上
        if ($contract_type == 1003 && $valuation_type == 2001 && $pay_type == 3002) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //补充协议-成本加成-现结/预充值
        if ($contract_type == 1003 && $valuation_type == 2001 && in_array($pay_type, [3003, 3004])) {
            return Enums::WF_CONTRACT_TYPE86;
        }

        //非标准-标准折扣
        if ($contract_type == 1002 && $valuation_type == 2002) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        //非标准-成本加成
        if ($contract_type == 1002 && $valuation_type == 2001) {
            return Enums::WF_CONTRACT_TYPE87;
        }

        return Enums::WF_CONTRACT_TYPE87;
    }

    /***
     * @Desc:其他合同——合同申请——列表查询
     * @param $builder
     * @param $biz_type
     * @param $biz_table_alias
     * @return mixed
     * @author: W_uniQue
     * @Time: 2022/11/3 20:42
     */
    public function getBizWorkflowOrderList($builder,$biz_type,$biz_table_alias)
    {
        $builder->leftjoin(WorkflowRequestModel::class, "request.biz_value = {$biz_table_alias}.id", 'request');
        $builder->leftjoin(WorkflowAuditLogModel::class, "request.id = log.request_id", 'log');
        $builder->andWhere('request.biz_type IN ({biz_type:array}) AND request.is_abandon = :is_abandon:', [
            'biz_type' => $biz_type,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);
        return $builder;
    }
}
