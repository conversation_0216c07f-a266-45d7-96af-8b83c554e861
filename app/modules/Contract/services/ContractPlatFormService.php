<?php
/**
 * Created by PhpStorm.
 * Date: 2023/4/13
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractPlatFormModel;

use App\Modules\Common\Models\ContractCompanyModel;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use Mpdf\MpdfException;


class ContractPlatFormService extends BaseService
{

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $validate_contract = [
        'cno'                  => 'Required|StrLenGeLe:1,50',  // 合同编号
        'cname'                => 'Required|StrLenGeLe:1,100',  // 合同名称
        'lang'                 => 'Required|StrLenGeLe:1,10',  // 合同语言
        'relate_master_cno'    => 'IfIntEq:contract_type,2|Required|StrLenGeLe:1,50',  // 关联主合同编号
        'payment_currency'     => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'sign_company_id'      => 'Required|Int',
        'platform_customer_id' => 'Required|IntIn:1,2,3,99',
        'contract_type'        => 'Required|IntIn:1,2',
        'remark'               => 'Required|StrLenGeLe:1,1000',//备注

        'contract_file_arr'                => 'Required|ArrLen:1',
        'contract_file_arr[*].file_name'   => 'Required|StrLenGeLe:2,300',
        'contract_file_arr[*].bucket_name' => 'Required|StrLenGeLe:2,300',
        'contract_file_arr[*].object_key'  => 'Required|StrLenGeLe:2,300',
        
        'attachment_arr'                   => 'ArrLenGeLe:0,50',
        'attachment_arr[*].file_name'      => "Required|StrLenGeLe:2,300",
        'attachment_arr[*].bucket_name'    => "Required|StrLenGeLe:2,300",
        'attachment_arr[*].object_key'     => "Required|StrLenGeLe:2,300",
        'other_customer_name'              => 'IfIntEq:platform_customer_id,99|Required|StrLenGeLe:1,100',

        'effected_date' => 'Required|date', // 生效时间
        'finished_date' => 'Required|date',      // 到期时间


    ];

    public static $not_must_params = [
        'cno',
        'cname',
        'created_at_start',
        'created_at_end',
        'status'
    ];

    public static $validate_list_search = [
        'pageSize'         => 'Required|IntGt:0',                  //每页条数
        'pageNum'          => 'Required|IntGt:0',                   //页码
        'created_at_start' => 'DateTime',                  //合同申请开始时间
        'created_at_end'   => 'DateTime',                    //合同申请结束时间
        'status'           => 'IntIn:1,2,3,4',                       //合同状态
        'is_reply'         => 'IntIn:0,1',
    ];

    /**
     * 更新附件兼容
     * @param array $param 请求参数组
     * @throws ValidationException
     */
    public static function validateAttachments($param)
    {
        $validate_contract = array_merge(self::$validate_contract, self::$validate_update);
        unset($validate_contract['contract_file_arr[*].bucket_name'], $validate_contract['contract_file_arr[*].object_key'], $validate_contract['attachment_arr[*].bucket_name'], $validate_contract['attachment_arr[*].object_key']);

        Validation::validate($param, $validate_contract);

        //上传合同
        if (!empty($param['contract_file_arr'])) {
            foreach ($param['contract_file_arr'] as $item) {
                if (empty($item['id']) && (empty($item['bucket_name']) || empty($item['object_key']))) {
                    throw new ValidationException(static::$t->_('contract_gpmd_edit_params_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        //上传附件
        if (!empty($param['attachment_arr'])) {
            foreach ($param['attachment_arr'] as $item) {
                if (empty($item['id']) && (empty($item['bucket_name']) || empty($item['object_key']))) {
                    throw new ValidationException(static::$t->_('contract_gpmd_edit_params_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
    }

    public function getInit()
    {

        return [
            'cno' => BaseService::genSerialNo('GP', RedisKey::CONTRACT_CREATE_COUNTER),
        ];
    }

    public function add($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = ContractPlatFormModel::findFirst([
                'conditions' => 'cno = :cno:',
                'bind'       => ['cno' => $data['cno']],
                'columns'    => 'id'
            ]);

            // 合同已存在
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('contract_number_has_been_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $data = $this->handleData($data, $user);

            $model = new ContractPlatFormModel();
            $bool  = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('gpmd合同创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // 合同相关附件/文件
            $attach_arr = [];

            //兼容复制新增场景
            $attachment_ids = array_merge(array_column($data['contract_file_arr'], 'id'), array_column($data['attachment_arr'],'id'));
            if ($attachment_ids) {
                $attachment_list = AttachModel::find([
                    'conditions' => 'id in({attachment_ids:array})',
                    'bind'       => ['attachment_ids' => $attachment_ids]
                ])->toArray();
                foreach ($attachment_list as $v) {
                    $tmp                    = [];
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['oss_bucket_type'] = $v['oss_bucket_type'];
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attach_arr[]           = $tmp;
                }
            }

            //合同文件
            if (!empty($data['contract_file_arr'])) {
                foreach ($data['contract_file_arr'] as $k => $v) {
                    //有附件id直接跳过
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp                    = [];
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_FILE;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attach_arr[]           = $tmp;
                }
            }

            //附件
            if (!empty($data['attachment_arr'])) {
                foreach ($data['attachment_arr'] as $k => $v) {
                    //有附件id直接跳过
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp                    = [];
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_ATTACHMENT;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attach_arr[]           = $tmp;
                }
            }
            if (!empty($attach_arr)) {
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attach_arr);
                if ($attach_bool === false) {
                    throw new BusinessException('合同附件创建失败, 原因可能是: ' . get_data_object_error_msg($attach) . '; 数据: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            $flow_bool = (new ContractPlatFormFlowService ())->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException('gpmd合同审批流生成失败, biz_id = ' . $model->id, ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('platform-contract-create-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];

    }

    public function edit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = ContractPlatFormModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            // 合同不存在
            if (empty($exists)) {
                throw new ValidationException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$VALIDATE_ERROR);
            }


            // 编辑更新, 仅能更新自己申请的合同
            $contract = ContractPlatFormModel::findFirst([
                'conditions' => 'id = :id: AND create_id = :create_id:',
                'bind'       => ['id' => $data['id'], 'create_id' => $user['id']]
            ]);
            $request  = (new ContractPlatFormFlowService ())->getRequest($data['id']);

            $can_edit = (new WorkflowServiceV2())->isPendingApproval($request);

            if (empty($contract) || empty($contract->cno) || !$can_edit) {
                throw new ValidationException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $data = $this->handleData($data, $user);
            unset($data['cno']);

            $bool = $exists->save($data);
            if ($bool === false) {
                throw new BusinessException('gpmd合同创建失败, 原因可能是: ' . get_data_object_error_msg($exists) . '; 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //删除历史附件
            $attachment_ids = array_merge(array_column($data['contract_file_arr'], 'id'), array_column($data['attachment_arr'],'id'));
            $conditions = 'oss_bucket_key = :key: and oss_bucket_type in ({oss_bucket_type:array})';
            $bind = ['key' => $exists->id, 'oss_bucket_type' => [Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_FILE, Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_ATTACHMENT]];
            if (!empty($attachment_ids)) {
                //没有历史附件需要保留，直接删除所有;有历史附件需要保留，则只需删除非保留的附件即可
                $conditions .= ' and id not in({attachment_ids:array})';
                $bind['attachment_ids'] = $attachment_ids;
            }
            $bool = AttachModel::find([
                'conditions' => $conditions,
                'bind'       => $bind
            ])->delete();
            if ($bool === false) {
                throw new BusinessException('platform_attachment_delete_failed_' . $data['id'], ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            // 合同相关附件/文件
            $attach_arr = [];

            //合同文件
            if (!empty($data['contract_file_arr'])) {
                foreach ($data['contract_file_arr'] as $k => $v) {
                    //有附件id说明是原有的无需处理
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp                    = [];
                    $tmp['oss_bucket_key']  = $exists->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_FILE;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attach_arr[]           = $tmp;
                }
            }

            //附件
            if (!empty($data['attachment_arr'])) {
                foreach ($data['attachment_arr'] as $k => $v) {
                    //有附件id说明是原有的无需处理
                    if (!empty($v['id'])) {
                        continue;
                    }
                    $tmp                    = [];
                    $tmp['oss_bucket_key']  = $exists->id;
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_ATTACHMENT;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attach_arr[]           = $tmp;
                }
            }
            if (!empty($attach_arr)) {
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attach_arr);
                if ($attach_bool === false) {
                    throw new BusinessException('合同附件创建失败, 原因可能是: ' . get_data_object_error_msg($attach) . '; 数据: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            $flow_bool = (new ContractPlatFormFlowService ())->recommit($exists, $user);
            if ($flow_bool === false) {
                throw new BusinessException('gpmd合同审批流生成失败, biz_id = ' . $exists->id, ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('platform-contract-update-failed:' . $real_message);
        }


        return [
            'code'    => $code,
            'message' => $message
        ];

    }


    /**
     * @param $data
     * @param $user
     * @return array
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        //补充协议校验关联合同号
        if ($data['contract_type'] == ContractEnums::PLATFORM_CONTRACT_TYPE_2) {
            $relate_contract = ContractPlatFormModel::findFirst([
                'conditions' => 'cno = :cno: and status = :status:',
                'bind'       => ['cno' => $data['relate_master_cno'], 'status' => Enums::CONTRACT_STATUS_APPROVAL]
            ]);

            if (empty($relate_contract)) {
                throw new ValidationException(static::$t->_('master_cno_has_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
        }
        $insert_data['cno']                    = $data['cno'];
        $insert_data['status']                 = Enums::CONTRACT_STATUS_PENDING;
        $insert_data['cname']                  = $data['cname'];
        $insert_data['relate_master_cno']      = $data['relate_master_cno'];
        $insert_data['payment_currency']       = $data['payment_currency'];
        $insert_data['lang']                   = $data['lang'];
        $insert_data['sign_company_id']        = $data['sign_company_id'];
        $insert_data['platform_customer_id']   = $data['platform_customer_id'];
        $insert_data['other_customer_name']    = $data['other_customer_name'];
        $insert_data['contract_type']          = $data['contract_type'];
        $insert_data['other_customer_name']    = $data['other_customer_name'];
        $insert_data['effected_date']          = $data['effected_date'];
        $insert_data['finished_date']          = $data['finished_date'];
        $insert_data['created_at']             = date('Y-m-d H:i:s');
        $insert_data['create_id']              = $user['id'] ?? 0;
        $insert_data['create_name']            = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
        $insert_data['create_department_id']   = $user['department_id'];
        $insert_data['create_department_name'] = $user['department'];
        $insert_data['contract_file_arr']      = $data['contract_file_arr'];
        $insert_data['attachment_arr']         = $data['attachment_arr'];
        $insert_data['remark']                 = $data['remark'];

        if ($data['contract_type'] == ContractEnums::PLATFORM_CONTRACT_TYPE_1) {
            $data['relate_master_cno'] = '';
        }
        return $insert_data;
    }

    /**
     * @param $condition
     * @param $uid
     * @param int $type
     * @return array
     */
    public function getList($condition, $uid, $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size        = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num         = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset           = $page_size * ($page_num - 1);

        $items = [];
        $count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => ContractPlatFormModel::class]);
            $builder = $this->getCondition($builder, $condition, $type);

            if (in_array($type, [self::LIST_TYPE_FYR])) {
                $count_sql = 'COUNT(c.id) AS total';
            } else {
                $count_sql = 'COUNT(DISTINCT(c.id)) AS total';
            }

            $count = (int)$builder->columns($count_sql)->getQuery()->getSingleResult()->total;
            $items = [];
            if ($count) {
                $columns = 'c.id,c.cno,c.cname,c.status,c.payment_currency,c.create_id,c.create_name,c.created_at,c.contract_type';

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns .= ',log.audit_at';
                } else if ($type == self::LIST_TYPE_APPLY) {
                    $columns .= ',request.state,count(1) as log_num';
                } else if ($type == self::LIST_TYPE_FYR) {
                    // 回复列表:返回征询ID，回复详情接口用到
                    $columns .= ',reply.fyr_id';
                }

                $builder->columns($columns);

                if (!in_array($type, [self::LIST_TYPE_FYR])) {
                    $builder->groupBy('c.id');
                }
                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('c.id desc');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $type);
            }
        } catch (\Exception $e) {
            $this->logger->warning('GPMD集团平台合同列表获取失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ]
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type = 0)
    {
        $cno              = $condition['cno'] ?? '';
        $c_name           = $condition['cname'] ?? '';
        $created_at_start = $condition['created_at_start'] ?? '';
        $created_at_end   = $condition['created_at_end'] ?? '';
        $status           = $condition['status'] ?? 0;

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;


        if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('c.create_id = :uid:', ['uid' => $condition['uid']]);
            $builder = (new ContractPlatFormFlowService())->getBizWorkflowOrderList($builder, Enums::WF_CONTRACT_GPMD_BIZ_TYPE, 'c');

        } else if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_CONTRACT_GPMD_BIZ_TYPE], $condition['uid'], 'c');

        } else if ($type == self::LIST_TYPE_FYR) {
            $biz_table_info = ['table_alias' => 'c'];
            $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_CONTRACT_GPMD_BIZ_TYPE], $condition['uid'], $biz_table_info);

        }
        if (!empty($cno)) {
            $builder->andWhere('c.cno = :cno:', ['cno' => $cno]);
        }
        if (!empty($status)) {
            $builder->andWhere('c.status = :status:', ['status' => $status]);
        }

        if (!empty($created_at_start)) {
            $builder->andWhere('c.created_at >= :created_at_start:', ['created_at_start' => $created_at_start]);
        }

        if (!empty($created_at_end)) {
            $builder->andWhere('c.created_at < :created_at_end:', ['created_at_end' => $created_at_end]);
        }

        if (!empty($c_name)) {
            $builder->andWhere('c.cname LIKE :cname:', ['cname' => $c_name . '%']);
        }

        return $builder;
    }

    public function getDetail($params, $uid = 0, $module_type = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $id      = $params['id'];
        $fyr_id  = $params['fyr_id'] ?? 0;

        try {
            $conditions = "id = :id:";
            $bind       = ['id' => $id];
            if ($module_type == ContractEnums::MODULE_TYPE_APPLY) {
                $conditions        .= ' AND create_id = :create_id:';
                $bind['create_id'] = $uid;
            }
            $contract = ContractPlatFormModel::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }


            $file                          = $contract->getFile(['columns' => 'id,file_name']);
            $attach                        = $contract->getAttach(['columns' => 'id,file_name']);
            $contract                      = $contract->toArray();
            $contract['contract_file_arr'] = [];
            $contract['attachment_arr']    = [];

            if (!empty($file)) {
                $contract['contract_file_arr'] = $file->toArray();
            }
            if (!empty($attach)) {
                $contract['attachment_arr'] = $attach->toArray();
            }

            $contract_req = (new ContractPlatFormFlowService())->getRequest($contract['id']);
            if (empty($contract_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            //待回复征询ID
            $auth_logs             = (new WorkflowServiceV2())->getAuditLogs($contract_req, true);
            $contract['auth_logs'] = $auth_logs;

            // 新逻辑
            if ($fyr_id) {
                $fyr_info = FYRService::getInstance()->getFyrInfoById($contract_req->id, $fyr_id, $uid);
                if (!empty($fyr_info)) {
                    $contract['fyr_info'] = $fyr_info;
                    $contract['ask_id']   = $fyr_info['ask_id'];
                }

            } else {
                // 历史逻辑
                $ask                = FYRService::getInstance()->getRequestToByReplyAsk($contract_req, $uid);
                $contract['ask_id'] = $ask ? $ask->id : '0';
            }


            //归档信息
            if (in_array($module_type, [ContractEnums::MODULE_TYPE_DATA, ContractEnums::MODULE_TYPE_ARCHIVE]) && $contract['status'] == Enums::CONTRACT_STATUS_APPROVAL) {
                $archive = ContractArchive::findFirst([
                    'conditions' => 'cno = :cno:',
                    'bind'       => ['cno' => $contract['cno']]
                ]);

                if (empty($archive)) {
                    throw new ValidationException(static::$t->_('contract_archive_data_not_exist', ['cno' => $contract['cno'] . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
                }
                $archive_file                                = $archive->getfile(['columns' => 'id,file_name']);
                $contract['archive_detail']                  = [];
                $contract['archive_detail']['id']            = $archive->id;
                $contract['archive_detail']['holder_name']   = $archive->holder_name ?? '';
                $contract['archive_detail']['contract_file'] = !empty($archive_file) ? $archive_file->toArray() : [];
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract ?? []
        ];
    }

    /**
     * 快速复制回显
     * @param $params
     * @param int $uid
     * @return array
     */
    public function editDetail($params, $uid = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $id      = $params['id'];

        try {

            $contract = ContractPlatFormModel::findFirst([
                'conditions' => 'id = :id: and create_id = :create_id:',
                'bind'       => ['id' => $id, 'create_id' => $uid]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            if ($contract->status != Enums::CONTRACT_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('contract_status_not_reject', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }


            $file   = $contract->getFile(['columns' => 'id,file_name']);
            $attach = $contract->getAttach(['columns' => 'id,file_name']);
            $contract        = $contract->toArray();
            $contract['cno'] = BaseService::genSerialNo('GP', RedisKey::CONTRACT_CREATE_COUNTER);
            unset($contract['id']);
            $contract['contract_file_arr'] = [];
            $contract['attachment_arr']    = [];

            if (!empty($file)) {
                $contract['contract_file_arr'] = $file->toArray();
            }
            if (!empty($attach)) {
                $contract['attachment_arr'] = $attach->toArray();
            }


        } catch (ValidationException $e) {
            $code     = $e->getCode();
            $message  = $e->getMessage();
            $contract = [];
        }


        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract ?? []
        ];
    }



    public function handleItems($items, $type)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        $platform_contract_type = ContractEnums:: $platform_contract_type;
        $contract_status        = Enums::$contract_status;
        foreach ($items as &$item) {
            $status                     = $contract_status[$item['status']] ?? '';
            $item['status_text']        = static::$t->_($status);
            $item['contract_type_text'] = static::$t->_($platform_contract_type[$item['contract_type']] ?? '');

            if ($type == BaseService::LIST_TYPE_APPLY) {
                $item['can_cancel'] = $item['state'] == Enums::WF_STATE_PENDING && $item['log_num'] <= 2 ? 1 : 2;//1 可撤回 0不可撤回
                $item['can_edit']   = $item['state'] == Enums::WF_STATE_PENDING && $item['log_num'] == 1 ? GlobalEnums::IS_CAN_EDIT : GlobalEnums::IS_NOT_EDIT;
                $item['can_copy'] = ($item['status'] == Enums::CONTRACT_STATUS_REJECTED && $item['created_at'] > '2023-09-01 00:00:00') ? 1 : 2;
            }
            $item['can_download'] = Enums::CONTRACT_STATUS_APPROVAL == $item['status'] ? GlobalEnums::IS_CAN_DOWNLOAD : GlobalEnums::IS_NOT_DOWNLOAD;
        }
        return $items;

    }


    /**
     * @param $condition
     * @return array
     */
    public function getArchiveList($condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $builder   = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder = $this->getArchiveCondition($builder, $condition);
        $count   = (int)$builder->columns('COUNT(ca.id) AS total')->getQuery()->getSingleResult()->total;
        $items   = [];
        if ($count) {
            $columns = [
                'ca.id',
                'ca.filing_at',
                'ca.cno',
                'ca.cname',
                'ca.approved_at',
                'ca.status as archive_status',
                'ca.create_name',
                'ca.filing_name',
                'ca.holder_name',
                'ca.contract_file',
                'ca.is_master',
            ];
            $builder->orderBy('ca.approved_at desc');

            $builder->columns($columns)->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleArchiveItems($items);
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ]
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @return mixed
     */
    private function getArchiveCondition($builder, $condition)
    {
        $cno    = $condition['cno'] ?? '';
        $c_name = $condition['cname'] ?? '';

        $status = $condition['archive_status'] ?? 0;
        $builder->andWhere('ca.contract_type = :contract_type:', ['contract_type' => ContractEnums::CONTRACT_TYPE_PLATFORM]);

        if (!empty($cno)) {
            $builder->andWhere('ca.cno = :cno:', ['cno' => $cno]);
        }

        if (!empty($status)) {
            $builder->andWhere('ca.status = :status:', ['status' => $status]);
        }
        if (!empty($c_name)) {
            $builder->andWhere('ca.cname LIKE :cname:', ['cname' => $c_name . '%']);
        }

        return $builder;
    }

    /**
     * 申请-下载
     * @param $id
     * @param $user
     * @return array
     * @throws GuzzleException
     */
    public function download($id, $user)
    {
        $real_message = '';
        try {
            $exist = WorkflowRequestModel::findFirst([
                'conditions' => "biz_type = :biz_type: and biz_value = :id: and FIND_IN_SET(:uid:,viewer_ids)",
                'bind'       => ['id' => $id, 'uid' => $user['id'], 'biz_type' => Enums::WF_CONTRACT_GPMD_BIZ_TYPE]
            ]);
            if (empty($exist)) {
                throw new ValidationException(static::$t->_('no_permission_to_download'), ErrCode::$CONTRACT_GET_INFO_NO_AUTH_ERROR);
            }

            $contract = ContractPlatFormModel::findFirst([
                'conditions' => 'id = :id: and status = ' . Enums::CONTRACT_STATUS_APPROVAL,
                'bind'       => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_file_not_exist'), ErrCode::$CONTRACT_GET_NO_CONTRACT_FILE_ERROR);
            }

            $file      = $contract->getFile(["columns" => "bucket_name,object_key,file_name"]);
            $file_info = $file->toArray() ?? [];
            // 判断合同是否即合同的归档状态为待归档，已归档，待上传盖章合同，已作废，待作废，已终止，待终止其中的一种
            $archive = ContractArchive::findFirst([
                'conditions' => 'cno = :cno:',
                'bind'       => ['cno' => $contract->cno]
            ]);
            $archive = isset($archive->id) ? $archive->toArray() : [];
            if (empty($archive)) {
                // 归档处理
                $this->saveArchive($id, $user);
            }

            // pdf 下载
            if (empty($file_info)) {
                throw new ValidationException(static::$t->_('contract_file_not_exist'), ErrCode::$CONTRACT_GET_NO_CONTRACT_FILE_ERROR);
            }

            // 下载 V22074 由于附件私有化，所以这里需要私有化读取，然后再加水印
            $path = OssHelper::downloadFileHcm($file_info[0]['object_key'], 600);
            $path = $path['file_url'] ?? '';
//            $path = $this->getShowPath($file_info);
            $file_name = !empty($file_info[0]['file_name']) ? $file_info[0]['file_name'] : 'contract_platform_' . date('ymdHis') . '.pdf';

            // pdf 加水印 V22074 私有化的水印附件需要私有化解密链接返回给用户
            $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
            if (!empty($water_pdf_info['object_key'])) {
                $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
                $download_url = $result['file_url'];
            } else {
                $download_url = $path;
            }

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出

            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('gpmd 合同下载失败' . $real_message);
        }

        return [
            'url' => $download_url ?? '',
        ];
    }

    public function saveArchive(int $id, array $user = [])
    {
        $contract = ContractPlatFormModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id]
        ]);
        if (empty($contract)) {
            throw new BusinessException("gpmd 平台合同归档-合同信息为空, 不可归档, ID => $id", ErrCode::$BUSINESS_ERROR);
        }

        if ($contract->status != Enums::WF_STATE_APPROVED) {

            throw new BusinessException("gpmd 平台合同归档-合同审批未通过, 不可归档, ID => $id", ErrCode::$BUSINESS_ERROR);
        }
        $archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => ['cno' => $contract->contract_id],
            'columns'    => 'id'
        ]);
        if (!empty($archive)) {
            // 同一个合同编号的归档数据已存在(需再次核实确认是否是同一个合同 或 合同号重复导致)
            throw new BusinessException("gpmd 平台合同归档-归档数据已存在, 请核实: archive_id-{$archive->id}, cid-{$id}, cno-{$contract->contract_id}", ErrCode::$BUSINESS_ERROR);
        }

        $contract = $contract->toArray();
        $contract = $this->handleArchiveData($contract);
        $model    = (new ContractArchive());
        $bool     = $model->i_create($contract);
        if ($bool === false) {
            throw new BusinessException('gpmd 平台合同归档-归档数据写入失败: 原因可能是: ' . get_data_object_error_msg($model) . '; 待归档数据: ' . json_encode($contract, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    private function handleArchiveData($contract)
    {
        if (empty($contract) || !is_array($contract)) {
            return [];
        }
        $userInfo                         = HrStaffInfoModel::getUserInfo($contract['create_id'], 'name');
        $new_contract['cno']              = $contract['cno'];     //合同编号
        $new_contract['cname']            = $contract['cname'];        //合同名称
        $new_contract['status']           = ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING;                            //归档状态，1:待归档，2:已归档'
        $new_contract['template_id']      = 0;            //模版ID
        $new_contract['is_master']        = $contract['contract_type'];         //是否是主合同
        $new_contract['sub_cno']          = $contract['relate_master_cno'];               //关联的主合同编号
        $new_contract['payment_currency'] = $contract['payment_currency'];          //付款币种，1:泰铢，2:美元，3:人民币
        $new_contract['create_id']        = $contract['create_id'];               //申请人
        $new_contract['create_name']      = $userInfo['name'] ?? '';           //申请人姓名
        $new_contract['created_at']       = date('Y-m-d H:i:s');
        $new_contract['approved_at']      = $contract['approved_at'];
        $new_contract['contract_type']    = ContractEnums::CONTRACT_TYPE_PLATFORM;
        return $new_contract;
    }

    public function handleArchiveItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        $platform_contract_type = ContractEnums:: $platform_contract_type;
        $contract_status        = ContractEnums::$contract_archive_status;

        //处理数据
        $cno_list = array_values(array_filter(array_column($items, 'cno')));

        $contract_list = [];
        if (!empty($cno_list)) {
            $contract_list = ContractPlatFormModel::find([
                'conditions' => 'cno IN ({cno:array})',
                'bind'       => ['cno' => $cno_list],
                'columns'    => ['id', 'cno']
            ])->toArray();
            $contract_list = array_column($contract_list, 'id', 'cno');
        }

        foreach ($items as &$item) {
            $status                      = $contract_status[$item['archive_status']] ?? '';
            $item['archive_status_text'] = static::$t->_($status);
            $item['contract_type_text']  = static::$t->_($platform_contract_type[$item['is_master']]);
            $item['can_download']        = GlobalEnums::IS_CAN_DOWNLOAD;
            $item['cid']                 = $contract_list[$item['cno']] ?? '';
        }
        return $items;
    }

    /**
     *上传盖章合同
     * @param $archive_id
     * @param $data
     * @param $user
     * @return array
     * @throws ValidationException
     */
    public function archivePerform($archive_id, $data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $archive = ContractArchive::findFirst([
                'conditions' => 'id = :id: and status = :status:',
                'bind'       => ['id' => $archive_id, 'status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]
            ]);
            if (empty($archive)) {
                throw new BusinessException('gpmd归档合同不存在archive_id:' . $archive_id);
            }
            $hr_is_staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $data['holder_name']]
            ]);
            if (empty($hr_is_staff_info)) {
                throw new ValidationException(self::$t['access_data_staff_not_exist_hr_is_sys'] . "[{$data['holder_name']}]", ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->handlePerformData($data, $user);

            $contract_file         = $data['contract_file'];
            $data['contract_file'] = '';
            $bool                  = $archive->i_update($data);
            if ($bool === false) {
                throw new BusinessException('gpmd更新归档信息失败', ErrCode::$CONTRACT_ARCHIVE_UPDATE_INFO_ERROR);
            }

            //只改一次，所以没有删除 
            if (!empty($contract_file)) {
                $attachArr = [];
                foreach ($contract_file as $k => $v) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE;
                    $tmp['oss_bucket_key']  = $archive_id;
                    $tmp['bucket_name']     = $v['bucket_name'];
                    $tmp['object_key']      = $v['object_key'];
                    $tmp['file_name']       = $v['file_name'];
                    $attachArr[]            = $tmp;
                }
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attachArr);
                if ($attach_bool === false) {
                    throw new BusinessException('gpmd归档合同附件创建失败=' . json_encode($contract_file, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $db->rollback();
            $this->logger->warning('platform-archive-perform-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    private function handlePerformData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $data['status']      = ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL;
        $data['updated_at']  = date('Y-m-d H:i:s');
        $data['filing_at']   = date('Y-m-d H:i:s');
        $data['filing_id']   = $user['id'] ?? 0;
        $data['filing_name'] = $user['name'] ?? '';
        $data['holder_name'] = $data['holder_name'] ?? '';
        return $data;
    }


    /**
     * 归档 - 下载
     *
     * @param $id
     * @return array
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function genArchiveDownload($id)
    {
        if (empty($id)) {
            return ['url' => ''];
        }

        $contract = ContractPlatFormModel::findFirst([
            'conditions' => 'id = :id: AND status = :status:',
            'bind'       => ['id' => $id, 'status' => Enums::CONTRACT_STATUS_APPROVAL]
        ]);

        if (empty($contract)) {
            throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id]), ErrCode::$CONTRACT_GET_INFO_ERROR);
        }

        $file      = $contract->getFile(["columns" => "bucket_name,object_key,file_name"]);
        $file_info = $file->toArray() ?? [];
        if (empty($file_info)) {
            throw new ValidationException(static::$t->_('contract_file_not_exist'), ErrCode::$CONTRACT_GET_NO_CONTRACT_FILE_ERROR);
        }

        // 下载 V22074 由于附件私有化，所以这里需要私有化读取，然后再加水印
        $path = OssHelper::downloadFileHcm($file_info[0]['object_key'], 600);
        $path = $path['file_url'] ?? '';
//        $path       = $this->getShowPath($file_info);
        $file_name = !empty($file_info[0]['file_name']) ? $file_info[0]['file_name'] : 'contract_platform_' . date('ymdHis') . '.pdf';

        // pdf 加水印
        $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
        if (!empty($water_pdf_info['object_key'])) {
            $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
            $download_url = $result['file_url'];
        } else {
            $download_url = $path;
        }

        return [
            'url' => $download_url,
        ];
    }


    /**
     * 生成审批流pdf文件
     *
     * @param array $params
     * @param int $uid
     * @return array
     * @throws GuzzleException
     * @throws MpdfException
     */
    public function wfDownload(array $params, int $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang        = $this->getLang();
        $id          = $params['id'];
        $return_data = [];

        try {

            $data = $this->getDetail($params);
            $data = $data['data'] ?? [];
            if (!$this->isCanDownload($data, $uid)) {
                throw new ValidationException('该合同状态不允许下载文件', ErrCode::$CONTRACT_WORKFLOW_LOG_DOWNLOAD_ERROR);
            }

            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $sys_tmp_dir . '/';
            $file_name   = 'platform_workflow_audit_log_' . md5($id) . "_{$lang}.pdf";
            $file_path   = $file_dir . $file_name;

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序
            $view->render("contract", "contract_platform_workflow_audit_log_" . $lang);
            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path);
            if (!empty($upload_res['object_url'])) {
                $return_data['file_name'] = 'contract_platform_workflow_audit_log.pdf';
                $return_data['file_url']  = $upload_res['object_url'];
                if (!empty($upload_res['object_key'])){
                    $result = OssHelper::downloadFileHcm($upload_res['object_key']);
                    $return_data['file_url'] = $result['file_url'];
                }
            } else {
                throw new BusinessException('合同审批记录下载失败，请重试', ErrCode::$CONTRACT_WORKFLOW_LOG_DOWNLOAD_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('contract-platform_workflow-audit-log-download-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $return_data,
        ];
    }

    /**
     * @param $condition
     * @param $uid
     * @return array
     * @throws BusinessException
     */

    public function getDownloadList($condition, $uid)
    {
        $condition['uid'] = $uid;
        $builder          = $this->modelsManager->createBuilder();
        $builder->columns('
            distinct c.id,
            c.cno,
            c.cname,
            c.status,
            c.relate_master_cno,
            c.payment_currency,
            c.lang,
            c.create_id,
            c.create_name,
            c.create_department_id,
            c.create_department_name,
            c.created_at,
            c.sign_company_id,
            c.platform_customer_id,
            c.other_customer_name,
            c.contract_type,
            c.effected_date,
            c.finished_date,
            ca.id archive_id,
            ca.status archive_status,
            ca.filing_id a_create_id,
            ca.filing_name a_create_name,
            ca.filing_at,
            ca.holder_name
        ');
        $builder->from(['c' => ContractPlatFormModel::class]);
        $builder->leftJoin(ContractArchive::class, 'ca.cno = c.cno', 'ca');
        $builder = $this->getCondition($builder, $condition, self::LIST_TYPE_SEARCH);
        $builder->orderBy('c.id desc');
        $items = $builder->getQuery()->execute()->toArray();
        $items = $this->handleItemExport($items);

        return $this->exportContract($items);
    }

    /**
     * 按照excel导出格式处理数据
     * @param $items
     * @return array
     */
    private function handleItemExport($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }


        $contract_company        = ContractCompanyModel::find()->toArray();
        $contract_company        = array_column($contract_company, 'company_name', 'company_code');
        $contract_status         = Enums::$contract_status;
        $plat_form_customer      = ContractEnums::$plat_form_customer;
        $contract_lang           = Enums::$contract_lang;
        $currency_item           = GlobalEnums::$currency_item;
        $platform_contract_type  = ContractEnums::$platform_contract_type;
        $contract_archive_status = ContractEnums::$contract_archive_status;
        $t                       = static::$t;
        foreach ($items as &$item) {
            $status                         = $contract_status[$item['status']] ?? '';
            $item['status_title']           = $t->_($status);
            $is_archive_done                = $item['archive_status'] == ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL;
            $item['platform_customer_name'] = $plat_form_customer[$item['platform_customer_id']];
            $item['payment_currency']       = $t->_($currency_item[$item['payment_currency']] ?? '');
            $item['company_name']           = $contract_company[$item['sign_company_id']] ?? '';
            $item['lang']                   = $t->_($contract_lang[$item['lang']] ?? '');
            $item['contract_type_text']     = $t->_($platform_contract_type[$item['contract_type']] ?? '');
            $item['archive_status_text']    = $t->_($contract_archive_status[$item['archive_status']] ?? '');
            // 归档内容
            $item['a_create_id']   = $is_archive_done ? $item['a_create_id'] : '';
            $item['a_create_name'] = $is_archive_done ? $item['a_create_name'] : '';
            $item['filing_at']     = $is_archive_done ? $item['filing_at'] : '';

        }
        return $items;
    }

    /**
     * 导出excel数据
     * @param $items
     * @return array
     * @throws BusinessException
     */
    private function exportContract($items)
    {

        $new_data = [];
        foreach ($items as $key => $val) {

            $new_data[] = [
                $val['cname'],
                $val['cno'],
                $val['platform_customer_name'],
                $val['other_customer_name'],
                $val['company_name'],
                $val['contract_type_text'],
                $val['relate_master_cno'],
                $val['lang'],
                $val['payment_currency'],
                $val['effected_date'],
                $val['finished_date'],
                $val['create_department_name'],
                $val['create_id'],
                $val['create_name'],
                $val['created_at'],
                $val['status_title'],
                $val['archive_status_text'],
                $val['a_create_id'],
                $val['a_create_name'],
                $val['filing_at'],
                $val['holder_name'],
            ];
        }

        $file_name = "contract_platform_" . date("YmdHis");
        $header    = [
            static::$t->_('csr_field_contract_name'),//'合同名称',
            static::$t->_('csr_field_contract_id'),//'合同编号',
            static::$t->_('csr_field_platform_customer'),//'平台客户名称',
            static::$t->_('csr_field_other_customer_name'), //    other客户名称：
            static::$t->_('csr_field_sign_company'), //  合同签署公司：
            static::$t->_('contract_export_field_is_master'), // 合同主从属性
            static::$t->_('contract_export_field_sub_cno'),//关联主合同编号
            static::$t->_('csr_field_contract_lang'),//合同语言
            static::$t->_('csr_field_money_type'),//付款币种
            static::$t->_('contract_export_field_effected_date'),// 合同生效日期
            static::$t->_('contract_export_field_finished_date'),// 合同到期日期
            static::$t->_('csr_field_dept_name'),// 申请人部门
            static::$t->_('csr_field_manage_id'),//申请人工号
            static::$t->_('csr_field_create_name'),//申请人姓名
            static::$t->_('csr_field_apply_at'),//合同申请日期
            static::$t->_('csr_field_contract_status'),//申请状态
            static::$t->_('csr_filed_archive_status'),//合同归档状态
            static::$t->_('contract_export_field_archive_staff_id'),//归档人工号
            static::$t->_('contract_export_field_archive_staff_name'),//归档人姓名
            static::$t->_('contract_export_field_archive_create_at'),// 归档日期
            static::$t->_('contract_export_field_holder_name'),  // 合同原件保管人姓名',
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }
}