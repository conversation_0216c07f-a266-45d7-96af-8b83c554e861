<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Enums\ContractEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicClientOtpLogModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\oa\ContractElectronicFormdataRepository;
use App\Repository\oa\ContractElectronicRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class ContractElectronicFlowService extends AbstractFlowService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取POA 的otp日志 并 生成Excel文件
     *
     * @param object $sign_info_model
     * @param int $relate_contract_id
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function generatePOAOtpLogFile(object $sign_info_model, int $relate_contract_id)
    {
        // 获取POA otp 签字场景最后一条日志
        $otp_log_list = ContractElectronicClientOtpLogModel::find([
            'conditions' => 'electronic_no = :electronic_no: AND otp_scence IN ({otp_scence:array})',
            'bind' => [
                'electronic_no' => $sign_info_model->electronic_no,
                'otp_scence' => [
                    ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN,
                    ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN
                ]
            ],
        ])->toArray();

        $otp_latest_record = [];
        foreach ($otp_log_list as $log) {
            $latest_record_info = $otp_latest_record[$log['otp_email_uuid']] ?? [];
            if (empty($latest_record_info)) {
                $otp_latest_record[$log['otp_email_uuid']] = $log;
                continue;
            }

            if ($log['use_at'] > $latest_record_info['use_at']) {
                $otp_latest_record[$log['otp_email_uuid']] = $log;
            }
        }

        // 生成日志文件
        $file_header = EnumsService::getInstance()->getSettingEnvValueIds('electronic_contract_poa_otp_excel_header');

        // 关联的其他合同编号
        $relate_contract_no = Contract::findFirst($relate_contract_id)->cno ?? '';

        $poa_item = json_decode($sign_info_model->custom_poa_item, true);
        $poa_item = array_column($poa_item, 'sign_name', 'sign_key');

        $file_data = [];
        $index = 1;
        foreach ($otp_latest_record as $record) {
            $file_data[] = [
                $index,
                $record['electronic_no'],
                $relate_contract_no, // 关联的其他合同编号
                $record['sender_uuid'],
                $record['sender_role'] == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS ? 'Business Contact' : 'POA',
                $record['otp_uuid'],
                $record['otp_scence'] == ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN ? 'Business contact upload' : 'POA Signature',
                $record['otp_email'],
                $poa_item[$record['otp_email_uuid']] ?? '',// POA 姓名
                $record['otp_email_uuid'],
                $record['otp_code'],
                $record['send_ip'],
                $record['send_at'],
                $record['use_status'] ? 'Used' : '',
                $record['user_uuid'],
                $record['user_role'] == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS ? 'Business Contact' : 'POA',
                $record['use_at'],
                $record['use_ip'],
            ];

            $index++;
        }

        $this->logger->info(['electronic_contract_poa_otp_log_file_data' => $file_data]);

        // OPT日志文件
        $file_name = 'OTP Record.xlsx';
        $excel_res = $this->exportExcel($file_header, $file_data, $file_name, true);
        if (empty($excel_res['oss_file_data'])) {
            throw new BusinessException('电子合同签字审核-POA OTP日志文件生成失败', ErrCode::$BUSINESS_ERROR);
        }

        return $excel_res['oss_file_data'];
    }

    /**
     * 获取签字审批记录
     *
     * @param $sign_info_model
     * @return array
     */
    public function getAuditLogs($sign_info_model)
    {
        if (empty($sign_info_model)) {
            return [];
        }

        $request = $this->getRequest($sign_info_model);
        return (new WorkflowServiceV2())->getAuditLogs($request);
    }

    /**
     * 电子合同-签字复核审批-通过
     *
     * @param $electronic_no
     * @param $note
     * @param $user
     * @return array
     * @throws GuzzleException
     */
    public function approve($electronic_no, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 签字待复核状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_5) {
                throw new ValidationException(static::$t->_('sign_review_sign_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($sign_info_model->business_audit_status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('audited_error_001'), ErrCode::$BUSINESS_ERROR);
            }

            // 获取签字复核审批流
            $request = $this->getRequest($sign_info_model);
            if (empty($request->id)) {
                throw new BusinessException("复核签字审批-获取工作流失败, biz_value={$sign_info_model->id}", ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException("复核签字审批-审批状态错误，不允许进行该操作, biz_value={$sign_info_model->id}", ErrCode::$BUSINESS_ERROR);
            }

            // 通过
            $result = (new WorkflowServiceV2())->doApprove($request, $user, $this->getWorkflowParams($sign_info_model, $user), $note);

            // 终审通过
            if (!empty($result->approved_at)) {
                // 甲方商务下载链接有效天数
                $download_link_valid_days = (int)EnumsService::getInstance()->getSettingEnvValue('pmd_electronic_contract_download_link_valid_days');

                // 签约信息更新
                $sign_info_data = [
                    'business_audit_status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'download_link_valid_days' => $download_link_valid_days,
                    'updated_id' => $user['id'],
                    'updated_at' => date('Y-m-d H:i:s'),
                    'business_audit_at' => $result->approved_at,
                ];
                if ($sign_info_model->i_update($sign_info_data) === false) {
                    throw new BusinessException('复核签字审批-签约信息更新失败, ' . get_data_object_error_msg($sign_info_model) . '; data=' . json_encode($sign_info_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 待加水印的PDF文件
                $_pdf_url = !empty($model->file_url) ? \App\Library\OssHelper::downloadFileHcm($model->file_url) : '';
                $pdf_file_object_url = $_pdf_url['file_url'] ?? '';

                // 甲方先签的, 在末端环节需要追加乙方签字
                if ($sign_info_model->sign_order_type == ContractEnums::SIGN_ORDER_TYPE_PARTY_A) {
                    // 获取乙方授权人签章
                    $form_data_model = $model->getFormData();
                    $form_data = !empty($form_data_model->form_data) ? json_decode($form_data_model->form_data, true) : [];
                    $form_data = ContractElectronicService::getInstance()->getPartyBFormData($form_data, $model);

                    $this->logger->info('电子合同-复核签字审批-通过, pdf原文件=' . $pdf_file_object_url);

                    // 生成签字版pdf
                    $pdf_file_info = ContractElectronicService::getInstance()->generateElectronicContractPdfFile($model->department_id, $model->ftl_file_url, $form_data);

                    $this->logger->info('电子合同-复核签字审批-通过, pdf新文件(无水印)=' . $pdf_file_info['object_url']);

                    // 更新电子合同表单数据
                    ContractElectronicService::getInstance()->updateElectronicContractFormData($form_data_model, $form_data);

                    $pdf_file_object_url = $pdf_file_info['object_url'];
                }

                // 签字版合同加水印
                $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($pdf_file_object_url, $model->contract_name . '.pdf');

                $this->logger->info('电子合同-复核签字审批-通过, pdf新文件(有水印)=' . $water_pdf_info['object_url']);

                // 电子合同更新为已签约-线上
                $model_update = [
                    'file_url' => $water_pdf_info['object_key'],
                    'bucket_name' => $water_pdf_info['bucket_name'],
                    'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_3,
                    'updated_id' => $user['id'],
                    'updated_at' => date('Y-m-d H:i:s'),
                    'sign_completed_at' => date('Y-m-d H:i:s')
                ];
                if ($model->save($model_update) === false) {
                    throw new BusinessException('复核签字审批-签约状态更新失败, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 甲方商务上传的相关资料
                $related_files = $sign_info_model->getRelatedFileList()->toArray();

                // POA的OTP日志文件
                $poa_otp_file_data = $this->generatePOAOtpLogFile($sign_info_model, $model->relate_id);
                $related_files = array_merge($related_files, [$poa_otp_file_data]);

                // 关联的合同自动归档
                $contract_data = $model->toArray();
                $contract_data['holder_name'] = static::$t->_('original_contract_custodian_name');
                ArchiveUpdateService::getInstance()->autoArchive($contract_data, $related_files);

                // 给商务联系人发送下载签字版电子合同的邮件通知
                $email_val = [
                    'emails' => [$sign_info_model->custom_contact_email],
                    'contract_name' => $model->contract_name,
                    'contract_lang' => $model->lang,
                    'valid_days' => $download_link_valid_days,
                    'sign_link' => ContractElectronicService::getInstance()->generateEmailSignLink($sign_info_model->electronic_key, $sign_info_model->custom_contact_email_key, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN),
                ];
                ContractElectronicService::getInstance()->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_APPROVALED, $email_val);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->notice('electronic_contract_sign_audit_approve:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('electronic_contract_sign_audit_approve:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 电子合同-签字复核审批-驳回
     *
     * @param $electronic_no
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($electronic_no, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            // 获取电子合同信息
            $model = ContractElectronicRepository::getInstance()->getContractElectronicByNo($electronic_no);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 签字待复核状态
            if ($model->approve_status != Enums::WF_STATE_APPROVED || $model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_5) {
                throw new ValidationException(static::$t->_('sign_review_sign_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取电子合同签约信息
            $sign_info_model = $model->getSignInfo();
            if (empty($sign_info_model)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $electronic_no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($sign_info_model->business_audit_status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('audited_error_001'), ErrCode::$BUSINESS_ERROR);
            }

            // 获取签字复核审批流
            $request = $this->getRequest($sign_info_model);
            if (empty($request->id)) {
                throw new BusinessException("复核签字审批-获取工作流失败, biz_value={$sign_info_model->id}", ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException("复核签字审批-审批状态错误，不允许进行该操作, biz_value={$sign_info_model->id}", ErrCode::$BUSINESS_ERROR);
            }

            // 驳回
            $result = (new WorkflowServiceV2())->doReject($request, $user, $this->getWorkflowParams($sign_info_model, $user), $note);
            if ($result === false) {
                throw new BusinessException('复核签字审批-审批流驳回失败', ErrCode::$BUSINESS_ERROR);
            }

            // 重置发起签约日期
            $initiate_sign_date = date('Y-m-d');

            // 签约信息更新: 复核相关数据初始化
            $sign_info_data = [
                'sign_version' => $sign_info_model->sign_version + 1,
                'initiate_sign_date' => $initiate_sign_date,
                'business_audit_status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
                'business_audit_at' => $result->rejected_at,
            ];

            if ($sign_info_model->i_update($sign_info_data) === false) {
                throw new BusinessException('复核签字审批-签约信息更新失败,' . get_data_object_error_msg($sign_info_model) . '; data=' . json_encode($sign_info_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 生成pdf电子合同[回滚BD小签->清空]
            $form_data_model = $model->getFormData();
            $form_data = !empty($form_data_model->form_data) ? json_decode($form_data_model->form_data, true) : [];
            $form_data['flash_bd_sign_img'] = '';

            $this->logger->info('电子合同-复核签字审批-驳回, pdf原文件=' . $model->file_url);

            $pdf_file_info = ContractElectronicService::getInstance()->generateElectronicContractPdfFile($model->department_id, $model->ftl_file_url, $form_data);

            $this->logger->info('电子合同-复核签字审批-驳回, pdf新文件=' . $pdf_file_info['object_url']);

            // 电子合同更新为待重新签约
            $model_update = [
                'file_url' => $pdf_file_info['object_key'],
                'bucket_name' => $pdf_file_info['bucket_name'],
                'initiate_sign_date' => $initiate_sign_date,
                'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_6,
                'updated_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($model->save($model_update) === false) {
                throw new BusinessException('复核签字审批-签约状态更新失败, ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 更新电子合同表单数据
            ContractElectronicService::getInstance()->updateElectronicContractFormData($form_data_model, $form_data);

            // 给商务联系人发送待重新签约邮件通知
            $email_val = [
                'emails' => [$sign_info_model->custom_contact_email],
                'contract_name' => $model->contract_name,
                'contract_lang' => $model->lang,
                'reason' => $note,
                'sign_link' => ContractElectronicService::getInstance()->generateEmailSignLink($sign_info_model->electronic_key, $sign_info_model->custom_contact_email_key, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
            ];
            ContractElectronicService::getInstance()->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_RESIGN, $email_val);

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->notice('electronic_contract_sign_audit_reject:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('electronic_contract_sign_audit_reject:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     *
     * @param $id
     * @param $note
     * @param $user
     * @return mixed|void
     */
    public function cancel($id, $note, $user)
    {
    }

    /**
     * @param $model
     * @return Model
     */
    public function getRequest($model)
    {
        $biz_type = $this->getBizType($model);
        return $this->getRequestByBiz($model->id, $biz_type);
    }

    /**
     * @param $model
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($model, $user)
    {
        $workflowServiceV2 = new WorkflowServiceV2();
        $data['id'] = $model->id;
        $data['name'] = $model->electronic_no . '复核签字审批申请';
        $data['biz_type'] = $this->getBizType($model);
        $data['flow_id'] = $this->getFlowId($model);

        return $workflowServiceV2->createRequest($data, $user, $this->getWorkflowParams($model, $user));
    }

    /**
     * 根据业务类型获取审批流ID
     *
     * @param $model
     * @return int
     */
    public function getFlowId($model = null)
    {
        return $model->flow_id ?? Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_WF_ID;
    }

    /**
     * 获取业务类型
     *
     * @param $model
     * @return int
     */
    public function getBizType($model)
    {
        return Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE;
    }

    /**
     * @param $item
     * @param $user
     * @return array
     */
    private function getWorkflowParams($item, $user)
    {
        return [
            'submitter_id' => $item->created_id,
        ];
    }

}
