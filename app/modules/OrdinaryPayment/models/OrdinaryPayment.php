<?php

namespace App\Modules\OrdinaryPayment\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentAddService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Services\ListService;

class OrdinaryPayment extends Base implements BankFlowModelInterface,PayModelInterface
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化

    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('ordinary_payment');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT_MAIN." and deleted=0"
                ],
                "alias" => "File",
            ]
        );

        $this->hasOne(
            'id',
            OrdinaryPaymentExtend::class,
            'ordinary_payment_id',
            [
                "alias" => "Pay"
            ]
        );


        $this->hasMany(
            'id',
            OrdinaryPaymentPersonal::class,
            'ordinary_payment_id',
            [
                "alias" => "Persons"
            ]
        );


        $this->hasMany(
            'id',
            OrdinaryPaymentDetail::class,
            'ordinary_payment_id',
            [
                "alias" => "Details"
            ]
        );

    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'apply_no = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }

    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'apply_no in ({nos:array}) and approval_status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'apply_no in ({nos:array}) and approval_status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }

    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
            'no' => $this->apply_no,
            'amount' => floatval($this->amount_total_actually),
            'currency' => $this->currency,
            'status'   => $this->approval_status,
            'pay_status' => $this->pay_status
        ];
    }

    public function link(array $data)
    {
        $pay = $this->getPay();

        //判断现有的状态
        if (empty($this) || $this->approval_status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found ordinary_payment or ordinary_payment pay_status is error', ErrCode::$BUSINESS_ERROR);
        }

        if (empty($pay)) {
            throw new BusinessException('not found ordinary_payment extend', ErrCode::$BUSINESS_ERROR);
        }

        $item = [];
        $item['is_pay'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_bk_name'] = $data['bank_name'];
        $item['pay_bk_account'] = $data['bank_account'];
        $item['pay_bk_flow_date'] = $data['date'];
        $item['pay_staff_id'] = $data['create_id'];
        $item['pay_signer_name'] = $this->apply_name;
        $item['remark'] = $data['ticket_no'];
        $item['pay_from'] = 2;
        $item['pay_at'] = date('Y-m-d H:i:s');
        $item['updated_at'] = date('Y-m-d H:i:s');

        $bool = $pay->i_update($item);
        if ($bool === false) {
            throw new BusinessException('普通付款-支付失败, 原因可能是:' . get_data_object_error_msg($pay), ErrCode::$BUSINESS_ERROR);
        }

        $main_data = [
            'pay_status' => $item['is_pay'],
            'updated_at' => $item['updated_at'],
        ];

        $bool = $this->i_update($main_data);
        if ($bool === false) {
            throw new BusinessException("普通付款-更新主表失败", ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {

        $pay_at = date('Y-m-d H:i:s');

        $sql = 'update ordinary_payment_extend set 
                         is_pay=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         pay_bk_name="' . $data['bank_name'] . '",
                         pay_bk_account="' . $data['bank_account'] . '",
                         pay_bk_flow_date="' . $data['date'] . '",
                         pay_staff_id="' . $data['create_id'] . '",
                         pay_signer_name="",
                         remark="' . $data['ticket_no'] . '",
                         updated_at="' . date("Y-m-d H:i:s") . '",
                         pay_from=2,' . '
                         pay_at = "'. $pay_at .'" where ordinary_payment_id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('普通付款支付-批量更新失败==' . $sql);
        }

        $extends = OrdinaryPaymentExtend::find(
            [
                'conditions' => 'ordinary_payment_id in ({ids:array})',
                'bind'=> ['ids'=>$ids]
            ]
        )->toArray();



        //如果不相等，有需要插入的
        if(count($extends) != count($ids)){
            $now_ids = array_column($extends,"ordinary_payment_id");
            $insertIds = array_diff($ids,$now_ids);
            $batchData = [];
            $tmp = [];
            $tmp['is_pay'] = Enums::LOAN_PAY_STATUS_PAY;
            $tmp['pay_bk_name'] = $data['bank_name'] ?? '';
            $tmp['pay_bk_account'] = $data['bank_account'] ?? '';
            $tmp['pay_bk_flow_date'] = $data['date'] ?? date("Y-m-d");
            $tmp['pay_staff_id'] = $data['create_id'] ?? '0';
            $tmp['pay_signer_name'] = '';
            $tmp['remark'] = $data['ticket_no'] ?? '';
            $tmp['pay_from'] = 2;
            $tmp['pay_at'] = date('Y-m-d H:i:s');
            $tmp['created_at'] = date('Y-m-d H:i:s');
            $tmp['updated_at'] = date('Y-m-d H:i:s');
            if(!empty($insertIds)){
                foreach ($insertIds as $id){
                    $tmp['ordinary_payment_id'] = $id;
                    $batchData[] = $tmp;
                }
                $extend = new OrdinaryPaymentExtend();
                $bool = $extend->batch_insert($batchData);
                if ($bool === false) {
                    throw new BusinessException('普通付款支付-批量插入失败==' . $sql);
                }
            }
        }

        $sql = 'update ordinary_payment set 
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         updated_at="' . date("Y-m-d H:i:s") . '"
                        where id in (' . implode(',', $ids).')';

        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('普通付款-批量更新失败==' . $sql);
        }
        return true;
    }




    public function cancel(array $user)
    {
        //判断现有的状态
        if (empty($this) || $this->approval_status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found ordinary_payment or ordinary_payment pay_status is error');
        }

        //不能直接删除支付信息，因为还有供应商
        $pay = $this->getPay();

        $item = [];
        $item['is_pay'] = 0;    //是否已付款
        $item['pay_bk_name'] = '';
        $item['pay_bk_account'] = '';
        $item['pay_bk_flow_date'] = date("Y-m-d H:i:s");
        $item['pay_staff_id'] = 0;
        $item['pay_signer_name'] = '';
        $item['remark'] = '';
        $item['pay_from'] = 1;
        if(!empty($pay)){
            $bool = $pay->i_update($item);
            if ($bool === false) {
                $str = '';
                $messages = $pay->getMessages();
                foreach ($messages as $message){
                    $str.=$message;
                }
                throw new BusinessException("普通付款-支付撤销失败=$str", ErrCode::$CONTRACT_UPDATE_ERROR);
            }
        }
        $bool = $this->i_update(["pay_status" => Enums::LOAN_PAY_STATUS_PENDING]);
        if ($bool === false) {
            throw new BusinessException("普通付款-更新主表失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        $sql = 'update ordinary_payment_detail set 
                        is_deduct = '.intval($data['is_deduct']).'        
                        where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('普通付款-批量确认失败==' . $sql);
        }
        return true;
    }

    public function getPayData()
    {
        $arr = [
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
            'no' => $this->apply_no,
            'apply_staff_id' => $this->apply_id,
            'apply_staff_name' => $this->apply_name,
            'cost_department_id' => $this->cost_department_id,
            'cost_department_name' => $this->cost_department_name,
            'apply_date' => gmdate_customize_by_datetime($this->created_at , 'Y-m-d'),
            'pay_method' => $this->payment_method,
            'pay_where' => $this->pay_where,
            'currency' => $this->currency,
            'amount_total_no_tax' => $this->amount_total_no_tax,           //不含税金额
            'amount_total_vat' => $this->amount_total_vat,                 //税额
            //'amount_total_have_tax' => $this->amount_total_have_tax,       //含税金额（含VAT含WHT）
            'amount_total_have_tax' => bcadd($this->amount_total_no_tax,$this->amount_total_vat,2),       //含税金额（含VAT含WHT）= 不含税金额总计+vat总计
            'amount_total_wht' => $this->amount_total_wht,                 //wht总计
            'amount_total_have_tax_no_wht' => bcsub(bcadd($this->amount_total_no_tax,$this->amount_total_vat,2),$this->amount_total_wht,2),//含税金额总计（含VAT不含WHT） = 不含税金额总计+VAT总计-WHT总计
            'amount_loan' => 0,             //冲减借款金额,
            'amount_reserve' => 0,
            'amount_discount' => $this->amount_discount,                    //折扣
            'amount_total_actually' => $this->amount_total_actually,        //实付金额
            'amount_remark' => $this->remark,                                //备注
            'default_planned_pay_date' => empty($this->should_pay_date) ? date('Y-m-d') : $this->should_pay_date,//应付日期
            'planned_pay_date' => empty($this->should_pay_date) ? date('Y-m-d') : $this->should_pay_date,//计划支付日期
        ];

        //费用公司id
        $arr['cost_company_id'] = $this->cost_company_id;
        //费用公司名字
        $arr['cost_company_name'] = '';
        if(!empty($arr['cost_company_id'])){
            $arr['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($arr['cost_company_id']);
        }
        $arr['pays'] = [];

        if($this->payee_type == 1){
            $pay = $this->getPay();
            if(!empty($pay)){
                $tmp = [];
                $tmp['bank_name'] = $pay->supplier_bk_name;
                $tmp['bank_account'] = $pay->supplier_bk_account;
                $tmp['bank_account_name'] = $pay->supplier_bk_account_name;
                $tmp['amount'] = $arr['amount_total_actually'];
                $tmp['bank_address'] = $pay->bank_address??'';
                $tmp['swift_code'] = $this->swift_code??'';
                $arr['pays'][] = $tmp;
            }
        }else{
            $persons = $this->getPersons();
            foreach ($persons as $person){
                $tmp = [];
                $tmp['staff_info_id']  = $person->staff_info_id;
                $tmp['bank_name']  = $person->bank_name;
                $tmp['bank_account'] = $person->bank_no;
                $tmp['bank_account_name'] = $person->bank_no_name;
                $tmp['amount'] = $person->amount;
                $arr['pays'][] = $tmp;
            }
        }
        return $arr;
    }

    public function getPayCallBackData($data)
    {
        /*$validate_pay_param = [
            'id'               => 'Required|IntGe:1|>>>:id error',
            'is_pay'           => 'Required|IntIn:2,3|>>>:whether paid error',
            'pay_bk_name'      => 'IfIntEq:is_pay,2|Required|StrIn:Thai Military Bank,Siam Commercial Bank|>>>:payment bank error',
            'pay_bk_account'   => 'IfIntEq:is_pay,2|Required|StrIn:**********,**********|>>>:payment bank account error',
            'pay_signer_name'  => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,500|>>>:signer name error',
            'pay_bk_flow_date' => 'IfIntEq:is_pay,2|Required|Date|>>>:bank flow date error',
            'remark'           => 'IfIntEq:is_pay,3|Required|StrLenGeLe:1,1000|>>>:remark error',
        ];*/

        $new = [];
        $new['is_pay'] = $data['pay_status'];

        //支付
        if($data['pay_status'] == Enums::LOAN_PAY_STATUS_PAY){
            $new['pay_bk_name'] = $data['pay_bank_name'] ?? '';
            $new['pay_bk_account'] = $data['pay_bank_account'] ?? '';
            $new['pay_signer_name'] = $data['apply_staff_name']?? '';
            $new['pay_bk_flow_date'] = $data['pay_bank_flow_date'] ?? date("Y-m-d H:i:s");
        }else{
            $new['remark'] = $data['not_pay_reason'];
            $new['pay_bk_flow_date'] = null;
        }

        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     * @param $no
     * @param $pay_id
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no, $pay_id)
    {
        $myself = self::findFirst(
            [
                'conditions' => 'apply_no = :apply_no: and approval_status = :approval_status: and pay_status = :pay_status:',
                'bind' => [
                    'apply_no' => $no,
                    'approval_status' => Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING
                ]
            ]
        );
        //个人
        if ($myself->payee_type == Enums::PAYEE_TYPE_PERSONAL){
            $persons_model = $myself->getPersons();
            if (empty($persons_model)){
                return [];
            }
            //查询支付详情,得到收款人工号
            $payment_pay = PaymentPay::findFirst([
                'conditions' => 'id = :pay_id:',
                'bind' => ['pay_id' => $pay_id]
            ]);
            if (empty($payment_pay)){
                return [];
            }
            if (!isset($payment_pay->staff_info_id) || empty($payment_pay->staff_info_id)){
                return [];
            }
            $apply_info = OrdinaryPaymentAddService::getInstance()->getStaffInfo($payment_pay->staff_info_id);
            if (!isset($apply_info['code']) || $apply_info['code'] != ErrCode::$SUCCESS) {
                return [];
            }
            $bank_info = [];
            $bank_info['bank_name'] = $apply_info['data']['bank_name']??'';
            $bank_info['bank_account'] = $apply_info['data']['bank_no']??'';
            $bank_info['bank_account_name'] = $apply_info['data']['bank_no_name']??'';
            return [
                'type' => 1,
                'items' => $bank_info
            ];
        } elseif ($myself->payee_type == Enums::PAYEE_TYPE_VENDOR) {
            $extend_info = $myself->getPay();
            if (!isset($extend_info->supplier_id) || empty($extend_info->supplier_id)){
                return [];
            }
            $vendor_data = ListService::getInstance()->searchVendorTypeList($extend_info->supplier_name, 2, $extend_info->supplier_id);
            if (!isset($vendor_data['code']) || $vendor_data['code'] != ErrCode::$SUCCESS || empty($vendor_data['data'])){
                return [];
            }
            $vendor_info = $vendor_data['data'];
            if (!isset($vendor_info[0])){
                return [];
            }

            return [
                'type'=>3,
                'items'=>$vendor_info[0]
            ];
        }
        return [];
    }

    //打上支付模块标记
    public function updatePayTag():bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module'=>1]) === false){
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'apply_no = :no: AND approval_status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind' => [
                'no' => $data['payment_no'],
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 收款人类型非供应商, 不可变更收款人信息
        if (empty($main_model) || $main_model->payee_type != Enums::PAYEE_TYPE_VENDOR) {
            return true;
        }

        // 收款人信息
        $pay_bank_model = $main_model->getPay();
        if (empty($pay_bank_model)) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($pay_bank_model->toArray(), JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info = $data['pay'][0] ?? [];
        $sync_data = [
            'supplier_bk_account_name' => $pay_info['bank_account_name'],
            'supplier_bk_name' => $pay_info['bank_name'],
            'supplier_bk_account' => $pay_info['bank_account'],
        ];

        if ($pay_bank_model->i_update($sync_data) === false) {
            throw new BusinessException('普通付款单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($pay_bank_model), ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($pay_bank_model->toArray(), JSON_UNESCAPED_UNICODE));

        return true;
    }

}
