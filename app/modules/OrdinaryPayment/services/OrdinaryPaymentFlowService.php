<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use app\models\fle\SysStore;
use App\Modules\Budget\Models\BudgetDetail;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Services\BaseService AS PaymentBaseService;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Pay\Services\PayService;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Services\StaffService;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Budget\Services\BudgetService;
use App\Repository\HrStaffRepository;

class OrdinaryPaymentFlowService extends AbstractFlowService
{

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 普通付款审批 - 通过操作
     * @param $id
     * @param $note
     * @param $user
     * @param $update_data
     * @param $main_data
     * @return array
     */
    public function approve($id, $note, $user, $update_data = [], $main_data = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //和can_edit关联的字段为key，关联要修改却不在can_edit里面的字段为值
        $can_edit_save_relevance = ['deductible_vat_tax' => 'deductible_tax_amount'];
        /**
         * @var $work_req WorkflowRequestModel
         */
        $work_req = $this->getRequest($id);
        $db       = $this->getDI()->get('db_oa');

        $logger = $this->getDI()->get('logger');
        try {
            $db->begin();

            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = OrdinaryPayment::getFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            // 只有待审核的，方可驳回
            if ($main_model->approval_status != Enums::PAYMENT_APPLY_STATUS_PENDING) {
                throw new ValidationException('this apply already audit', ErrCode::$ORDINARY_PAYMENT_STATUS_CANCEL_ERROR);
            }

            // 业务主表待更新数据
            $update_main_data = [];

            // 是否可编辑指定字段
            // [1] 权限校验
            $can_edit = (new OrdinaryPaymentFlowService())->getCanEditFieldByReq($work_req, $user['id']);

            $logger->info('普通付款 - 审批通过 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE).'，是否变更金额详情表判断条件：'.json_encode(['can_edit'=>$can_edit,'update_data'=>$update_data]));

            // [2] 编辑更新
            if (!empty($can_edit) && !empty($update_data)) {
                $departmentCompanyId = (new EnumsService())->getSysDepartmentCompanyIds(true);
                $country_code = get_country_code();//国家码
                if (($country_code == GlobalEnums::TH_COUNTRY_CODE && $main_model->apply_company_id == $departmentCompanyId['FlashExpress']) || ($country_code == GlobalEnums::LA_COUNTRY_CODE && $main_model->apply_company_id == $departmentCompanyId['FlashLaos'])) {
                    $this->checkApBjNotEmpty($can_edit, $update_data, $work_req, $main_model->cost_company_id);
                }

                $ids           = array_column($update_data, 'detail_id');
                $amount_detail = OrdinaryPaymentDetail::find([
                    'conditions' => 'ordinary_payment_id = :ordinary_payment_id: AND id IN ({ids:array})',
                    'bind'       => ['ordinary_payment_id' => $main_model->id, 'ids' => $ids],
                ]);

                if (empty($amount_detail)) {
                    throw new BusinessException('金额详情数据为空, 不可继续编辑', ErrCode::$VALIDATE_ERROR);
                }

                // 重新计算WHT金额 和 实付金额
                $amount_total_wht = 0; // wht金额总计

                $wht_config = EnumsService::getInstance()->getWhtRateMap();
                $deductible_config = EnumsService::getInstance()->getDeductibleRateValueItem();

                $can_edit['amount_detail'] = $can_edit['amount_detail'] ?? [];
                foreach ($update_data as $_key => $_value) {
                    // wht 类别 和 税率校验  增加逻辑 如果can_edit没有配置  不做校验
                    if (in_array('wht_category', $can_edit['amount_detail'])) {
                        $_wht_info = $wht_config[$_value['wht_category']] ?? [];
                        if (empty($_wht_info)) {
                            throw new ValidationException(static::$t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    //  增加逻辑 如果can_edit没有配置  不做校验
                    if (in_array('wht_rate', $can_edit['amount_detail'])) {
                        if (empty($_wht_info['rate_list'][$_value['wht_rate']])) {
                            throw new ValidationException(static::$t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    // 可抵扣税校验   增加逻辑 如果can_edit没有配置  不做校验
                    if (in_array('deductible_vat_tax', $can_edit['amount_detail'])) {
                        if (!in_array($_value['deductible_vat_tax'], $deductible_config)) {
                            throw new ValidationException(static::$t->_('deductable_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    // 重新计算WHT总额
                    $amount_total_wht = bcadd($amount_total_wht, $_value['amount_wht'], 2);
                    $update_data[$_key] = $_value;
                }

                //15998需求马来/17688需求泰国菲律宾，针对AP（BJ）可修改对应的WHT金额,需要判断这个金额是否是负数
                if (in_array($country_code,[GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && $amount_total_wht < 0) {
                    throw new ValidationException(self::$t->_('amount_total_actually_value_negative_number'), ErrCode::$VALIDATE_ERROR);
                }
                //实付金额
                $amount_total_actually = $main_model->amount_total_have_tax - $amount_total_wht - $main_model->amount_discount;
                if ($amount_total_actually <= 0) {
                    throw new ValidationException(self::$t->_('amount_total_actually_value_error'), ErrCode::$VALIDATE_ERROR);
                }

                if (!empty($can_edit['amount_detail'])) {
                    $update_detail_data = array_column($update_data, null, 'detail_id');
                    // 更新detail表
                    foreach ($amount_detail as $detail_model) {
                        $_tmp_detail = $update_detail_data[$detail_model->id] ?? [];
                        if (empty($_tmp_detail)) {
                            continue;
                        }

                        $_tmp_detail['deductible_tax_amount'] = round(($detail_model->amount_vat * ($_tmp_detail['deductible_vat_tax'] / 100)), 2) * 100;

                        $up_tmp_detail = [];
                        foreach ($_tmp_detail as $key => $update_item) {
                            if (in_array($key, $can_edit['amount_detail'])) {
                                $up_tmp_detail[$key] = $update_item;
                                if (!empty($can_edit_save_relevance[$key])) {
                                    $up_tmp_detail[$can_edit_save_relevance[$key]] = $_tmp_detail[$can_edit_save_relevance[$key]];
                                }
                            }
                        }
                        if (!empty($up_tmp_detail)) {
                            if ($detail_model->i_update($up_tmp_detail) === false) {
                                throw new BusinessException('普通付款 - 审核通过- 金额详情操作失败', ErrCode::$ORDINARY_PAYMENT_APPROL_DETAIL_UPDATE_ERROR);
                            }
                        }

                        $logger->info('普通付款 - 审批通过 - 金额详情更新后数据: ' . json_encode($detail_model->toArray(), JSON_UNESCAPED_UNICODE) . ' 修改的数据为:' . json_encode($up_tmp_detail, JSON_UNESCAPED_UNICODE));
                    }
                }

                // 业务主表 wht 相关待更新数据
                $update_main_data['amount_total_wht']      = $amount_total_wht;
                $update_main_data['amount_total_actually'] = $amount_total_actually;
                $extra_message = $main_data['extra_message'] ?? '';
                $voucher_abstract = $main_data['voucher_abstract'] ?? '';
                $update_main_data['extra_message'] = $extra_message;
                $update_main_data['voucher_abstract'] = $voucher_abstract;
                // 补充附件上传
                if (isset($main_data['is_supplement_invoice']) &&
                    !empty($main_data['is_supplement_invoice'])) {
                    $update_main_data['is_supplement_invoice'] = $main_data['is_supplement_invoice'];
                    $update_main_data['supplement_file_change_date'] = date('Y-m-d H:i:s');
                }
            }

            // 审批
            $ws = new WorkflowServiceV2();

            $result = $ws->doApprove($work_req, $user, $this->getOrdinaryPaymentWorkflowParams($main_model, $user,$work_req->flow_id), $note);

            // 全部审批通过
            if (!empty($result->approved_at)) {
                $update_main_data['approval_status'] = Enums::PAYMENT_APPLY_STATUS_APPROVAL;
                $update_main_data['approved_at']     = $result->approved_at;
            }

            // 是否是ap泰国节点之后
            if ($main_model->is_after_ap_th != 1 && $ws->isAfterApTH($result)) {
                $update_main_data['is_after_ap_th'] = 1;
            }

            // 补充业务主表数据
            $main_bool = null;

            if (!empty($update_main_data)) {

                // 更新业务主表数据
                $main_bool = $main_model->i_update($update_main_data);
                if ($main_bool === false) {
                    throw new BusinessException('普通付款 - 审核通过- 主表操作失败', ErrCode::$ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR);
                }

                $logger->info('普通付款 - 审批通过 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));
            }

            // 全部审核通过 且 数据更新成功
            if (!empty($result->approved_at) && $main_bool) {
                // 同步数据到支付模块
                if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT, $main_model->cost_company_id)) {
                    PayService::getInstance()->saveOne($main_model);
                } else {
                    // 普通付款待支付人配置
                    $pay_staff_ids = (new PaymentBaseService())->getPayAuthStaffIdItem();

                    // 过滤费用所属公司所在管辖范围的管辖人 v18029
                    $pay_staff_ids = DataPermissionModuleConfigService::getInstance()->filterStaffIdsByDepartmentId($main_model->cost_company_id, $pay_staff_ids, SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT);

                    if ($pay_staff_ids) {
                        $this->sendEmailToAuditors($work_req, $pay_staff_ids, 1);
                        $this->delUnReadNumsKeyByStaffIds($pay_staff_ids);
                    }
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('ordinary_payment-approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 普通付款申请 - 驳回
     * @param $main_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($main_id, $note, $user)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = $real_message = '';
        /**
         * @var $work_req WorkflowRequestModel
         */
        $work_req = $this->getRequest($main_id);
        $db       = $this->getDI()->get('db_oa');

        try {
            $db->begin();

            // 前置获取审批人信息
            $user_base_info = (new PaymentBaseService())->getUserMetaFromBi($user['id'], 0);

            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = OrdinaryPayment::getFirst([
                'id = :id:',
                'bind' => ['id' => $main_id],
            ]);

            // 只有待审核的，方可驳回
            if ($main_model->approval_status != Enums::PAYMENT_APPLY_STATUS_PENDING) {
                throw new ValidationException('this apply already audit', ErrCode::$ORDINARY_PAYMENT_STATUS_CANCEL_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getOrdinaryPaymentWorkflowParams($main_model, $user,$work_req->flow_id), $note);

            if ($result === false) {
                throw new BusinessException('普通付款申请-审批流驳回失败', ErrCode::$ORDINARY_PAYMENT_REJECT_ERROR);
            }


            $bool = $main_model->i_update([
                'approval_status'        => Enums::PAYMENT_APPLY_STATUS_REJECTED,
                'updated_at'             => date('Y-m-d H:i:s'),
                'rejected_at'            => $result->rejected_at,
                'refuse_reason'          => $note,
                'pay_status'             => Enums::PAYMENT_PAY_STATUS_NOTPAY,
                'last_update_id'         => $user['id'],
                'last_update_name'       => $this->getNameAndNickName($user['name'] ?? '', $user['nick_name']),
                'last_update_department' => $user_base_info['create_node_department_name'] ?? '',
                'last_update_job_title'  => $user_base_info['create_job_title_name'] ?? '',
                'last_update_at'         => date('Y-m-d H:i:s'),
            ]);

            if ($bool === false) {
                throw new BusinessException('普通付款 - 申请驳回操作失败', ErrCode::$ORDINARY_PAYMENT_REJECT_MAIN_ERROR);
            }

            // V21975 - 区分是否关联预提单逻辑
            $this->removeBindBudgetWithholdingInfo($main_model, $user);

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款-申请驳回失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 普通付款申请 - 撤回
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        /**
         * @var $work_req WorkflowRequestModel
         */
        $work_req = $this->getRequest($id);
        $db       = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $ordinary_payment_model = OrdinaryPayment::getFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);
            if (empty($ordinary_payment_model)) {
                throw new BusinessException(static::$t->_('普通付款信息获取失败'), ErrCode::$ORDINARY_PAYMENT_GET_INFO_ERROR);
            }

            // 只有待审核的，方可撤回
            if ($ordinary_payment_model->approval_status != Enums::PAYMENT_APPLY_STATUS_PENDING) {
                throw new ValidationException('this apply already audit', ErrCode::$ORDINARY_PAYMENT_STATUS_CANCEL_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getOrdinaryPaymentWorkflowParams($ordinary_payment_model, $user,$work_req->flow_id), $note);
            if ($result === false) {
                throw new BusinessException('普通付款- 审批流 撤回 失败', ErrCode::$ORDINARY_PAYMENT_CANCEL_ERROR);
            }

            $bool = $ordinary_payment_model->i_update([
                'approval_status' => Enums::PAYMENT_APPLY_STATUS_WITHDRAW, //撤销
                'cancel_at'       => date('Y-m-d H:i:s'),//撤销时间
                'cancel_reason'   => $note,
                'pay_status'      => Enums::PAYMENT_PAY_STATUS_NOTPAY,


            ]);

            if ($bool === false) {
                throw new BusinessException('普通付款 - 申请撤回操作失败', ErrCode::$ORDINARY_PAYMENT_CANCEL_MAIN_ERROR);
            }

            // V21975 - 区分是否关联预提单逻辑
            $this->removeBindBudgetWithholdingInfo($ordinary_payment_model, $user);

            $db->commit();

        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 获取审批流信息
     * @param $ordinary_payment_id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($ordinary_payment_id)
    {
        return $this->getRequestByBiz($ordinary_payment_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE);
    }

    /**
     * 注入审批流
     * @param object $model
     * @param array $user
     * @param array $amount_detail
     * @return mixed|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($model, $user, $amount_detail = [])
    {
        $data['id']       = $model->id;
        $data['name']     = $model->apply_no . '审批申请';
        $data['biz_type'] = Enums::ORDINARY_PAYMENT_BIZ_TYPE;
        if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            $data['flow_id'] = $this->getFlowIdNew($model);
        } else {
            $data['flow_id'] = $this->getFlowId($model, $amount_detail);
        }
        //报销用的是申请人id，申请人名字
        $user['id']   = $model->apply_id;
        $user['name'] = $model->apply_name;
        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getOrdinaryPaymentWorkflowParams($model,[],$data['flow_id']));
    }

    /**
     * 获取普通付款审批流需要数据
     * @param $main_model
     * @param array $user
     * @param int $flow_id
     * @return array
     */
    public function getOrdinaryPaymentWorkflowParams($main_model, $user = [],$flow_id=0)
    {
        $amount_total_have_tax = EnumsService::getInstance()->amountExchangeRateCalculation($main_model->amount_total_have_tax, $main_model->exchange_rate, 2);
        /**
         * 临时方案-修复"税金及附加"审批流流转到HRBP的问题
         * 由于network(51)的审批流配置中将pay_type=0视为"其他"
         * 但产品文档中将非"外协",非"福利费"视为"其他" ,
         * 为满足产品需求,在network(51)审批流中暂时将"税金及附加"(3)归为"其他"(0)处理
         */
        $pay_type = $main_model->pay_type;
        if (($flow_id==51 && $main_model->pay_type==3) ||($flow_id==94 && $main_model->pay_type==3)){
            $pay_type = 0;
        }
        //查询是否包含 (付款分类=员工福利费 且 费用类型=其他)
        $detail = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $main_model->id],
        ])->toArray();
        $is_have_welfare = false;
        foreach ($detail as $dk=>$dv){
            if($dv['level_code'] == Enums::BUDGET_OBJECT_WELFARE_CODE && $dv['product_id'] == 588){
                $is_have_welfare = true;
            }
        }
        //是否包含外包外协 0 无 1.外包 2.外协  以首次出现为准,基于产品逻辑,泰国network外包外协不会混合提交,这里只取第一个,用于泰国network外包外协的审批流
        $out_worker = Enums\ReimbursementEnums::OUT_WORKER_NOT;
        foreach ($detail as $dv_two){
            if ($dv_two['budget_id'] == Enums::BUDGET_OBJECT_OUT_WORKER_ID){
                $out_worker = Enums\ReimbursementEnums::OUT_WORKER_EPIBOLY;
                break;
            }
            if ($dv_two['budget_id'] == Enums::BUDGET_OBJECT_EXTERNAL_COURIER_ID){
                $out_worker = Enums\ReimbursementEnums::OUT_WORKER_OUTSOURCE;
                break;
            }
        }

        return [
            'total_amount'       => $amount_total_have_tax,
            'currency'           => $main_model->currency,
            'submitter_id'       => $main_model->apply_id, // 申请人用，审批流创建人
            'department_id'      => $main_model->apply_sys_department_id,
            'node_department_id' => $main_model->apply_node_department_id,
            'cost_store_type'    => $main_model->cost_store_type,
            'store_id'           => $main_model->apply_store_id,
            'type'               => $pay_type,
            'create_staff_id'    => $main_model->create_id,
            //'company_id'         => $main_model->apply_company_id   //申请人公司id，用来判断flow_id=55的时候走哪个APS
            'company_id'         => $main_model->cost_company_id,   //费用所属公司id，v10533改为费用所属公司判断
            'create_company_id'  => $main_model->cost_company_id,   //为了取财务分组审批人,需要定义create_company_id=费用所属公司
            'is_have_welfare'    => $is_have_welfare, //是否包含(付款分类=员工福利费 且 费用类型=其他)
            'out_worker'         => $out_worker, //是否包含外包或外协
            'set_department_id'  => EnumsService::getInstance()->getSettingEnvValue('ordinary_payment_transportation_dpet_id', 0),//自定义部门id, 用来获取审批流中33,34,35类型的审批人
            'is_cmo'             => get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? (new HrStaffRepository())->isCMO($main_model->apply_id) : 0,//V21791-当申请人直线上级等于配置的CMO工号时该节点需要审批
        ];

    }

    /**
     * @param null $model
     * @return int
     */
    public function getFlowIdForExpress($model = null)
    {
        $flow_id = Enums::ORDINARY_PAYMENT_WF_ID;
        if (2 == $model->cost_store_type) {//网点 审批流
            $sysDepartment = StaffService::getInstance()->getParentDepartment($model->apply_sys_department_id, 1);
            if(trim($sysDepartment['id'])==Enums::SYS_DEPARTMENT_ONE_ID && get_country_code()=='TH'){
                $flow_id = Enums::REIMBURSEMENT_APPLY_WORKFLOW_NODE_ID;
            }else{
                $store = (new WorkflowServiceV2())->getStoreById($model->apply_store_id ?? 0);
                $store['category'] = $store['category'] ?? null;

                switch ($store['category']) {
                    //network
                    case 1:
                    case 2:
                    case 10:
                    case 14:
                        $flow_id = Enums::ORDINARY_PAYMENT_NETWORK_ID; //报销网点（Network）
                        break;
                    //门店shop
                    case 4:
                    case 5:
                    case 7:
                        //shop 拆分审批流
                    if ($model->pay_type == Enums::REIMBURSEMENT_TYPE_TAX && 'TH' == get_country_code()) {
                        $flow_id = Enums::ORDINARY_PAYMENT_SHOP_TAX_ID;//报销网点（门店shop）广告牌税
                    } else {
                        $flow_id = Enums::ORDINARY_PAYMENT_SHOP_ID;//报销网点（门店shop）
                    }
                        break;
                    //hub
                    case 8:
                    case 9:
                    case 12:
                        $flow_id = Enums::ORDINARY_PAYMENT_HUB_ID; //报销网点（hub）
                        break;
                    default:
                        break;
                }
        }
        }

        return $flow_id;
    }

    /**
     * v18028、v18960 获取审批流id
     * @param object $model
     * @return int 审批流id
     * @throws ValidationException
     */
    public function getFlowIdNew(object $model)
    {
        //校验混合提交
        $details = $model->getDetails()->toArray();
        $budget_ids = array_column($details, 'budget_id');
        OrdinaryPaymentAddService::getInstance()->addApplyCheckNew($budget_ids);
        //1. 获取所有配置
        $flow_config = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_create_flow_new_config');
        $department_config = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
        $company_config = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_company_ids');
        //2. 找到公司
        $company_key = array_search($model->cost_company_id, $company_config);
        //FlashHomeOperation和FlashHomeHolding统一转成FlashHome
        if (in_array($company_key, ['FlashHomeOperation','FlashHomeHolding'])) {
            $company_key = 'FlashHome';
        }
        if (isset($flow_config[$company_key])) {
            $company_config = $flow_config[$company_key];
        } elseif (isset($flow_config['other'])) {
            $company_config = $flow_config['other'];
        } else {
            $this->logger->warning('费用所属公司未配置审批流 config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('flow_config_not_exist_company'), ErrCode::$VALIDATE_ERROR);
        }
        //3. 找到部门
        //用申请人一级部门找
        $department_key = false;
        $country_code = get_country_code();
        if (!in_array($model->apply_store_id, [Enums::HEAD_OFFICE_STORE_FLAG, Enums::PAYMENT_HEADER_STORE_ID])) {
            $department_key = array_search($model->apply_sys_department_id, $department_config);
        } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE && isset($department_config['network']) && $model->apply_sys_department_id == $department_config['network']) {
            //v18960菲律宾-network部门分总部和网点-总部
            $department_key = 'network_head_office';
        } elseif ($company_key == 'FlashExpress' && $country_code == GlobalEnums::TH_COUNTRY_CODE && isset($department_config['pmd']) && $model->apply_sys_department_id == $department_config['pmd']) {
            //v21791泰国-总部-pmd部门
            $department_key = 'pmd';
        }
        if ($department_key !== false && isset($company_config[$department_key])) {
            $flow_department_config = $company_config[$department_key];
        } elseif (isset($company_config['headquarters'])) {
            $flow_department_config = $company_config['headquarters'];
        } else {
            $this->logger->warning('部门未配置审批流 config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('flow_config_not_exist_department'), ErrCode::$VALIDATE_ERROR);
        }
        //4. 找到付款分类
        //找到最优先的报销实质
        $flow_config_keys = array_keys($flow_department_config);
        //给付款分类分组
        $product_group = $this->getBudgetTypeNew($budget_ids);
        $find_budget = 'other';//默认找不到用other
        foreach ($flow_config_keys as $v) {
            if (key_exists($v, $product_group)) {
                $find_budget = $v;
                break;
            }
        }
        //找到审批流
        if (empty($flow_department_config[$find_budget])) {
            $this->logger->warning('报销实质未配置审批流 budget_product=' . $budget_ids . ' config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('ordinary_payment_flow_config_not_exist_budget'), ErrCode::$VALIDATE_ERROR);
        }
        //返回审批流id
        return $flow_department_config[$find_budget];
    }

    /**
     * v18028 获取预算科目的归类
     * @param $budget_ids
     * @return array
     */
    public function getBudgetTypeNew($budget_ids)
    {
        $subject_type = [];
        if (empty($budget_ids)) {
            return $subject_type;
        }
        $subject_config = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_create_check_config');
        $budget_ids = array_unique($budget_ids);
        foreach ($budget_ids as $budget_id) {
            $find_success = 0;
            foreach ($subject_config as $subject_name => $ids) {
                //匹配budget_id
                if (in_array($budget_id, $ids)) {
                    $find_success = 1;
                    $subject_type[$subject_name] = isset($subject_type[$subject_name]) ? $subject_type[$subject_name] + 1 : 1;
                }
            }
            //没找到归为其他
            if ($find_success === 0) {
                $subject_type['other'] = isset($subject_type['other']) ? $subject_type['other'] + 1 : 1;
            }
        }
        return $subject_type;
    }

    /**
     * @param null $model
     * @param array $amount_detail
     * @return int
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getFlowId($model=null, $amount_detail = [])
    {
        $country_code = get_country_code();
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();

        $biz_data = $model->toArray();
        $biz_data['amount_detail'] = $amount_detail;
        $apply_check = $this->workflowParamsCheck($country_code, $company_ids, $biz_data);
        //子公司审批流配置
        $company_flow_data = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_bu_flow_config');
        $company_key = array_search($model->cost_company_id, $company_ids);
        //先判断子公司
        $company_flow_id = $company_flow_data[$company_key] ?? ($company_flow_data['other'] ?? 0);
        // 老挝审批流
        if ($country_code == GlobalEnums::LA_COUNTRY_CODE) {
            if ((string)$model->cost_company_id == $company_ids['FlashExpress']) {
                $flow =  $this->getBelongCompanyFlowId($model,$apply_check,$amount_detail);
            } else {
                $flow = $company_flow_id;
            }
            return $flow;
        }
        // 印尼,越南审批流
        if (in_array($country_code, [GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
            return $company_flow_id;
        }
        //泰国,菲律宾,马来
        $departmentCompanyId = (new EnumsService())->getSysDepartmentCompanyIds(true);

        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && $model->cost_company_id == $departmentCompanyId['FlashExpress'] && $this->belongToStore($model)) {
            $flow = $this->getBelongCompanyFlowId($model, $apply_check, $amount_detail);

        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($model->cost_company_id, [$departmentCompanyId['FlashHomeOperation'], $departmentCompanyId['FlashHomeHolding']])) {
            $flow = $this->getBelongCompanyFlowId($model, $apply_check, $amount_detail);

        } else {
            if (!empty($company_flow_id)) {
                $flow = $company_flow_id;
            } else {
                $flow = $this->getFlowIdForExpress($model);
            }
        }
        return $flow;
    }


    /**
    * 所属网点
    * @Date: 2022-06-10 19:16
    * @author: peak pan
    * @return:
    **/
    public function belongToStore($model)
    {
        //如果为总部不做校验
        if($model->apply_store_id==Enums::PAYMENT_HEADER_STORE_ID){
            return true;
        }
        $data = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $model->apply_store_id]]);
        if (empty($data)) {
            throw new BusinessException('网点不存在', ErrCode::$ORDINARY_PAYMENT_CREATE_WORK_FLOW_ERROR);
        }
        //判断属于哪个网点
        if(in_array($data->category,OrdinaryPaymentEnums::BELONGTOSTORE_STORE_IDS)){
            return true;
        }
        return false;
    }

    /**
    * 定义审批流
    * @Date: 2022-06-10 19:52
    * @author: peak pan
    * @return:
    **/
    public function getBelongCompanyFlowId($model,$apply_check,$amount_detail)
    {
        $country_code = get_country_code();
        //v16993 泰国FlashHome取FlashHome的审批流优先级配置
        $department_company_id = (new EnumsService())->getSysDepartmentCompanyIds(); //公司id配置
        $base_config = json_decode((new EnvModel())->getEnvByCode('ordinary_payment_create_flow_config'), true);
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($model->cost_company_id, [$department_company_id['FlashHomeOperation'], $department_company_id['FlashHomeHolding']])) {
            $flow_config = $base_config['FlashHome'];
        } else {
            $flow_config = $base_config['FlashExpress'];
        }
        $amount_detail_arr  = array_unique(array_column($amount_detail, 'budget_id'));
        $check_config = [];
        foreach ($apply_check as $key => $item) {
            if (!empty(array_intersect($item, $amount_detail_arr))) {
                $check_config[] = $key;
            }
        }
        //提交的budget_id如果不在配置里面 走其他， 或者提交budget_id在配置里面但全部不在审批流配置里面兜底走其他
        if (empty($check_config) || empty(array_intersect($check_config, array_column($flow_config,'subject_number')))) {
            //如果为空 就是其他
            $check_config[] = 'other';
        }
        $commission = $apply_check['commission'][0] ?? '';
        $service_charge = $apply_check['service_charge'][0] ?? '' ;
        $water_and_electricity = $apply_check['water_and_electricity'][0] ?? '';
        $department_arr = [];
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::LA_COUNTRY_CODE) {
            //如果包含返佣 且包含其他 不包含银行 优先级变更为其他
            if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_ONE && !in_array($service_charge, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

            if (!in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_ONE && in_array($service_charge, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('service_charge', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

            if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_TWO && in_array($service_charge, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                $key_charge        = array_search('service_charge', $check_config);
                if (isset($key) && isset($key_charge)) {
                    unset($check_config[$key]);
                    unset($check_config[$key_charge]);
                }
            }
        } else if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            //包含返佣且包含其它 不包含银行水电
            if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_ONE && !in_array($service_charge, $amount_detail_arr) && !in_array($water_and_electricity, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }
            //包含水电 且包含其它 不包含返佣银行

            if (in_array($water_and_electricity, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_ONE && !in_array($service_charge, $amount_detail_arr) && !in_array($commission, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('water_and_electricity', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

            // 包含银行且包含其它 不包含返佣水电
            if (in_array($service_charge, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_ONE && !in_array($commission, $amount_detail_arr) && !in_array($water_and_electricity, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('service_charge', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

            //不包含返佣 包含银行 水电
            if (!in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_TWO && in_array($service_charge, $amount_detail_arr) && in_array($water_and_electricity, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('service_charge', $check_config);
                $key_water         = array_search('water_and_electricity', $check_config);
                if (isset($key) && isset($key_water)) {
                    unset($check_config[$key]);
                    unset($check_config[$key_water]);
                }
            }
            //不包含 银行  包含返佣水电
            if (!in_array($service_charge, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_TWO && in_array($commission, $amount_detail_arr) && in_array($water_and_electricity, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                $key_water         = array_search('water_and_electricity', $check_config);
                if (isset($key) && isset($key_water)) {
                    unset($check_config[$key]);
                    unset($check_config[$key_water]);
                }
            }

            //不包含水电 包含返佣银行
            if (!in_array($water_and_electricity, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_TWO && in_array($service_charge, $amount_detail_arr) && in_array($commission, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('service_charge', $check_config);
                $key_commission    = array_search('commission', $check_config);
                if (isset($key) && isset($key_commission)) {
                    unset($check_config[$key]);
                    unset($check_config[$key_commission]);
                }
            }

            //包含返佣 水电银行
            if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > OrdinaryPaymentEnums::ID_DEFAULT_THREE && in_array($service_charge, $amount_detail_arr) && in_array($water_and_electricity, $amount_detail_arr)) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                $key_charge        = array_search('service_charge', $check_config);
                $key_water         = array_search('water_and_electricity', $check_config);

                if (isset($key) && isset($key_charge) && isset($key_water)) {
                    unset($check_config[$key]);
                    unset($check_config[$key_charge]);
                    unset($check_config[$key_water]);
                }
            }

        } else if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            //马来增加其他
            $department_arr = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
            //符合条件的全部情况
            $all_where = count($amount_detail_arr) > 1 && !empty(array_diff($amount_detail_arr, [$commission, $water_and_electricity]));
            //不是总部符合条件情况
            $not_headquarters_where = $model->apply_store_id != Enums::PAYMENT_HEADER_STORE_ID && in_array($commission, $amount_detail_arr);
            if ($all_where || $not_headquarters_where) {

                //返佣
                if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > 1) {
                    $check_config_other = ['other'];
                    $check_config       = array_merge($check_config, $check_config_other);
                    $key                = array_search('commission', $check_config);
                    if (isset($key)) {
                        unset($check_config[$key]);
                    }
                }
                //水电费
                if (in_array($water_and_electricity, $amount_detail_arr) && count($amount_detail_arr) > 1) {
                    $check_config_other = ['other'];
                    $check_config       = array_merge($check_config, $check_config_other);
                    $key                = array_search('water_and_electricity', $check_config);
                    if (isset($key)) {
                        unset($check_config[$key]);
                    }
                }
            }

            //税金及附加
            $taxes_additional = $apply_check['taxes_additional'][0] ?? '';
            if (in_array($taxes_additional, $amount_detail_arr) && $model->apply_sys_department_id == $department_arr['hub'] && $model->apply_store_id != Enums::PAYMENT_HEADER_STORE_ID) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key                = array_search('taxes_additional', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }
            //第三方运费
            $freight_branch = $apply_check['freight'][0] ?? '';//第三方运费-支干线
            $freight_join   = $apply_check['freight'][1] ?? '';//第三方运费-加盟商
            if ((in_array($freight_branch, $amount_detail_arr) || in_array($freight_join, $amount_detail_arr)) && $model->apply_store_id != Enums::PAYMENT_HEADER_STORE_ID) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key                = array_search('freight', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

            $fuel           = $apply_check['linked_cost'][0] ?? '';//油费
            $travel         = $apply_check['linked_cost'][1] ?? '';//差旅费
            $transportation = $apply_check['linked_cost'][2] ?? '';//交通费
            if ((in_array($fuel, $amount_detail_arr) || in_array($travel, $amount_detail_arr) || in_array($transportation, $amount_detail_arr)) && $model->apply_store_id == Enums::PAYMENT_HEADER_STORE_ID) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key                = array_search('linked_cost', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }

        } else {
            //如果包含返佣 且包含其他 优先级变更为其他
            if (in_array($commission, $amount_detail_arr) && count($amount_detail_arr) > 1) {
                $check_config_other = ['other'];
                $check_config       = array_merge($check_config, $check_config_other);
                $key               = array_search('commission', $check_config);
                if (isset($key)) {
                    unset($check_config[$key]);
                }
            }
            //v16993需求 泰国 flashHome  其他>返佣>水电费
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($model->cost_company_id, [$department_company_id['FlashHomeOperation'], $department_company_id['FlashHomeHolding']])) {
                //如果包含水电费 且包含其他 优先级变更为其他
                if (in_array($water_and_electricity, $amount_detail_arr) && count($amount_detail_arr) > 1) {
                    $check_config_other = ['other'];
                    $check_config       = array_merge($check_config, $check_config_other);
                    $key               = array_search('water_and_electricity', $check_config);
                    if (isset($key)) {
                        unset($check_config[$key]);
                    }
                }
            }
        }
        $flow_config_arr = [];
        foreach ($flow_config as $value_) {
            if (in_array($value_['subject_number'], $check_config)) {
                $flow_config_arr[] = $value_;
            }
        }
        $weight    = array_column($flow_config_arr, 'weight');
        $min_array = $flow_config_arr[array_search(min($weight), $weight)];

        $flow_id = $this->getSearchFlowId($model, $min_array, $country_code, $flow_config, $department_arr, $department_company_id);
        return $flow_id;
    }

    /**
     * 释放报销
     *
     * @param $id
     * @param $user
     */
    public function freeBudget($id, $user)
    {
        // 验证默认国家是否开启预算(0:关闭 1:打开)
        if (!(new EnumsService())->getBudgetStatus()) {
            return true;
        }
        //获取付款申请主表信息
        $main_data = OrdinaryPayment::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);

        //金额详情
        $amount_detail = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $id],
        ]);

        if(empty($main_data) || empty($amount_detail)){
            throw new BusinessException(static::$t->_('普通付款信息-释放预算-获取付款信息失败'), ErrCode::$ORDINARY_PAYMENT_GET_INFO_ERROR);
        }
        $main_data     =  $main_data->toArray();
        $amount_detail =  $amount_detail->toArray();

        // 兼容菲律宾/马来历史未开启预算的部分数据
        $state_date_time = '2022-01-01 00:00:00';
        $country_code = get_country_code();
        if (in_array($country_code,['MY','PH']) && $main_data['created_at'] < $state_date_time) {
            return true;
        }
        // 兼容印尼/越南历史未开启预算的部分数据  TODO 优化代码
        $state_date_time = '2022-03-31 00:00:00';
        if (in_array($country_code,['ID','VN']) && $main_data['created_at'] < $state_date_time) {
            return true;
        }

        //实际使用金额
        $freedAmount = [];
        foreach ($amount_detail as $detail) {
            if (isset($freedAmount[$detail['level_code']])) {
                $freedAmount[$detail['level_code']] += 0;
            } else {
                $freedAmount[$detail['level_code']] = 0;
            }
        }

        $budgetService = new BudgetService();
        $result = $budgetService->re_back_budget($main_data['apply_no'], $user, BudgetService::ORDER_TYPE_3, $freedAmount);
        $this->getDI()->get('logger')->info('ordinary_payment  释放预算判断 params ' . json_encode([
                $main_data['apply_no'],
                $user,
                BudgetService::ORDER_TYPE_3,
                $freedAmount
            ]) . ' results ' . json_encode([$result]));

        if ($result['code'] != ErrCode::$SUCCESS) {
            throw new ValidationException($result['message']);
        }

        return true;

    }


    /**
     * 校验普通付款AP(BJ)节点  必填
     * @Date: 6/20/22 10:56 PM
     * @param $can_edit
     * @param $update_data
     * @param $work_req
     * @param int $cost_company_id 扣费公司
     * @return bool :
     * @throws ValidationException
     * @author: peak pan
     */
    public function checkApBjNotEmpty($can_edit, $update_data, $work_req, $cost_company_id)
    {
        $department_company_id = EnumsService::getInstance()->getSysDepartmentCompanyIds(); //公司id配置
        $ap_bj_string = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_create_ap_bj_not_empty');
        $base_flow_arr = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_create_flow_config');

        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && in_array($cost_company_id, [$department_company_id['FlashHomeOperation'], $department_company_id['FlashHomeHolding']])) {
            $flow_arr = $base_flow_arr['FlashHome'];
        } else {
            $flow_arr = $base_flow_arr['FlashExpress'];
        }
        $item_ = [];
        foreach ($flow_arr  as $item){
            foreach ($item['store_flow_map'] as $value_map){
                $item_[]=$value_map;
            }
        }



        if(!empty($can_edit['amount_detail']) && empty(array_diff($can_edit['amount_detail'],$ap_bj_string['amount_detail'])) && in_array($work_req->flow_id,$item_)){

            $finance_category          = array_column($update_data, 'finance_category_id');
            $ledger_account         = array_column($update_data, 'ledger_account_id');
            $description          = array_column($update_data, 'voucher_description');
            foreach ($finance_category as $finance){
                if(empty($finance)){
                    throw new ValidationException(static::$t->_('finance_category_id_not_null'), ErrCode::$VALIDATE_ERROR);
                }
            }
            foreach ($ledger_account as $ledger){
                if(empty($ledger)){
                    throw new ValidationException(static::$t->_('ledger_account_id_not_null'), ErrCode::$VALIDATE_ERROR);
                }
            }
            foreach ($description as $item_value){
                if(trim($item_value)==""){
                    throw new ValidationException(static::$t->_('voucher_description_not_null'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        return true;
    }


    /**
     * 普通付款查找审批流id
     * @param object $model 普通付款订单对象
     * @param array $min_array 处理优先级之后的普通付款审批流配置数据
     * @param string $country_code 国家
     * @param array $flow_config 普通付款审批流配置
     * @param array $department_arr 部门id配置
     * @param array $department_company_id 各国公司id配置
     * @return  int
     **/
    public function getSearchFlowId(object $model, array $min_array, string $country_code, array $flow_config, array $department_arr, array $department_company_id)
    {
        $find_key = '';
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            if ($model->apply_store_id != Enums::PAYMENT_HEADER_STORE_ID) {
                if ($model->apply_sys_department_id == $department_arr['hub']) {
                    $find_key = 'hub';
                } elseif ($model->apply_sys_department_id == $department_arr['network']) {
                   $find_key = 'network';
                }
            }
        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($model->cost_company_id, [$department_company_id['FlashHomeOperation'], $department_company_id['FlashHomeHolding']])) {
            $find_key = 'headquarters';
        } else {
            $data = SysStoreModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' =>
                    ['id' => $model->apply_store_id]
            ]);
            if ($model->apply_store_id == Enums::PAYMENT_HEADER_STORE_ID) {
                //总部
                $flow_id = $min_array['store_flow_map']['headquarters'];
                return $flow_id;
            } else {
                switch ($country_code) {
                    case GlobalEnums::LA_COUNTRY_CODE:
                        $ordinaryPaymentArr = OrdinaryPaymentEnums::FLOW_ID_LA_ARRAY_KEY;
                        break;
                    case GlobalEnums::TH_COUNTRY_CODE:
                        $ordinaryPaymentArr = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                        break;
                    case GlobalEnums::PH_COUNTRY_CODE:
                        $ordinaryPaymentArr = OrdinaryPaymentEnums::FLOW_ID_PH_ARRAY_KEY;
                        break;
                    default:
                        $ordinaryPaymentArr = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                }
                foreach ($ordinaryPaymentArr as $key => $value) {
                    if (in_array($data->category, $value)) {
                        $find_key = $key;
                    }
                }
            }
        }

        if (empty($find_key)) {
            $find_key = 'headquarters';//终极兜底 如果四项Hub、Network、Shop、Network Bulky  都没找到 即为总部
        }
        $flow_id = $min_array['store_flow_map'][$find_key];
        if (empty($flow_id)) {
            //当前部门兜底
            $other_key = array_search('other', array_column($flow_config, 'subject_number'));
            $flow_id   = $flow_config[$other_key]['store_flow_map'][$find_key];
        }
        return $flow_id;
    }

    /**
     * 审批流创建必备参数检测
     *
     * 说明:
     * 1. 明细行的预算科目(付款分类)校验, 不同审批流所需
     *
     * @param $country_code
     * @param $company_ids
     * @param $biz_data
     * @return array|mixed
     * @throws ValidationException
     */
    protected function workflowParamsCheck($country_code, $company_ids,  $biz_data)
    {
        $apply_check = [];
        // 1. 明细行的预算科目(付款分类)校验, 不同审批流所需
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($biz_data['cost_company_id'], [$company_ids['FlashExpress'], $company_ids['FlashHomeOperation'], $company_ids['FlashHomeHolding']])) {
            $apply_check = OrdinaryPaymentAddService::getInstance()->addApplyCheck($biz_data, $biz_data['apply_store_id']);
        } else if (in_array($country_code, [GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && $biz_data['cost_company_id'] == $company_ids['FlashExpress']) {
            $apply_check = OrdinaryPaymentAddService::getInstance()->addApplyCheck($biz_data, $biz_data['apply_store_id']);
        } else if ($country_code == GlobalEnums::LA_COUNTRY_CODE && $biz_data['cost_company_id'] == $company_ids['FlashLaos']) {
            $apply_check = OrdinaryPaymentAddService::getInstance()->addApplyCheck($biz_data);
        } else {
            //网点下  network shop  hub 下校验审批流不可以交叉
            //内部同样走了一遍此逻辑后期需要优化
            if (Enums::PAYMENT_COST_STORE_TYPE_02 == $biz_data['cost_store_type']) {
                //网点 审批流
                $store = ((new WorkflowServiceV2()))->getStoreById($biz_data['apply_store_id'] ?? '');

                // network shop  hub
                if (isset($store['category']) && in_array($store['category'], [1, 2, 4, 5, 7, 8, 9, 10, 12])) {
                    $check_payment_data = OrdinaryPaymentAddService::getInstance()->check_payment($biz_data['amount_detail']);
                    if (!is_array($check_payment_data)) {
                        throw new ValidationException($check_payment_data, ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        }

        return $apply_check;
    }

    /**
     * V21975 - 普通付款- 区分是否关联预提单 - 撤回、驳回、未支付 ｜｜ 支付模块-未支付操作
     * @param object $ordinary_payment 普通付款对象
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function removeBindBudgetWithholdingInfo(object $ordinary_payment, array $user)
    {
        //关联预提编号
        if (!empty($ordinary_payment->budget_withholding_id) && in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            //则判断提交时是否占用预算，如果未占用预算则无需释放预算，如果已占用预算，则释放提单时占用的预算金额
            $detail_info = BudgetDetail::findFirst(['conditions' => 'order_no = :order_no: and type = :order_type:', 'bind' => ['order_no' => $ordinary_payment->apply_no, 'order_type' => BudgetService::ORDER_TYPE_3]]);
            if ($detail_info) {
                //释放占用的金额
                $this->freeBudget($ordinary_payment->id, $user);
            }

            //普通付款 - 关联预提单 - 变更预提单使用金额、使用状态、预提单-费用明细-按照成本中心变更使用金额
            $budget_withholding_info = BudgetWithholdingService::getInstance()->getBudgetWithholdingInfoById($ordinary_payment->budget_withholding_id);
            BudgetWithholdingService::getInstance()->updateAmount($ordinary_payment, $budget_withholding_info, 'decr');
        } else {
            //当前未关联预提编号，则保留原有预算释放逻辑
            $this->freeBudget($ordinary_payment->id, $user);
        }
        return true;
    }
}
