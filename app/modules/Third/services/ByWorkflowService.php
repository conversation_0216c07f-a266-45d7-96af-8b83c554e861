<?php
/**
 * by审批流服务层
 */
namespace App\Modules\Third\Services;

use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;

class ByWorkflowService extends BaseService
{
    //by审批流相关url配置组
    public static $url_config = [
        'add' => 'SystemExternalApprovalAdd',//创建审批申请接口
        'edit' => 'SystemExternalApprovalEdit',//创建审批重新申请接口
        'audit' => 'SystemExternalApprovalUpdate',//审批接口
        'log' => 'SystemExternalApprovalGetLog',//审批日志详情接口
        'list' => 'SystemExternalApprovalGetList',//获取待审批列表以及count接口
        'pending' => 'SystemExternalGetStaffPendingBySerialNo', //获取审批状态
        'send_cc' => 'messageReminder',//抄送
        'pending_count' => 'SystemExternalApprovalGetPendingCount', // 获取指定人全部待审批数
    ];

    /**
     * 创建审批
     * @param array $params 参数组
     * @return array
     * @throws ValidationException
     */
    public function add($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['add'], static::$language);
        $ac->setParams(
            [
                [
                    'submitter_id' => $params['submitter_id'],
                    'summary_data' => $params['summary_data'],
                    'biz_type' => $params['biz_type'],
                    'audit_params' => $params['audit_params']
                ]
            ]
        );
        $res = $ac->execute();
        $code = $res['result']['code'] ?? 0;
        $data = $res['result']['data'] ?? [];
        if ($code != ErrCode::$SUCCESS || empty($data) || empty($data['serial_no'])) {
            throw new ValidationException($res['result']['msg'] ?? static::$t->_('by_workflow_operate_failed'), ErrCode::$VALIDATE_ERROR);
        }
        return $data;
    }

    /**
     * 创建审批重新申请接口
     * @param array $params 参数组
     * @return array
     * @throws ValidationException
     */
    public function edit($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['edit'], static::$language);
        $ac->setParams(
            [
                [
                    'serial_no'    => $params['serial_no'], // 第一次创建时返回的申请编号
                    'submitter_id' => $params['submitter_id'],
                    'summary_data' => $params['summary_data'],
                    'biz_type'     => $params['biz_type'],
                    'audit_params' => $params['audit_params'],
                ]
            ]
        );
        $res = $ac->execute();
        $code = $res['result']['code'] ?? 0;
        $data = $res['result']['data'] ?? [];
        if ($code != ErrCode::$SUCCESS || empty($data) || empty($data['serial_no'])) {
            throw new ValidationException($res['result']['msg'] ?? static::$t->_('by_workflow_operate_failed'), ErrCode::$VALIDATE_ERROR);
        }
        return $data;
    }

    /**
     * 审批
     * @param array $params 查询日志参数组
     * @throws ValidationException
     * @return string
     */
    public function audit($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['audit'], static::$language);
        $ac->setParams(
            [
                [
                    'serial_no'   => $params['serial_no'],   //审批编号
                    'biz_type'    => $params['biz_type'],    //审批类型
                    'reason'      => $params['reason'],      //审批原因
                    'status'      => $params['status'],      //操作  2 同意  3 驳回   4 撤销
                    'operator_id' => $params['operator_id'], //审批人
                    'is_force'    => $params['is_force'] ?? 0//是否跳过权限验证强制审批1是
                ]
            ]
        );
        $res = $ac->execute();
        $code = $res['result']['code'] ?? 0;
        $data = $res['result']['data'] ?? '';
        if ($code == ErrCode::$SUCCESS) {
            return $data;
        }
        //非审批人要审批的数据进行非法审批或者是审批人已经完成了审批
        if ($code == 1005) {
            $res['result']['msg'] = static::$t->_('by_workflow_no_auth_or_audited');
        }
        throw new ValidationException($res['result']['msg'] ?? static::$t->_('by_workflow_operate_failed'), ErrCode::$VALIDATE_ERROR);
    }

    /**
     * 获取待审批列表以及count
     * @param array $params 查询日志参数组
     * @return array
     */
    public function getList($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['list'], static::$language);
        $ac->setParams(
            [
                [
                    "serial_no" => $params['serial_no'],   //审批编号
                    "approval_start_date" => "",// 开始日期
                    "approval_end_date" => '',//审批结束日期
                    "submitter_id" => '',//申请人ID
                    "biz_type" => $params['biz_type'],  //类型
                    "approval_id" => $params['approval_id'],  //审批人id
                    "state" => $params['state'],//审批状态
                    'sort' => $params['sort'] ?? 2,// 1=序列号倒序，2=序列号正序，3=创建时间倒序，4=创建时间正序，不传递默认序列号正序
                    "page_num" => $params['page_num'],//页码
                    "page_size" => $params['page_size']//每页条数
                ]
            ]
        );
        $res = $ac->execute();
        return $res['result']['data'] ?? [];
    }

    /**
     * 获取审批日志详情
     * @param array $params 查询日志参数组
     * @return array
     */
    public function log($params)
    {
        $version = $params['version'] ?? 2;
        $ac = new ApiClient('by', '', self::$url_config['log'], static::$language);
        $ac->setParams(
            [
                [
                    'serial_no'   => $params['serial_no'],    //审批编号
                    'biz_type'    => $params['biz_type'],     //类型
                    'operator_id' => $params['operator_id'],  //查看数据的人
                    'version'     => "v{$version}",
                ]
            ]
        );
        $res = $ac->execute();
        return $res['result']['data'] ?? [];
    }


    /**
     * 根据序列号获取审批审批状态
     * @param array $params 查询参数组
     * @return array
     */
    public function pending($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['pending'], static::$language);
        $ac->setParams(
            [
                [
                    'approval_id'=> $params['approval_id'], //审批人
                    'audit_type' => $params['audit_type'], //审批类型
                    'serial_no' => $params['serial_no']    //workflow_no 数组
                ]
            ]
        );
        $res = $ac->execute();
        return $res['result'] ?? [];
    }

    /**
     * 发送抄送
     * @param array $params 参数组
     * @return array
     * @throws ValidationException
     */
    public function sendCC($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['send_cc'], static::$language);
        $ac->setParams(
            [
                [
                    'staff_info_id' => $params['staff_info_id'],//抄送人
                    'remind_title' => $params['remind_title'], //抄送标题
                    'remind_content' => $params['remind_content'],//抄送内容
                    'audit_type' => $params['audit_type'],//审批类型
                    'serial_no' => $params['serial_no'],//by审批流-流水号
                    'message_category' => $params['message_category'],//发送消息类型
                ]
            ]
        );
        $res = $ac->execute();
        $code = $res['result']['code'] ?? 0;
        $data = $res['result']['data'] ?? [];
        if ($code != ErrCode::$SUCCESS) {
            throw new ValidationException($res['result']['msg'] ?? static::$t->_('by_workflow_operate_failed'), ErrCode::$VALIDATE_ERROR);
        }
        return $data;
    }

    /**
     * 获取用户某些业务的待审批数
     * @param array $params 查询参数组
     * @return array
     */
    public function pendingCount($params)
    {
        $ac = new ApiClient('by', '', self::$url_config['pending_count'], static::$language);
        $ac->setParams([
            [
                'approval_id'=> $params['approval_id'], //审批人
                'biz_type' => is_array($params['biz_type']) ? $params['biz_type'] : [$params['biz_type']], //审批类型, 数组类型
            ]
        ]);

        $res = $ac->execute();
        $pending_count_item = $res['result']['data']['pending_count'] ?? [];
        return !empty($pending_count_item) ? array_column($pending_count_item, 'count', 'audit_type') : [];
    }

    /**
     * 待审批数据-按照by待审核的排序好数据映射oa侧业务数据，目的是oa侧业务数据也按照by侧审批数据排序进行排序；按照by的审批序列号映射
     * @param array $by_list_sort_data by待审批的数据
     * @param array $oa_need_sort_data oa待审批的数据
     * @return array
     */
    public function pendingDataSort($by_list_sort_data, $oa_need_sort_data)
    {
        if (empty($by_list_sort_data) || empty($oa_need_sort_data)) {
            return [];
        }

        $oa_need_sort_data = array_column($oa_need_sort_data, null, 'workflow_no');
        $oa_sort_data = [];
        foreach ($by_list_sort_data as $item) {
            $oa_sort_data[] = $oa_need_sort_data[$item['serial_no']];
        }
        return $oa_sort_data;
     }

}
