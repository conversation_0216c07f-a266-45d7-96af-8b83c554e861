<?php
namespace App\Modules\Third\Services;

use App\Library\BaseController;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Models\oa\PaymentOnlinePayModel;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\FinalPayService;
use App\Modules\Pay\Services\FlashPayStatusUpdateService;
use App\Modules\Pay\Services\FlashPayApiUpdateParams;
use App\Modules\Third\Services\OaExternalApiService;
use App\Modules\User\Services\UserService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

/**
 * FlashPay在线支付服务层
 * Class FlashPayService
 * @package App\Modules\Pay\Services
 */
class FlashPayService extends BaseService
{
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return FlashPayService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 在线支付-FlashPay-异步通知回调接口
     * @var array
     */
    public static $validate_pay_trade_sync = [
        'appKey' => 'Required|StrLenGeLe:1,32',//FlashPay 颁发的APP_KEY
        'charset' => 'Required|StrEqI:UTF-8',//请求使用的编码格式，默认且只支持 UTF-8
        'signType' => 'Required|StrEqI:RSA2',//签名类型，默认且只支持RSA2(SHA256withRSA)
        'sign' => 'Required|StrLenGe:1',//签名，不参与签名
        'time' => 'Required|DateTime',//时间字符串，格式：yyyy-MM-dd HH:mm:ss
        'version' => 'Required|StrEq:1.0',//调用的接口版本，目前固定为：1.0
        'data' => 'Required|Obj',//数据体
        'data.outTradeNo' => 'Required|StrLenGeLe:1,32',//商户订单号，32 个字符以内
        'data.tradeNo' => 'Required|StrLenGeLe:1,32'//FlashPay 交易号
    ];

    /**
     * 组装FlashPay公共请求参数
     * @param string $app_key APP Key
     * @return array
     */
    public function getPublicRequestParams($app_key)
    {
        return [
            'appKey' => $app_key,//FlashPay颁发的APP_KEY
            'charset' => 'UTF-8',//请求使用的编码格式，默认且只支持UTF-8
            'sign' => '',//签名，不参与签名
            'signType' => 'RSA2',//签名类型，默认且只支持RSA2(SHA256withRSA)
            'time' => date('Y-m-d H:i:s'),//时间字符串，格式：yyyy-MM-dd HH:mm:ss
            'version' => '1.0',//调用的接口版本，目前固定为：1.0
            'data' => null//具体每个接口的实际参数结构体
        ];
    }

    /**
     * 组装FlashPay公共响应参数
     * @return array
     */
    public function getPublicResponseParams()
    {
        return [
            'code' => 0,//网关码(0：成功，只表达请求成功，非业务成功)
            'message' => '成功',//网关返回码描述
            'sign' => '',//签名，不参与签名
            'data' => ''//具体每个接口的实际参数结构体
        ];
    }

    /**
     * 代付 API
     * @地址 https://flashexpress.feishu.cn/file/boxcnvUm939J1tVu2C308jgnDAd
     * @param array $flash_pay_create_order 参数组
     * @param array $flash_pay_config 支付单据中各费用所属公司配置组
     * @return mixed
     * @throws Exception
     */
    public function transfer($flash_pay_create_order, $flash_pay_config)
    {
        try {
            //公共的请求参数
            $params = $this->getPublicRequestParams($flash_pay_config['app_key']);
            //实际的请求参数组
            $params['data'] = $flash_pay_create_order;
            //调取FlashPay首先要生成签名
            $params['sign'] = FlashPaySupportService::getInstance()->generateSign($params, $flash_pay_config['merchant_private_key']);
            $json_params = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $this->logger->info('FlashPay transfer request_params is: [' . $json_params . ']');
            $header[] = 'Content-type: application/json;charset=utf-8';
            $header[] = 'Accept: application/json';
            $header[] = 'Accept-Language: ' . static::$language;
            $res_data = curl_request(env('flash_pay') . '/fund/trans/transfer', $json_params, 'POST', $header);
            $this->logger->info('FlashPay transfer response_result is: [' . $res_data . ']，outTradeNo [' . $params['data']['outTradeNo'] . ']');

            $res_data_arr = json_decode($res_data, true);
            if (!isset($res_data_arr['code'])) {
                //调取创建订单接口异常
                throw new Exception('flash pay create order request failed');
            }

            //解析接口返回首先要进行验签
            if (isset($res_data_arr['sign'])) {
                $verify = FlashPaySupportService::getInstance()->verifySign($res_data_arr, $flash_pay_config['flashpay_public_key']);
                if ($verify) {
                    return $res_data_arr;
                } else {
                    //验签未通过
                    throw new Exception('flash pay sign is invalid');
                }
            } else {
                return $res_data_arr;
            }

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取pay在线支付人
     * @return array
     */
    public function getPayer()
    {
        //从pay_module_payer设置中获取第一个三级支付人作为驳回人
        $payer_arr = EnumsService::getInstance()->getPayModulePayer();
        return array_shift($payer_arr);
    }

    /**
     * pay支付失败的单据需要驳回到一级支付人
     * @param object $payment 支付单据对象信息
     * @param array $user_id 三级支付人用户id
     * @throws BusinessException
     */
    public function reject($payment, $user_id)
    {
        return FinalPayService::getInstance()->flashPayFailedReject($payment, $user_id);
    }

    /**
     * 查询交易结果
     * @地址 https://flashexpress.feishu.cn/file/boxcnvUm939J1tVu2C308jgnDAd
     * @param array $get_payment_result_params 查询交易结果参数组
     * @param array $pay_config 支付配置信息
     * @return bool|mixed|string
     * @throws Exception
     */
    public function getPaymentResult($get_payment_result_params, $pay_config)
    {
        try {
            //公共的请求参数
            $params = $this->getPublicRequestParams($pay_config['app_key']);
            //实际的请求参数组
            $params['data'] = $get_payment_result_params;
            //调取FlashPay首先要生成签名
            $params['sign'] = FlashPaySupportService::getInstance()->generateSign($params, $pay_config['merchant_private_key']);
            $json_params = json_encode($params,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            $header[] = 'Content-type: application/json;charset=utf-8';
            $header[] = 'Accept: application/json';
            $header[] = 'Accept-Language: ' . static::$language;
            $this->logger->info('FlashPay getPaymentResult request_params is:【' . $json_params . '】');
            $res_data = curl_request(env('flash_pay').'/fund/trans/get-transfer-result', $json_params, 'POST', $header);
            $this->logger->info('FlashPay getPaymentResult response_result is:【' . $res_data . '】');
            $res_data_arr = json_decode($res_data, true);
            if (!isset($res_data_arr['code'])) {
                //调取查询交易结果接口异常
                throw new Exception('flash pay payment result request failed');
            }
            //解析接口返回首先要进行验签
            $verify = FlashPaySupportService::getInstance()->verifySign($res_data_arr, $pay_config['flashpay_public_key']);
            if ($verify) {
                return $res_data_arr;
            } else {
                //验签未通过
                throw new Exception('flash pay sign is invalid');
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 保存支付信息
     *
     * @param $payment_id --支付单据id
     * @return PaymentOnlinePayModel|bool
     * @throws Exception
     */
    public function savePayDb($flash_pay_method,$payment_id,$params = [])
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询库里是否已被其他任务执行
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $payment_id],
                'for_update' => true,
            ]);
            //未找到或者已被其它任务已执行
            if (empty($payment) || $payment->out_send_status != PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO) {
                $log = '支付前判断捞取的支付单据是否已被其它任务已执行，非未传输 ' . (!empty($payment) ? $payment->no : '或未找到') . '直接跳过';
                throw new ValidationException($log, ErrCode::$VALIDATE_ERROR);
            }
            //是否为pay支付中
            if ($payment->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                $log = 'pay_status状态异常 pay_status is ' .$payment->pay_status ;
                throw new ValidationException($log, ErrCode::$VALIDATE_ERROR);
            }
            if ($flash_pay_method != $payment->flash_pay_method) {
                throw new ValidationException('flash_pay_method is not match '.$payment->no, ErrCode::$VALIDATE_ERROR);
            }

            $now_time                  = time();
            $zero_time                 = gmdate('Y-m-d H:i:s', $now_time);                                       //0时区
            $business_time             = date('Y-m-d H:i:s', $now_time);                                         //业务时间
            $payment->out_send_status  = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT;                          //发送状态待反馈
            $payment->out_send_at      = $business_time;                                                         //发送时间
            $payment->updated_at       = $zero_time;                                                             //更新时间需要0时区存储
            $payment->out_batch_number = $params['out_batch_number'] ?? null;                                   //更新时间需要0时区存储
            $bool                      = $payment->save();
            if ($bool === false) {
                $log = 'savePayDb payment->save失败,' . get_data_object_error_msg($payment) . ';data=' . json_encode($payment->toArray(),
                        JSON_UNESCAPED_UNICODE);
                throw new BusinessException($log, ErrCode::$BUSINESS_ERROR);
            }

            //每笔单据交易流水号
            $out_trade_no = $payment->no . get_generate_random_string();
            //记录支付模块关联在线支付交易流水
            $payment_online_pay_data = [
                'payment_id'     => $payment->id,
                'out_trade_no'   => $out_trade_no,
                'trade_no'       => '',
                'trade_time'     => null,
                'payment_amount' => 0,
                'cur'            => '',
                'code'           => null,
                'created_at'     => $business_time,
                'updated_at'     => $business_time,
            ];
            if (!empty($params['out_batch_number'])) {
                $payment_online_pay_data['out_batch_number'] = $params['out_batch_number'];
            }
            $payment_online_pay      = new PaymentOnlinePayModel();
            $bool                    = $payment_online_pay->i_create($payment_online_pay_data);
            if ($bool === false) {
                $log = 'savePayDb payment_online_pay->i_create 失败,' . get_data_object_error_msg($payment_online_pay) . ';data=' . json_encode($payment_online_pay_data,
                        JSON_UNESCAPED_UNICODE);
                throw new BusinessException($log, ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

            return $payment_online_pay;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * FlashPay在线支付异步回调
     * @param object $paymentModel 支付单据
     * @param array $pay_config 支付配置信息
     * @throws ValidationException
     * @throws Exception
     */
    public function syncFlashPayTradeStatus($paymentModel, $pay_config)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $paymentModel->id],
                'for_update' => true,
            ]);
            if (empty($payment)) {
                throw new Exception('payment not found ' . $paymentModel->id);
            }

            // 获得FlashPay的交易状态
            $payment_result = $this->getPaymentResult(['tradeNo' => $payment->out_trade_no], $pay_config);
            $payment_data = $payment_result['data'] ?? [];

            // 查询结果成功且支付状态为支付中时才处理
            if ($payment_result['code'] == 0 && $payment_data && $payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                // 创建API参数对象
                $params = new FlashPayApiUpdateParams($payment, $payment_data['tradeStatus'], $payment_data, $payment_result);

                // 使用通用的FlashPay支付状态更新服务
                FlashPayStatusUpdateService::getInstance()->updatePaymentStatus($params);

            } else {
                // 如果查询失败或状态不匹配，仍需要更新基本信息
                $zero_time = gmdate('Y-m-d H:i:s'); // 0时区
                $payment->out_trade_code = $payment_result['code']; // 外部交易code码
                $payment->updated_at = $zero_time; // 更新时间需要0时区存储
                $payment->save();
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 获取支付信息
     * @param string $out_trade_no 外部交易号，如FlashPay交易号
     * @return mixed
     */
    public function getPaymentInfo($out_trade_no)
    {
        return Payment::findFirst([
            'conditions' => 'out_trade_no = :out_trade_no: and is_online_pay = :is_online_pay:',
            'bind' => [
                'out_trade_no' => $out_trade_no,
                'is_online_pay' => PayEnums::IS_ONLINE_PAY_YES
            ]
        ]);
    }

    /**
     * 根据appKey获取支付配置信息
     * @param string $app_key appKey
     * @return mixed
     * @throws ValidationException
     */
    public function getPayOnlineConfig($app_key)
    {
        $pay_config = PaymentFlashPayConfigModel::findFirst([
            'columns' => 'app_key,merchant_private_key,flashpay_public_key',
            'conditions' => 'app_key = :app_key:',
            'bind' => ['app_key' => $app_key]
        ]);
        if (empty($pay_config)) {
            throw new ValidationException(static::$t->_('payment_pay_online_pay_config_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $pay_config->toArray();
    }

    /**
     * FlashPay在线支付异步回调
     * https://yapi.flashexpress.pub/project/133/interface/api/70172
     * @param array $params['outTradeNo'=>'商户订单号', 'tradeNo'=>'FlashPay交易号']
     * @return array
     * @throws Exception
     */
    public function payTradeNoSync($params)
    {
        $this->logger->info('FlashPay payTradeNoSync ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        try {
            //获取公共返回参数
            $response_to_pay = $this->getPublicResponseParams();

            //验证请求参数合法性
            Validation::validate($params, FlashPayService::$validate_pay_trade_sync);

            //先根据商户appKey去获取密钥
            $pay_config = $this->getPayOnlineConfig($params['appKey']);

            //针对FlashPay的请求要先进行验签
            $params['data']['paymentAmount'] = intval($params['data']['paymentAmount']);//金额数字化
            $params['data']['tradeStatus'] = intval($params['data']['tradeStatus']);//状态数字化
            $verify = FlashPaySupportService::getInstance()->verifySign($params, $pay_config['flashpay_public_key']);
            if ($verify) {
                $this->logger->info('FlashPay payTradeNoSync verify pass');
                //验签通过, 获取FlashPay 交易号
                $out_trade_no = (isset($params['data']) && $params['data']['tradeNo'] ?? '') ? $params['data']['tradeNo'] : '';
                if ($out_trade_no) {
                    $payment = $this->getPaymentInfo($out_trade_no);
                    if (!$payment) {
                        $this->logger->info('FlashPay payTradeNoSync cannot found flashpay_trade_no 1 [' . $out_trade_no . '],开始兜底方案');
                        //pay代付接口未拿到pay结果，外部交易发送状态仍是1待反馈会被重复支付，以下逻辑为了兜底避免重复支付
                        $handle_ret = $this->handleExceptionData($params['data']);
                        if ($handle_ret) {
                            $payment = $handle_ret;
                        } else {
                            $this->logger->notice(['FlashPay payTradeNoSync cannot found flashpay_trade_no 2' => [
                                'out_trade_no' => $out_trade_no,
                                'message' => '未找到符合条件的订单'
                            ]]);
                            //未找到满足条件的订单
                            $response_to_pay['code'] = ErrCode::$OUT_TRADE_NO_ERROR;
                            $response_to_pay['message'] = '未找到符合条件的订单';
                        }
                    }
                    if ($payment && $payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                        //pay支付中的订单，才做交易状态与订单状态的同步
                        $this->logger->info('FlashPay payTradeNoSync sync tradeStatus to orderStatus' . $out_trade_no);
                        $this->syncFlashPayTradeStatus($payment, $pay_config);
                    }
                } else {
                    //缺少必要参数传递
                    $response_to_pay['code'] = ErrCode::$OUT_TRADE_NO_ERROR;
                    $response_to_pay['message'] = '商户交易号缺失';
                }
            } else {
                $this->logger->info('FlashPay payTradeNoSync verify invalid');
                //验签未通过
                $response_to_pay['code'] = ErrCode::$VERIFY_FAILED;
                $response_to_pay['message'] = '验签失败';
            }
            $response_to_pay['sign'] = FlashPaySupportService::getInstance()->generateSign($response_to_pay, $pay_config['merchant_private_key']);
        }  catch (ValidationException $e) {
            $response_to_pay['code'] = ErrCode::$VALIDATE_ERROR;
            $response_to_pay['message'] = $e->getMessage();
        } catch (Exception $e) {
            $this->logger->warning('FlashPay payTradeNoSync' . $e->getMessage());
            $response_to_pay['code'] = ErrCode::$SYNC_FAILED;
            $response_to_pay['message'] = $e->getMessage();
        }
        return $response_to_pay;
    }

    /**
     * 支付回调异常情况处理
     * @param array $params_data 异步回调
     * @return bool
     */
    private function handleExceptionData($params_data)
    {
        //payment_online_pay表的out_trade_no
        $out_trade_no = $params_data['outTradeNo'] ? $params_data['outTradeNo'] : '';
        if (!empty($out_trade_no)) {
            $payment_no_arr = explode('_', $out_trade_no);
            $payment_no = $payment_no_arr[0] ?? '';//申请单号
            if (!empty($payment_no)) {
                $payment = Payment::findFirst([
                    'conditions' => 'no = :no:',
                    'bind' => ['no' => $payment_no],
                    'for_update' => true
                ]);
                if ($payment && $payment->out_send_status == PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT && $payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                    $trade_time = $params_data['tradeTime'] ?? null;//交易时间
                    $trade_no = $params_data['tradeNo'] ?? '';//pay交易流水号
                    $payment->out_trade_code = 0;//pay返回的code
                    $payment->out_trade_no = $trade_no;
                    $payment->out_trade_at = $trade_time;
                    $payment->updated_at = gmdate('Y-m-d H:i:s');//0时区
                    $bool = $payment->save();
                    if ($bool === false) {
                        $this->logger->warning('FlashPay payTradeNoSync out_trade_no [' . $out_trade_no . ']，同步pay交易流水号、交易日期失败; 原因可能是：' . get_data_object_error_msg($payment) . '; 参数组：' . json_encode(['out_trade_no' => $trade_no, 'out_trade_at' => $trade_time], JSON_UNESCAPED_UNICODE));
                        return false;
                    }
                    return $payment;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 获取所有FlashPay配置
     * @param string $pay_bank_account 支付账号
     * @return mixed
     */
    public function getAllFlashPayConfig(string $pay_bank_account='')
    {
        if ($pay_bank_account) {
            $condition = 'flashpay_sftp_shopname = :flashpay_sftp_shopname:';
            $bind      = ['flashpay_sftp_shopname' => $pay_bank_account];
        } else {
            $condition = '1 = :test:';
            $bind      = ['test' => 1];
        }

        // 获取所有FlashPay配置
        return PaymentFlashPayConfigModel::find(
            [
                'conditions' => $condition,
                'bind'       => $bind,
            ]
        )->toArray();

    }

    /**
     * 获取凭证
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function doPaymentVoucher($params): bool
    {
        if (empty($params['payment_id'])) {
            return false;
        }
        $payment = Payment::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['payment_id']],
        ]);
        if (empty($payment)) {
            return false;
        }
        if (!empty($payment->payment_voucher)) {
            return true;
        }
        $flash_pay_config = array_column($this->getAllFlashPayConfig($payment->pay_bank_account), null,
            'flashpay_sftp_shopname');
        return $this->makePaymentVoucher($payment, $flash_pay_config[$payment->pay_bank_account]);
    }


    /**
     * 获取凭证
     * @param $out_trade_no
     * @return array|true
     * @throws BusinessException
     * @throws Exception
     * @throws GuzzleException
     */
    public function batchDoPaymentVoucher($out_trade_no)
    {

        $created_at =  env('batch_get_voucher_created_at','2025-01-01 00:00:00');
        $condition =  "pay_status = :pay_status: AND is_online_pay = :is_online_pay: AND
         payment_voucher = '' AND created_at >= :created_at: AND out_trade_no != '' ";
        $bind = [
            'is_online_pay' => PayEnums::IS_ONLINE_PAY_YES,
            'pay_status'    => PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY,
            'created_at'    => $created_at,
        ];
        if (!empty($out_trade_no)) {
            $condition                .= " AND out_trade_no = :out_trade_no:";
            $bind['out_trade_no'] = $out_trade_no;
        }
        $payments = Payment::find([
            'conditions' => $condition,
            'bind'       => $bind,
        ]);
        if (empty($payments->toArray())) {
            return [];
        }

        $flashPayConfigMap = array_column($this->getAllFlashPayConfig(), null, 'flashpay_sftp_shopname');

        foreach ($payments as $payment) {
            $flash_pay_config = $flashPayConfigMap[$payment->pay_bank_account] ?? [];
            $this->makePaymentVoucher($payment,$flash_pay_config);
            $this->logger->info('FlashPay getPaymentVoucher success ! out_trade_no is  [' . $payment->out_trade_no . ']');
            sleep(1);
        }
        return true;
    }

    /**
     * 处理凭证
     * @param Payment $payment
     * @param $flash_pay_config
     * @return bool
     * @throws BusinessException
     * @throws GuzzleException
     */
    protected function makePaymentVoucher(Payment $payment,$flash_pay_config): bool
    {
        if (empty($flash_pay_config)) {
            $this->logger->error('FlashPay getPaymentVoucher cannot found flashpay config  cost_company_id is [' . $payment->cost_company_id . ']');
            return false;
        }
        $result_pay = $this->getPaymentVoucher(['tradeNo' => $payment->out_trade_no], $flash_pay_config);
        if (empty($result_pay['data']['voucherUrl'])) {
            $this->logger->error('FlashPay getPaymentVoucher voucherUrl is empty ! out_trade_no is  [' . $payment->out_trade_no . ']');
            return false;
        }
        $file_name = $payment->out_trade_no . '.pdf';
        $result    = OssHelper::uploadFile($result_pay['data']['voucherUrl'], 'paymentVoucher', $file_name);
        if (empty($result['object_url'])) {
            $this->logger->error('FlashPay getPaymentVoucher upload oss  failed ! out_trade_no is  [' . $payment->out_trade_no . ']');
            return false;
        }
        return $payment->i_update(['payment_voucher' => $result['object_url']]);

    }

    /**
     * 获取代付凭证
     * @param array $queryData 查询参数组
     * @param array $flash_pay_config 支付配置信息
     * @return mixed
     * @throws Exception
     */
    private function getPaymentVoucher(array $queryData, array $flash_pay_config)
    {
        //公共的请求参数
        $params = $this->getPublicRequestParams($flash_pay_config['app_key']);
        //实际的请求参数组
        $params['data'] = $queryData;
        //调取FlashPay首先要生成签名
        $params['sign'] = FlashPaySupportService::getInstance()->generateSign($params,
            $flash_pay_config['merchant_private_key']);
        $json_params    = json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $this->logger->info('FlashPay get-transfer-voucher request_params is: [' . $json_params . ']');
        $header[] = 'Content-type: application/json;charset=utf-8';
        $header[] = 'Accept: application/json';
        $header[] = 'Accept-Language: ' . static::$language;
        $res_data = curl_request(env('flash_pay') . '/fund/trans/get-transfer-voucher', $json_params, 'POST', $header);
        $this->logger->info('FlashPay get-transfer-voucher response_result is: [' . $res_data . ']，tradeNo [' . $params['data']['tradeNo'] . ']');

        $res_data_arr = json_decode($res_data, true);
        if (!isset($res_data_arr['code'])) {
            throw new Exception('flash pay create get-transfer-voucher request failed');
        }

        //解析接口返回首先要进行验签
        if (isset($res_data_arr['sign'])) {
            $verify = FlashPaySupportService::getInstance()->verifySign($res_data_arr,
                $flash_pay_config['flashpay_public_key']);
            if ($verify) {
                return $res_data_arr;
            } else {
                //验签未通过
                throw new Exception('flash pay sign is invalid');
            }
        } else {
            return $res_data_arr;
        }
    }

}
