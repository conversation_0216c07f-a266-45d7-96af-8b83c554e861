<?php
/**
 * Created by PhpStorm.
 * Date: 2023/8/17
 * Time: 11:37
 */

namespace App\Modules\Third\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\oa\RequestKingDeeModel;
use App\Modules\Reimbursement\Services\DetailService as ReimbursementDetailService;
use App\Util\RedisKey;

/**
 * 金碟财务系统对接服务层
 * Class KingDeeService
 *
 */
class KingDeeService extends BaseService
{
    private $session_id;

    private $account_id; //账户号
    private $user_id;//用户名
    private $app_key;//密钥key
    private $app_secret;//密钥
    private $url;//地址


    private static $instance;

    private function __construct()
    {
        $this->account_id = env('kingdee_account_id');
        $this->user_id    = env('kingdee_user_id');
        $this->app_key    = env('kingdee_app_key');
        $this->app_secret = env('kingdee_app_secret');
        $this->url        = env('kingdee_url');
        $this->session_id = $this->getSessionId();
    }

    private function __clone()
    {
    }

    /**
     * @return KingDeeService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取金碟session id
     * */
    protected function getSessionId()
    {
        try {

            $cache_key    = RedisKey::KING_DEE_SESSION_KEY;
            $session_data = $this->getCache($cache_key);

            if (empty($session_data)) {
                $data = ['parameters' => [
                    $this->account_id,
                    $this->user_id,
                    $this->app_key,
                    $this->app_secret,
                    '2052'
                ]];

                $response = curl_request($this->url . '/k3cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginByAppSecret.common.kdsvc', json_encode($data, JSON_UNESCAPED_UNICODE), 'POST');

                $response = json_decode($response, true);
                if (isset($response['LoginResultType']) && $response['LoginResultType'] == 1) {
                    $session_data = $response['KDSVCSessionId'] ?? '';
                    if (!empty($session_data)) {
                        $this->setCache($cache_key, $session_data, 600);
                    }
                } else {
                    $this->logger->warning('获取金碟session_id 失败,response:' . json_encode($response, JSON_UNESCAPED_UNICODE));
                }

            }

        } catch (\Exception $e) {
            $this->logger->warning('获取金碟session_id 失败');
        }

        return $session_data ?? '';

    }

    /**
     * 同步供应商数据
     * 到金碟系统
     * @param $params
     */
    public function syncVendorData($params)
    {

        try {
            $data = [
                'formid' => 'BD_Supplier',
                'data'   => [
                    'IsAutoAdjustField'    => true,
                    'IsAutoSubmitAndAudit' => true,
                    'Model'                => [
                        'FSupplierId'    => 0,//目前新增和编辑都为0
                        'FCreateOrgId'   => [
                            'FNumber' => $params['organization_code']
                        ],
                        'FNumber'        => $params['vendor_id'],
                        'FUseOrgId'      => [
                            'FNumber' => $params['organization_code'],
                        ],
                        'FName'          => $params['vendor_name'],
                        'FGroup'         => [
                            'FNumber' => 300,
                        ],
                        'FallocateOrgId' => [
                            'FNumber' => $params['assigned_organization_code']
                        ]
                    ]
                ]
            ];


            $this->logger->info('供应商同步金碟request参数' . json_encode($data, JSON_UNESCAPED_UNICODE));

            $response = curl_request($this->url . '/k3cloud/Kingdee.BOS.WebAPI.ServiceExtend.ServicesStub.CustomBusinessService.SaveBaseInfo.common.kdsvc', json_encode($data, JSON_UNESCAPED_UNICODE), 'POST', ['content-type: application/json', 'kdservice-sessionid: ' . $this->session_id]);
            $this->logger->info('供应商同步金碟response' . $params['vendor_id'] . '=========' . $response);

            $response = (json_decode($response, true))['Result'];

            if (!$response['ResponseStatus']['IsSuccess']) {
                $this->logger->warning('供应商同步金碟失败params' . json_encode($params, JSON_UNESCAPED_UNICODE) . 'response:' . json_encode($response, JSON_UNESCAPED_UNICODE));
            }

        } catch (\Exception $e) {
            $this->logger->error('供应商同步金碟失败' . $e->getTraceAsString() . 'params' . json_encode($params, JSON_UNESCAPED_UNICODE));
        }

        return $response ?? [];


    }

    /**
     * 应付
     * @param array $params 参数组
     * @param integer $is_cancel 应付（付款）是否取消支付：0否（正向），1是（反向）
     * @return array
     */
    public function payable($params, $is_cancel = KingDeeEnums::IS_CANCEL_PAY_NO)
    {
        try {
            $data = [
                'formid' => 'AP_Payable',
                'data' => [
                    'NeedUpDateFields' => [],
                    'NeedReturnFields' => [],
                    'IsDeleteEntry' => 'true',
                    'SubSystemId' => '',
                    'IsVerifyBaseDataField' => 'false',
                    'IsEntryBatchFill' => 'true',
                    'ValidateFlag' => 'true',
                    'NumberSearch' => 'true',
                    'InterationFlags' => '',
                    'IgnoreInterationFlag' => '',
                    'IsControlPrecision' => 'false',
                    'ValidateRepeatJson' => 'false',
                    'IsAutoSubmitAndAudit' => 'true',
                    'IsAutoAdjustField' => 'true',
                    'Model' => [
                        'FID' => 0,//单据内码
                        'FBillNo' => $params['bill_no'],//应付单编号
                        'FBillTypeID' => [//单据类型
                            'FNUMBER' => 'YFD01_SYS',
                        ],
                        'FDATE' => $params['apply_date'],//业务日期
                        'FENDDATE_H' => $params['expire_date'],//到期日=当前日期+180天
                        'FSUPPLIERID' => [//供应商编码
                            'FNumber' => $params['vendor_id'],
                        ],
                        'FCURRENCYID' => [//币别
                            'FNumber' => $params['currency'] ? static::$t->_(GlobalEnums::$currency_item[$params['currency']]) : '',
                        ],
                        'FPayConditon' => [//付款条件
                            'FNumber' => '',
                        ],
                        'FSETTLEORGID' => [//结算组织
                            'FNumber' => $params['sap_company_id'],
                        ],
                        'FAP_Remark' => '',//备注
                        'FPURCHASEDEPTID' => [//采购部门
                            'FNumber' => '',
                        ],
                        'F_SPPJ_YWBH' => $params['no'],//业务编号
                        'F_SPPJ_YWLX' => [
                            'FNUMBER' => $params['business_type_no'] ?? '',
                        ],
                        'FOrderDiscountAmountFor' => 0,
                        'FALLAMOUNTFOR' => ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) ? bcsub(0,  $params['amount'], 2) : $params['amount'],//折前价税合计
                        'F_SPPJ_SSWD' => $params['cost_store_name'],//所属网点
                        'F_SPPJ_CDJK' => $params['is_offset_loan'],//是否冲抵借款
                        'F_SPPJ_CDJE' =>  ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) ? bcsub(0,  $params['loan_amount'], 2) : $params['loan_amount'],//冲抵金额
		            ]
	            ]
            ];
            if (!empty($params['details'])) {
                //计价数量
                foreach ($params['details'] as $detail) {
                    $data['data']['Model']['FEntityDetail'][] = [
                        'F_SPPJ_XM' => [
                            'FNUMBER' => $detail['company_project'] ?? '',
                        ],//项目
                        'F_SPPJ_ZY' => $detail['abstract'],//摘要
                        'F_SPPJ_CPMC' => '',//产品名称
                        'F_SPPJ_CPBM' => '',//产品编码
                        'F_SPPJ_FYXMFZ' => [//费用项目分组
                            'FNUMBER' => '',
                        ],
                        'FMATERIALID' => [//物料编码
                            'FNumber' => '9999',
                        ],
                        'FCOSTDEPARTMENTID' => [//费用承担部门
                            'FNumber' => $detail['cost_center_code'],
                        ],
                        'FPriceQty' => $detail['quantity'],//计价数量
                        'FPrice' => $detail['tax_not_price'],//不含税单价
                        'FNoTaxAmountFor_D' => ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) ? bcsub(0, $detail['tax_not'], 2) : $detail['tax_not'],//不含税金额
                        'FTaxPrice' => $detail['amount_price'],//含税单价
                        'FALLAMOUNTFOR_D' => ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) ? bcsub(0, $detail['amount'], 2) : $detail['amount'],//含税金额价税合计
                        'FEntryTaxRate' => $detail['rate'],//税率
                        'FTAXAMOUNTFOR_D' => ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) ? bcsub(0, $detail['tax'], 2) : $detail['tax'],//税额
                        'FIsFree' => false,//是否赠品
                        'F_SPPJ_KM' => [//核算科目编码
                            'FNUMBER' => $detail['subjects_code'],
                        ],
                        'F_SPPJ_FYFL' => '',//费用明细
                        "F_SPPJ_FYFZ" => '',//费用分组
                    ];
                }
            }

            $this->logger->info('kingdee_payable ' . $params['no'] . ' request_data:' . json_encode($data, JSON_UNESCAPED_UNICODE));
            $response = curl_request($this->url . '/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc', json_encode($data, JSON_UNESCAPED_UNICODE), 'POST', ['content-type: application/json', 'kdservice-sessionid: ' . $this->session_id]);
            $this->logger->info('kingdee_payable ' . $params['no'] . ' response_data:' . $response);

            $response = (json_decode($response, true))['Result'];
            if (!isset($response['ResponseStatus']['IsSuccess'])) {
                $this->logger->notice('kingdee_payable failed' . json_encode($params, JSON_UNESCAPED_UNICODE) . 'response:' . json_encode($response, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $this->logger->warning('kingdee_payable exception:' . $e->getTraceAsString() . 'params' . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return $response ?? [];
    }

    /**
     * 付款
     * @param array $params 请求参数
     * @return array
     */
    public function paybill($params)
    {
        try {
            $data = [
                'formid' => 'AP_PAYBILL',
                'data' => [
                    'NeedUpDateFields' => [],
                    'NeedReturnFields' => [],
                    'IsDeleteEntry' => 'true',
                    'SubSystemId' => '',
                    'IsVerifyBaseDataField' => 'false',
                    'IsEntryBatchFill' => 'true',
                    'ValidateFlag' => 'true',
                    'NumberSearch' => 'true',
                    'InterationFlags' => '',
                    'IgnoreInterationFlag' => '',
                    'IsControlPrecision' => 'false',
                    'ValidateRepeatJson' => 'false',
                    'IsAutoAdjustField' => 'true',
                    'IsAutoSubmitAndAudit' => 'true',
                    'Model' => [
                        'FID' => 0,
                        'FBillNo' => $params['bill_no'],//付款单编号
                        'FBillTypeID' => [
                            'FNUMBER' => 'FKDLX01_SYS',
                        ],
                        'FDATE' => $params['pay_at'],//业务日期
                        'FBookingDate' => '',//期望付款日期
                        'FCONTACTUNITTYPE' => 'BD_Supplier',//往来单位类型
                        'FCONTACTUNIT' => [//供应商编码
                            'FNumber' => $params['vendor_id'],
                        ],
                        'FRECTUNITTYPE' => 'BD_Supplier',//收款单位类型
                        'FRECTUNIT' => [//收款单位
                            'FNumber' => $params['vendor_id'],
                        ],
                        'FCURRENCYID' => [//币别
                            'FNumber' => $params['currency'] ? static::$t->_(GlobalEnums::$currency_item[$params['currency']]) : '',
                        ],
                        'FSETTLEORGID' => [//结算组织
                            'FNumber' => $params['sap_company_id'],
                        ],
                        'FPAYORGID' => [//付款组织
                            'FNumber' => $params['sap_company_id'],
                        ],
                        'FREMARK' => $params['remark'],//备注
                        'F_SPPJ_YWLX' => [//业务类型编号
                            'FNUMBER' => $params['business_type_no'] ?? '',
                        ],
                        'F_SPPJ_YWBH' => $params['no'],//业务编号
                        'F_SPPJ_ZDZKJE' => 0,//整单折扣金额
                        'FPAYBILLENTRY' => [
                            [
                                'F_SPPJ_FYFZ' => '',//费用分组
                                'F_SPPJ_FYFL' => '',//费用明细
                                'F_SPPJ_ZY' => $params['abstract'] ?? '',//摘要
                                'F_SPPJ_FPRQ' => '',//发票日期
                                'F_SPPJ_FPHM' => '',//发票号码
                                'FSETTLETYPEID' => [//结算方式
                                    'FNumber' => $params['pay_method'],
                                ],
                                'FPURPOSEID' => [//付款用途
                                    'FNumber' => 'SFKYT08_SYS',
                                ],
                                'FACCOUNTID' => [//我方银行账号
                                    'FNumber' => $params['pay_bank_account'],
                                ],
                                'F_SPPJ_FYXMFZ' => [//费用项目分组
                                    'FNUMBER' => '',
                                ],
                                'FCOSTID' => [//费用项目编码
                                    'FNUMBER' => '',
                                ],
                                'FEXPENSEDEPTID_E' => [
                                    "FNUMBER" => '',//费用承担部门
                                ],
                                'FPAYTOTALAMOUNTFOR' => $params['amount'],//表体-应付金额
                                'FOVERSHORTAGEFOR' => 0, //长短款
                                'FREALPAYAMOUNTFOR_D' => $params['real_amount'],//实付金额
                                'FSETTLEDISTAMOUNTFOR' => $params['wht_tax_amount'],//WHT税额-现金折扣
                                'FCOMMENT' => '',//备注
                                'F_SPPJ_YHKM' => [//银行科目编码
                                    'FNUMBER' => '',
                                ],
                                'FPOSTDATE' => date('Y-m-d')//登账日期
                            ],
                        ],
                    ]
                ]
            ];

            $this->logger->info('kingdee_paybill ' . $params['no'] . ' request_data:' . json_encode($data, JSON_UNESCAPED_UNICODE));
            $response = curl_request($this->url . '/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc', json_encode($data, JSON_UNESCAPED_UNICODE), 'POST', ['content-type: application/json', 'kdservice-sessionid: ' . $this->session_id]);
            $this->logger->info('kingdee_paybill ' . $params['no'] . ' response_data:' . $response);

            $response = (json_decode($response, true))['Result'];
            if (!isset($response['ResponseStatus']['IsSuccess'])) {
                $this->logger->notice('kingdee_paybill failed' . json_encode($params, JSON_UNESCAPED_UNICODE) . 'response:' . json_encode($response, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $this->logger->warning('kingdee_paybill exception:' . $e->getTraceAsString() . 'params' . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return $response ?? [];
    }


    /**
     * 每笔请求落库操作
     * @param array $response 金蝶支付返回值
     * @param array $log 推送金蝶日志信息组
     * @param object $item 单据对象信息
     * @return bool
     * @throws BusinessException
     */
    public function savePayLog($response, $log, $item)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $date = date('Y-m-d H:i:s');
            $kingdee_no = $response['Number'] ?? '';
            //成功
            if ($response['ResponseStatus']['IsSuccess']) {
                //保存单据各状态枚举值
                if ($log['type'] == KingDeeEnums::PAY_TYPE_PAYABLE && $log['is_cancel_pay'] == KingDeeEnums::IS_CANCEL_PAY_NO) {
                    //应付正向
                    $item->payable_positive_is_send_kingdee = KingDeeEnums::PAY_IS_SEND_KINGDEE_YES;
                    $item->payable_positive_kingdee_no = $kingdee_no;
                } else if ($log['type'] == KingDeeEnums::PAY_TYPE_PAYABLE && $log['is_cancel_pay'] == KingDeeEnums::IS_CANCEL_PAY_YES) {
                    //应付反向
                    $item->payable_negative_is_send_kingdee = KingDeeEnums::PAY_IS_SEND_KINGDEE_YES;
                    $item->payable_negative_kingdee_no = $kingdee_no;
                } else if ($log['type'] == KingDeeEnums::PAY_TYPE_PAYBILL) {
                    //付款
                    $item->paybill_is_send_kingdee = KingDeeEnums::PAY_IS_SEND_KINGDEE_YES;
                    $item->paybill_kingdee_no = $kingdee_no;
                }
                $item->updated_at = $date;
                $bool = $item->save();
                if ($bool === false) {
                    throw new BusinessException('更新单据同步至金蝶状态失败, 原因可能是=' . get_data_object_error_msg($item) . '; 数据=' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                $log['status'] = KingDeeEnums::PAY_LOG_PUSH_STATUS_SUCCESS;
                $log['reason'] = '';
            } else {
                $log['reason'] = json_encode($response['ResponseStatus']['Errors'] ?? '', JSON_UNESCAPED_UNICODE);
                $log['status'] = KingDeeEnums::PAY_LOG_PUSH_STATUS_FAIL;
            }
            $log['send_at'] = $date;
            $log['kingdee_no'] = $kingdee_no;
            $log['created_at'] = $date;
            $request_kingdee = new RequestKingDeeModel();
            $bool = $request_kingdee->i_create($log);
            if ($bool === false) {
                throw new BusinessException('记录推送金蝶日志失败, 原因可能是=' . get_data_object_error_msg($request_kingdee) . '; 数据=' . json_encode($log, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
        return $response['ResponseStatus']['IsSuccess'];
    }
}