<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/17
 * Time: 10:24
 */

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\PurchaseEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialScmAssetUnmatchedModel;
use App\Modules\Material\Services\AssetCodeService;
use App\Modules\Material\Services\AssetReturnService;
use App\Modules\Material\Services\ClassifyService;
use App\Modules\Material\Services\StandardService;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\PurchaseProductList;
use App\Modules\Purchase\Models\PurchaseStorage;
use App\Modules\Purchase\Models\PurchaseStorageProduct;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Util\RedisKey;
use mysql_xdevapi\Exception;
use App\Library\Validation\Validation;

class StorageService extends BaseService
{
    private static $storage_order_status = [
        '1' => 'purchase_storage_pending_key',
        '2' => 'purchase_storage_audited_key',
        '3' => 'purchase_storage_stored_key',
        '4' => 'purchase_storage_received_key',
        '5' => 'purchase_storage_withdrawn_key',
        '6' => 'purchase_storage_not_stored_key'//已入库（非scm）
    ];

    /**
     *  add参数校验
     * */
    public static $validate_add_param = [
        'psno'                               => 'Required|StrLenGeLe:10,20',
        'po'                                 => 'Required|StrLenGeLe:10,20',
        'create_id'                          => 'Required|Int',
        'sap_order_no'                       => 'StrLenGeLe:0,50',
        'create_name'                        => 'Required',
        'cost_department_name'               => 'Required|StrLenGeLe:0,50',
        'cost_company_name'                  => 'Required|StrLenGeLe:0,50',
        'cost_company_id'                    => 'Required|Int',
        'remark'                             => 'StrLenGeLe:0,150',
        'use_limit'                          => 'IntLe:9999',//维保期限（月）非必填，0～9999
        'product'                            => 'Required|Arr|ArrLenGe:1',
        'product[*].product_option_code'     => 'Required',
        'product[*].product_name'            => 'StrLenGeLe:0,200',
        'product[*].this_time_num'           => 'Required|IntGe:1',
        'product[*].total'                   => 'Required|Int',
        'product[*].total_quantity_received' => 'Required|Int',
        'product[*].real_quantity_received'  => 'Required|Int',
        'product[*].unit'                    => 'Required|StrLenGeLe:1,30',
        'product[*].real_unit'               => 'Required|StrLenGeLe:1,30',//实际收货单位
        'product[*].delivery_date'           => 'Required|Date',
        'product[*].price'                   => 'Required|FloatGe:0',
        'product[*].item_id'                 => 'Required|Int',
        'product[*].use_limit'               => 'IntLe:9999',//维保期限（月）非必填，0～9999
        'product[*].category_type'           => 'Required|IntIn:0,1,2',//物料类型，1资产，2耗材
        'product[*].update_to_scm'           => 'Required|IntIn:0,1,2',//是否更新至SCM，1否，2是
        'product[*].category_code'           => 'Required|StrLenGeLe:0,30',//物料编码

    ];
    /**
     *  add参数校验
     * */
    public static $validate_recall_param = [
        'sign'                               => 'Required|StrLenGeLe:30,33',
        'orderSn'                                 => 'Required|StrLenGeLe:1,20',
        'externalOrderSn' => 'Required|StrLenGeLe:1,20',
        'timestamp'                          => 'Required'
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StorageService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 创建订单默认数据
     * */
    public function defaultData($user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $data['psno']        = 'RKD' . static::country_code() . static::getNo(date("Ymd"));
            $data['apply_date']  = date("Y-m-d");
            $data['create_id']   = $user['id'];
            $data['create_id']   = $arr['create_id'];
            $data['create_name'] = $arr['create_name'];

            $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
            //查询满足条件的采购单
            $data['po_list'] = PurchaseOrder::find([
                'columns' => 'id,pono',
                'conditions' => 'status = :status: and stock_status = :stock_status: and purchase_type is not null ',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'stock_status' => PurchaseEnums::PURCHASE_ORDER_STOCK_STATUS_UNDONE,
                ],
                'limit' => 100
            ]);
            $data['cargo_owner'] = $this->cargoOwnerEnums(self::SCM_TYPE_PO);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-get-default-data-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     * 入库通知单-新增-参数验证
     * @param array $data 参数组
     * @throws ValidationException
     */
    public function validationCreate($data)
    {
        //执行参数校验
        $validate_add_param = self::$validate_add_param;
        if (empty($data['mach_code'])) {
            //15050需求，非SCM的实际收货日期必填必须是日期
            $validate_add_param['product[*].real_arrival_date'] = 'Required|Date';
        }
        Validation::validate($data, $validate_add_param);
        //验证是否存在空产品编号
        foreach ($data['product'] as $k => $v) {
            if (empty($v['product_option_code'])) {
                throw new ValidationException(static::$t->_('product_code_have_empty'), ErrCode::$VALIDATE_ERROR);
            }
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
         * 原来SCM货主必输、仓库必输
         * 修改为如果采购订单行里含有barcode是传输至SCM，
         * 则入库单上SCM货主必输、仓库必输，否则SCM货主和仓库不必输。
         */
        $update_to_scm = array_unique(array_values(array_filter(array_column($data['product'], 'update_to_scm'))));
        //一个入库订单里只可存在一种传输至SCM状态的barcode，即订单里要不全部传输至SCM,要不就不传。
        if (count($update_to_scm) >= 2) {
            throw new ValidationException(static::$t->_('product_update_to_scm_error'), ErrCode::$VALIDATE_ERROR);
        }
        if (in_array(MaterialClassifyEnums::MATERIAL_CATEGORY_NO, $update_to_scm)) {
            //含有barcode是传输至SCM,则验证入库单上SCM货主必输、仓库必输
            Validation::validate($data, ['mach_code' => 'Required|StrLenGeLe:1,128', 'mach_name' => 'Required|StrLenGeLe:1,256', 'product[*].stock_id' => 'Required|StrLenGeLe:1,10']);
        }
    }

    /**
     * 新建入库单
     *
     * @param $user
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @throws \Exception
     */
    public function createOrder($user, $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');

        if (isset($data['code'])) {
            return $data;
        }

        if (count(array_unique(array_column($data['product'], 'delivery_date'))) > 1) {
            throw new ValidationException(static::$t->_('delivery_date_key'), ErrCode::$VALIDATE_ERROR);
        }

        //check 校验状态
        $po_order = PurchaseOrder::getFirst([
            'columns' => 'id,currency,stock_status,purchase_type,cost_department,cost_department_name,cost_store,cost_store_name,cost_company,cost_company_name,cost_center_code,cno,vendor_id,vendor,status',
            'conditions' => 'pono  = :pono: and purchase_type is not null',
            'bind' => ['pono' => $data['po']]
        ]);
        if (empty($po_order)) {
            throw new BusinessException('该采购订单不存在=' . $data['po'], ErrCode::$VALIDATE_ERROR);
        }
        $po_order = $po_order->toArray();
        if (PurchaseEnums::PURCHASE_ORDER_STOCK_STATUS_DONE == $po_order['stock_status']) {
            throw new BusinessException('该采购订单已入库处理完成，不能添加=' . $data['po'], ErrCode::$VALIDATE_ERROR);
        }

        if ($po_order['status'] != Enums::WF_STATE_APPROVED) {
            throw new ValidationException(static::$t->_('storage_order_no_status_error'), ErrCode::$VALIDATE_ERROR);
        }

        /**
         * 判断sap_order_no, 如果是库存类,sap_order_no必填
         * 12330【OA | PH/MY/LA/VN】入库通知单迁移
         * https://l8bx01gcjr.feishu.cn/docs/doccnlzHEH05AszhJDH9Z3ecFph#ODNT0A
         * 由于其他国家的sap还没有，但是入库通知单需求要上线，所以关于sap的判断逻辑只开放已配置sap的国家（泰国、菲律宾）
         * 马来上了SAP,需要增设马来国家判断
         * 2022-09-13 邮件需求增加逻辑 : po单上的"费用所属公司" = 泰国-flash express , 马来-flash malaysia express , 菲律宾-Philippines flash express 才需要校验必填 (配置中各国的express公司)
         */
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        if ($po_order['purchase_type'] == PurchaseEnums::PURCHASE_TYPE_STOCK && in_array(get_country_code(),[GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && $po_order['cost_company'] == $company_ids['FlashExpress']) {
            if (!isset($data['sap_order_no']) || empty($data['sap_order_no'])) {
                throw new ValidationException(static::$t->_('sap_order_no_empty_error'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 批量判断传输过来的输入数据大小
        foreach ($data['product'] as $key => $value) {
            $last_total_count= $this->getCountStorage($data['po'], $value);
            $this_time_num = bcsub($value['total'], $last_total_count);

            if ($value['this_time_num'] > $this_time_num) {
                throw new ValidationException(static::$t->_('storage_this_time_num_max_count'), ErrCode::$VALIDATE_ERROR);
            }
        }

        //判断是否存在
        $exists = PurchaseStorage::getFirst([
            'conditions' => 'psno = ?0',
            'columns'    => 'id',
            'bind'       => [$data['psno']]
        ]);

        if (!empty($exists)) {
            throw new ValidationException('该采购入库通知单编号已经存在，不能重复添加=' . $data['psno'], ErrCode::$VALIDATE_ERROR);
        }

        //查询po下入库单
        if (isset($data['is_submit']) && 0 == $data['is_submit']) {
            $storage_order_data = PurchaseStorage::find([
                'conditions' => 'po_id = :po_id: and status =:status:',
                'bind'       => [
                    'po_id'     => $po_order['id'],
                    'status' => 1
                ]
            ])->toArray();

            if ($storage_order_data) {
                $data['is_submit'] = 1;

                return [
                    'code'    => $code,
                    'message' => $message,
                    'data'    => $data
                ];
            }
        }
//        $product_list = PurchaseProductList::find([
//            'columns'=>'material_id,description,base_uom,product_cate,create_asset_code,asset_type',
//            'conditions' => 'material_id in ({product_codes:array}) and create_asset_code = :create_asset_code: and is_del = 0',
//            'bind' => [
//                'product_codes' => $product_codes,
//                'create_asset_code' => 1,
//            ]
//        ])->toArray();

        try{
            $db->begin();
            $data['currency'] = $po_order['currency'];
            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账--p1
             * 原逻辑：在purchase_product_list表中新增字段“是否生产资产编码”，区分是否生成资产编码」，如果启用，则按照下方的编码规则自动生成资产编码，并在接口传输至SCM，生成入库通知单。
             * 新逻辑：入库单是传输至SCM，根据barcode上的物料类型，
             *       如果是资产，则自动生成草稿资产编码。并在接口传输至SCM。
             *       SCM回传之后，将对应的资产编码状态改为启用。
             *      如果是物料，则传输至SCM即可。
             *     入库单不是传输至scm，根据barcode上的物料类型，如果是资产，则自动生成非草稿（启用）资产编码，不用传输至scm.
             */
            $product_list = [];//需要生成资产编码的barcode清单
            foreach ($data['product'] as $item) {
                //物料类型=资产类 才需要生成资产编码
                if ($item['category_type'] == Enums\MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    $product_list[] = [
                        'material_id' => $item['product_option_code'],
                        'asset_type' => $item['category_code'],
                        'status' => empty($data['mach_code']) ? Enums\MaterialEnums::ASSET_STATUS_UNUSED : Enums\MaterialEnums::ASSET_STATUS_DRAFT,//不传输至scm，资产编码闲置状态，否则草稿状态
                    ];
                }
            }
            if (!empty($product_list)) {
                //获取当前时间
                $timestamp = time();
                //本次入库的产品及数量
                $product_list_kv = array_column($product_list,null,'material_id');
                //计算本次入库的产品需要生成的资产编码数量;字段绑定到需要生成资产编码的产品数据上
                $count_asset_code = 0;
                foreach ($data['product'] as $p_k => &$p_v) {
                    $p_v['create_asset_code'] = 2;
                    $p_v['asset_type'] = '';
                    if (key_exists($p_v['product_option_code'], $product_list_kv)) {
                        $count_asset_code += $p_v['this_time_num'];
                        $p_v['create_asset_code'] = 1;
                        $p_v['asset_type'] = $product_list_kv[$p_v['product_option_code']]['asset_type'];
                        $p_v['status'] = $product_list_kv[$p_v['product_option_code']]['status'];
                    }
                }
                //生成资产编码
                if ($count_asset_code > 0) {
                    $serial_number_array = AssetCodeService::getInstance()->createSerialNumber($count_asset_code, $timestamp);
                    //生成资产编码
                    foreach ($data['product'] as $dp_k => &$dp_v) {
                        if ($dp_v['create_asset_code'] == 1) {
                            for ($i=0; $i<$dp_v['this_time_num']; $i++) {
                                $dp_v['asset_code'][] = AssetCodeService::getInstance()->createAssetCode($dp_v['asset_type'], array_shift($serial_number_array), $timestamp);
                            }
                        }
                    }
                }
            }
            $response = [];
            if (!empty($data['mach_code'])) {
                // 同步scm==================================
                $param = [
                    'warehouseId'    => $data['product'][0]['stock_id'],
                    'type'           => "1",//采购入库
                    'orderSn'        => $data['psno'],
                    'channelSource'  => "FlashOA",
                    'arrivalStart'   => $data['product'][0]['delivery_date'] . ' 08:00',
                    'arrivalEnd'     => $data['product'][0]['delivery_date'] . ' 09:00',
                    'deliveryNumber' => $data['po'],
                    'remark'         => ($data['cost_department_name'] ?? '') . ';' . ($data['storage_remark'] ?? ''),//14848需求，入库到scm时备注修改为”费用所属部门;备注“
                ];
                //15050【OA-ALL】入库通知单的SCM接口优化及数据查询优化，特定配置的货主的耗材类传输不含税单价以及币种信息；非特定的不区分啥物料类型一律默认1000000
                $ffm_mach_code = EnvModel::getEnvByCode('purchase_storage_ffm');
                $send_price = (!empty($ffm_mach_code) && in_array($data['mach_code'], explode(',', $ffm_mach_code))) ? true : false;

                //15257【OA-ALL】入库通知单支持一个barcode多行入库；但是传输至SCM的还是一个barcode，数量累加和
                $scm_goods_arr = [];
                foreach ($data['product'] as $product) {
                    if (!isset($scm_goods_arr[$product['product_option_code']])) {
                        $scm_goods_arr[$product['product_option_code']] = $product;
                    } else {
                        $scm_goods_arr[$product['product_option_code']]['this_time_num'] = bcadd($scm_goods_arr[$product['product_option_code']]['this_time_num'], $product['this_time_num']);
                        if (isset($product['create_asset_code']) && $product['create_asset_code'] == 1) {
                            //资产类需要生产资产码的才需要将同类barcode下的资产编码合并
                            $scm_goods_arr[$product['product_option_code']]['asset_code'] = array_merge($scm_goods_arr[$product['product_option_code']]['asset_code'], $product['asset_code']);
                        }
                    }
                }
                $no = 0;
                foreach ($scm_goods_arr as $k1 => $v1) {
                    $no++;
                    $this_product_info = [
                        "i"       => $no,
                        "barCode" => $v1['product_option_code'],
                        "num"     => $v1['this_time_num'],
                        'price'   => 1000000
                    ];
                    if ($send_price === true) {
                        //如果是true，才需要给scm传递的数据信息
                        $this_product_info['price'] = ($v1['category_type'] == Enums\MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) ? bcmul($v1['price'], 1000000) : 1000000;
                        $this_product_info['currency'] = $po_order['currency'] ? static::$t->_(GlobalEnums::$currency_item[$po_order['currency']]) : '';
                    }
                    if (isset($v1['create_asset_code']) && $v1['create_asset_code'] == 1) {
                        $this_product_info['asset'] = $v1['asset_code'];
                    }
                    $param['goods'][] = $this_product_info;
                }
                $response['data'] = '';

                // 去掉重试 3 -> 1
                $i                = 0;
                while (empty($response['data']) && $i < 1) {
                    $i++;
                    $response = (new ScmService($data['mach_code']))->scmCreate($param);
                }
                if (empty($response['data'])) {
                    throw new ValidationException('scm同步失败入库单创建失败原因是: '.$response['message'].'错误码('.$response['code'].')', ErrCode::$VALIDATE_ERROR);
                }
            }
            //=================================================================================
            $storage_data = [
                'po_id'                => $po_order['id'],
                'psno'                 => $data['psno'],
                'apply_date'           => date('Y-m-d'),
                'create_id'            => $user['id'] ?? ($data['create_id'] ?? 0),
                'create_name'          => $data['create_name'],
                'po'                   => $data['po'],
                'sap_order_no'         => $data['sap_order_no'],
                'purchase_type'         => $po_order['purchase_type'],
                'currency'             => $po_order['currency'],
                'cost_department_id'   => $po_order['cost_department'],
                'cost_department_name' => $data['cost_department_name'],
                'cost_company_name'    => $data['cost_company_name'],
                'cost_company_id'      => intval($data['cost_company_id'] ?? 0),
                'scm_no'               => $response['data'] ?? null,
                'storage_remark'       => $data['storage_remark'] ?? '',
                'status'               => empty($data['mach_code']) ? PurchaseEnums::PURCHASE_STORAGE_STATUS_UN_SCM_DONE : PurchaseEnums::PURCHASE_STORAGE_STATUS_WAIT,//非传递SCM则状态设置为6已入库（非SCM）否则设置1待处理
                'create_at'            => date('Y-m-d H:i:s'),
                'update_at'            => date('Y-m-d H:i:s'),
                'mach_code'            => $data['mach_code'],
                'mach_name'            => $data['mach_name'],
                'use_limit'            => $data['use_limit'],//维保期限（月）

            ];
            if (is_string($data)) {
                throw new  BusinessException($data, ErrCode::$VALIDATE_ERROR);
            }

            $model = new PurchaseStorage();
            $bool  = $model->i_create($storage_data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('采购订单创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }
            $order_product_delivery_total = [];
            foreach ($data['product'] as $key => $storage_product) {
                $storage_product_model = new PurchaseStorageProduct();
                $storage_product_bool  = $storage_product_model->i_create([
                    'psnoid'                  => $model->id,
                    'product_name'            => $storage_product['product_name'],
                    'product_option_code'     => $storage_product['product_option_code'],
                    'stock_id'                => $storage_product['stock_id'],
                    'total'                   => $storage_product['total'],
                    'this_time_num'           => $storage_product['this_time_num'],
                    'total_quantity_received' => $storage_product['total_quantity_received'],
                    'real_quantity_received'  => empty($data['mach_code']) ? $storage_product['this_time_num'] : 0 ,//不需要传递scm则实际收货数量=本次收货数量。
                    'unit'                    => $storage_product['unit'],
                    'real_unit'               => $storage_product['real_unit'],//实际收货单位=入库单行上产品编号（barcode）上的基本单位（英文名称）
                    'delivery_date'           => $storage_product['delivery_date'],
                    'real_arrival_date'       => empty($data['mach_code']) ? $storage_product['real_arrival_date'] : null,//实际收货日期
                    'price'                   => bcmul($storage_product['price'], 1000000),
                    'create_at'               => date('Y-m-d H:i:s'),
                    'update_at'               => date('Y-m-d H:i:s'),
                    'order_product_id'        => $storage_product['pid']??'',
                    'item_id'                 => $storage_product['item_id'],
                    'use_limit'               => $storage_product['use_limit'],//维保期限（月）
                    'category_type'           => $storage_product['category_type'],//物料类型，1资产，2耗材
                    'update_to_scm'           => $storage_product['update_to_scm'],//是否更新至SCM，1否，2是
                ]);

                if ($storage_product_bool === false) {
                    $messages = $storage_product_model->getMessages();
                    throw new ValidationException('采购入库通知单-产品创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                }
                // 如果不通过scm出库的,直接把实际收货数量累加到采购订单行上的交付数量(delivery_total) 不通过scm出库的 实际收货数量就等于本次收货数量
                if (empty($data['mach_code']) && !empty($storage_product['pid'])) {
                    $order_product_delivery_total[$storage_product['pid']] = $storage_product['this_time_num'];
                }
            }
            //更新交付数量,判断交付状态
            $this->updateDeliveryTotal($data['po'], $order_product_delivery_total, $user);
            //生成资产数据
            $material_assets_data = [];
            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P2
             * 入库通知单入库的资产台账数据-相关属性字段初始化逻辑增加
             */
            //获取采购入库通知单行上所选barcode的列表
            $barcode_arr = array_values(array_unique(array_filter(array_column($data['product'], 'product_option_code'))));
            $barcode_list = StandardService::getInstance()->getCategoryBarcodeList(['barcode' => $barcode_arr]);
            $barcode_list = array_column($barcode_list, null, 'barcode');

            //获取采购订单行明细
            $purchase_order_products = PurchaseOrderProduct::find([
                'columns' => 'product_option_code,cost_store_id,cost_store_name,cost_center_name, wrs_code,vat7_rate,id',
                'conditions' => 'poid = :poid:',
                'bind' => ['poid' => $po_order['id']]
            ])->toArray();
            $purchase_order_products_barcode = array_column($purchase_order_products, null, 'id');
            //采购订单行明细上的财务分类
            $finance_code = array_values(array_unique(array_filter(array_column($purchase_order_products, 'wrs_code'))));
            $finance_list = (new MaterialFinanceCategoryModel())->getList(['code'=>$finance_code]);
            $finance_list = array_column($finance_list,  null,  'code');
            foreach ($data['product'] as $material_key => $material_value) {
                if(isset($material_value['create_asset_code']) && $material_value['create_asset_code'] == 1) {
                    foreach ($material_value['asset_code'] as $asset_code_one) {
                        $purchase_price = 0;
                        $purchase_price = $this->materialAssetsPrice(is_null($po_order['cost_company']) ? '' : $po_order['cost_company'], $material_value['price'], $purchase_order_products_barcode[$material_value['pid']]['vat7_rate']);
                        $assets = [
                            'bar_code' => $material_value['product_option_code'],
                            'asset_code' => $asset_code_one,
                            'pono' => $storage_data['po'],
                            'psno' => $storage_data['psno'],
                            'scm_no' => $storage_data['scm_no'],
                            'source_code' => Enums\MaterialEnums::SOURCE_PURCHASE,
                            'plan_arrival_date' => $material_value['delivery_date'],
                            'created_at' => gmdate('Y-m-d H:i:s'),
                            'status' =>$material_value['status'],
                            'use' => Enums\MaterialEnums::USE_PERSONAL,//个人使用
                            'quality_status' => Enums\MaterialEnums::QUALITY_STATUS_GOOD,//正品
                            'purchase_price' => sprintf("%.2f", $purchase_price),//采购价（不含税）=入库单行的不含税单价
                            'currency'=>$po_order['currency'],//币种 = 采购订单头上的币种
                            'net_value' => sprintf("%.2f", $purchase_price),//净值 = 等于采购价（不含税）

                            //采购订单头上相关属性赋值
                            'node_department_id' => is_null($po_order['cost_department']) ? '' : $po_order['cost_department'],//使用部门ID = 采购订单上的费用部门ID
                            'node_department_name' => is_null($po_order['cost_department_name']) ? '' : $po_order['cost_department_name'],//使用部门名称 = 采购订单上费用部门名称
                            'company_id' => is_null($po_order['cost_company']) ? '' : $po_order['cost_company'],//所属公司ID = 采购订单上的费用所属公司id
                            'company_name' => is_null($po_order['cost_company_name']) ? '' : $po_order['cost_company_name'],//所属公司名称 = 采购订单上的费用所属公司
                            'purchase_lease_contract_no' => $po_order['cno'],//采购/租赁合同编号 = 采购订单上的合同编号
                            'real_arrival_date' => empty($data['mach_code']) ? $material_value['delivery_date'] : null,//入库时间 = 非同步到scm的入库单行的计划交货日期，同步至scm为null
                            'vendor_id' => is_null($po_order['vendor_id']) ? '' : $po_order['vendor_id'],//供应商编码 = 采购订单上的供应商id
                            'vendor_name' => is_null($po_order['vendor']) ? '' : $po_order['vendor'],//供应商名称 = 采购订单上的供应商
                            'maintenance_vendor' => is_null($po_order['vendor']) ? '' : $po_order['vendor'],//维保供应商 = 采购订单上的供应商
                            'maintenance_expire_date' => empty($data['mach_code']) ? ($material_value['use_limit'] ? date('Y-m-d', strtotime('-1 day', strtotime("+".$material_value['use_limit']." months", strtotime($material_value['delivery_date'])))) : null) : null,//维保到期日 = 非同步至scm入库单行上的计划交货日期+维保月份-1,如果维保月份为0，则维保到期日为空；同步至scm=null
                        ];
                        if (!empty($barcode_list[$material_value['product_option_code']])) {
                            //barcode 身上相关属性赋值
                            $assets['name_zh'] = $barcode_list[$material_value['product_option_code']]['name_zh'];//物料名称-中文
                            $assets['name_en'] = $barcode_list[$material_value['product_option_code']]['name_en'];//物料名称-英文
                            $assets['name_local'] = $barcode_list[$material_value['product_option_code']]['name_local'];//物料名称-当地语言
                            $assets['category_id'] = $barcode_list[$material_value['product_option_code']]['category_id'];//物料分类表ID
                            $assets['category_name'] = $barcode_list[$material_value['product_option_code']]['category_name'];//物料分类名称
                            $assets['category_code'] = $barcode_list[$material_value['product_option_code']]['category_code'];//物料分类编码
                            $assets['unit_zh'] = $barcode_list[$material_value['product_option_code']]['unit_zh'];//单位-中文
                            $assets['unit_en'] = $barcode_list[$material_value['product_option_code']]['unit_en'];//单位-英文
                            $assets['model'] = $barcode_list[$material_value['product_option_code']]['model'];//规格型号
                            $assets['brand'] = $barcode_list[$material_value['product_option_code']]['brand'];//品牌
                            $assets['use_land'] = $material_value['stock_name'] ?? '';//使用地=仓库名称
                        }
                        if (!empty($purchase_order_products_barcode[$material_value['pid']])) {
                            //采购订单行上相关属性赋值
                            $cost_store_name = $purchase_order_products_barcode[$material_value['pid']]['cost_store_name'];//使用网点名称
                            $assets['sys_store_id'] = ($cost_store_name == 'Head Office') ? -1 : $purchase_order_products_barcode[$material_value['pid']]['cost_store_id'];//使用网点
                            $assets['store_name'] = $cost_store_name;//使用网点名称
                            $assets['pc_code'] = $purchase_order_products_barcode[$material_value['pid']]['cost_center_name'];//成本中心
                            $product_wrs_code = $purchase_order_products_barcode[$material_value['pid']]['wrs_code'];
                            if ($product_wrs_code && !empty($finance_list[$product_wrs_code])) {
                                $assets['finance_category_id'] = $finance_list[$product_wrs_code]['id'];//财务分类表ID
                                $assets['finance_category_name'] = $finance_list[$product_wrs_code]['name'];//财务分类名称
                                $assets['finance_category_code'] = $finance_list[$product_wrs_code]['code'];//财务分类编码
                                $assets['use_limit'] = $finance_list[$product_wrs_code]['use_limit'];//折旧期限
                            }
                        }
                        $material_assets_data[] = $assets;
                    }
                }
            }
            if (!empty($material_assets_data)) {
                $material_asset_model = new MaterialAssetsModel();
                $material_asset_bool = $material_asset_model->batch_insert($material_assets_data);
                if ($material_asset_bool === false) {
                    throw new BusinessException('批量添加资产数据失败', ErrCode::$CREATE_ASSET_DATA_ERROR);
                }
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }


    /**
     * po单入库详情
     * */
    public function purchaseOrderDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $apply = PurchaseOrder::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (empty($apply)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }
            $purchase_data = $apply->toArray();

            $purchase_type_enum = OrderService::getInstance()->getPurchaseType();
            $purchase_type_enum = array_column($purchase_type_enum,'name_key','id');

            $data = [
                'purchase_type'        => $purchase_data['purchase_type'],
                'purchase_type_text'   => $purchase_type_enum[$purchase_data['purchase_type']]??'',
                'pono'                 => $purchase_data['pono'],
                'sap_order_no'         => $purchase_data['sap_order_no'],
                'cost_company_name'    => $purchase_data['cost_company_name'],
                'cost_company_id'      => $purchase_data['cost_company'] ?: '0',
                'cost_department_name' => $purchase_data['cost_department_name'],
                'is_create_storage'    => 0
            ];

            unset($purchase_data);
            $purchase_products = $apply->getProducts()->toArray();//采购明细
            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账--p1
             * 返回barcode的物料类型、是传输至SCM返回、计量单位、所属物料分类一级分类编码
             */
            $product_barcode_list = [];
            $product_barcode_arr = array_values(array_filter(array_column($purchase_products, "product_option_code")));
            if ($product_barcode_arr) {
                $product_barcode_list = StandardService::getInstance()->getBarcodeList(['barcode'=>$product_barcode_arr]);
                if ($product_barcode_list) {
                    $product_barcode_list = array_column($product_barcode_list, null, 'barcode');
                    //找出各个barcode身上的所属物料分类所在的一级分类的编码
                    $category_id_arr = array_values(array_column($product_barcode_list, 'category_id'));
                    $category_list = ClassifyService::getInstance()->getCategoryParents(['id'=>$category_id_arr]);
                }
            }
            foreach ($purchase_products as $key => $value) {

                $last_total_count= $this->getCountStorage($data['pono'],$value);

                $this_time_num = bcsub($value['total'],$last_total_count);
                $item = [
                    'product_option_code'     => $value['product_option_code'],
                    'total'                   => $value['total'],
                    'this_time_num'           => $this_time_num < 0?0:(int)$this_time_num,//本次入库数量自动代入po单数量
                    'max_this_time_num'       => $this_time_num < 0?0:(int)$this_time_num,
                    'product_name'            => $value['desc'],
                    'total_quantity_received' => 0,
                    'real_quantity_received'  => 0,
                    'unit'                    => $value['unit'],
                    'real_unit'               => '',//（barcode）上的基本单位
                    'stock_id'                => '',
                    'price'                   => bcdiv($value['price'], 1000000, 6),
                    'delivery_date'           => $value['delivery_date'],
                    'pid'                     => $value['id'],
                    'item_id'                 => 10*(int)($key+1),
                    'category_type'           => 0,//物料类型，1资产，2耗材
                    'update_to_scm'           => 0,//是否更新至SCM，1否，2是
                    'category_code'           => '',//所属物料分类一级分类编码
                ];
                if (!empty($value['product_option_code']) && !empty($product_barcode_list) && isset($product_barcode_list[$value['product_option_code']])) {
                    $barcode_info = $product_barcode_list[$value['product_option_code']];
                    $item['real_unit']     = $barcode_info['unit_en'];
                    $item['category_type'] = intval($barcode_info['category_type']);
                    $item['update_to_scm'] = intval($barcode_info['update_to_scm']);
                    if (!empty($category_list) && isset($category_list[$barcode_info['category_id']])) {
                        $item['category_code'] = $category_list[$barcode_info['category_id']]['code'];
                    }

                }
                $data['product'][] = $item;
            }

            //查询po下入库单
            $storage_order_data = PurchaseStorage::find([
                'conditions' => 'po_id = :po_id:',
                'bind'       => [
                    'po_id' => $id,
                ]
            ])->toArray();

            if (!empty($storage_order_data)) {
                foreach ($storage_order_data as $item) {
                    if (1 == $item['status'] || 2 == $item['status']) {
                        $data['is_create_storage'] = 1;
                    }
                }

                $storage_order_ids = array_column($storage_order_data, 'id');

                $purchase_storage_products = PurchaseStorageProduct::find([
                    'columns'    => 'psnoid,sum(real_quantity_received) as real_total,order_product_id',
                    'conditions' => ' psnoid in ({ids:array}) group by order_product_id',
                    'bind'       => ['ids' => $storage_order_ids]
                ]);


                $purchase_storage_products = $purchase_storage_products->toArray();
                $purchase_storage_products = array_column($purchase_storage_products, 'real_total', 'order_product_id');

                foreach ($data['product'] as $storage_k1 => $storage_v1) {
                    $data['product'][$storage_k1]['total_quantity_received'] = $purchase_storage_products[$storage_v1['pid']] ?? 0;
                    $data['product'][$storage_k1]['real_quantity_received']  = $purchase_storage_products[$storage_v1['pid']] ?? 0;
                    $data['product'][$storage_k1]['stock_id']                = '';
                }
            }

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-po-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    /**
     * 入库单详情
     * */
    public function getDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $storage_order = PurchaseStorage::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($storage_order)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }
            $storage_order = $storage_order->toArray();

            $purchase_storage_products = PurchaseStorageProduct::find([
                'conditions' => 'psnoid =:id:',
                'bind'       => ['id' => $id]
            ])->toArray();

            foreach ($purchase_storage_products as &$item) {

                $item['price'] = bcdiv($item['price'], 1000000, 6);//15050 需求发现，表里面价格存储小数点6位，展示确是4位
            }
            $mach_code = $storage_order['mach_code']??'';
            $stock_detail = [];
            if (!empty($mach_code)) {
                //因为印尼没有货主，这里需要判断下没有货主不需要去获取仓库
                $stock_detail              = (new ScmService($mach_code))->getWarehouseList();

                $stock_detail = array_column($stock_detail, 'name', 'id');
            }

            foreach ($purchase_storage_products as &$item) {
                $item['stock_id_text'] = $stock_detail[$item['stock_id']] ?? '';
            }


            $storage_order['product'] = $purchase_storage_products;
            //增加采购类型
            $purchase_type_enum = OrderService::getInstance()->getPurchaseType();
            $purchase_type_enum = array_column($purchase_type_enum,'name_key','id');
            $storage_order['purchase_type_text'] = $purchase_type_enum[$storage_order['purchase_type']]??'';

        } catch (\Exception $e) {
            $code          = ErrCode::$SYSTEM_ERROR;
            $message       = static::$t->_('retry_later');
            $real_message  = $e->getMessage();
            $storage_order = [];

        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-psno-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $storage_order
        ];
    }


    /**
     * @param array $condition
     * @param array $user
     * @param int $type
     * @param bool $export
     * @return array
     * 入库单列表
     * */

    public function getList($condition = [], $user = [], $type = 0, $export = false)
    {
        $condition['uid'] = $uid = $user['id'] ?? 0;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            $builder    = $this->modelsManager->createBuilder();

            $column_str = 'o.*';
            $builder->from(['o' => PurchaseStorage::class]);
            //15050需求，可以按照产品编号精确搜索
            if ($export || !empty($condition['product_option_code'])) {
                $builder->leftjoin(PurchaseStorageProduct::class, 'o.id=p.psnoid', 'p');
            }
            if ($export) {
                // 字段顺序与Excel列的顺序是一致的, 切勿乱调
                $column_str = 'o.psno,o.scm_no,o.sap_order_no,o.po,o.create_id,o.create_name,o.apply_date,o.storage_remark,o.purchase_type, o.status, o.mach_code,p.id,p.product_option_code,p.product_name,p.price,p.this_time_num,p.total,p.unit,p.total_quantity_received,p.real_quantity_received,p.delivery_date,p.real_arrival_date,p.stock_id';
            }

            $builder = $this->getListCondition($builder, $condition, $type, $user);
            $builder->andWhere('o.is_del = :is_del:', ['is_del' => GlobalEnums::IS_NO_DELETED]);
            $count = (int) $builder->columns('COUNT(DISTINCT o.id) AS total')->getQuery()->getSingleResult()->total;
            $builder->columns($column_str);
            if (!$export) {
                $builder->groupBy('o.id');
                $builder->orderBy("o.create_at desc");
                $builder->limit($page_size, $offset);
            }

            $items = [];
            if ($count) {
                $items = $builder->getQuery()->execute()->toArray();
                $export ? $this->exportHandleList($items) : $this->handleListItems($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchase_storage-list-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     * 查询条件组合
     * @param $builder
     * @param $condition
     * @param int $type
     * @param array $user
     * @return object
     * @throws BusinessException
     */
    private function getListCondition($builder, $condition, $type = 0, $user = [])
    {
        // 如果是我的申请列表-这里的uid就是必填，表示的是当前登陆者名下的单据，其他列表及导出不要传递该值
        if (isset($condition['uid']) && !empty($condition['uid']) && $type != self::LIST_TYPE_DATA) {
            $builder->andWhere('o.create_id = :uid:', ['uid' => $condition['uid']]);
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
         * 发现历史bug，按照申请人搜索不起作用
         */
        if (isset($condition['create_id']) && !empty($condition['create_id'])) {
            $builder->andWhere('o.create_id = :create_id:', ['create_id' => $condition['create_id']]);
        }

        if (!empty($condition['psno'])) {
            $builder->andWhere('o.psno = :psno:', ['psno' => $condition['psno']]);
        }

        if (!empty($condition['scm_no'])) {
            $builder->andWhere('o.scm_no = :scm_no:', ['scm_no' => $condition['scm_no']]);
        }

        if (!empty($condition['sap_order_no'])) {
            $builder->andWhere('o.sap_order_no = :sap_order_no:', ['sap_order_no' => $condition['sap_order_no']]);
        }

        if (!empty($condition['po'])) {
            $builder->andWhere('o.po = :po:', ['po' => $condition['po']]);
        }

        if (!empty($condition['status'])) {
            $builder->andWhere('o.status = :status:', ['status' => $condition['status']]);
        }

        if (isset($condition['purchase_type']) && !empty($condition['purchase_type'])){
            $builder->andWhere('o.purchase_type = :purchase_type:', ['purchase_type' => $condition['purchase_type']]);
        }
        //15050需求，增加按照产品编号搜索
        if (isset($condition['product_option_code']) && !empty($condition['product_option_code'])) {
            $builder->andWhere('p.product_option_code = :product_option_code:', ['product_option_code' => $condition['product_option_code']]);
        }

        if (!empty($condition['cost_company_id'])) {
            if (is_array($condition['cost_company_id'])) {
                $builder->andWhere('o.cost_company_id IN ({cost_company_id:array}) ', ['cost_company_id' => array_values($condition['cost_company_id'])]);
            } else {
                $builder->andWhere('o.cost_company_id = :cost_company_id:', ['cost_company_id' => $condition['cost_company_id']]);
            }
        }

        if ($type == self::LIST_TYPE_DATA && !empty($user)) {
            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'o',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'cost_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_STORAGE, $table_params);
        }

        return $builder;
    }

    private function handleListItems(&$items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        //获取采购类型枚举
        $purchase_type_enum = OrderService::getInstance()->getPurchaseType();
        $purchase_type_enum = array_column($purchase_type_enum,'name_key','id');
        foreach ($items as &$value) {
            $value['status_text'] = static::$t->_(self::$storage_order_status[$value['status']]);
            $value['purchase_type_text'] = $purchase_type_enum[$value['purchase_type']]??'';
        }

        return $items;
    }

    private function exportHandleList(&$items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        //多货主优化 查询到所有货主仓库信息然后再查仓库信息
        $cargo_owner     = $this->cargoOwnerEnums(self::SCM_TYPE_PO);
        $ware_house_list = [];
        foreach ($cargo_owner as $cargo) {
            $ware_house_list[$cargo['code']] = array_column((new ScmService($cargo['code']))->getWarehouseList(), 'name', 'id');
        }

        // 获取采购类型枚举
        $purchase_type_enum = OrderService::getInstance()->getPurchaseType();
        $purchase_type_enum = array_column($purchase_type_enum,'name_key','id');
        foreach ($items as &$value) {
            $value['stock_id'] = $ware_house_list[$value['mach_code']][$value['stock_id']] ?? '';
            $value['price'] = bcdiv($value['price'], 1000000, 6);//15050 需求发现，表里面价格存储小数点6位，展示确是4位
            $value['status'] = static::$t->_(self::$storage_order_status[$value['status']]);
            $value['purchase_type'] = $purchase_type_enum[$value['purchase_type']] ?? '';

            unset($value['mach_code']);
        }
        return $items;

    }

    public function orderStatus()
    {
    }


    public function dealScmStatus($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $logger = $this->getDI()->get('logger');
            $logger->info('scm_callback_log' . json_encode($params));

            if ((new validSignService())->validator($params)) {//处理业务
                $oa_no = substr($params['externalOrderSn'], 0, 3);
                if ($oa_no == 'RKD') {
                    //采购入库回调
                    $response = $this->updateStorageOrder($params);
                } else if ($oa_no == 'ART') {
                    //资产退回入库回调
                    $response = AssetReturnService::getInstance()->updateStorageOrder($params);
                }
                $code     = $response['code'] ?? $code;
                $message  = $response['message'] ?? $message;
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            return [
                'code'    => $code,
                'message' => $message,
                'data'    => null
            ];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-psno-deal-failed:' . $real_message);
        }
        $this->getDI()->get('logger')->info('storage-psno-deal-return: code=' .$code.";message=".$message.";data=".json_encode($data));
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    /**
     * scm 回调处理入库单
     * @param array $params 参数组
     * @return array
     */
    public function updateStorageOrder($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //查询当前入库单
            $purchaseStorageModel = PurchaseStorage::getFirst([
                'conditions' => 'scm_no = :orderSn: and status = :status:',
                'bind'       => ['orderSn' => $params['orderSn'], 'status' => PurchaseEnums::PURCHASE_STORAGE_STATUS_PASS]
            ]);
            if (empty($purchaseStorageModel)) {
                throw new ValidationException('scm单号不存在或状态不对' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }
            $purchase_storage_data = $purchaseStorageModel->toArray();

            //查询当前入库单商品信息
            $storage_products = PurchaseStorageProduct::find([
                'conditions' => 'psnoid =:id:',
                'bind'       => ['id' => $purchase_storage_data['id']]
            ])->toArray();

            //比较当前入库单商品与scm回传是否一致,15257需求会改为同一个入库单上barcode明细行会有重复
            $product_option_codes = array_values(array_unique(array_column($storage_products, 'product_option_code')));
            $goods                = array_column($params['goods'], 'barCode');
            if (array_diff($product_option_codes, $goods) || array_diff($goods, $product_option_codes)) {
                throw new ValidationException('scm实际入库商品不符' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }

            //查询当前入库单关联的po单，并查询出该po单下所关联的所有入库单
            $storage_order_data = PurchaseStorage::find([
                'conditions' => 'po_id = :po_id:',
                'bind'       => [
                    'po_id' => $purchase_storage_data['po_id'],
                ]
            ])->toArray();
            if (empty($storage_order_data)) {
                throw new ValidationException('入库单不存在' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }
            //所有的入库单ID组
            $storage_order_ids = array_column($storage_order_data, 'id');

            //15257【OA-ALL】入库通知单支持一个barcode多行入库,scm那边回调也是barcode维度的数量
            $barcode_total = PurchaseStorageProduct::find([
                'columns'    => 'sum(real_quantity_received) as real_total,sum(total) as total,product_option_code',
                'conditions' => 'psnoid in ({ids:array}) and product_option_code in ({product_option_code:array}) group by product_option_code',
                'bind'       => ['ids' => $storage_order_ids, 'product_option_code' => $product_option_codes]
            ])->toArray();
            $barcode_total = array_column($barcode_total, null, 'product_option_code');
            foreach ($params['goods'] as $k1 => $goods) {
                $one_product = $barcode_total[$goods['barCode']];
                if ($one_product['total'] - $one_product['real_total'] - $goods['num'] < 0) {
                    throw new ValidationException('scm实际入库商品数量不合法' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
                }
            }

            //修改当前入库单状态
            $purchaseStorageModel->status    = PurchaseEnums::PURCHASE_STORAGE_STATUS_IN_STORAGE;
            $purchaseStorageModel->update_at = date('Y-m-d H:i:s');
            $update_bool                     = $purchaseStorageModel->i_update();
            if ($update_bool == false) {
                throw new ValidationException('修改入库单状态失败order' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }

            //组装scm会传回来的数据,依据barcode为key的数组
            $goods_list = array_column($params['goods'], null, 'barCode');

            //查询出所有的入库单ID组下的所有入库单明细，并按照采购订单明细行ID分组下的实际收货数量
            $purchase_storage_products = PurchaseStorageProduct::find([
                'columns'    => 'sum(real_quantity_received) as real_total,total,order_product_id',
                'conditions' => 'psnoid in ({ids:array}) group by order_product_id',
                'bind'       => ['ids' => $storage_order_ids]
            ])->toArray();
            //用来下面判断po的单是否全部入库完毕
            $goods_num = array_column($purchase_storage_products, 'real_total', 'order_product_id');

            //各barcode累计实际收货数量组
            $barcode_real_quantity_received = [];
            //各po行的实际收货数量
            $po_product_real_quantity_received = [];
            //修改当前入库单-各产品明细行实际收货数量、收货单位、收货时间，以及各行入库明细关联的po明细行累计收获数量汇总本次实际收获数量值
            foreach ($storage_products as $product_k => $product_v) {
                $purchaseStorageProductModel = PurchaseStorageProduct::getFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $product_v['id']]
                ]);
                //barcode
                $barcode = $product_v['product_option_code'];
                //scm当前barcode信息
                $scm_one_good =  $goods_list[$barcode];
                //回传的当前barcode总数量
                $scm_barcode_total = $scm_one_good['num'];
                if (!in_array($barcode, array_keys($barcode_real_quantity_received))) {
                    $real_quantity_received = $scm_barcode_total - $purchaseStorageProductModel->this_time_num;
                    if ($real_quantity_received <= 0) {
                        $real_quantity_received = $scm_barcode_total;
                        $barcode_real_quantity_received[$barcode] = $scm_barcode_total;
                    } else {
                        $real_quantity_received = $purchaseStorageProductModel->this_time_num;
                        $barcode_real_quantity_received[$barcode] = $purchaseStorageProductModel->this_time_num;
                    }
                } else {
                    //剩余累计收货数量值
                    $last_has_received_num = $scm_barcode_total - $barcode_real_quantity_received[$barcode];
                    $real_quantity_received = $last_has_received_num - $purchaseStorageProductModel->this_time_num;
                    if ($real_quantity_received <= 0) {
                        $real_quantity_received = $last_has_received_num;
                        $barcode_real_quantity_received[$barcode] += $last_has_received_num;
                    } else {
                        $real_quantity_received = $purchaseStorageProductModel->this_time_num;
                        $barcode_real_quantity_received[$barcode] += $purchaseStorageProductModel->this_time_num;
                    }
                }
                $purchaseStorageProductModel->real_quantity_received = $real_quantity_received;
                $purchaseStorageProductModel->real_unit              = $scm_one_good['unit'];
                //15050需求，入库通知单明细行上需要保存scm那边回传回来的实际收货时间，我们只要日期
                $purchaseStorageProductModel->real_arrival_date      = !empty($scm_one_good['real_arrival_date']) ? date('Y-m-d', strtotime($scm_one_good['real_arrival_date'])) : null;
                $update_product_bool                                 = $purchaseStorageProductModel->i_update();
                if ($update_product_bool == false) {
                    throw new ValidationException('修改入库单状态失败product' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
                }

                //给该明细行关联的po行累计收获数量累加本次实际收获数量
                $goods_num[$purchaseStorageProductModel->order_product_id] += $real_quantity_received;
                $po_product_real_quantity_received[$purchaseStorageProductModel->order_product_id] = $purchaseStorageProductModel->real_quantity_received;
            }
            //修改po单状态
            $apply = PurchaseOrder::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $purchase_storage_data['po_id']]
            ]);
            if (!empty($apply)) {
                $purchase_o_products = $apply->getProducts()->toArray();//采购明细
                $update_po_bool = true;
                foreach ($purchase_o_products as $o_product_k => $o_product_v) {
                    //非库存类存在产品编码为空
                    if(empty($o_product_v['product_option_code'])) {
                        continue;
                    }
                    // 不存在产品编码
                    if ((isset($goods_num[$o_product_v['id']]) && $o_product_v['total'] != $goods_num[$o_product_v['id']]) || !isset($goods_num[$o_product_v['id']])) {
                        $update_po_bool = false;
                    }
                }
                if ($update_po_bool) {
                    $apply->stock_status = PurchaseEnums::PURCHASE_ORDER_STOCK_STATUS_DONE;
                    $apply->updated_at   = date('Y-m-d H:i:s');
                    $apply_bool          = $apply->i_update();
                    if ($apply_bool == false) {
                        throw new ValidationException('修改入库单状态失败po' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
                    }
                }
                //更新交付数量
                $this->updateDeliveryTotal($apply->pono, $po_product_real_quantity_received, ['id' => 0, 'name' => 'scm回调']);
            }

            //匹配资产清单,给sn码赋值,修改状态为启用
            //1.查询是否存在资产清单
            $bar_codes = array_column($params['goods'],'barCode');
            $material_assets_model = new MaterialAssetsModel();
            $material_assets = $material_assets_model::find([
                'conditions' => 'scm_no = :scm_no: and bar_code in ({bar_codes:array}) ',
                'bind' => [
                    'scm_no' => $params['orderSn'],
                    'bar_codes' => $bar_codes,
                ]
            ])->toArray();
            //2.匹配到的资产编码的数据update,资产清单有却匹配不到的记录失败记录
            $material_update_data = $material_error_data = [];
            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P2
             * scm同步回调，设置维保日期
             */
            $storage_product_barcode_list = array_column($storage_products, null, 'product_option_code');
            foreach ($material_assets as $mk => $mv) {
                //是否匹配到
                $is_match = false;
                //匹配参数中的资产
                if (isset($goods_list[$mv['bar_code']]['asset'])) {
                    foreach ($goods_list[$mv['bar_code']]['asset'] as $gk => $gv) {
                        if (isset($gv['assetCode']) && $gv['assetCode'] == $mv['asset_code']) {
                            $use_limit = $storage_product_barcode_list[$mv['bar_code']]['use_limit'];
                            $maintenance_expire_date = $use_limit ? date('Y-m-d', strtotime('-1 day', strtotime("+".$use_limit." months", strtotime($gv['real_arrival_date'])))) : null;
                            $material_update_data[] = [
                                'id'=>$mv['id'],
                                'real_arrival_date'=>$gv['real_arrival_date'],
                                'maintenance_expire_date'=> $maintenance_expire_date,
                                'sn_code'=>$gv['snCode'],
                                'status'=>Enums\MaterialEnums::ASSET_STATUS_UNUSED,
                            ];
                            $is_match = true;
                            break;
                        }
                    }
                }
                if ($is_match == false) {
                    $material_error_data[] = [
                        'scm_return_json'=>json_encode($params),
                        'order_sn'=>$params['orderSn'],
                        'psno'=>$mv['psno'],
                        'bar_code'=>$mv['bar_code'],
                        'asset_code'=>$mv['asset_code'],
                        'created_at'=>gmdate('Y-m-d H:i:s'),
                        'call_date'=>gmdate('Y-m-d H:i:s'),
                    ];
                }
            }
            $this->logger->info('material_update_data:' . json_encode($material_update_data));
            $this->logger->info('material_error_data:' . json_encode($material_error_data));
            if (!empty($material_update_data)) {
                foreach ($material_update_data as $update_data) {
                    $this_id = $update_data['id'];
                    unset($update_data['id']);
                    //update
                    $update_success = $db->updateAsDict(
                        $material_assets_model->getSource(),
                        $update_data,
                        ['conditions'=>'id=?','bind' => $this_id]
                    );
                    if (!$update_success) {
                        throw new BusinessException('更新资产数据失败',ErrCode::$UPDATE_ASSET_DATA_ERROR);
                    }
                }

            }
            if (!empty($material_error_data)) {
                //记录匹配失败的资产编码
                $material_unmatched_model = new MaterialScmAssetUnmatchedModel();
                $material_unmatched_bool = $material_unmatched_model->batch_insert($material_error_data);
                if ($material_unmatched_bool === false) {
                    throw new BusinessException('批量添加未匹配记录失败', ErrCode::$CREATE_UNMATCHED_DATA_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('storage-update-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message
        ];
    }



    /**
     * 撤回
     **/
    public function recallOrder($id, $remark)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $storage_order = PurchaseStorage::getFirst([
                'conditions' => 'id =:id: and is_del =0',
                'bind'       => ['id' => $id]
            ]);

            if (empty($storage_order)) {
                throw new BusinessException('订单不存在');
            }

            if ($storage_order->status != 2) {
                throw new ValidationException('订单状态无效,无法撤回');
            }

            $storage_order->status        = 5;
            $storage_order->recall_remark = $remark??'';
            $storage_order->update_at     = date('Y-m-d H:i:s');

            $update_bool = $storage_order->i_update();

            if ($update_bool == false) {
                throw new BusinessException('更新失败单号：' . $storage_order->psno);
            }

            $response = (new ScmService($storage_order->mach_code))->recall(['inboundSn' => $storage_order->scm_no]);

            if ($response['code'] != 1) {
                throw new ValidationException($response['message']);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (Exception $e) {
            $code         = ErrCode::$VALIDATE_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('storage-recall-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' :$message,
            'data' => []
        ];
    }

    /**
     *
     * 删除
     * */
    public function delete($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $storage_order = PurchaseStorage::getFirst([
                'conditions' => 'id =:id: and is_del =0',
                'bind'       => ['id' => $id]
            ]);

            if (empty($storage_order)) {
                throw new BusinessException('订单不存在');
            }

            if ($storage_order->status != 1 && $storage_order->status != 5) {
                throw new ValidationException('订单状态无效,无法删除');
            }

            $storage_order->is_del    = 1;
            $storage_order->update_at = date('Y-m-d H:i:s');

            $update_bool = $storage_order->i_update();

            if ($update_bool == false) {
                throw new BusinessException('更新失败单号：' . $storage_order->psno);
            }
            //更新资产清单状态
            $material_assets_model = new MaterialAssetsModel();
            $material_assets = $material_assets_model::find([
                'conditions' => 'psno = :psno: and is_deleted = :is_deleted:',
                'bind' => [
                    'psno' => $storage_order->psno,
                    'is_deleted' => Enums\MaterialClassifyEnums::IS_DELETED_NO
                ]
            ])->toArray();
            if (!empty($material_assets)){
                foreach ($material_assets as $mak=>$mav){
                    //update
                    $update_success = $db->updateAsDict(
                        $material_assets_model->getSource(),
                        ['is_deleted'=>Enums\MaterialClassifyEnums::IS_DELETED_YES],
                        ['conditions'=>'id=?','bind' => $mav['id']]
                    );
                    if (!$update_success){
                        throw new BusinessException('删除资产数据失败',ErrCode::$DELETE_ASSET_DATA_ERROR);
                    }
                }
            }
            $response = (new ScmService($storage_order->mach_code))->delete(['inboundSn' => $storage_order->scm_no]);
            if ($response['code'] != 1) {
                throw new ValidationException($response['message']);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (Exception $e) {
            $code         = ErrCode::$VALIDATE_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('storage-recall-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 枚举
     * */
    public function getPurchaseParams()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $status_arr = self::$storage_order_status;

        foreach ($status_arr as $k => $v) {
            $status_arr[$k] = static::$t->_($v);
        }

        $purchase_type = OrderService::getInstance()->getPurchaseType();
        return [
            'code' => $code,
            'message' => $message,
            'data' => [
                'status'=> $status_arr,
                'purchase_type'=> $purchase_type
                ]
        ];
    }


    /**
     * 导出列表
     *
     * @param $condition
     * @return array :
     **@throws BusinessException
     * @Date: 2021-11-02 21:11
     * @author: peak pan
     */
    public function export($condition, $user = [], $type = 0)
    {
        $data = $this->getList($condition, $user, $type, true);
        if ($data['code'] != ErrCode::$SUCCESS) {
            return $data;
        }
        $new_data = $data['data']['items'];
        $new_data = array_map('array_values', $new_data);
        $file_name = "storage_" . date("YmdHis");
        $header = [
            static::$t->_('storage_psno'),//OA入库单号
            static::$t->_('storage_scm_no'),   //SCM入库单号
            static::$t->_('storage_sap_order_no'),//SAP PO号
            static::$t->_('storage_po'),//OA PO号
            static::$t->_('storage_create_id'),  //申请人工号
            static::$t->_('storage_create_name'),  //申请人姓名
            static::$t->_('storage_apply_date'),  //申请时间
            static::$t->_('storage_remark'), //备注
            static::$t->_('storage_purchase_type'), //采购类型
            static::$t->_('storage_status'), //状态
            static::$t->_('storage_product_id'),       //序号
            static::$t->_('storage_product_option_code'),   //产品编号
            static::$t->_('storage_product_name'),   //产品名称
            static::$t->_('storage_product_price'),   //不含税单价
            static::$t->_('storage_product_this_time_num'),   //本次收货数量
            static::$t->_('storage_product_total'), //PO订单数量
            static::$t->_('storage_product_unit'), //订单单位
            static::$t->_('storage_product_total_quantity_received'),  //累积收货数量
            static::$t->_('storage_product_real_quantity_received'),      //实际收货数量
            static::$t->_('storage_product_delivery_date'),   //计划交货日期
            static::$t->_('storage_product_real_arrival_date'),   //实际入库日期
            static::$t->_('storage_product_stock_id'),   //仓库
        ];

        return $this->exportExcel($header, $new_data, $file_name);
    }



    /**
     * @param Array $condition
     * @param Intger $uid
     * @param int $type
     * @return array
     * 入库单列表
     * */

    public function getCountStorage($pono,$product)
    {
            $builder    = $this->modelsManager->createBuilder();
            $column_str = 'p.this_time_num';
            $builder->from(['o' => PurchaseStorage::class]);
            $builder->leftjoin(PurchaseStorageProduct::class, 'o.id=p.psnoid', 'p');
            $builder->columns($column_str);
            $builder->andWhere('o.po = :po: and o.status != 5 and o.is_del = 0', ['po' => $pono]);
            $builder->andWhere('p.product_option_code = :product_option_code: and  order_product_id=:order_product_id:', ['product_option_code' => $product['product_option_code'],'order_product_id'=>$product['id'] ?? 0]);
            $builder->groupBy('p.id');
            $items = $builder->getQuery()->execute()->toArray();
        return  !empty($items)?array_sum(array_column($items, 'this_time_num')):0;
    }

    /**
     * 获取仓库信息
     * */
    public function getWarehouseList($mach_code)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = (new ScmService($mach_code))->getWarehouseList();
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     * 入库通知单搜索可用的采购订单
     * @param $keywords
     * @return array
     * @date 2022/12/30
     */
    public function getPurchaseOrderOptions($keywords)
    {
        if (empty($keywords)) {
            return [];
        }
        return PurchaseOrder::find([
            'columns' => 'id,pono',
            'conditions' => 'status = :status: and stock_status = :stock_status: and purchase_type is not null and pono like :pono: and is_hand_close = :is_hand_close:',
            'bind' => [
                'status' => Enums::WF_STATE_APPROVED,
                'stock_status' => PurchaseEnums::PURCHASE_ORDER_STOCK_STATUS_UNDONE,
                'pono' => '%' . $keywords . '%',
                'is_hand_close' => PurchaseEnums::IS_HAND_CLOSE_NO
            ],
            'limit' => 100
        ])->toArray();
    }

    /**
     * 资产价格计算逻辑
     *
     * @param $cost_company_id
     * @param $price
     * @param $vate_rate
     * @return float|int|string
     */
    public function materialAssetsPrice($cost_company_id, $price, $vate_rate)
    {
        static $setting_env_data = null;
        if (is_null($setting_env_data)) {
            $setting_env_data = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_price_company_ids');
        }

        //优先级 泰国 flash express > setting_env > 默认
        static $department_company_ids = null;
        if (is_null($department_company_ids)) {
            $department_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        }

        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $cost_company_id == $department_company_ids['FlashExpress']) {
            $purchase_price = bcadd($price, ($price * $vate_rate * (1 - 0.0882)) / 100, 4);
        } else if (!empty($setting_env_data && in_array($cost_company_id, $setting_env_data))) {
            $purchase_price = $price * (1 + ($vate_rate / 100));
        } else {
            $purchase_price = $price;
        }

        return $purchase_price;
    }

    /**
     * 更新采购订单交付数量和采购订单执行状态, 不需要事务,当前调用的地方有事务包裹,新调用的需要注意
     * @param string $order_no 采购订单编号
     * @param array $order_product_delivery_total [key:采购订单行=>value:实际收货数量]
     * @param $user
     * @return bool
     * @date 2022/12/26
     * @throws BusinessException
     */
    public function updateDeliveryTotal($order_no, $order_product_delivery_total, $user)
    {
        $result = false;
        if (empty($order_product_delivery_total)) {
            return $result;
        }
        //查询订单
        $po_order = PurchaseOrder::findFirst([
            'conditions' => 'pono  = :order_no:',
            'bind' => ['order_no' => $order_no]
        ]);
        $po_order_arr = $po_order ? $po_order->toArray() : [];
        //execute_status=0是历史数据, 不处理
        if (empty($po_order_arr) || $po_order_arr['execute_status'] == 0) {
            return $result;
        }
        //查询订单每行交付数量
        $purchase_order_products_obj = PurchaseOrderProduct::find([
            'conditions' => 'poid = :poid:',
            'bind' => ['poid' => $po_order_arr['id']]
        ]);
        $purchase_order_products = $purchase_order_products_obj->toArray();
        if (empty($purchase_order_products)) {
            return $result;
        }
        //加上本次行交付数量, 判断采购订单执行状态
        $all_delivery_total = 0;
        $all_total = 0;
        $new_product_delivery_total = [];
        foreach ($purchase_order_products as $one_product) {
            $this_product_delivery_total = $one_product['delivery_total'];
            if (isset($order_product_delivery_total[$one_product['id']])) {
                $this_product_delivery_total += $order_product_delivery_total[$one_product['id']];
                $new_product_delivery_total[$one_product['id']] = $this_product_delivery_total;
            }
            //累加全部行的交付数量
            $all_delivery_total += $this_product_delivery_total;
            //累加全部行的总数量
            $all_total += $one_product['total'];
        }

        //更新本次行交付数量
        foreach ($purchase_order_products_obj as $product_obj) {
            if (isset($new_product_delivery_total[$product_obj->id])) {
                $old_product = $product_obj->toArray();
                $product_obj->delivery_total = $new_product_delivery_total[$product_obj->id];
                $after_product = $product_obj->toArray();
                if ($product_obj->save() === false) {
                    $this->logger->warning('采购订单修改[更新交付数量失败]: 待处理数据: before=' . json_encode($old_product, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($after_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product_obj));
                    throw new BusinessException('采购订单修改[更新交付数量失败]', ErrCode::$BUSINESS_ERROR);
                }
                $this->saveUpdateTotalLog(Enums::WF_PURCHASE_ORDER, $po_order_arr['id'], $po_order_arr['pono'], $old_product, $after_product, $user);
                $this->logger->info('采购订单修改[更新交付数量成功]: 待处理数据: before=' . json_encode($old_product, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($product_obj->toArray(), JSON_UNESCAPED_UNICODE));
            }
        }
        //交付状态更新
        //总交付数量 <= 0 执行状态 = 未交付
        //总交付数量 > 0  执行状态 = 部分交付
        //总交付数量 >= 所有行总数量 执行状态 = 完全交付
        $old_po_order = $po_order_arr;
        if ($all_delivery_total >= $all_total) {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_DONE;
        } elseif ($all_delivery_total > 0) {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_PARTLY;
        } else {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_NO;
        }
        if ($po_order->execute_status != $update_execute_status) {
            $po_order->execute_status = $update_execute_status;
            //如果全部交付,需要把关闭状态改成'已关闭'
            if ($update_execute_status == PurchaseEnums::ORDER_EXECUTE_STATUS_DONE) {
                $po_order->is_close = PurchaseEnums::IS_CLOSE_YES;
            }
            if ($po_order->save() === false) {
                $this->logger->warning('采购订单修改[更新执行状态失败]: 待处理数据: before=' . json_encode($old_po_order, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($po_order->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($po_order));
                throw new BusinessException('采购订单修改[更新执行状态失败]', ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info('采购订单修改[更新执行状态成功]: 待处理数据: before=' . json_encode($old_po_order, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($po_order->toArray(), JSON_UNESCAPED_UNICODE));
        }
        return true;
    }
}
