<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Models\oa\PurchaseApplyProductStaffsModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Models\ExchangeRateModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Services\ClassifyService;
use App\Modules\Material\Services\StandardService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\PurchaseType;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\OrdinaryPayment\Services\BaseService AS OrdinaryPaymentBaseService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialSauRepository;
use Mpdf\Mpdf;

class ApplyService extends BaseService
{
    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,100';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';

    public $digits = 1000000;//原来4位 扩大为6位 需刷数据库
    public $digits_num = 6;

    // 英文相关字符正则规则:
    const EN_CHAR_REGULAR = '/^[a-zA-Z\d\s`~!@$^&*()_-+=[]{};:\'\"<>\?/|\.,]+$/';// 半角 \x00-\xff

    // 中文/泰文规则
    const CN_TH_CHAR_REGULAR = '/[\x{4e00}-\x{9fa5}\x{0E00}-\x{0E7F}]+/u';

    // 产品批量上传最多允许行数
    const PRODUCT_BATCH_UPLOAD_MAX_ALLOW_COUNT = 50;

    public static $validate_detail = [
        'id' => 'Required|IntGe:1'
    ];

    public static $validate_audit = [
        'id' => 'Required|IntGe:1',
        'flag' => 'Required|IntIn:0,1',
        'note' => 'IfIntEq:flag,1|Required|StrLenGeLe:1,1000',
        'data.workflow_attachment' => 'Arr|ArrLenGeLe:0,20',
        'data.products[*].purchase_staff'=> 'Arr|ArrLenGeLe:0,5',
    ];

    public static $validate_cancel = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];

    public static $validate_product = [
        'cost_department' => 'Required|IntGe:1',
        'cost_store' => 'Required|StrLenGeLe:1,20',
    ];

    public static $validate_param = [
        'pano' => 'Required|StrLenGeLe:10,20',
        'apply_date' => 'Required|Date',
        'create_id' => 'Required',
        'create_name' => 'Required',
        'create_department' => 'Required',
        'create_department_name' => self::REQUIRED_LONG_TEXT_LEN,
        'demand_date' => 'Required|Date',
        'cost_department' => 'Required',
        'cost_department_name' => self::REQUIRED_LONG_TEXT_LEN,
        'cost_company_id' => 'Required|StrLenGeLe:1,10',
        // 基础信息:费用所属网点/总部 变更为网点 或 总部
        'cost_store' => 'Required|IntIn:1,2|>>>:expense store error',
        'currency' => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'amount' => 'Required|FloatGe:0',
        'is_submit' => 'Required|IntIn:0,1',
        'attachment' => 'Arr|ArrLenGeLe:0,10',
        'apply_reason'=> 'Required|StrLenGeLe:1,5000',
        'business_type' =>'Required|IntIn:0,'.PurchaseEnums::APPLY_BUSINESS_TYPE_ADMINISTRATION.','.PurchaseEnums::APPLY_BUSINESS_TYPE_OPERATE,//业务类型0未选择，1行政类，2运营类
        'products' => 'Required|Arr|ArrLenGe:1',
        'products[*]' => 'Required|Obj',
        'products[*].category_a' => 'Required|IntEq:0',
        'products[*].category_b' => 'Required|IntEq:0',
        'products[*].budget_id' => 'Required|IntGt:0',
        'products[*].product_id' => 'IntGe:0',
        'products[*].product_name' => 'StrLenGeLe:0,200',
        'products[*].ledger_account_id'=>'IntGe:0',         //核算科目id
        'products[*].cost_store_id'=>'Required|StrLenGeLe:1,32|>>>:expense store id error[item]',         // 费用所属网点id
        'products[*].cost_store_name'=>'Required|StrLenGeLe:0,256|>>>:expense store name error[item]',         // 费用所属网点名称
        'products[*].product_option' => 'StrLenGeLe:0,255',//规格型号
        //'products[*].product_option_other_flag'=>'Required|IntIn:0,1',      规格没用，因为本身存的就是字符串
        'products[*].product_option_code' => 'StrLenGeLe:0,30',//barcode
        'products[*].finance_code' => 'Required',
        'products[*].desc' => self::REQUIRED_LONG_TEXT_LEN,
        'products[*].total' => 'Required|IntGt:0',
        'products[*].unit' => 'Required|StrLenGeLe:1,30',
        'products[*].unit_other_flag' => 'Required|IntIn:0,1',
        'products[*].price' => 'Required|FloatGe:0',
        'products[*].total_price' => 'Required|FloatGe:0',
        'products[*].remarks' => self::NOTREQUIRED_LONG_TEXT_LEN,
        'products[*].vat7'  => 'Required|FloatGe:0',
        'products[*].vat7_rate'=>'Required|FloatGe:0',
        'products[*].metere_unit' => 'StrLenGeLe:0,20',//记量单位
    ];

    //必须有相关算数的数据
    public static $validate_products_param = [
        'currency' => 'Required|IntGt:0',
        'products' => 'Required|Arr|ArrLenGe:1',
        'products[*]' => 'Required|Obj',
        'products[*].total' => 'Required|IntGe:0',
        'products[*].price' => 'Required|FloatGe:0',
        'products[*].vat7_rate'=>'Required|FloatGe:0',
        'products[*].from_currency' => 'Required|IntGt:0'
    ];

    public static $validate_update_param = [
        'id' => 'Required|IntGe:1',
        'products' => 'Required|Arr|ArrLenGe:1',
        'products[*]' => 'Required|Obj',
        'products[*].total' => 'Required|IntGe:0',
        'products[*].remark'=> 'Required|StrLenGeLe:0,255'  //前端帮忙限制，因为有不能修改的时候，备注是空的。传过来。
    ];

    public static $validate_update_product_param = [
        'id' => 'Required|IntGe:1',
        'total' => 'Required|IntGe:0',
        'remark'=>'Required|StrLenGeLe:0,255',
    ];


    public static $validate_department = [
        'department_id' => 'Required|IntGe:1'
    ];

    //搜索barcode
    public static $validate_search_barcode = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'name' => 'StrLenGeLe:0,30', //物料名称
        'barcode' => 'StrLenGeLe:0,20', //标准型号的唯一标识
        'model' => 'StrLenGeLe:0,50', //规格型号
    ];
    //关闭申请单
    public static $validate_close = [
        'id' => 'Required|IntGe:1'
    ];

    //数据查询-采购申请单-编辑
    public static $validate_data_update = [
        'id' => 'Required|IntGe:1',
        'products' => 'Required|Arr|ArrLenGe:1',
        'products[*]' => 'Required|Obj',
        'products[*].id' => 'Required|IntGt:0',
        'products[*].product_option_code' => 'StrLenGeLe:0,30',
        'products[*].desc' => self::NOTREQUIRED_LONG_TEXT_LEN,
        'products[*].unit' => 'StrLenGeLe:0,50',//单位
        'products[*].metere_unit' => 'StrLenGeLe:0,20',//记量单位
        'products[*].product_option' => 'StrLenGeLe:0,255',//规格型号
        'products[*].purchase_staff' => 'Arr|ArrLenGeLe:0,5',//采购员
        'products[*].purchase_staff[*]' => 'IntGt:0',//采购员工号
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ApplyService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取页面初始化枚举
     * @return array
     * @date 2022/12/6
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                'execute_status' => PurchaseEnums::$apply_execute_status_list,
                'is_close' => PurchaseEnums::$is_close_list,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('采购订单申请-枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    public function defaultData($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $now_date = date("Ymd");
            $data['pano'] = 'PUR' . static::getNo($now_date);
            $data['apply_date'] = date("Y-m-d", strtotime($now_date));
            $data['create_id'] = $user['id'];
            $data = array_merge($data, $arr);
            $data['create_name'] = $this->getNameAndNickName($user['name']??"",$user['nick_name']??"");

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-get-default-data-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 采购申请单 根据人的部门返回
     * 科目与商品树状关联图
     *
     * @param $data
     * @return array
     */
    public function productList($data)
    {
        $budgetService = new BudgetService();
        $budgetTrees = $budgetService->objectList([
            'department_id' => $data['cost_department'], // 一级 部门

            // 业务侧: 1-总部；2-网点 => 预算侧: 1-网点； 2-总部
            'organization_type' => in_array($data['cost_store'], [-1, 1]) ? 2 : 1
        ], BudgetService::ORDER_TYPE_2);
        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);

        if ($endBudgetIds) {
            $budgets = BudgetObject::find([
                'conditions' => ' id in ({ids:array}) ',
                'bind' => ['ids' => $endBudgetIds]
            ])->toArray();
            $products = $budgetService->purchaseProducts(array_column($budgets, 'level_code'));
            $budgetTrees = $this->bindProduct($budgetTrees, $products);
        }

        return $budgetTrees;
    }

    private function bindProduct($budgetTrees, $products)
    {
        foreach ($budgetTrees as $k => $budgetTree) {
            if (isset($budgetTree['list'])) {
                $budgetTrees[$k]['list'] = $this->bindProduct($budgetTree['list'], $products);
            } else {
                if (isset($products[$budgetTree['level_code']])) {

                    $budgetTrees[$k]['products'] = isset($products[$budgetTree['level_code']]) ? $products[$budgetTree['level_code']] : [];
                }
            }
        }
        return $budgetTrees;
    }

    /**
     * @param $id
     * @param $uid
     * @param bool $if_download
     * @param null $flag
     * @param
     * @return array
     */
    public function getDetail($id, $uid=null, $if_download = false, $flag = null,$is_audit = false)
    {
        try {
            $apply = PurchaseApply::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($apply)) {
                return [];
            }
            $req = (new PayFlowService(Enums::WF_PURCHASE_APPLY))->getRequest($id);
            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败');
            }

            $attachment = $apply->getFile(["columns"=>"bucket_name,object_key,file_name"]);
            $attachment = $attachment ? $attachment->toArray():[];

            $products = $apply->getProducts();
            $products = $products ? $products->toArray() : [];

            $data = $apply->toArray();
            //返回业务类型文本
            $data['business_type_text'] = $data['business_type'] ? static::$t->_(PurchaseEnums::$apply_business_type[$data['business_type']]) : '';

            // 历史数据兼容处理 - 费用所属网点/总部: -1,1 总部
            $data['cost_store'] = in_array($data['cost_store'], [-1, 1]) ? 1 : 2;

            // 费用一级部门名称获取: 老数据
            if (empty($data['cost_sys_department_name']) && !empty($data['cost_sys_department'])) {
                $data['cost_sys_department_name'] = SysDepartmentModel::findFirst($data['cost_sys_department'])->name ?? '';
            }
            $categories = array_values(array_filter(array_column($products, 'category_a')));
            $categories = array_merge($categories, array_values(array_filter(array_column($products, 'category_b'))));
            $budgetIds = array_values(array_filter(array_column($products, 'budget_id')));
            if ($categories) {
                $categories = PurchaseProductCategory::find([
                    'conditions' => ' id in ({ids:array}) ',
                    'bind' => ['ids' => $categories]
                ])->toArray();
                $categories = array_column($categories, null,'id');
            }
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets = $budgetService->budgetObjectList($budgetIds);
            }

            //费用所属公司 10124需求,查询采购申请单的费用所属公司,如果为空走原逻辑
            $data['cost_company'] = $data['cost_company_id'];
            $data['cost_company_name'] = '';
            $dept_info = SysDepartmentModel::findFirst($data['cost_company_id']);
            if(!empty($dept_info)){
                $data['cost_company_name'] = $dept_info->name;
            }
            if (empty($data['cost_company'])){
                //原逻辑
                //费用所属公司
                $data['cost_company']      = '';
                $data['cost_company_name'] = '';
                //获取费用部门的部门信息, 9975需求 1. 根据【费用部门】字段，自动带出部门所属的BU级别的信息。2. 如果无BU或者所属Clevel不是COO\CEO，则默认值为空，且必填；需要选择对应COO\CEO下的BU级别的信息。
                $dept_info = SysDepartmentModel::findFirst($data['cost_department']);
                if (!empty($dept_info) && !empty($dept_info->company_id)) {
                    $coo_company_list = $this->getCooCostCompany();
                    if (!empty($coo_company_list)) {
                        foreach ($coo_company_list as $company) {
                            if ($company['cost_company_id'] == $dept_info->company_id) {
                                $data['cost_company'] = $company['cost_company_id'];
                                $data['cost_company_name'] = $company['cost_company_name'];
                                break;
                            }
                        }
                    }
                }
            }
             //新加的 采购类型 对应翻译
            $data['purchase_name'] = '';
            if(!empty($data['purchase_type'])){
                 $p_info = PurchaseType::findFirst("purchase_type = {$data['purchase_type']}");
                 if(!empty($p_info))
                     $data['purchase_name'] = static::$t->_($p_info->name_key);
            }

            $ledger_account_ids = array_values(array_filter(array_column($products,'ledger_account_id')));

            $ledgerIdToName = [] ;
            if(!empty($ledger_account_ids)){
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'],'name','id');
                }
            }

            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账--p1
             * 需要返回关联的barcode所在的财务分类的编码到前端
             */
            $product_barcode_list = [];
            $product_barcode_arr = array_values(array_filter(array_column($products, 'product_option_code')));
            if ($product_barcode_arr) {
                $product_barcode_list = StandardService::getInstance()->getFinanceBarcodeList(['barcode'=>$product_barcode_arr]);
                if ($product_barcode_list) {
                    $product_barcode_list = array_column($product_barcode_list, null, 'barcode');
                }
            }
            //采购员
            $product_staff_list = $this->getProductStaffs(array_column($products, 'id'));
            foreach ($products as $k =>$product) {
                if (isset($categories) && isset($categories[$product['category_a']])) {
                    $products[$k]['category_a_text'] = $categories[$product['category_a']]['name'];
                }
                if (isset($categories) && isset($categories[$product['category_b']])) {
                    $products[$k]['category_b_text'] = $categories[$product['category_b']]['name'];
                }
                if (isset($budgets) && isset($budgets[$product['budget_id']])) {
                    $products[$k]['budget_text'] = $budgets[$product['budget_id']]['name_' . strtolower(substr(static::$language, -2))];
                }

                //如果不通过，则不能修改数量
                if($apply->status != Enums::CONTRACT_STATUS_APPROVAL){
                    $products[$k]['is_can_update'] = 0;
                }

                //核算科目名字
                $products[$k]['ledger_account_name'] = $ledgerIdToName[$product['ledger_account_id']] ?? '';

                // 老数据的费用所属网点获取: -1 总部; -2 其他=>按网点处理
                if (empty($product['cost_store_id'])) {
                    if ($data['cost_store'] == 1) {
                        $products[$k]['cost_store_id'] = Enums::PAYMENT_HEADER_STORE_ID;
                        $products[$k]['cost_store_name'] = self::$t['payment_cost_store_type_1'];
                    } else {
                        $products[$k]['cost_store_id'] = (string)$data['cost_store'];
                        $products[$k]['cost_store_name'] = $data['cost_store_name'];
                    }
                }
                $products[$k]['wrs_code'] = '';//财务分类编码
                $products[$k]['wrs_code_name'] = '';//财务分类名称
                $products[$k]['purchase_type'] = 0;//采购类型
                $products[$k]['update_to_acceptance'] = 0;//是否验收，1否，2是
                $products[$k]['barcode_name_zh'] = '';//barcode中文名称
                $products[$k]['barcode_name_en'] = '';//barcode英文名称
                $products[$k]['barcode_name_local'] = '';//barcode本地语言名称
                if (isset($product_barcode_list[$product['product_option_code']])) {
                    $products[$k]['wrs_code'] = $product_barcode_list[$product['product_option_code']]['code'];
                    $products[$k]['wrs_code_name'] = $product_barcode_list[$product['product_option_code']]['name'];
                    $products[$k]['purchase_type'] = intval($product_barcode_list[$product['product_option_code']]['purchase_type']);
                    $products[$k]['update_to_acceptance'] = intval($product_barcode_list[$product['product_option_code']]['update_to_acceptance']);
                    $products[$k]['barcode_name_zh'] = $product_barcode_list[$product['product_option_code']]['name_zh'];
                    $products[$k]['barcode_name_en'] = $product_barcode_list[$product['product_option_code']]['name_en'];
                    $products[$k]['barcode_name_local'] = $product_barcode_list[$product['product_option_code']]['name_local'];
                }

                //采购员
                $one_product_staff = $product_staff_list[$product['id']] ?? [];
                $products[$k]['staffs'] = implode(',', $one_product_staff['staffs'] ?? []);
                $products[$k]['purchase_staff_ids'] = $one_product_staff['purchase_staff_ids'] ?? [];
                $products[$k]['purchase_staff'] = $one_product_staff['purchase_staff'] ?? [];
            }
            if ($budgetIds) {
                $data['products_v1'] = $products;
            } else {
                $data['products'] = $products;
            }
            $data['attachment'] = $attachment;
            $ask = (new FYRService())->getRequestToByReplyAsk($req,$uid);
            $data['ask_id'] = $ask ? $ask->id:'';
            $data['auth_logs'] = $this->getAuditLogs($req, $if_download);
            $data['edit_logs'] = (new PayFlowService(Enums::WF_PURCHASE_APPLY))->getEditLog($req);
            //判断是否能更改
            $data['can_edit'] = false;
            if($is_audit){
                $can_edit_data = (new PayFlowService(Enums::WF_PURCHASE_APPLY))->getCanEditFieldByReq($req,$uid);
                $data['can_edit_fields'] = $can_edit_data=== false ? (object)[] :$can_edit_data;
                $data['can_edit'] = $can_edit_data===false?false:true;
            }

            // 获取当前审批节点node_tag
            $data['node_tag'] = (new PayFlowService(Enums::WF_PURCHASE_APPLY))->getPendingNodeTag($req, $uid);
            $data = $this->handleDetailData($data,$flag);

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('Apply-get-audit-detail-failed:' . $real_message);
        }
        return [
            'code' => $code??ErrCode::$SUCCESS,
            'message' => $message??'success',
            'data' => $data ??[]
        ];
    }

    /**
     * @param int $id
     * @param int $uid
     * @return array
     */
    public function download(int $id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $download_url = '';

        $lang = $this->getLang();
        try {
            $data = $this->getDetail($id);
            $data = $data['data'];
            $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);

            $file_path = sys_get_temp_dir() . '/';
            $file_name = "apply_" . md5($id) . "_" . $lang . ".pdf";

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序
            $view->render("purchase", "apply_" . $lang);
            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode' => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path . $file_name, "f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path.$file_name, $file_path.$file_name);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path.$file_name);
            $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Mpdf\MpdfException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-detail-apply-download-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $download_url,
        ];
    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }

    /**
     * @param $data
     * @param $is_filter
     * @return array
     * @throws BusinessException
     */
    private function handleDetailData($data,$is_filter)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['amount'] = bcdiv($data['amount'], 1000, 2);
        $data['purchase_type'] = !empty($data['purchase_type']) ? $data['purchase_type'] : '';

        $key = '';
        $products = [];
        $country_code = get_country_code();
        if (!empty($data['products'])) {
            $key = 'products';
        } else {
            $key = 'products_v1';
            $productIds = array_values(array_filter(array_column($data['products_v1'], 'product_id')));
            if ($productIds) {
                $products = BudgetObjectProduct::find(
                    [
                        'conditions' => ' id in ({ids:array})',
                        'bind' => ['ids' => $productIds]
                    ]
                )->toArray();
                $products = array_column($products, null, 'id');
            }
        }
        $currency = $data['currency'];

        foreach ($data[$key] as $k => $v) {
            //有产品id,名字不是其他
            if (!empty($v['product_id'])) {
                if($key == 'products_v1'){
                    $data[$key][$k]['product_name'] = isset($products) && isset($products[$v['product_id']]) ? $products[$v['product_id']]['name_' . strtolower(substr(static::$language, -2))] : $v['product_name'];
                }else{
                    $data['products'][$k]['product_name'] = static::$t->_($v['product_name']);
                }
            }

            //单位是其他
            if (empty($v['unit_other_flag'])) {
                $data[$key][$k]['unit'] = static::$t->_($v['unit']);
            }

            $data[$key][$k]['price'] = bcdiv($v['price'],$this->digits,$this->digits_num);

            //采购订单，关联采购申请单，给前端算最大值用
            $data[$key][$k]['price_usd'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_USD, $data[$key][$k]['price']);
            $data[$key][$k]['price_thb'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_THB, $data[$key][$k]['price']);
            $data[$key][$k]['price_cny'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_CNY, $data[$key][$k]['price']);
            $data[$key][$k]['price_php'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_PHP, $data[$key][$k]['price']);
            $data[$key][$k]['price_lak'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_LAK, $data[$key][$k]['price']);
            $data[$key][$k]['price_myr'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_MYR, $data[$key][$k]['price']);
            $data[$key][$k]['price_idr'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_IDR, $data[$key][$k]['price']);
            $data[$key][$k]['price_vnd'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_VND, $data[$key][$k]['price']);
            $data[$key][$k]['price_eur'] = $this->transPriceAmount($currency,GlobalEnums::CURRENCY_EUR, $data[$key][$k]['price']);

            $data[$key][$k]['total_price'] = bcdiv($v['total_price'], 1000, 2);

            $data[$key][$k]['vat7'] = bcdiv($v['vat7'], 1000, 2);
            $data[$key][$k]['vat7_rate'] = '0';
            if(!empty($v['total_price'])){
                if (in_array($country_code,GlobalEnums::CAN_MODIFY_VAT_AMOUNT_COUNTRY_LIST)) {
                    $data[$key][$k]['vat7_rate'] = (string) $v['vat7_rate'];
                } else {
                    $data[$key][$k]['vat7_rate'] = (string) (round($v['vat7'] / $v['total_price'],2) * 100);
                }
            }

            $data[$key][$k]['all_total'] = bcdiv($v['all_total'], 1000, 2);
            $data[$key][$k]['can_order_total'] ="".($v['total'] - $v['order_total']);
        }

        //过滤可以订的为0的
        if($is_filter){
            $productArr = [];
            foreach ($data[$key] as $k=>$v){
                if($v['can_order_total'] == 0){
                    continue;
                }
                $productArr[] = $v;
            }
            $data[$key] = $productArr;
        }

        return $data;
    }


    private function getAuditLogs($req, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        return $auth_logs;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function saveOne($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $data = $this->handleData($data, $user);

            //判断是否存在，之前缺失，只靠数据库唯一索引报错，提示系统错误，不友好
            $exists = PurchaseApply::findFirst([
                'conditions' => 'pano = ?0',
                'columns' => 'id',
                'bind' => [$data['pano']]
            ]);
            if (!empty($exists)) {
                throw new ValidationException('该采购申请单编号已经存在，不能重复添加='.$data['pano'], ErrCode::$VALIDATE_ERROR);
            }

            $model = new PurchaseApply();
            $bool = $model->i_create($data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('采购申请单创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $amountInfo = [];
            foreach ($data['products'] as $product) {
                $t_model = new PurchaseApplyProduct();
                $product['paid'] = $model->id;
                $product['is_can_update'] = 1;
                /**
                 * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
                 * 应该是先获取预算分类下的产品名称下面的核算科目
                 * 预算分类下面没有产品名称的才获取预算分类下面的
                 * 这上面的逻辑前端一直就有，本期增加以下逻辑
                 * 这两个都没有才获取产品barcode下面的
                 * 但是v10124: 前端隐藏核算科目,后端保持取值逻辑这个需求后前端的逻辑传递过来的核算科目ID又没有直接存储，
                 * 而是直接获取的预算分类下的核算科目ID；跟产品沟通确认后，后端逻辑不要，直接存储前端传递过来的核算科目ID即可
                 * 故而以下代码注释掉了。
                 */
                //v10124: 前端隐藏核算科目,后端保持取值逻辑
                /*$leger_info = LedgerAccountService::getInstance()->getLedgerAccountByBudgetIdOrProdcutId($product['budget_id'],0);
                if($leger_info['code'] != ErrCode::$SUCCESS){
                    throw new BusinessException('采购申请单-获取核算科目失败=' . implode(",", $leger_info['message']), ErrCode::$CONTRACT_CREATE_ERROR);
                }
                $product['ledger_account_id'] = $leger_info['data']['ledger_account_id'];*/
                unset($product['id']);
                $product_bool = $t_model->i_create($product);

                // 准备占用预算 科目 转泰铢
                // 金额需根据汇率转换为系统默认币种的金额: 与预算侧的币种金额保持一致
                $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($product['all_total'], $data['exchange_rate'], 0);
                $amountInfo[] = [
                    'budget_id' => $product['budget_id'],
                    'amount' => $default_currency_amount,
                ];

                if ($product_bool === false) {
                    $messages = $t_model->getMessages();
                    throw new BusinessException('采购申请单-产品创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                }

                //V20771 需要存储明细行上的采购员
                if (!empty($product['product_option_code'])) {
                    $this->savePurchaseStaff($product['product_option_code'], $t_model);
                }
            }

            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_PURCHASE_APPLY;
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachments[] = $tmp;
                }
                $attach_bool = (new AttachModel())->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('采购申请单-附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            // 采购申请单预算占用
            $budgetServer = new BudgetService();

            // 网点/总部标识符 转换：统一业务侧 和 预算侧的 定义
            $data['cost_store'] = $data['cost_store'] == 1 ? -1 : $data['cost_store'];
            $budgetServer->checkBudgets($data['pano'], $amountInfo, BudgetService::ORDER_TYPE_2, $data, (integer)$data['is_submit'], $user['id']);

            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_APPLY))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('loan_create_work_flow_failed'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code = ErrCode::$SUCCESS;
                $result = [
                    'message' => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1
                ];
            } else {
                $code = $e->getCode();
                $message = $e->getMessage();
            }

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchaseApply-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 保存采购申请-明细行-barcode下在职、非子账号、编织采购员
     * @param string $barcode barcode
     * @param object $purchase_product_model 采购订单-单个明细行对象
     * @return bool
     * @throws BusinessException
     */
    private function savePurchaseStaff($barcode, $purchase_product_model)
    {
        //获取barcode上设置的采购员
        $staff_ids = MaterialSauRepository::getInstance()->getBarcodePurchaseStaffIds($barcode);
        $staff_info_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
        $purchase_product_staff = [];
        foreach ($staff_info_list as $one_staff_info) {
           if ($one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN || $one_staff_info['formal'] != StaffInfoEnums::FORMAL_IN || $one_staff_info['is_sub_staff'] != StaffInfoEnums::IS_SUB_STAFF_NO) {
               continue;
           }
            $purchase_product_staff[] = [
                'paid' => $purchase_product_model->paid,
                'apply_product_id' => $purchase_product_model->id,
                'staff_id' => $one_staff_info['staff_info_id'],
                'created_at' => date('Y-m-d H:i:s')
            ];
        }

        //存储采购员
        if ($purchase_product_staff) {
            $purchase_apply_product_staffs_model = new PurchaseApplyProductStaffsModel();
            $bool = $purchase_apply_product_staffs_model->batch_insert($purchase_product_staff);
            if ($bool === false) {
                throw new BusinessException('保存采购申请-明细行-采购员失败：' . json_encode($purchase_product_staff, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($purchase_apply_product_staffs_model), ErrCode::$BUSINESS_ERROR);
            }
        }
        return true;
    }

    /**
     * 释放 采购申请单
     * 兼容老数据
     *
     * @param $id
     * @param $user
     * @return bool
     * @throws ValidationException
     */
    public function freedBudget($id, $user)
    {
        // 验证默认国家是否开启预算(0:关闭 1:打开)
        if (!(new EnumsService())->getBudgetStatus()) {
            return true;
        }
        $purchanse = $this->purchaseInfo($id);

        // 兼容菲律宾/马来历史未开启预算的部分数据
        $state_date_time = '2022-01-01 00:00:00';
        $country_code = get_country_code();
        if (in_array($country_code,['MY','PH']) && isset($purchanse['created_at']) && $purchanse['created_at'] < $state_date_time) {
            return true;
        }
        // 兼容印尼/越南历史未开启预算的部分数据 TODO 优化代码
        $state_date_time = '2022-03-31 00:00:00';
        if (in_array($country_code,['ID','VN']) && isset($purchanse['created_at']) && $purchanse['created_at'] < $state_date_time) {
            return true;
        }

        if (isset($purchanse['products']) && isset($purchanse['products'][0]['budget_id']) && $purchanse['products'][0]['budget_id']) {
            // 是 新数据 预算占用
            $freedAmount = [];
            foreach ($purchanse['products'] as $product) {
                if (isset($freedAmount[$product['level_code']])) {
                    $freedAmount[$product['level_code']] += 0;
                } else {
                    $freedAmount[$product['level_code']] = 0;
                }
            }

            $budgetService = new BudgetService();
            $result = $budgetService->re_back_budget($purchanse['pano'], $user, BudgetService::ORDER_TYPE_2, $freedAmount);
            $this->getDI()->get('logger')->info('freed_budget  释放预算判断 params ' . json_encode([
                    $purchanse['pano'],
                    $user,
                    BudgetService::ORDER_TYPE_2,
                    $freedAmount
                ]) . ' results ' . json_encode([$result], JSON_UNESCAPED_UNICODE));

            if ($result['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($result['message']);
            }
        }
        return true;
    }

    /**
     * 获取采购申请单详情 加上 商品列表 兼容老数据
     *
     * @param $id
     */
    public function purchaseInfo($id)
    {
        $apply = PurchaseApply::findFirst([
            'conditions' => 'id = :id: ',
            'bind' => ['id' => $id]
        ]);
        $apply = $apply ? $apply->toArray() : [];
        if ($apply) {
            $products = PurchaseApplyProduct::find([
                'conditions' => ' paid = :paid: ',
                'bind' => ['paid' => $id]
            ])->toArray();
            $apply['products'] = $products;
        }

        return $apply;
    }

    /**
     * @param $data
     * @param $user
     * @return array|string
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            throw new ValidationException('data empty', ErrCode::$VALIDATE_ERROR);
        }

        $staffInfo = (new UserService())->getUserByIdInRbi($user['id']);
        $is_check_flag=0;

        $data['amount'] = bcmul(floatval($data['amount']), 1000);
        $data['status'] = Enums::CONTRACT_STATUS_PENDING;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = $data['created_at'];
        $data['create_id'] = $user['id'] ?? 0;
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '',$user['nick_name'] ?? '');
        $data['sys_department_id'] = $staffInfo->sys_department_id ?? '';
        $data['node_department_id'] = $staffInfo->node_department_id ?? '';

        // 申请人个人手机号
        $data['create_mobile'] = !empty($staffInfo->mobile) ? $staffInfo->mobile : '';

        // 存储费用所属公司名称
        $data['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($data['cost_company_id'] ?? '');

        $data['apply_type'] = Enums::PURCHASE_APPLY_TYPE_DEFAULT;

        // 基本信息: 费用所属网点/总部变更：具体网点 变更为 总部[1] 或 网点[2]两个类型
        $data['cost_store'] = isset($data['cost_store']) && $data['cost_store'] == 1 ? 1 : 2;
        $data['cost_store_name'] = $data['cost_store'] == 1 ? Enums::PAYMENT_HEADER_STORE_NAME : 'Branch';

        $is_ad = 0;

        // 获取币种与系统默认币种的汇率
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        // 费用所属一级部门
        $sysDepartment = StaffService::getInstance()->getParentDepartment($data['cost_department'], 1);
        $data['cost_sys_department'] = 0;
        $data['cost_sys_department_name'] = '';
        if ($sysDepartment) {
            $data['cost_sys_department'] = $sysDepartment['id'];
            $data['cost_sys_department_name'] = $sysDepartment['name'];
        }

        $budgetIds = array_column($data['products'], 'budget_id');

        if (
            $this->isHasNotEndBudgetIds($data, $budgetIds) // 验证是否都是末级科目
            ||
            $this->isHasNotRelationBind($data['products']) // 验证存在科目与商品的错误绑定关系
        ) {
            // 验证 存在非末级科目ID
            throw new ValidationException('存在非末级科目ID 或 存在错误的商品绑定关系, 请重新选择', ErrCode::$VALIDATE_ERROR);
        }

        $budgetService = new BudgetService();

        $budgetList = $budgetService->budgetObjectList($budgetIds);
        $levelCodes = array_column($budgetList, 'level_code');
        $purchaseProducts = $budgetService->purchaseProducts($levelCodes);

        //总数后端算下
        $this->handleProductsData($data);

        foreach ($data['products'] as $k => $v) {
            // level_code
            $data['products'][$k]['level_code'] = $budgetList[$v['budget_id']]['level_code'];

            //名字不是其他
            if (!empty($v['product_id'])) {
                $levelCode = $budgetList[$v['budget_id']]['level_code'];
                $tmpPurchaseProducts = array_column($purchaseProducts[$levelCode], null, 'id');

                $data['products'][$k]['product_name'] = $tmpPurchaseProducts[$v['product_id']]['name'];
            }

            $data['products'][$k]['unit_other_flag'] = 1;
            //该产品行传递了barcode&&该barcode也需要验收，则标记该产品行需要验收
            $is_check = 0;
            if ($v['product_option_code'] && $v['update_to_acceptance'] == Enums\MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $is_check_flag = 1;
                $is_check = 1;
            }
            $data['products'][$k]['is_check'] = $is_check;
            //添加是po使用数量默认为0
            $data['products'][$k]['order_use_total'] = 0;
        }
        $data['is_check_flag'] = $is_check_flag;

        //如果有广告，不能有费用网点
        if (!empty($is_ad) && !empty($data['cost_store'])) {
            throw new ValidationException('费用网点不能申请广告费', ErrCode::$VALIDATE_ERROR);
        }
        //执行状态,新单置为未生成po
        $data['execute_status'] = PurchaseEnums::APPLY_EXECUTE_STATUS_NO;
        $data['is_close'] = PurchaseEnums::IS_CLOSE_NO;
        return $data;
    }

    /**
     * 计算相关提取出来，多一次循环
     * @param array $data 传的引用
     * @param bool $is_mul_thousands 是否乘以（单价乘以1w)
     * @param bool $is_submit 是否属于提交
     * @throws BusinessException
     */
    public function handleProductsData(&$data, $is_mul_thousands = true, $is_submit = true)
    {
        $amount = 0;
        $country_code = get_country_code();

        //传引用了
        foreach ($data['products'] as $k => &$v) {
            //单价转string,避免程序自动转科学技术法
            $v['price'] = strval($v['price']);
            //16712需要根据币种进行转换
            if (isset($v['from_currency']) && isset($data['currency']) && $v['from_currency'] != $data['currency']) {
                $v['price'] = ApplyService::getInstance()->transPriceAmount($v['from_currency'], $data['currency'], $v['price']);
            }
            //单价
            $v['price'] = round($v['price'], $this->digits_num);
            $v['price'] = bcadd($v['price'], 0, $this->digits_num);

            //计算之后和前端有误差 就取2为计算 防止 3位的计算和前端展示的2位数不一样
            $v['total_price'] = round($v['price'] * $v['total'], 2);

            $v['total_price'] = bcadd($v['total_price'], 0, 3);
            if ($is_mul_thousands) {
                $v['total_price'] = bcmul($v['total_price'], 1000);
            }

            //税额
            if ($is_submit && in_array($country_code, GlobalEnums::CAN_MODIFY_VAT_AMOUNT_COUNTRY_LIST)) {
                $v['vat7'] = round($v['vat7'], 2);
            } else {
                $v['vat7'] = round($v['price'] * $v['total'] * $v['vat7_rate'] / 100, 2);
            }

            $v['vat7'] = bcadd($v['vat7'], 0, 2);
            if ($is_mul_thousands) {
                $v['vat7'] = bcmul($v['vat7'], 1000);
            }

            //含税金额
            $v['all_total'] = bcadd($v['vat7'], $v['total_price'], 2);

            //单价最后乘以1w 改成 后6位了
            if ($is_mul_thousands) {
                //价格保留4位小数
                $v['price'] = bcmul($v['price'], $this->digits);
            }

            //总数
            $amount = bcadd($amount, $v['all_total'], 2);
        }

        //总数后端算下
        $data['amount'] = $amount;
    }


    /**
     * 是否存在非末级的科目ID
     *
     * @param $data
     * @param $budgetIds
     *
     */
    protected function isHasNotEndBudgetIds($data, $budgetIds)
    {
        $budgetTrees = $this->productList($data);
        $budgetService = new BudgetService();
        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);
        return array_diff($budgetIds, $endBudgetIds) ? 1 : 0;
    }

    /**
     * 是否存在错误的科目与商品绑定关系
     *
     * @param $products
     *
     */
    protected function isHasNotRelationBind($products)
    {
        $budgetIds = array_column($products, 'budget_id');
        $budgetService = new BudgetService();
        $objectList = $budgetService->budgetObjectList($budgetIds);
        $levelCodes = array_column($objectList, 'level_code');
        $purchaseProducts = $budgetService->purchaseProducts($levelCodes);
        foreach ($products as $product) {
            if ($product['budget_id'] && $product['product_id']) {
                if (!(
                    isset($objectList[$product['budget_id']])
                    && isset($purchaseProducts[$objectList[$product['budget_id']]['level_code']])
                    && in_array($product['product_id'], array_column($purchaseProducts[$objectList[$product['budget_id']]['level_code']], 'id'))
                )) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param $condition
     * @param $user
     * @param int $type
     * @return array
     */
    public function getList($condition, $user = null, $type = 0)
    {
        $condition['uid'] = $user['id'];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $column_str = 'c.id,c.pano,c.create_department_name,c.apply_date,c.currency,c.amount,c.cost_department_name,c.cost_store_name,c.status,c.is_can_update,c.is_close,c.execute_status';
            //本次数据查询加上参数LIST_TYPE_DATA了,给这里也加上
            if (in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR, self::LIST_TYPE_DATA])) {
                $column_str .= ',c.create_id,c.create_name';
            }

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $column_str .= ',log.audit_at';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => PurchaseApply::class]);

            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
             * 发现历史bug，采购申请单，按照产品编号搜索不起作用，在此增加产品编号
             */
            if (!empty($condition['product_name']) || (isset($condition['product_option_code']) && !empty($condition['product_option_code'])) || !empty($condition['purchase_staff'])) {
                $builder->leftjoin(PurchaseApplyProduct::class, 'c.id=p.paid', 'p');
            }

            $builder = $this->getCondition($builder, $condition, $type, $user);

            $count = (int) $builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns($column_str);
                $builder->groupBy('c.id');

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('c.id desc');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $user['id'], $type);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('purchase-apply-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param int $type
     * @param null $user
     * @return mixed|object
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type = 0, $user = [])
    {
        $product_name = $condition['product_name'] ?? '';
        $pano = $condition['pano'] ?? '';

        // v11037: 支持审批状态多选
        $status = isset($condition['status']) && is_array($condition['status']) ? $condition['status'] : [];
        if (!empty($condition['status']) && !is_array($condition['status'])) {
            $status = [
                $condition['status']
            ];
        }

        $cost_department = $condition['cost_department'] ?? '';
        $cost_store = $condition['cost_store'] ?? '';
        $apply_date_start = $condition['apply_date_start'] ?? '';
        $apply_date_end = $condition['apply_date_end'] ?? '';
        $create_id = $condition['create_id'] ?? '';
        $approved_start_date    = $condition['approved_start_date'] ?? '';
        $approved_end_date      = $condition['approved_end_date'] ?? '';
        $is_close = $condition['is_close'] ?? [];
        $execute_status = $condition['execute_status'] ?? [];
        $purchase_staff = $condition['purchase_staff'] ?? [];

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $cost_company_id = $condition['cost_company_id'] ?? [];
        $product_option_code = $condition['product_option_code'] ?? '';

        // 审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PURCHASE_APPLY], $condition['uid'], 'c');

        } else if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('c.create_id = :uid:', ['uid' => $condition['uid']]);

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'c'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PURCHASE_APPLY], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'c',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'cost_department',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_APPLY, $table_params);
        }

        if (!empty($pano)) {
            $builder->andWhere('c.pano = :pano:', ['pano' => $pano]);
        }

        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('c.cost_company_id IN ({cost_company_id:array}) ', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('c.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        //审核，或者是下载，或者数据
        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('c.create_id = :create_id: or c.create_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->inWhere('c.status', array_values($status));
        }

        if (!empty($cost_department)) {
            $builder->andWhere('c.cost_department = :cost_department:', ['cost_department' => $cost_department]);
        }

        if (!empty($cost_store)) {
            $builder->andWhere('c.cost_store = :cost_store:', ['cost_store' => $cost_store]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('c.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('c.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($product_name)) {
            $builder->andWhere('p.product_name = :product_name:', ['product_name' => $product_name]);
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
         * 发现历史bug，采购申请单，按照产品编号搜索不起作用，在此增加产品编号
         */
        if (!empty($product_option_code)) {
            $builder->andWhere('p.product_option_code = :product_option_code:', ['product_option_code' => $product_option_code]);
        }

        //起始日期
        if (!empty($approved_start_date)) {
            $approved_start_date .= ' 00:00:00';
            $builder->andWhere('c.approve_at >= :approve_start_date:', ['approve_start_date' => $approved_start_date]);
        }

        //截止日期
        if (!empty($approved_end_date)) {
            $approved_end_date .= ' 23:59:59';
            $builder->andWhere('c.approve_at <= :approve_end_date:', ['approve_end_date' => $approved_end_date]);
        }
        //是否关闭
        if (!empty($is_close)) {
            foreach ($is_close as &$is_close_v) {
                $is_close_v = (int)$is_close_v;
            }
            $builder->inWhere('c.is_close', $is_close);
        }
        //执行状态
        if (!empty($execute_status)) {
            foreach ($execute_status as &$execute_status_v) {
                $execute_status_v = (int)$execute_status_v;
            }
            $builder->inWhere('c.execute_status', $execute_status);
        }

        //V20771 采购员搜索
        if ($purchase_staff) {
            $builder->leftJoin(PurchaseApplyProductStaffsModel::class, 'staff.apply_product_id = p.id', 'staff');
            $builder->inWhere('staff.staff_id', $purchase_staff);
        }
        return $builder;
    }

    /**
     * @param $items
     * @param null $uid
     * @param int $type
     * @return array
     */
    private function handleItems($items, $uid = null, $type = 0)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        //是否特权用户
        $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($uid);
        $product_staff_list = $this->getProductStaffs(array_column($items, 'apply_product_id'));
        foreach ($items as &$item) {
            $status = Enums::$loan_status[$item['status']] ?? '';
            $item['amount'] = bcdiv($item['amount'], 1000, 2);
            $item['status_text'] = static::$t->_($status);
            $payment_currency = GlobalEnums::$currency_item[$item['currency']] ?? '';
            $item['currency_text'] = static::$t->_($payment_currency);

            //只有我的申请界面有,如果不是通过也是0
            if (empty($uid) || $item['status'] != Enums::CONTRACT_STATUS_APPROVAL) {
                $item['is_can_update'] = PurchaseEnums::IS_CAN_UPDATE_NO;
            } else {
                if (!$is_privilege_staff_id && $type == self::LIST_TYPE_DATA) {
                    $item['is_can_update'] = PurchaseEnums::IS_CAN_UPDATE_NO;
                }
            }

            $item['is_close_text'] = isset(PurchaseEnums::$is_close_list[$item['is_close']]) ? static::$t->_(PurchaseEnums::$is_close_list[$item['is_close']]) : '';
            $item['execute_status_text'] = isset(PurchaseEnums::$apply_execute_status_list[$item['execute_status']]) ? static::$t->_(PurchaseEnums::$apply_execute_status_list[$item['execute_status']]) : '';
            //是否可以关闭
            $item['can_close'] = PurchaseEnums::CAN_CLOSE_NO;
            //申请状态为"已通过",执行状态为"未生成PO","部分生成PO",且关闭状态为未关闭
            $can_close_conditions = $item['status'] == Enums::PURCHASE_APPLY_STATUS_APPROVAL && in_array($item['execute_status'], [PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY, PurchaseEnums::APPLY_EXECUTE_STATUS_NO]) && isset($item['is_close']) && $item['is_close'] == PurchaseEnums::IS_CLOSE_NO;
            if ($type == self::LIST_TYPE_DATA) {
                //数据查询-必须要有特权才能关闭
                if ($can_close_conditions && $is_privilege_staff_id) {
                    $item['can_close'] = PurchaseEnums::CAN_CLOSE_YES;
                }
            } elseif ($type == self::LIST_TYPE_APPLY) {
                if ($can_close_conditions) {
                    $item['can_close'] = PurchaseEnums::CAN_CLOSE_YES;
                }
            }
            $one_product_staff = isset($item['apply_product_id']) ? ($product_staff_list[$item['apply_product_id']] ?? []) : [];
            $item['staffs'] = implode(',', $one_product_staff['staffs'] ?? []);
        }
        return $items;
    }

    /**
     * 获取采购申请单-各明细行下采购员
     * @param array $apply_product_ids 采购申请单-明细行ID组
     * @return array
     */
    private function getProductStaffs($apply_product_ids)
    {
        $group_list = [];
        if (empty($apply_product_ids)) {
            return $group_list;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['staff' => PurchaseApplyProductStaffsModel::class]);
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_id = hr.staff_info_id', 'hr');
        $builder->columns('staff.apply_product_id, staff.staff_id, hr.name');
        $builder->inWhere('staff.apply_product_id', $apply_product_ids);
        $list = $builder->getQuery()->execute()->toArray();

        foreach ($list as $item) {
            $group_list[$item['apply_product_id']]['staffs'][] = $item['name'] . '（' . $item['staff_id'] . '）';
            $group_list[$item['apply_product_id']]['purchase_staff_ids'][] = $item['staff_id'];
            $group_list[$item['apply_product_id']]['purchase_staff'][] = ['staff_info_id' => $item['staff_id'], 'name' => $item['name']];
        }
        return $group_list;
    }

    public function getDownloadList($condition, $type = 0, $user = [])
    {
        $condition['uid'] = null;
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $column_str = 'c.id,
            c.pano,
            c.create_name,
            c.create_id,
            c.create_mobile,
            c.apply_date,
            c.amount,
            c.currency,
            c.cost_sys_department_name,
            c.cost_department_name,
            c.cost_store_name,
            c.status,
            c.updated_at,
            c.apply_reason,
            c.cost_company_name,
            p.budget_id,
            p.ledger_account_id,
            p.category_a,
            p.category_b,
            p.product_name,
            p.product_id,
            p.product_option,
            p.product_option_code,
            p.finance_code,
            p.total,
            p.order_total,
            p.unit,
            p.unit_other_flag,
            p.price,
            p.total_price,
            p.id as apply_product_id,
            p.vat7_rate,
            p.vat7,
            p.all_total,
            p.[desc],
            c.is_close,
            c.execute_status,
            p.order_use_total
            ';

            $builder->columns($column_str);
            $builder->from(['p' => PurchaseApplyProduct::class]);
            $builder->leftJoin(PurchaseApply::class, "p.paid=c.id", "c");
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $builder->orderBy('c.id desc');
            $items = $builder->getQuery()->execute()->toArray();

            $items = $this->handleItems($items);
            $data = [
                'items' => $items,
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-apply-download-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * 导出列表
     * @param $condition
     * @param int $type
     * @param array $user
     * @return array|mixed
     * @throws BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function export($condition, $type = 0, $user = [])
    {
        ini_set('memory_limit', '1024M');

        $data = $this->getDownloadList($condition, $type, $user);
        if ($data['code'] != ErrCode::$SUCCESS) {
            return $data;
        }
        $myLang = strtolower(
            substr(static::$language, -2)
        );

        if (!in_array($myLang, ['en', 'th', 'cn'])) {
            $myLang = 'en';
        }
        $data = $data['data']['items'];

        $apply_product_ids = array_column($data,"apply_product_id");

        $orders = $this->getOrderProducts($apply_product_ids);
        if ($orders['code'] != ErrCode::$SUCCESS) {
            return $orders;
        }
        $orders = $orders['data'];

        $list = PurchaseProductCategory::find()->toArray();
        $nameKeyArr = array_column($list,"name_key","id");

        $budgetIds = array_values(array_unique(array_column($data, 'budget_id')));
        if ($budgetIds) {
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
        }

        // 获取核算科目配置表数据
        $ledger_account_list = LedgerAccountService::getInstance()->getList();
        $ledger_account_list = !empty($ledger_account_list['data']) ? array_column($ledger_account_list['data'], 'name', 'id') : [];
        //获取商品信息
        $productIds = array_values(array_filter(array_column($data, 'product_id')));
        if ($productIds) {
            $products = BudgetObjectProduct::find(
                [
                    'conditions' => ' id in ({ids:array})',
                    'bind' => ['ids' => $productIds]
                ]
            )->toArray();
            $products = array_column($products, null, 'id');
        }


        $new_data = [];
        foreach ($data as $key => $val) {
            $new_data[$key] = [];
            $new_data[$key][] = $val['pano'];
            $new_data[$key][] = $val['create_name'];
            $new_data[$key][] = $val['create_id'];
            $new_data[$key][] = $val['create_mobile'];
            $new_data[$key][] = $val['apply_date'];
            $new_data[$key][] = $val['amount'] . $val['currency_text'];
            $new_data[$key][] = $val['cost_company_name'] ?? "";
            $new_data[$key][] = $val['cost_sys_department_name'] ?? "";
            $new_data[$key][] = $val['cost_department_name'] ?? "";
            $new_data[$key][] = $val['cost_store_name'] ?? '';
            $new_data[$key][] = $val['status_text'];

            // 执行状态
            $new_data[$key][] = !empty(PurchaseEnums::$apply_execute_status_list[$val['execute_status']]) ? static::$t->_(PurchaseEnums::$apply_execute_status_list[$val['execute_status']]) : '';

            // 关闭状态
            $new_data[$key][] = !empty(PurchaseEnums::$is_close_list[$val['is_close']]) ? static::$t->_(PurchaseEnums::$is_close_list[$val['is_close']]) : '';
            if ($val['status'] == Enums::CONTRACT_STATUS_APPROVAL) {
                $new_data[$key][] = $val['updated_at'];
            } else {
                $new_data[$key][] = '';
            }

            if ($val['budget_id'] && isset($budgets[$val['budget_id']])) {
                $new_data[$key][] = $budgets[$val['budget_id']]['name_' . strtolower(substr(self::$language, -2))];
            } else {
                $new_data[$key][] = '';
            }

            // 核算科目 ledger_account_id
            $new_data[$key][] = !empty($val['ledger_account_id']) ? $ledger_account_list[$val['ledger_account_id']] ?? ''  : '';

            if (!empty($nameKeyArr[$val['category_a']])) {
                $new_data[$key][] = static::$t->_($nameKeyArr[$val['category_a']]);
            } else {
                $new_data[$key][] = '';
            }

            if (!empty($nameKeyArr[$val['category_b']])) {
                $new_data[$key][] = static::$t->_($nameKeyArr[$val['category_b']]);
            } else {
                $new_data[$key][] = '';
            }

            $new_data[$key][] = isset($products) && isset($products[$val['product_id']]) ? $products[$val['product_id']]['name_' . $myLang] : $val['product_name'];

            $new_data[$key][] = $val['product_option'];
            $new_data[$key][] = $val['product_option_code'];

            $new_data[$key][] = $val['desc'];
            $new_data[$key][] = $val['total'];
            $new_data[$key][] = $val['unit'];
            $new_data[$key][] = bcdiv($val['price'],$this->digits,$this->digits_num);
            $new_data[$key][] = bcdiv($val['total_price'],1000,2);
            $new_data[$key][] = $val['vat7_rate'].'%';
            $new_data[$key][] = bcdiv($val['vat7'],1000,2);
            $new_data[$key][] = bcdiv($val['all_total'],1000,2);
            $new_data[$key][] = $val['order_use_total'];

            if (empty($orders[$val['apply_product_id']])) {
                $new_data[$key][] = "";
                $new_data[$key][] = "";
                $new_data[$key][] = "";
                $new_data[$key][] = "";
                $new_data[$key][] = "";

            } else {
                //相关采购订单编码
                $new_data[$key][] = $orders[$val['apply_product_id']]['pono'];

                $str = $orders[$val['apply_product_id']]['status'];
                $status_str = "";

                if(!empty($str)){
                    $statusArr = explode(";",$str);
                    foreach ($statusArr as $kk=>$vv){
                        $status_str .= (static::$t->_(Enums::$loan_status[$vv]) ?? '').";";
                    }
                    $status_str = trim($status_str,";");
                }


                //相关采购订单申请状态
                $new_data[$key][] = $status_str;

                //相关采购订单产品数量
                $new_data[$key][] = $orders[$val['apply_product_id']]['total'];

                //相关采购订单当前审批人
                $new_data[$key][] = $orders[$val['apply_product_id']]['auditor_id'];

                //相关采购订单申请日期
                $new_data[$key][] = $orders[$val['apply_product_id']]['apply_date'];
            }

            //采购申请单原因
            $new_data[$key][] = $val['apply_reason']??'';
            $new_data[$key][] = $val['staffs'];//采购员
        }
        $file_name = "purchase_apply_" . date("YmdHis");
        $header = [
            static::$t->_('global.number'),//编号
            static::$t->_('global.applicant.name'),   //申请人
            static::$t->_('global.applicant.id'),//申请人工号
            static::$t->_('re_field_apply_mobile'),//申请人电话
            static::$t->_('global.apply.date'),//申请日期
            static::$t->_('global.amount'),  //金额
            static::$t->_('purchase_apply_cost_company'),  //费用所属公司

            static::$t->_('re_filed_apply_cost_first_department'),  // 费用所属一级部门
            static::$t->_('global.cost.department.name'),  //费用部门
            static::$t->_('global.cost.store.name'),  //费用网点
            static::$t->_('global.apply.status.text'), //处理状态
            static::$t->_('purchase_apply_field_execute_status'),  //执行状态
            static::$t->_('purchase_apply_field_is_close'),  //关闭状态
            static::$t->_('purchase_apply_approve_date'),       //审核通过时间
            static::$t->_('pruchase_budget_classification'),   //产品分类
            static::$t->_('purchase_apply_ledger_account'),   // 核算科目
            static::$t->_('purchase_payment_invoice_head_3'),   //产品分类（一级分类）
            static::$t->_('purchase_payment_invoice_head_4'),   //申请事项（二级分类）
            static::$t->_('purchase_payment_invoice_head_5'),   //产品名称
            static::$t->_('purchase_apply_field_product_option'), //规格型号
            static::$t->_('purchase_apply_field_bar_code'),//产品编号bar code
            static::$t->_('purchase_apply_field_desc'),      //产品描述
            static::$t->_('purchase_payment_invoice_head_6'),   //数量
            static::$t->_('purchase_payment_invoice_head_7'),   //单位
            static::$t->_('purchase_apply_field_price'),        //单价
            static::$t->_('purchase_apply_field_total_price'),  //不含税金额
            static::$t->_('purchase_apply_field_vat_rate'),//税率
            static::$t->_('purchase_apply_field_vat_amount'),//税额
            static::$t->_('purchase_apply_field_all_total'),//含税金额
            static::$t->_('purchase_apply_order_use_total'),//已关联PO的数量
            static::$t->_('purchase_apply_field_link_order_code'),  //相关采购订单编码
            static::$t->_('purchase_apply_field_link_order_status'),//相关采购订单状态
            static::$t->_('purchase_apply_field_link_order_num'),   //相关采购订单产品数量
            static::$t->_('purchase_apply_field_link_order_auditor_id'),    //相关采购订单当前审批人
            static::$t->_('purchase_apply_field_link_order_apply_date'),    //相关采购订单申请日期
            static::$t->_('purchase_apply_field_apply_reason'),        //申请原因
            static::$t->_('purchase_set.staff'),//采购员
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * @param $id
     * @return array
     */
    public function getPcCode($id){

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try{
            $data = $this->getPcCodeArrByDepartmentId($id);
        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('apply-get-pc-code-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' =>$data
        ];
    }

    /**
     * 根据部门ID获得费用中心
     * @param $departmentId
     * @return array
     */
    public function getPcCodeArrByDepartmentId($departmentId)
    {
        $data = [];
        $data['cost_center_code_is_update'] = '0';
        $data['cost_center_code_list'] = [];
        $data['cost_center_code'] = '';
        $data['cost_company_id'] = '';
        $data['cost_company_name'] = '';

        $cost_company_list = $this->getCooCostCompany();
        $cost_company_kv = array_column($cost_company_list,'cost_company_name','cost_company_id');
        $department_info = DepartmentModel::findFirst($departmentId);
        if(!empty($department_info) && key_exists($department_info->company_id,$cost_company_kv)){
            $data['cost_company_id'] = $department_info->company_id;
            $data['cost_company_name'] = $cost_company_kv[$department_info->company_id];
        }
        $item = Pccode::findFirst(["conditions" => "department_id = :id:", "bind" => ["id" => $departmentId]]);
        if (!empty($item)) {
            $data['cost_center_code'] = $item->pc_code;
        }
        $asset_department_ids = EnvModel::getEnvByCode('purchase_department_asset_ids', "55,75,77,76");
        $asset_departmentArr = explode(",", $asset_department_ids);
        if (in_array($departmentId, $asset_departmentArr)) {
            $data['cost_center_code_is_update'] = '1';
            $data['cost_center_code_list'][] = ['label' => 'PC40005', 'value' => 'PC40005'];
            $data['cost_center_code_list'][] = ['label' => 'PC40021', 'value' => 'PC40021'];
            $data['cost_center_code_list'][] = ['label' => 'PC60002', 'value' => 'PC60002'];
        }
        return $data;
    }

    /**
     * 判断用户是否可以更改费用中心
     * @param $userId
     * @return string
     */
    public function isCanUpdateCostDepartment($userId)
    {
        $userIds = EnvModel::getEnvByCode('purchase_is_can_update_cost_department_user_ids', "41081");
        $userArr = explode(",", $userIds);
        $department_id = EnvModel::getEnvByCode('purchase_is_can_update_cost_department_id', "8");
        $department_id = (int)$department_id;
        //赵晨曦需求 https://l8bx01gcjr.feishu.cn/docs/doccnfaNJugM3pCiDUWevxdalOc
        //采购部(Group Procurement)所有员工可以更改费用部门
        $userDep = HrStaffInfoModel::getUserInfo($userId, 'node_department_id');
        if (!$userDep) {
            return '0';
        }

        //获取采购部门部门链
        $departments = [];
        $dept = SysDepartmentModel::findFirst([
            'conditions' => 'id =:id:',
            'bind'   =>['id'=>$department_id]
        ]);
        if ($dept) {
            $departments = SysDepartmentModel::find([
                'conditions' => 'ancestry_v3 like :department_chain: or id = :department_id:',
                'bind'       => [
                    'department_chain' => $dept->ancestry_v3 . '/%',
                    'department_id' => $dept->id,
                ],
            ])->toArray();
            $departments = array_column($departments, 'id');
        }

        //如果是指定人员或者是采购部门的人，可以调整费用部门
        if (in_array($userId, $userArr) || in_array($userDep['node_department_id'], $departments)) {
            return '1';
        } else {
            return '0';
        }
    }

    public function getUserMetaFromBi($userId)
    {

        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }

        $data = [];
        $data['create_id'] = $model->staff_info_id ?? "";
        $data['create_name'] = $this->getNameAndNickName($model->name ?? "",$model->nick_name??"");
        $data['create_mobile'] = !empty($model->mobile) ? $model->mobile : '';

        $department_id = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $data['create_department'] = empty($node_department_id) ? $department_id : $node_department_id;
        $data['cost_department'] = $data['create_department'];

        // 费用所属一级部门
        $sys_department_info = StaffService::getInstance()->getParentDepartment($data['cost_department'], 1);
        $data['cost_sys_department'] = '';
        $data['cost_sys_department_name'] = '';
        if ($sys_department_info) {
            $data['cost_sys_department'] = $sys_department_info['id'];
            $data['cost_sys_department_name'] = $sys_department_info['name'];
        }

        $t = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind" => [
                "id" => $data['create_department'],
            ]
        ]);
        $data['create_department_name'] = !empty($t) ? $t->name : "";
        $data['cost_department_name'] = $data['create_department_name'];
        //COO/CEO下的BU级公司列表
        $data['cost_company_list'] = $this->getCooCostCompany();
        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $data['cost_company_id'] = '';
        $data['cost_company_name'] = '';
        $cost_company_kv = array_column($data['cost_company_list'],'cost_company_name','cost_company_id');
        if(!empty($t) && key_exists($t->company_id,$cost_company_kv)){
            $data['cost_company_id'] = $t->company_id;
            $data['cost_company_name'] = $cost_company_kv[$t->company_id];
        }
        $data['department_id'] = $department_id;
        $data['node_department_id'] = $node_department_id;

        $data['cost_department_is_update'] = $this->isCanUpdateCostDepartment($userId);

        //费用中心相关数据
        $center_code = $this->getPcCodeArrByDepartmentId($data['cost_department']);

        $data = array_merge($data, $center_code);

        return $data;
    }

    /**
     * 根据采购申请单产品id,找到采购订单
     * @param $apply_product_ids
     * @return array
     */
    public function getOrderProducts($apply_product_ids){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $sql = '';

        try {
            if (empty($apply_product_ids)) {
                throw new ValidationException(static::$t->_('interior_goods_not_null'), ErrCode::$VALIDATE_ERROR);
            }

            $apply_product_ids = array_values(array_filter(array_unique($apply_product_ids)));

            $sql = "SELECT
group_concat(c.pono SEPARATOR ';') as pono,group_concat(c.status SEPARATOR ';') as status,group_concat(po.total SEPARATOR ';') as total,group_concat(w.current_node_auditor_id SEPARATOR ';') as auditor_id,group_concat(c.apply_date SEPARATOR ';') as apply_date,po.apply_product_id
FROM
	purchase_order_product AS po
	LEFT JOIN purchase_order AS c ON c.id = po.poid
	LEFT JOIN workflow_request AS w ON w.biz_value = c.id 
	and w.biz_type = 10 
WHERE
	po.apply_product_id IN (".implode(",",$apply_product_ids).") GROUP BY po.apply_product_id;";

            $items = $this->db_oa->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC);
            if(!empty($items)){
                $data = array_column($items,null,"apply_product_id");
            }
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . "sql: $sql";
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-order-product-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];

    }


    /**
     * 单价从一个货币类型，转换成另一个货币类型
     *
     * @param $from
     * @param $to
     * @param $amount
     * @return string
     * @throws BusinessException
     */
    public function transPriceAmount($from,$to,$amount){
        $country_code = get_country_code();

        // 获取默认国家
        if ('TH' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_THB;
        } else if ('PH' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_PHP;
        } else if ('LA' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_LAK;
        } else if ('MY' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_MYR;
        } else if ('ID' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_IDR;
        } else if ('VN' == $country_code) {
            $baseCurrency = GlobalEnums::CURRENCY_VND;
        }else {
            $baseCurrency = GlobalEnums::CURRENCY_THB;
        }

        $exchangeArr = [
            GlobalEnums::CURRENCY_THB => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_THB),
            GlobalEnums::CURRENCY_USD => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_USD),
            GlobalEnums::CURRENCY_CNY => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_CNY),
            GlobalEnums::CURRENCY_PHP => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_PHP),
            GlobalEnums::CURRENCY_LAK => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_LAK),
            GlobalEnums::CURRENCY_MYR => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_MYR),
            GlobalEnums::CURRENCY_IDR => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_IDR),
            GlobalEnums::CURRENCY_VND => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_VND),
            GlobalEnums::CURRENCY_EUR => ExchangeRateModel::getRateByCodeId($baseCurrency,GlobalEnums::CURRENCY_EUR)
        ];

        if(empty($exchangeArr[$to]) || empty($exchangeArr[$from])){
            throw new BusinessException('currency is not exist');
        }

        //(string)强转会变科学计数法
        $amount = strval($amount);
        if($from != $to){
            //$amount = $amount*$exchangeArr[$from]/$exchangeArr[$to];
            $bcmul_result = bcmul($amount,(string)$exchangeArr[$from],$this->digits_num);
            $amount = bcdiv($bcmul_result,$exchangeArr[$to],$this->digits_num);
        }

        //保留4位小数，舍去其他小数
        return bcmul($amount,1,$this->digits_num);
    }

    /**
     * 调整采购申请单数量
     * @param array $data  提交的数据
     * @param array $user  当前用户
     * @param int $type  4 数据查询
     * @return mixed
     */
    public function updateTotal(array $data, array $user, int $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        foreach ($data['products'] as $k => $v) {
            $data['products'][$k] = array_only($v, array_keys(self::$validate_update_product_param));
        }

        $newProducts = array_column($data['products'],null,'id');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if ($type == BaseService::LIST_TYPE_DATA) {
                //校验是否有修改权限
                $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($user['id']);
                if (!$is_privilege_staff_id) {
                    throw new ValidationException(static::$t->_('purchase_apply_privilege_can_not_close'), ErrCode::$VALIDATE_ERROR);
                }
                $item = PurchaseApply::findFirst([
                    'conditions'  => 'id = :id: AND status = :status: AND is_can_update = :is_can_update:',
                    'bind'        => [
                        'id'            => $data['id'],
                        'status'        => Enums::WF_STATE_APPROVED,
                        'is_can_update' => PurchaseEnums::IS_CAN_UPDATE_YES
                    ],
                    'for_updated' => true,
                ]);
            } else {
                $item = PurchaseApply::findFirst([
                    'conditions'  => 'id = :id: AND create_id =:create_id: AND status = :status: AND is_can_update = :is_can_update:',
                    'bind'        => [
                        'id'            => $data['id'],
                        'create_id'     => $user['id'],
                        'status'        => Enums::WF_STATE_APPROVED,
                        'is_can_update' => PurchaseEnums::IS_CAN_UPDATE_YES
                    ],
                    'for_updated' => true,
                ]);
            }

            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_apply_is_linked'), ErrCode::$VALIDATE_ERROR);
            }

            // PUR的所有行
            $products = $item->getProducts();

            //用来算总金额
            $newData = ['amount' => 0, 'products' => []];

            //用来算预算
            $applyAmount = [];

            $is_update = 0;
            //所有行被po使用的数量
            $all_order_use_total = 0;
            // PUR所有行
            foreach ($products as $product) {
                $tmp = [];
                $tmp['item'] = $product;
                $tmp['id'] = $product->id;
                $tmp['total'] = $product->total;
                $tmp['price'] = bcdiv($product->price, $this->digits, $this->digits_num);
                $tmp['is_update_flag'] = 0;
                $tmp['vat7_rate'] = $product->vat7_rate;
                //通过所有行被po使用的数量
                $all_order_use_total += $product->order_use_total;
                // $tmp['vat7'] = bcadd($newProducts[$product->id]['total'] * $tmp['price'] * $tmp['vat7_rate'] / 100, 0, 2);
                //修复bug:vat税额直接舍去到2位 改为 舍去到3位小数,四舍五入保留2位
                $total_mul_price = bcmul($newProducts[$product->id]['total'], $tmp['price'], 6);
                $vat7_rate = bcdiv($tmp['vat7_rate'], '100', 6);
                $vat7 = bcmul($total_mul_price, $vat7_rate, 3);
                $tmp['vat7'] = round($vat7, 2);
                //不能修改，直接跳过,或者本次没有传跳过
                if ($product->is_can_update == PurchaseEnums::IS_CAN_UPDATE_NO || !isset($newProducts[$product->id])) {
                    $newData['products'][] = $tmp;
                    continue;
                }

                //如果数量相等，则不修改
                if ($product->total == $newProducts[$product->id]['total']) {
                    $newData['products'][] = $tmp;
                    continue;
                }

                //不能调大
                if ($product->total < $newProducts[$product->id]['total']) {
                    throw new ValidationException(static::$t->_('purchase_product_total_adjustment_error',
                        [
                            'new_total' => $newProducts[$product->id]['total'],
                            'old_total' => $product->total,
                            'item_id' => $product->id
                        ]
                    ), ErrCode::$VALIDATE_ERROR);
                }

                $is_update = 1;
                $tmp['total'] = $newProducts[$product->id]['total'];
                $tmp['remark'] = $newProducts[$product->id]['remark'];
                $tmp['is_update_flag'] = 1;
                $newData['products'][] = $tmp;
            }

            if (empty($is_update)) {
                throw new ValidationException(static::$t->_('purchase_no_update_product'), ErrCode::$VALIDATE_ERROR);
            }

            $this->handleProductsData($newData);
            $total = 0;

            // PUR被PO关联: 则PUR行金额以PO行关联的PAR行含税金额为准 PUR行ID => 发票金额(含税)的和
            $linked_payment_receipts = [];
            if ($item->is_link_po) {
                $linked_payment_receipts = $this->getAllRelatedOrdersFullPaidMap($item->id, $newData['products']);
            }
            $this->logger->info('获取PUR行通过PO行关联的PAR行的含税发票金额合计值: '. json_encode($linked_payment_receipts, JSON_UNESCAPED_UNICODE));

            // 要处理的PUR行
            foreach ($newData['products'] as $product) {
                $detail = $product['item'];
                $old = $detail->toArray();

                // 关联过总量相同的付款行, 则取付款行的 发票金额(含税)金额
                if (isset($linked_payment_receipts[$product['id']])) {
                    $amount = $linked_payment_receipts[$product['id']];
                } else {
                    // 否则, 取申请单行的含税总额即可: 金额转换为国家基准币种
                    $amount = EnumsService::getInstance()->amountExchangeRateCalculation($product['all_total'], $item->exchange_rate, 0);
                }

                if (isset($applyAmount[$detail->level_code])) {
                    $applyAmount[$detail->level_code] += $amount;
                } else {
                    $applyAmount[$detail->level_code] = $amount;
                }

                if (!empty($product['is_update_flag'])) {
                    $detail->total = $product['total'];
                    $detail->total_price = $product['total_price'];
                    $detail->all_total = $product['all_total'];
                    $detail->remark = $product['remark'];
                    $detail->vat7 = $product['vat7'];
                    if (empty($product['total'])) {
                        $detail->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
                    }

                    if ($detail->save() === false) {
                        throw new BusinessException('采购申请单-我的申请-修改数量-PUR行更新失败, 原因可能是: ' . get_data_object_error_msg($detail) . '; 待更新数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }

                    $this->saveUpdateTotalLog(Enums::WF_PURCHASE_APPLY, $item->id, $item->pano, $old, $detail->toArray(), $user);
                }

                $total += $product['total'];
            }

            $item->amount = $newData['amount'];
            if (empty($total)) {
                $item->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
            }

            //15362 检查是否完全生成po(pur调减后有可能会变成完全生成po),
            if ($item->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                //查询调减后的产品总数量
                $all_total = PurchaseApplyProduct::sum([
                    'column' => 'total',
                    'conditions' => 'paid = :id:',
                    'bind' => ['id' => $item->id]
                ]);

                $this->logger->info('采购申请单调减-更新执行状态 : pa_id=' . $item->id . ';调减后all_total=' . $all_total . ';po使用数量=' . $all_order_use_total);

                // PUR 没有关联过PO 且 PUR各行的数量调减为0: 申请单已关闭
                if ($all_order_use_total == 0 && $all_total == 0) {
                    $item->is_close = PurchaseEnums::IS_CLOSE_YES;

                } else if ($all_order_use_total > 0 && $all_order_use_total >= $all_total) {
                    // 被PO关联 且 被关联完毕
                    //如果完全生成po,关闭状态改为已关闭, 部分生成po不用修改(加入人工设置为已关闭,这里判断部分生成po改成未关闭就错了)
                    $item->is_close = PurchaseEnums::IS_CLOSE_YES;
                    $item->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_DONE;

                } else if ($all_order_use_total > 0) {
                    // po使用数量大于0且小于总量 : 部分生成po
                    $item->is_close = PurchaseEnums::IS_CLOSE_NO;
                    $item->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY;
                }

                $this->logger->info('采购申请单调减-更新执行状态 : execute_status=' . $item->execute_status . '; is_close=' . $item->is_close);
            }

            if ($item->save() === false) {
                throw new BusinessException('采购申请单-我的申请-修改数量-PUR主数据更新失败, 原因可能是: ' . get_data_object_error_msg($item) . '; 待更新数据: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 验证默认国家是否开启预算
            $budgetStatus = (new EnumsService())->getBudgetStatus();
            $re_budget_flag = PaymentService::getInstance()->getHistoryBudgetStatus($item->created_at);
            if ($budgetStatus && $re_budget_flag) {
                $budgetService = new BudgetService();
                $result = $budgetService->re_back_budget($item->pano, $user, BudgetService::ORDER_TYPE_2, $applyAmount);

                $this->logger->info('purchase_apply  释放预算 params ' . json_encode([$item->pano, $user, BudgetService::ORDER_TYPE_2, $applyAmount], JSON_UNESCAPED_UNICODE) . ' results ' . json_encode([$result], JSON_UNESCAPED_UNICODE));
            } else {
                $result = [
                    'code' => $code,
                    'message' => $message,
                    'data' => []
                ];
            }

            if ($result['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($result['message'], ErrCode::$VALIDATE_ERROR);
            }

            $db->commit();
        }catch (ValidationException $e){
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e){
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e){
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning("purchase-apply-updateTotal-failed:".$real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 获取修改数量日志
     * @param $id
     * @param $user
     * @return array
     */
    public function getUpdateLog($id,$user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $item = PurchaseApply::findFirst(
                [
                    'conditions'=>'id = :id:',
                    'bind'=>['id'=> $id]
                ]
            );

            if(empty($item)){
                throw new ValidationException('not found the pa');
            }

            $data = $this->getUpdateTotalLog(Enums::WF_PURCHASE_APPLY,$id);


        }catch (ValidationException $e){

            $code = $e->getCode();
            $message = $e->getMessage();

        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if(!empty($real_message)){
            $this->logger->warning("purchase-apply-getUpdateTotalLog-failed:".$real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取采购申请产品名称列表
     * @param $product_name
     * @return array
     */
    public function productNameList($product_name){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $itemList = PurchaseApplyProduct::find(
            [
                'conditions'=>"product_name like :product_name:",
                'column'=>'product_name',
                'group'=>'product_name',
                'bind'=>['product_name'=> '%'.$product_name.'%']
            ]
        )->toArray();

        foreach ($itemList as $key => $item) {
            $data[] = [
                'id' => $key + 1,
                'name' => $item['product_name']
            ];
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    private function forPdfData($data)
    {
        return $data['data'];
    }

    /**
     * 获取采购类型列表、采购类型关联的物料编码
     * @return array
     */
    public function purchase_type_list(){
        $list = PurchaseType::find()->toArray();
        $data = array();
        if(!empty($list)){
            $row = array();
            foreach ($list as $li){
                $row['id'] = $li['purchase_type'];
                $row['name_key'] = static::$t->_($li['name_key']);
                $data[] = $row;
            }
        }
        $return['type_list'] = $data;
        return [
            'code' => ErrCode::$SUCCESS,
            'message' => '',
            'data' => $return
        ];
    }

    /**
     * 根据采购类型获取对应的财务列表-树状
     * @param integer $purchase_type 采购类型值
     * @return array
     */
    public function purchase_wrs_code($purchase_type)
    {
        $purchase_type = intval($purchase_type);
        //非采购类型是混合类返回按照采购类型筛选的所有启用的财务分类
        if(!empty($purchase_type) && $purchase_type != PurchaseEnums::PURCHASE_TYPE_MIX) {
            $params['purchase_type'] = $purchase_type;
        }
        $params['type'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
        return ClassifyService::getInstance()->getClassifyArr($params, 'search');
    }

    /**
     * 同步采购申请单数量和预算释放: 来自采购订单渠道的PUR预算释放
     * @param string $po_no
     * @param array $data
     * @param array $user
     * @return mixed
     */
    public function updateTotalByOrderModify(string $po_no, array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        foreach ($data['products'] as $k => $v) {
            $data['products'][$k] = array_only($v, array_keys(self::$validate_update_product_param));
        }

        $newProducts = array_column($data['products'],null,'id');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $item = PurchaseApply::findFirst([
                'conditions' => 'id = :id: AND status = :status:',
                'bind' => ['id' => $data['id'], 'status' => Enums::WF_STATE_APPROVED],
                'for_updated' => true,
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_("purchase_apply_is_empty"), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('采购订单数量修改同采购申请单:[同步前数据]' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));


            // 采购申请单-产品明细
            $products = $item->getProducts();

            //用来算总金额
            $newData = ['amount' => 0, 'products' => []];

            //用来算预算
            $applyAmount = [];

            $is_update = 0;
            foreach ($products as $product) {
                $tmp = [];
                $tmp['item'] = $product;
                $tmp['id'] = $product->id;
                $tmp['total'] = $product->total;
                $tmp['price'] = bcdiv($product->price,$this->digits,$this->digits_num);
                $tmp['vat7_rate'] = $product->vat7_rate;

                //$tmp['vat7'] = bcadd($newProducts[$product->id]['total'] * $tmp['price'] * $tmp['vat7_rate'] / 100, 0, 2);
                //修复bug:vat税额直接舍去到2位 改为 舍去到3位小数,四舍五入保留2位
                $total_mul_price = bcmul($newProducts[$product->id]['total'], $tmp['price'], 6);
                $vat7_rate = bcdiv($tmp['vat7_rate'], '100', 6);
                $vat7 = bcmul($total_mul_price, $vat7_rate, 3);
                $tmp['vat7'] = round($vat7, 2);
                // 本次没有传跳过
                if (!isset($newProducts[$product->id])) {
                    $newData['products'][] = $tmp;
                    continue;
                }

                // 如果数量相等，则不修改
                if (isset($newProducts[$product->id]) && $product->total == $newProducts[$product->id]['total']) {
                    $newData['products'][] = $tmp;
                    continue;
                }

                // 不能调大
                if (isset($newProducts[$product->id]) && $product->total< $newProducts[$product->id]['total']) {
                    throw new ValidationException(static::$t->_('purchase_product_total_adjustment_error',
                        [
                            'new_total' => $newProducts[$product->id]['total'],
                            'old_total' => $product->total,
                            'item_id' => $product->id
                        ]
                    ), ErrCode::$VALIDATE_ERROR);
                }

                $is_update = 1;
                $tmp['total'] = $newProducts[$product->id]['total'];
                $tmp['remark'] = $newProducts[$product->id]['remark'];
                $tmp['is_update_flag'] = 1;

                $newData['products'][] = $tmp;
            }

            if (empty($is_update)) {
                throw new ValidationException(static::$t->_("purchase_no_update_product"), ErrCode::$VALIDATE_ERROR);
            }

            $this->handleProductsData($newData,true,false);

            // PUR被PO关联: 则PUR行金额以PO行关联的PAR行含税金额为准 PUR行ID => 发票金额(含税)的和
            $linked_payment_receipts = [];
            if ($item->is_link_po) {
                $linked_payment_receipts = $this->getAllRelatedOrdersFullPaidMap($item->id, $newData['products']);
            }
            $this->logger->info('PUR通过PO关联的PAR行的含税发票金额合计的取数结果: '. json_encode($linked_payment_receipts, JSON_UNESCAPED_UNICODE));

            $total = 0;
            foreach ($newData['products'] as $product) {
                $detail = $product['item'];
                $old = $detail->toArray();

                // 关联过总量相同的付款行, 则取付款行的 发票金额(含税)金额
                if (isset($linked_payment_receipts[$product['id']])) {
                    $amount = $linked_payment_receipts[$product['id']];
                } else {
                    // 金额转换为国家基准币种
                    $amount = EnumsService::getInstance()->amountExchangeRateCalculation($product['all_total'], $item->exchange_rate, 0);
                }

                if (isset($applyAmount[$detail->level_code])) {
                    $applyAmount[$detail->level_code] += $amount;
                } else {
                    $applyAmount[$detail->level_code] = $amount;
                }

                if (!empty($product['is_update_flag'])) {
                    $detail->total = $product['total'];
                    $detail->total_price = $product['total_price'];
                    $detail->all_total = $product['all_total'];
                    $detail->remark = $product['remark'];
                    $detail->vat7 = $product['vat7'];
                    if (empty($product['total'])) {
                        $detail->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
                    }

                    if ($detail->save() === false) {
                        throw new BusinessException('采购订单数量修改同采购申请单:[申请单产品信息更新失败] 待处理数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($detail), ErrCode::$BUSINESS_ERROR);
                    }

                    $detail_log = $detail->toArray();

                    // PO单渠道释放预算,同步PUR单是否状态和对应备注
                    $detail_log['is_free_budget'] = 'Y';
                    $detail_log['po_free_budget_remark'] = $po_no;

                    $this->saveUpdateTotalLog(Enums::WF_PURCHASE_APPLY, $item->id, $item->pano, $old, $detail_log, $user);
                }

                $total += $product['total'];
            }

            $item->amount = $newData['amount'];
            $item->updated_at = date('Y-m-d H:i:s');
            if (empty($total)) {
                $item->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
            }

            if ($item->save() === false) {
                throw new BusinessException('采购订单数量修改同采购申请单:[申请单更新失败] 待处理数据: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('采购订单数量修改同采购申请单:[同步后数据]' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));

            // 验证默认国家是否开启预算
            $budgetStatus = (new EnumsService())->getBudgetStatus();
            $re_budget_flag = PaymentService::getInstance()->getHistoryBudgetStatus($item->created_at);
            if (!$budgetStatus || !$re_budget_flag) {
                $result = [
                    'code' => $code,
                    'message' => 'No need to release budget',// 无需释放预算
                    'data' => []
                ];
            } else {
                $budgetService = new BudgetService();
                $result = $budgetService->re_back_budget($item->pano, $user, BudgetService::ORDER_TYPE_2, $applyAmount);
                $this->getDI()->get('logger')->info(
                    'purchase_apply  释放预算 params ' . json_encode(
                        [
                            $item->pano,
                            $user,
                            BudgetService::ORDER_TYPE_2,
                            $applyAmount
                        ]
                    ) . ' results ' . json_encode([$result])
                );
            }

            if ($result['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException($result['message'], ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e){
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (BusinessException $e) {
            $db->rollback();

            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error("purchase-apply-updateTotal-failed[po->pur]:".$real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 下载采购申请单批量导入模版
     */
    public function getDownloadTpl()
    {
        $result = [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => [
                'file_url' => ''
            ]
        ];

        $header = $this->genBatchDownloadExcelHeader(true);
        $data = [];
        $file_name = 'PurchaseApplyBatchUploadTpl_'.date('YmdHis').'.xlsx';
        $export_res = $this->exportExcel($header, $data, $file_name);
        if ($export_res['code'] == 1 && !empty($export_res['data'])) {
            $result['data']['file_url'] = $export_res['data'];
        } else {
            $result['code'] = ErrCode::$BUSINESS_ERROR;
            $result['msg'] = 'Failed to generate template file, please try again later';
        }

        return $result;
    }

    /**
     * 组合批量上传表头
     * @param bool $is_download_tpl
     * @return mixed
     */
    protected function genBatchDownloadExcelHeader(bool $is_download_tpl = true)
    {
        $header = [
            self::$t['purchase_apply_tpl_budget_classification'], // 预算分类
            self::$t['purchase_apply_tpl_budget_detail'], // 预算明细
            self::$t['purchase_apply_tpl_product_no'], // 产品编号(Barcode)
            self::$t['purchase_apply_tpl_cost_type'], // 费用所属网点/总部
            self::$t['purchase_apply_tpl_product_desc'], // 产品描述
            self::$t['purchase_apply_tpl_product_model'], // 规格型号
            self::$t['purchase_apply_tpl_product_num'], // 数量
            self::$t['purchase_apply_tpl_product_unit'], // 单位
            self::$t['purchase_apply_tpl_product_net_price'], // 不含税单价
            self::$t['purchase_apply_tpl_product_vat_tax_rate'], // VAT税率
            self::$t['purchase_apply_tpl_product_remark'], // 备注
        ];

        if (!$is_download_tpl) {
            $header[] = self::$t['payment_upload_result_hint']; // 失败原因
        }

        return $header;
    }

    /**
     * 产品批量上传校验
     * @param array $batch_data Excel 批量上传的数据
     * @param array $params 业务参数
     * @param array $user_info
     *
     * @return mixed
     */
    public function batchUploadCheck(array $batch_data, array $params, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            // 模板格式校验
            // 空白行不做统计 且 返回文件里要合并掉
            $barcode_list = [];
            foreach ($batch_data as $k => $row) {
                $row = array_map('trim', $row);
                $row_str = implode('', $row);
                if (empty($row_str)) {
                    unset($batch_data[$k]);
                    continue;
                }

                $batch_data[$k] = $row;
                $barcode_list[] = $row[2];
            }

            $batch_data = array_values($batch_data);
            if (count($batch_data) > self::PRODUCT_BATCH_UPLOAD_MAX_ALLOW_COUNT) {
                throw new ValidationException(self::$t->_('purchase_batch_upload_error_01'));
            }

            // 正常的批量导入模板应有11列 不符合此列数的, 认为模板不正确
            $excel_tpl_column_total = 11;
            if (count($batch_data[0]) != $excel_tpl_column_total) {
                throw new ValidationException(self::$t->_('purchase_batch_upload_error_02'));
            }

            // 获取当前费用所属部门/网点 支持的 采购类型 预算分类及其产品明细
            $product_list = $this->productList($params);
            $curr_budget_products = [];
            if (!empty($product_list)) {
                foreach ($product_list as $budget_val) {
                    // 产品列表key 产品名称
                    $_tmp_products = [];
                    if (!empty($budget_val['products'])) {
                        foreach ($budget_val['products'] as $product_val) {
                            $_tmp_products[strtolower(trim($product_val['name']))] = $product_val;
                        }
                    }

                    $budget_val['products'] = $_tmp_products;

                    // 预算列表key 预算名称
                    $curr_budget_products[strtolower(trim($budget_val['name']))] = $budget_val ?? [];
                }

                unset($product_list);
            }

            // 获取网点
            $store_list = (new OrdinaryPaymentBaseService)->getSysStoreList([], [], true);
            $sys_store_list = [];
            if (!empty($store_list)) {
                foreach ($store_list as $store) {
                    $sys_store_list[strtolower(trim($store['name']))] = $store;
                }

                unset($store_list);
            }

            // vat 税率配置: 转换为小数, 便于 和 Excel 中的税率对比(浮点数)
            // 税率统一的需求时, 产品确定所有国家的vat都取配置的项, 泰国去掉仅允许 7% 和 0% 的限制
            $vat_setting_arr = EnumsService::getInstance()->getVatRateValueItem();

            $vat_setting_arr = array_map(function ($v) {
                return $v . '%';
            }, $vat_setting_arr);

            // 如果是总部, 则取费用部门对应的费用成本中心
            $cost_department_center_name = '';
            if ($params['cost_store'] == 1) {
                $cost_department_center_info = $this->getPcCodeArrByDepartmentId($params['cost_department']);
                $cost_department_center_name = $cost_department_center_info['cost_center_code'] ?? '';
            }

            // 产品编号中有效的sau: 已启用 且 未删除
            $barcode_list = array_values(array_unique($barcode_list));
            if (!empty($barcode_list)) {
                $list_param = [
                    'barcode' => $barcode_list,
                    'status' => MaterialClassifyEnums::MATERIAL_START_USING
                ];
                $barcode_list = StandardService::getInstance()->getBarcodeList($list_param);
                $barcode_list = array_column($barcode_list, null, 'barcode');
            }

            // 财务分类列表
            $material_finance_category = ClassifyService::getInstance()->getAllMaterialFinanceCategory();
            $material_finance_category = array_column($material_finance_category, null, 'id');

            // Excel 数据处理
            $excel_products = [];
            $cost_store_type = $params['cost_store'];// 1 总部；2 网点
            $head_office = strtolower(Enums::PAYMENT_HEADER_STORE_NAME);
            $user_organization_type = $user_info['organization_type'];// 用户所属机构类型: 1 网点; 2 总部
            foreach ($batch_data as $k => $row) {
                // 税率格式转换: 浮点型 且 小数位 最多两位
                if (is_numeric($row[9])) {
                    $row[9] = (float) sprintf("%.3f", $row[9] * 100) . '%';
                }

                // 预算校验
                $_budget_info = $curr_budget_products[strtolower($row[0])] ?? [];
                if (empty($_budget_info)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_03'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 预算明细校验
                $_product_name = $row[1];
                // 预算关联的产品-预算明细
                $_budget_products = $_budget_info['products'];
                $_products_info = $_budget_products[strtolower($_product_name)] ?? [];
                if ((empty($_budget_products) && !empty($_product_name)) || (!empty($_budget_products) && empty($_product_name)) || (!empty($_budget_products) && !empty($_product_name) && empty($_products_info))) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_04'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 产品编号barcode 校验: barcode 为0有实意, 需注意
                $barcode = $row[2];
                // 预算科目的barcode设置为必填, 则barcode不能为空
                if ($_budget_info['bar_code'] == 1 && empty($barcode) && $barcode != '0') {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_14'];
                    $batch_data[$k] = $row;
                    continue;
                }

                $sau_info = $barcode_list[$barcode] ?? [];
                // 若barcode已填, 则必须是sau中未删除且已开启的状态
                if ((!empty($barcode) || $barcode == '0') && empty($sau_info)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_15'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 费用所属网点/总部校验
                $_cost_store_name = strtolower($row[3]);
                $_cost_store_info = $sys_store_list[$_cost_store_name] ?? [];
                if ($cost_store_type == 1 && ($_cost_store_name != $head_office)) {
                    // 基本信息费用所属网点是总部, 明细里必须是总部
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_05'];
                    $batch_data[$k] = $row;
                    continue;
                } else if ($cost_store_type != 1 && ($_cost_store_name == $head_office)) {
                    // 基本信息费用所属网点是网点, 明细里必须是非总部
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_05'];
                    $batch_data[$k] = $row;
                    continue;
                } else if (empty($_cost_store_name) || empty($_cost_store_info)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_05'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 产品描述: 若sau有值, 则取sau的英文名称
                if (!empty($sau_info)) {
                    $_desc = $sau_info['name_en'];
                } else {
                    // 总部 - 英文
                    $_desc = $row[4];
                    $_desc_length = mb_strlen($_desc);
                    if ($_desc_length < 1) {
                        $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_17'];
                        $batch_data[$k] = $row;
                        continue;
                    } else if ($_desc_length > 100) {
                        $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_12'];
                        $batch_data[$k] = $row;
                        continue;
                    } else if ($user_organization_type == 2 && preg_match(self::CN_TH_CHAR_REGULAR, $_desc)) {
                        $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_06'];
                        $batch_data[$k] = $row;
                        continue;
                    }
                }

                // 规格型号(非必填): 若sau有值, 则取sau的规格型号; 否则 若Excel有录入, 则最长不超过100个字符
                if (!empty($sau_info)) {
                    $product_option = $sau_info['model'];
                } else {
                    $product_option = $row[5];
                    if (!empty($product_option) && mb_strlen($product_option) > 100) {
                        $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_16'];
                        $batch_data[$k] = $row;
                        continue;
                    }
                }

                // 数量
                $_total = $row[6];
                if (!preg_match('/^[1-9]+[0-9]*$/', $_total)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_07'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 单位: 若sau有值, 则取sau的sap单位
                if (!empty($sau_info)) {
                    $_unit = $sau_info['sap_unit'];
                } else {
                    $_unit = $row[7];
                    $_unit_len = mb_strlen($_unit);
                    if ($_unit_len < 1 || $_unit_len > 30) {
                        $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_13'];
                        $batch_data[$k] = $row;
                        continue;
                    }
                }

                // 不含税单价: 最多 6 位小数
                $_price = $row[8];
                if (!preg_match('/^[0-9]+\.?[0-9]{0,6}$/', $_price)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_09'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // vat 税率
                if (!in_array($row[9], $vat_setting_arr)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_10'];
                    $batch_data[$k] = $row;
                    continue;
                }
                $_vat = rtrim($row[9], '%');
                $_vat7_rate = $row[9];

                // 备注: 总部 = 英文
                $_remark = $row[10];
                if (!empty($_remark) && $user_organization_type == 2 && preg_match(self::CN_TH_CHAR_REGULAR, $_remark)) {
                    $row[$excel_tpl_column_total] = self::$t['purchase_batch_upload_error_11'];
                    $batch_data[$k] = $row;
                    continue;
                }

                // 如下计算顺序 与 金额计算接口(calculate)保持一致: 参考 handleProductsData
                $_price = round($_price, $this->digits_num);
                $_price = bcadd($_price,0, $this->digits_num);

                $_total_price = round($_total * $_price, 2);
                $_total_price = bcadd($_total_price,0,3);

                $_vat7_amount = round($_total * $_price * $_vat / 100, 2);
                $_vat7_amount = bcadd($_vat7_amount,0,2);

                $_all_total_amount = bcadd($_vat7_amount, $_total_price, 2);


                $finance_code = '';
                $ledger_account_id = '';
                if (!empty($material_finance_category) && !empty($sau_info)) {
                    $material_finance_category_info = $material_finance_category[$sau_info['finance_category_id']] ?? [];
                    $finance_code = $material_finance_category_info['code'] ?? '';
                    $ledger_account_id = $material_finance_category_info['ledger_account_id'] ?? '';
                }

                $_row = [
                    'budget_id' => $_budget_info['id'], // 预算分类id
                    'isbudget' => $_budget_info['is_budget'], // 是否参与预算
                    'budget_name' => $_budget_info['name'], // 预算分类名称
                    'category_a' => '0', // 产品一级分类 为 0 即可
                    'category_b' => '0', // 产品二级分类
                    'product_id' => $_products_info['id'] ?? '0', // 产品id
                    'goodsNameId' => $_products_info['id'] ?? '', // 同产品id
                    'product_name' => $_products_info['name'] ?? '', // 产品名称
                    'product_option' => $product_option, // 产品规格型号
                    'product_option_code' => $barcode, // 产品编码
                    'ledger_account_id' => $ledger_account_id, // 产品对应的核算科目id
                    'ledger_account_name' => '', // 产品对应的核算科目名称
                    'cost_store_id' => $_cost_store_info['id'], // 费用所属总部/网点id
                    'cost_store_name' => $_cost_store_info['name'], // 费用所属总部/网点名称
                    'cost_center_name' => stripos($_cost_store_name, 'head office') === false ? $_cost_store_info['sap_pc_code'] : $cost_department_center_name, // 费用所属中心
                    'desc' => (string)$_desc, // 产品描述
                    'unit' => (string)$_unit, // 单位
                    'metere_unit' => $sau_info['unit_en'] ?? '',// 计量单位: 默认取sau自带的基本单位:英文
                    'finance_code' => $finance_code, // 财务编码
                    'unit_code' => '',// 默认值
                    'unit_other_flag' => '0', // 默认值
                    'total' => $_total, // 数量
                    'price' => $_price, // 不含税单价
                    'total_price' => $_total_price, // 不含税金额
                    'vat7_rate' => $_vat7_rate, // vat税率
                    'vat7' => $_vat7_amount, // vat 金额
                    'all_total' => $_all_total_amount, // 含税金额
                    'remark' => $_remark, // 备注
                    'update_to_acceptance' => $sau_info['update_to_acceptance'] ?? MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, // 是否需要验收:1-否;2-是
                    'barcode_name_zh' => $sau_info['name_zh'] ?? '',
                    'barcode_name_en' => $sau_info['name_en'] ?? '',
                    'barcode_name_local' => $sau_info['name_local'] ?? '',
                ];

                $excel_products[$k] = $_row;
                $batch_data[$k] = $row;
            }

            // 将失败的生成返回文件
            $header = $this->genBatchDownloadExcelHeader(false);
            $file_name = 'PurchaseApplyBatchUploadResult_'.date('YmdHis').'.xlsx';
            $export_res = $this->exportExcel($header, $batch_data, $file_name);

            $excel_total_row = count($batch_data);
            $success_count = count($excel_products);
            $error_total = $excel_total_row - $success_count;
            $data = [
                'row_total' => $excel_total_row, // 数据总条数
                'success_total' => $success_count, // 成功条数
                'error_total' => $error_total > 0 ? $error_total : 0, // 错误条数
                'file_url' => $export_res['data'] ?? '', // 返回的检测文件地址
                'products' => $excel_products, // 上传返回的产品列表
            ];

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();

            $logger = $this->getDI()->get('logger');
            $logger->warning('采购管理 - 采购申请单 - 产品批量上传校验: ' . $message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];

    }

    /**
     * 搜索barcode
     * @param array $params 搜索条件
     * @return array
     */
    public function searchBarcode($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $items = [];
        try {
            $locale = static::$language;
            $page_size = empty($params['pageSize']) ? 10 : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['ms' => MaterialSauModel::class]);
            $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
            $builder->where('ms.is_deleted = :is_deleted: and ms.status =:status:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'status'=>MaterialClassifyEnums::MATERIAL_START_USING]);
            if (isset($params['barcode']) && !empty($params['barcode'])) {
                $builder->andWhere('barcode like :barcode:', ['barcode' => '%'.trim($params['barcode']).'%']);
            }
            if (isset($params['name']) && !empty($params['name'])) {
                //名称搜索
                $builder->andWhere('name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' like :name:', ['name' => '%' . trim($params['name']) . '%']);
            }
            if (isset($params['model']) && !empty($params['model'])) {
                //规格
                $builder->andWhere('model like :model:', ['model' => '%' . trim($params['model']) . '%']);
            }
            $builder->columns('count(ms.id) as total');
            $total_info = $builder->getQuery()->getSingleResult();
            $count = intval($total_info->total);
            if ($count > 0) {
                $builder->columns('ms.id,barcode,name_zh,name_en,name_local,unit_en,sap_unit,model,ms.brand,ms.price,ms.currency,ms.purchase_type,ms.update_to_acceptance,mfc.code,mfc.name as wrs_code_name,mfc.ledger_account_id');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                if (!empty($items)) {
                    $materialAttachment = new  MaterialAttachmentModel();
                    $pic_arr_key_arr = $materialAttachment->getColumnArr($items);
                    foreach ($items as &$item) {
                        $item['show_name'] = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                        $item['price'] = $item['price'].static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                        $item['pic'] = $pic_arr_key_arr[$item['id']] ?? '';
                    }
                }
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-apply-search-barcode-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取采购申请单的行已被订单关联完毕 且 这些订单已被全额付款的映射关系
     * 说明:
     * 1. 申请单的行已被订单关联完毕: 即申请单行的数量 == 其关联的订单行的数量合计
     * 2. 这些订单已被全额付款: 即 订单行的含税金额总计 == 订单行关联的所有的付款单的行的发票含税金额合计
     *
     * 返回的Map结构:
     * [
     *   申请单行ID => 发票含税总额合计
     * ]
     *
     * @param int $purchase_apply_id 采购申请单ID
     * @param array $purchase_apply_products 采购申请单的行列表
     * @param bool $is_from_payment_pay 是否来自付款单支付
     * @param array $payment_linked_pup_ids 当来自付款单支付时, 付款的行所关联的申请单的行
     *
     * @return array
     * @throws ValidationException
     */
    public function getAllRelatedOrdersFullPaidMap(int $purchase_apply_id, array $purchase_apply_products, bool $is_from_payment_pay = false, array $payment_linked_pup_ids = [])
    {
        $result = [];
        if (empty($purchase_apply_id) || empty($purchase_apply_products)) {
            return $result;
        }

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): is_from_payment_pay = ' . $is_from_payment_pay);
        if ($is_from_payment_pay) {
            $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 付款单的行关联的申请单的行 = ' . json_encode($payment_linked_pup_ids, JSON_UNESCAPED_UNICODE));
        }

        // 采购申请单行ID与总数Map
        $apply_detail_ids = array_column($purchase_apply_products, 'total', 'id');
        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单的行与其数量: ' . json_encode($apply_detail_ids, JSON_UNESCAPED_UNICODE));

        // 1.对比PUR行的数量 与 其关联相关PO的行的数量合计
        // 1.1获取PUR的行 已关联的 待审批|已通过的PO行
        $builder = $this->modelsManager->createBuilder();
        $column_str = 'pop.apply_product_id, po.id AS po_id, po.currency, po.exchange_rate, pop.id AS pop_id, pop.total AS pop_total, pop.all_total AS pop_all_total';
        $builder->columns($column_str);
        $builder->from(['po' => PurchaseOrder::class]);
        $builder->leftJoin(PurchaseOrderProduct::class, 'pop.poid = po.id', 'pop');
        $builder->where('po.pa_id = :pa_id:', ['pa_id' => $purchase_apply_id]);
        $builder->inWhere('po.status', [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]);
        $builder->inWhere('pop.apply_product_id', array_keys($apply_detail_ids));
        $order_products = $builder->getQuery()->execute()->toArray();

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单行 与 关联的PO行(审批中和审批通过的): ' . json_encode($order_products, JSON_UNESCAPED_UNICODE));

        if (empty($order_products)) {
            return $result;
        }

        // 1.2整理PUR行/PO/PO行的级联关系
        $_order_products = [];
        foreach ($order_products as $_pop) {
            // PUR行的汇总数据
            $_pup_info = $_order_products[$_pop['apply_product_id']] ?? [];
            if (empty($_pup_info)) {
                $_pup_info['pup_id'] = $_pop['apply_product_id'];
                $_pup_info['pop_total_sum'] = 0; // PUR行关联的所有PO行的数量合计
                $_pup_info['po_list'] = [];
            }

            // PUR行关联的PO及行
            if (empty($_pup_info['po_list'][$_pop['po_id']])) {
                $_pup_info['po_list'][$_pop['po_id']] = [
                    'po_id' => $_pop['po_id'],
                    'currency' => $_pop['currency'],
                    'exchange_rate' => $_pop['exchange_rate'],
                    'pop_total' => $_pop['pop_total'],
                    'pop_all_total' => $_pop['pop_all_total'],
                    'pop_id' => $_pop['pop_id']
                ];

                $_pup_info['pop_total_sum'] = bcadd($_pup_info['pop_total_sum'], $_pop['pop_total'], 0);
            }

            $_order_products[$_pop['apply_product_id']] = $_pup_info;
        }
        unset($order_products);

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单行与关联的PO行的关系: ' . json_encode($_order_products, JSON_UNESCAPED_UNICODE));

        // 1.3对比PUR行的总数量 与 关联的PO行的合计数量
        // 若上述两者相等, 则收集此PUR行及关联PO/PO行的信息
        $_matching_apply_detail_ids = [];
        $_order_ids = [];
        $_order_detail_ids = [];
        $enums_service = EnumsService::getInstance();
        foreach ($apply_detail_ids as $detail_id => $detail_total) {
            // 归集PUR行关联的PO及PO行: PUR行的数量 == 其关联的相关PO的行的数量合计
            $_pup_info = $_order_products[$detail_id] ?? [];
            if (!empty($_pup_info) && $detail_total == $_pup_info['pop_total_sum']) {
                // PUR行关联的所有PO行的含税金额合计, 根据PO的汇率转换为系统的本位币(不同PO单的币种/汇率有可能不同)
                $_linked_po_all_total_sum = 0;
                foreach ($_pup_info['po_list'] as $po_info) {
                    $_po_all_total_sum = $enums_service->amountExchangeRateCalculation($po_info['pop_all_total'], $po_info['exchange_rate'], 0);
                    $_linked_po_all_total_sum = bcadd($_linked_po_all_total_sum, $_po_all_total_sum);

                    // PO行的id集合
                    $_order_detail_ids[] = $po_info['pop_id'];
                }

                // PUR 与 PO 匹配的结果
                $_matching_apply_detail_ids[$detail_id] = [
                    'pup_id' => $_pup_info['pup_id'],
                    'linked_po_all_total_sum' => $_linked_po_all_total_sum,
                    'po_list' => $_pup_info['po_list']
                ];

                // PO的ID集合
                $_order_ids = array_merge($_order_ids, array_keys($_pup_info['po_list']));
            }
        }

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单行的数量与关联的PO行的数量合计相匹配的结果: ' . json_encode($_matching_apply_detail_ids, JSON_UNESCAPED_UNICODE));
        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单行的数量与关联的PO行的数量合计相匹配的POIDS: ' . json_encode($_order_ids, JSON_UNESCAPED_UNICODE));
        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单行的数量与关联的PO行的数量合计相匹配的PO行IDS: ' . json_encode($_order_detail_ids, JSON_UNESCAPED_UNICODE));

        // 不存在PUR行的总数量 与 关联PO行的数量合计 相等的情况
        if (empty($_matching_apply_detail_ids)) {
            return $result;
        }

        // 与PUR行总数量 相等的PO ID 及 行ID
        $linked_po_ids = array_values(array_unique($_order_ids));
        $linked_po_detail_ids = array_values(array_unique($_order_detail_ids));

        // 2.获取上述PO单关联的PAR行的含税发票金额合计(已通过&已支付)
        $payment_builder = $this->modelsManager->createBuilder();
        $column_str = 'payment.po_id, pap.pop_id, SUM(pap.ticket_amount) AS ticket_amount_sum';
        $payment_builder->columns($column_str);
        $payment_builder->from(['payment' => PurchasePayment::class]);
        $payment_builder->leftJoin(PurchasePaymentReceipt::class, 'pap.ppid = payment.id', 'pap');
        $payment_builder->inWhere('payment.po_id', $linked_po_ids);
        $payment_builder->andWhere('payment.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
        $payment_builder->andWhere('payment.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
        $payment_builder->inWhere('pap.pop_id', $linked_po_detail_ids);
        $payment_builder->groupBy('payment.po_id, pap.pop_id');
        $payment_receipt = $payment_builder->getQuery()->execute()->toArray();

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 上述PO行关联的PAR行的数据(已通过/已支付): ' . json_encode($payment_receipt, JSON_UNESCAPED_UNICODE));

        if (empty($payment_receipt)) {
            return $result;
        }

        $_payment_receipt = [];
        foreach ($payment_receipt as $receipt) {
            $_payment_receipt[$receipt['po_id'] . '_' . $receipt['pop_id']] = $receipt['ticket_amount_sum'];
        }
        unset($payment_receipt);

        // 2.1对比PUR行关联的PO行的含税总额合计 与 其关联的PAR行的含税发票金额合计
        // 若上述相等, 则将PAR行的发票金额(含税)合计值 赋给 该PUR行
        foreach ($_matching_apply_detail_ids as $apply_detail_info) {
            // PO行及其关联的PAR行的发票金额(含税)合计
            $_linked_receipt_ticket_amount_sum = 0;
            foreach ($apply_detail_info['po_list'] as $order_info) {
                $_po_pop_row_key = $order_info['po_id'] . '_' . $order_info['pop_id'];
                if (isset($_payment_receipt[$_po_pop_row_key])) {
                    $_payment_receipt_ticket_amount_sum = $enums_service->amountExchangeRateCalculation($_payment_receipt[$_po_pop_row_key], $order_info['exchange_rate'], 0);
                    $_linked_receipt_ticket_amount_sum = bcadd($_linked_receipt_ticket_amount_sum, $_payment_receipt_ticket_amount_sum);
                }
            }

            $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单的行信息: ' . json_encode($apply_detail_info, JSON_UNESCAPED_UNICODE) . '; 该行关联的PO行对应的PAR行的含税发票金额合计的结果: ' . $_linked_receipt_ticket_amount_sum);

            // 2.2对比PO单的行含税金额合计 与 其关联的PAR行的发票金额(含税)合计的结果
            // 来自付款单支付 且 PUR的行 与 PAR行有关联 且 PO的含税金额合计 < 其关联的付款单发票含税总额合计的时候, 需给出提示
            if ($is_from_payment_pay && in_array($apply_detail_info['pup_id'], $payment_linked_pup_ids) && $apply_detail_info['linked_po_all_total_sum'] < $_linked_receipt_ticket_amount_sum) {
                throw new ValidationException(static::$t->_('purchase_payment_receipt_ticket_amount_error'), ErrCode::$VALIDATE_ERROR);
            }

            // PO的含税金额合计 == 关联的付款单的发票含税金额合计, 则该申请单行的预算金额 = 发票含税金额合计
            if ($apply_detail_info['linked_po_all_total_sum'] == $_linked_receipt_ticket_amount_sum) {
                $result[$apply_detail_info['pup_id']] = $_linked_receipt_ticket_amount_sum;
            }
        }

        $this->logger->info('获取申请单的行关联的订单行被全额付款的金额(用于预算金额重算): 采购申请单的行 关联的PO行 对应的PAR行的含税发票金额合计的结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

        return $result;
    }

    /**
     * 关闭采购申请单
     * @param $data
     * @param $user
     * @return array
     */
    public function close($data, $user, $self = true)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        try {
            if (!$self) {
                //数据查询-必须要有特权才能关闭
                $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($user['id']);
                if (!$is_privilege_staff_id) {
                    throw new ValidationException(static::$t->_('purchase_apply_privilege_can_not_close'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $item = PurchaseApply::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $data['id']
                ]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_apply_empty_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            if ($self) {
                //我的申请-不能关闭别人的
                if ($item->create_id != $user['id']) {
                    throw new ValidationException(static::$t->_('purchase_apply_close_not_self'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //申请状态为"已通过",执行状态为"未生成PO","部分生成PO",且关闭状态为未关闭
            $can_close_conditions = $item->status == Enums::PURCHASE_APPLY_STATUS_APPROVAL && in_array($item->execute_status, [PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY, PurchaseEnums::APPLY_EXECUTE_STATUS_NO]) && $item->is_close == PurchaseEnums::IS_CLOSE_NO;
            if (!$can_close_conditions) {
                throw new ValidationException(static::$t->_('purchase_apply_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            $item->is_close = PurchaseEnums::IS_CLOSE_YES;
            if ($item->save() === false) {
                throw new BusinessException('采购申请单-我的申请-关闭采购申请单失败, 原因可能是: ' . get_data_object_error_msg($item) . '; 待更新数据: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info("purchase-apply-close-success params=" . json_encode($data) . '; user_id=' . $user['id']);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("purchase-apply-close-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 给审核节点名称为“采购经理”，“本地采购总监”，“集团采购总监”赋予“采购员”的编辑权限，保存采购员
     * @param object $purchase_apply_product 申请单-明细行对象
     * @param array $one_apply_product_params 明细行参数组
     * @return mixed
     * @throws ValidationException
     */
    public function saveAuditPurchaseStaff($purchase_apply_product, $one_apply_product_params)
    {
        $purchase_staff = $one_apply_product_params['purchase_staff'] ?? [];
        $purchase_staff_list = (new HrStaffRepository())->getStaffListByStaffIds($purchase_staff);
        $now = date('Y-m-d H:i:s');
        $purchase_apply_product_staff_arr = [];
        foreach ($purchase_staff as $staff_id) {
            $one_staff_info = $purchase_staff_list[$staff_id] ?? [];
            if (empty($one_staff_info) || $one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN || $one_staff_info['formal'] != StaffInfoEnums::FORMAL_IN || $one_staff_info['is_sub_staff'] != StaffInfoEnums::IS_SUB_STAFF_NO) {
                throw new ValidationException(static::$t->_('purchase_set.staff_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $purchase_apply_product_staff_arr[] = [
                'paid' => $purchase_apply_product->paid,
                'apply_product_id' => $purchase_apply_product->id,
                'staff_id' => $staff_id,
                'created_at' =>$now
            ];
        }

        //先删除原有的
        $purchase_staff_obj = $purchase_apply_product->getProductStaffs();
        $purchase_apply_product->purchase_staff = implode(',', array_column($purchase_staff_obj->toArray(), 'staff_id'));
        $purchase_staff_obj->delete();

        //保存修改采购员
        if ($purchase_apply_product_staff_arr) {
            $purchase_apply_product_staff_model = new PurchaseApplyProductStaffsModel();
            $purchase_apply_product_staff_model->batch_insert($purchase_apply_product_staff_arr);
        }
        $one_apply_product_params['purchase_staff'] = implode(',', $purchase_staff);
        return $one_apply_product_params;
    }

    /**
     * 数据查询-采购申请单-编辑 -【barcode、产品描述，单位，计量单位，规格型号、采购员】
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function dataUpdate($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //校验是否有修改权限
            if (!EnumsService::getInstance()->isPrivilegeStaffId($user['id'])) {
                throw new ValidationException(static::$t->_('purchase_apply_privilege_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            $item = PurchaseApply::findFirst([
                'conditions' => 'id = :id: AND status = :status: AND is_close = :is_close:',
                'bind' => [
                    'id' => $params['id'],
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_close' => PurchaseEnums::IS_CLOSE_NO
                ]
            ]);
            //采购申请的关闭状态不等于“已关闭”且审核状态为“已通过”才可编辑
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_apply_cannot_update'), ErrCode::$VALIDATE_ERROR);
            }
            // 要处理的PUR行
            foreach ($params['products'] as $product) {
                $purchase_apply_product = PurchaseApplyProduct::findFirst([
                    'conditions' => 'id = :id: AND paid = :paid:',
                    'bind' => ['id' => $product['id'], 'paid' => $item->id]
                ]);
                if (empty($purchase_apply_product)) {
                    throw new ValidationException(static::$t->_('purchase_product_not_exits'), ErrCode::$VALIDATE_ERROR);
                }

                //保存采购员
                $product = $this->saveAuditPurchaseStaff($purchase_apply_product, $product);
                $before = $purchase_apply_product->toArray();
                $before['purchase_staff'] = $purchase_apply_product->purchase_staff;

                //保存明细行信息
                $update_product_data = [
                    'product_option_code' => $product['product_option_code'] ?? '',
                    'desc' => $product['desc'] ?? '',
                    'unit' => $product['unit'] ?? '',
                    'metere_unit' => $product['metere_unit'] ?? '',
                    'product_option' => $product['product_option'] ?? ''
                ];
                $bool = $purchase_apply_product->save($update_product_data);
                if ($bool === false) {
                    throw new BusinessException('数据查询-采购申请单-编辑-PUR主数据更新失败, 原因可能是: ' . get_data_object_error_msg($purchase_apply_product) . '; 待更新数据: ' . json_encode($product, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                $after = $purchase_apply_product->toArray();
                $after['purchase_staff'] = $product['purchase_staff'];
                $this->saveUpdateLog(Enums::WF_PURCHASE_APPLY, $item->id, $item->pano, $before, $after, $user);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchase-apply-dataUpdate-failed:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data ?? []
        ];
    }
}
