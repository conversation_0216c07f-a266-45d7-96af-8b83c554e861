<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\LedgerAccount;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Deposit\Models\DepositLossModel;
use App\Modules\OrdinaryPayment\Services\SapService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Services\PayFlowService as Pay_PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Models\PurchaseAcceptanceModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentCargoModel;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Reimbursement\Models\RequestSapLog;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use App\Library\Enums\DepositEnums;
use App\Modules\Contract\Models\Contract;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Services\DepositService;
use App\Models\oa\PurchasePaymentClearanceLogModel;

class PaymentService extends BaseService
{
    // 采购付款单号前缀
    const PURCHASE_PAYMENT_NO_PREFIX = 'PAR';

    public $digits = 1000000;//原来4位 扩大为6位 需刷数据库
    public $digits_num = 6;
    public static $validate_receipt_param = [
        'receipt' => 'Required|Arr|ArrLenGe:1',
        'receipt[*]' => 'Required|Obj',
        'receipt[*].ticket_amount' => 'Required|FloatGt:0',     //发票金额
        'receipt[*].ticket_amount_not_tax' => 'Required|FloatGe:0', //发票金额不含税，小于等于ticket_amount
        'receipt[*].total' => 'Required|IntGe:0',               //数量
        'receipt[*].not_tax_price' => 'Required|FloatGt:0',     //不含税单价
        'receipt[*].vat7_rate' => 'Required|FloatGe:0',          //vat7税率
        'receipt[*].wht_type' => 'Required|FloatGe:0',          //0=无,1=PND3，2=PND53，
        'receipt[*].wht_ratio' => 'Required|FloatGe:0',         //0=无,1,2,3,5
    ];

    private static $instance;

    private function __construct(){
    }

    /**
     * @return PaymentService
     */
    public static function getInstance(){
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取新增默认值
     *
     * @param $param
     * @param array $user
     * @return array
     */
    public function defaultData($param, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new ValidationException(static::$t->_('access_data_staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 是否生成单号: 重新提交场景 = false, 只需获取其他默认值; 新增场景 = true
            $is_make_no = $param['is_make_no'] ?? true;
            $data['ppno'] = $is_make_no ? self::PURCHASE_PAYMENT_NO_PREFIX . static::getNo(date('Ymd'),RedisKey::PURCHASE_PAYMENT_COUNTER) : '';
            $data['apply_date'] = date('Y-m-d');
            $data['create_id'] = $user['id'];
            $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
            $data = array_merge($data, $arr);

            //查询未被关联且已通过审核的采购订单
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('o.id, o.pono');
            $builder->from(['o' => PurchaseOrder::class]);
            $builder->where('o.status = :statue: and o.is_cite = :is_cite:', [
                'statue' => Enums::WF_STATE_APPROVED,
                'is_cite' => PurchaseEnums::IS_CITE_NO
            ]);
            $data['orders'] = $builder->getQuery()->execute()->toArray();

            //关联采购申请单的时候用
            $data['applys'] = PurchaseApply::find([
                'columns' => 'id, pano',
                'conditions' => 'status = :status: and is_cite = :is_cite: and is_link_po = :is_link_po:',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_cite' => PurchaseEnums::IS_CITE_NO,
                    'is_link_po' => PurchaseEnums::IS_LINK_PO_NO
                ]
            ])->toArray();

            $data['cargo_owner'] = $this->cargoOwnerEnums(self::SCM_TYPE_PU);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $this->logger->warning('order-get-default-data-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 参数校验
     *
     * @param array $data
     * @param bool $is_recommint
     * @return void
     * @throws ValidationException
     */
    public function validation(array $data, bool $is_recommint = false)
    {
        //15362增加付款形式和付款数量校验
        $pay_method = array_unique(array_column($data['receipt'], 'pay_method'));
        if (count($pay_method) > 1) {
            throw new ValidationException(static::$t->_('purchase_payment_pay_method_error'), ErrCode::$VALIDATE_ERROR);
        }

        foreach ($data['receipt'] as &$receipt) {
            if ($receipt['pay_method'] == PurchaseEnums::PAYMENT_PAY_METHOD_NUMBER) {
                if (!isset($receipt['pay_num']) || empty($receipt['pay_num']) || !is_numeric($receipt['pay_num']) || $receipt['pay_num'] > $receipt['total']) {
                    throw new ValidationException(static::$t->_('purchase_payment_pay_num_error'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                if (isset($receipt['pay_num'])) {
                    unset($receipt['pay_num']);
                }
            }
        }

        // 静态参数校验规则
        $validate_data = self::$validate_payment_param;

        // 货主相关字段校验
        if (!empty($data['inbound_no'])) {
            $validate_data = array_merge($validate_data, self::$cargo_owner_param);
        }

        $country_code = get_country_code();

        // 菲律宾: 校验明细行的发票类型
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            $invoice_type_enums = implode(',', array_keys(GlobalEnums::$financial_invoice_type_item));
            $validate_data['receipt[*].invoice_type'] = "Required|IntIn:{$invoice_type_enums}|>>>:" . static::$t->_('financial_invoice_type_params_error');
        }

        // 印尼的必填校验
        if ($country_code == GlobalEnums::ID_COUNTRY_CODE) {
            $validate_data['receipt[*].is_with_vat_invoice'] = 'Required|IntIn:' . implode(',', GlobalEnums::$is_with_vat_invoice_item) . '|>>>:' . static::$t->_('is_with_vat_invoice_params_error');
            $validate_data['receipt[*].vat_invoice_number'] = 'IfIntEq:is_with_vat_invoice,' . GlobalEnums::IS_WITH_VAT_INVOICE_YES . '|Required|StrLenGeLe:1,100' . '|>>>:' . static::$t->_('vat_invoice_params_error');
        }

        // 判断swift_code 字段长度: 境外时, 必填; 非境外时, 非必填, 但最长不得超过30个字符
        if (isset($data['swift_code']) && isset($data['pay_where']) && $data['pay_where'] != Enums::PAY_WHERE_OUT) {
            $validate_data['swift_code'] = 'StrLenLe:30';
        }

        if ($is_recommint) {
            $validate_data['id'] = 'Required|IntGe:1';
        }

        Validation::validate($data, $validate_data);
    }

    /**
     * 保存采购付款申请单
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function saveOne($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $inbounds_data = $data['inbound_no'];

            $data = $this->handleData($data, $user);

            $this->logger->info('payment-save-one==data=' . json_encode($data, JSON_UNESCAPED_UNICODE));

            //判断是否存在
            $exists = PurchasePayment::getFirst([
                'conditions' => 'ppno = ?0',
                'columns'    => 'id',
                'bind'       => [$data['ppno']],
            ]);
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('purchase_payment_no_exist_error',
                    ['payment_no' => $data['ppno']]), ErrCode::$VALIDATE_ERROR);
            }

            $model = new PurchasePayment();
            $data['status'] = Enums::WF_STATE_PENDING;
            $bool  = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('采购付款申请单创建失败, ' . get_data_object_error_msg($model),
                    ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //是否订单所有产品都已经关联过,去掉数量限制
            $is_all = 0;
            foreach ($data['receipt'] as $product) {
                unset($product['id']);

                $t_model         = new PurchasePaymentReceipt();
                $product['ppid'] = $model->id;
                $product_bool    = $t_model->i_create($product);
                if ($product_bool === false) {
                    throw new BusinessException('采购付款申请单-发票信息创建失败, ' . get_data_object_error_msg($t_model),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }

                //可以不关联采购订单,原来是不限制数量，现在重新记录下，用来判断是否采购订单是否能修改。
                if (!empty($product['pop_id'])) {
                    $order_product = PurchaseOrderProduct::getFirst([
                        'conditions' => 'id = ?0',
                        'bind'       => [$product['pop_id']],
                    ]);
                    /*if (($order_product->total < $product['total']) || (($order_product->total - $order_product->pay_total) < $product['total'])) {
                        throw new ValidationException('不可多于采购订单的数量', ErrCode::$CONTRACT_CREATE_ERROR);
                    }*/
                    $order_product->pay_total     = bcadd($order_product->pay_total, $product['total']);
                    $order_product->is_can_update = 0;
                    //统计是否还有剩下的
                    $is_all           += ($order_product->total - $order_product->pay_total);
                    $up_order_product = $order_product->i_update();
                    if ($up_order_product === false) {
                        throw new BusinessException('采购付款申请单-修改采购订单产品已购数量失败, ' . get_data_object_error_msg($order_product),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }

                //关联采购申请单就是全关联
                if (!empty($product['pap_id'])) {
                    $apply_product = PurchaseApplyProduct::findFirst(
                        [
                            'conditions' => 'id = ?0',
                            'bind'       => [$product['pap_id']],
                        ]
                    );

                    $apply_product->order_total   = $apply_product->total;
                    $apply_product->is_can_update = 0;
                    $up_apply_product             = $apply_product->i_update();
                    if ($up_apply_product === false) {
                        throw new BusinessException('采购付款申请单-修改采购申请单产品已购数量失败, ' . get_data_object_error_msg($apply_product),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }

                //新增了个发票相关的字段 付款百分比 不能为0
                if (empty($product['percent'])) {
                    throw new BusinessException('采购付款申请单-付款百分比不能为0', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //如果已选择的没有剩下的,判断是否全部关联过=去掉数量限制，只限制总额
            if (!empty($data['po_id'])) {
                $po_id = $data['po_id'];

                //读写分离，读不到新加的,不要未付款的
                $total = PurchasePayment::sum([
                    'column'     => 'receipt_amount',
                    'conditions' => 'po_id = :id: AND status IN ({status_item:array}) AND id != :pid: AND pay_status != :pay_status:',
                    'bind'       => [
                        'id'          => $po_id,
                        'status_item' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL],
                        'pid'         => $model->id,
                        'pay_status'  => Enums::LOAN_PAY_STATUS_NOTPAY,
                    ],
                ]);

                $order = PurchaseOrder::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $po_id],
                ]);

                //加上本次的
                $total = bcadd($total, $data['receipt_amount']);

                //v8313===金额小计(不含税金额总计)+税费（vat7)
                $tAmount = bcadd($order->subtotal_amount, $order->taxation);
                //去掉发票金额和含税金额总的校验 改为行校验
                if ($total > $tAmount) {
                    /*  throw new ValidationException(
                          static::$t->_("purchase_payment_not_more_than_purchase_order"),
                          ErrCode::$CONTRACT_CREATE_ERROR
                      );*/
                }

                //如果等于直接设置不能关联
                if ($total == $tAmount) {
                    $order->is_cite = 1;
                    if ($order->save() === false) {
                        throw new BusinessException('采购付款申请单: order is_cite 更新失败, ' . get_data_object_error_msg($order),
                            ErrCode::$BUSINESS_ERROR);
                    }
                }

                //已通过的
                $total_pay_column = PurchaseOrderProduct::count([
                    'conditions' => 'poid = :id:',
                    'bind'       => ['id' => $po_id],
                ]);

                $can_not_update = PurchaseOrderProduct::count([
                    'conditions' => 'poid = :id: AND is_can_update = 0',
                    'bind'       => ['id' => $po_id],
                ]);
                $this->logger->info("total_pay_column===" . $total_pay_column . "====can_not_update===" . $can_not_update . "====");

                if ($total_pay_column == $can_not_update) {
                    $order->is_can_update = 0;
                    if ($order->save() === false) {
                        throw new BusinessException('采购付款申请单: order is_can_update 更新失败, ' . get_data_object_error_msg($order),
                            ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            //关联采购申请单的时候
            if (!empty($data['pa_id'])) {
                $pa_id = $data['pa_id'];

                //这只能找未关联的，只有一次
                $apply = PurchaseApply::findFirst([
                    'conditions' => 'id = :id: AND is_cite = 0',
                    'bind'       => ['id' => $pa_id],
                ]);

                if (empty($apply)) {
                    throw new ValidationException("not found pa", ErrCode::$VALIDATE_ERROR);
                }

                //如果PUR的含税金额总计 < 发票金额
                if ($apply->amount < $data['receipt_amount']) {
                    throw new ValidationException(self::$t->_("receipt_amount_gt_purchase_apply_amount"),
                        ErrCode::$VALIDATE_ERROR);
                }

                $apply->is_can_update = 0;
                $apply->is_cite       = 1;
                if ($apply->save() === false) {
                    throw new BusinessException('采购付款申请单: apply is_can_update/is_cite 更新失败, ' . get_data_object_error_msg($apply),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            if (!empty($inbounds_data)) {
                $inbounds = [];
                foreach ($inbounds_data as $inbound) {
                    $inbound_tmp               = [];
                    $inbound_tmp['inbound_no'] = $inbound['no'];
                    $inbound_tmp['mach_code']  = $inbound['mach_code'];
                    $inbound_tmp['mach_name']  = $inbound['mach_name'];
                    $inbound_tmp['payment_id'] = $model->id;
                    $inbound_tmp['created_at'] = date('Y-m-d H:i:s');
                    $inbounds[]                = $inbound_tmp;
                }
                $cargo_model = new PurchasePaymentCargoModel();

                $cargo_bool = $cargo_model->batchInsert($inbounds);
                if ($cargo_bool === false) {
                    throw new BusinessException('采购付款申请单-入库单货主关联创建失败, ' . get_data_object_error_msg($cargo_model),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT;
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['bucket_name']     = $attachment['bucket_name'];
                    $tmp['object_key']      = $attachment['object_key'];
                    $tmp['file_name']       = $attachment['file_name'];
                    $attachments[]          = $tmp;
                }

                $t_model     = new AttachModel();
                $attach_bool = $t_model->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('采购付款申请单-附件创建失败, ' . get_data_object_error_msg($t_model),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_PAYMENT))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('purchase_payment_create_work_flow_failed'),
                    ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchasePayment-create-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 处理数据
     *
     * @param  $data
     * @param  $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            throw new BusinessException('变量为空或数据类型有误, data = ' . json_decode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        $is_link_pa = $data['is_link_pa'];
        //po 带出的供应商需要校验
        if (!$is_link_pa) {
            $vendor_info = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'columns'    => 'vendor_id, vendor_name,company_address,tax_number,grade,grade_status,status',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);

            if (empty($vendor_info)) {
                throw new ValidationException(static::$t->_('vendor_info_get_fail'), ErrCode::$VALIDATE_ERROR);
            }
            // 等级为认证或注册 状态为暂停不可提交
            if ($vendor_info->grade_status == VendorEnums::VENDOR_GRADE_STATUS_SUSPEND && in_array($vendor_info->grade, [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION])) {
                throw new ValidationException(static::$t->_('vendor_grade_status_not_commit'), ErrCode::$VALIDATE_ERROR);
            }

            // 等级为注销 不可提交
            if ($vendor_info->grade == VendorEnums::VENDOR_GRADE_CANCELLATION) {
                throw new ValidationException(static::$t->_('vendor_grade_cancellation_not_commit'), ErrCode::$VALIDATE_ERROR);
            }

            // 等级为拉黑 不可提交
            if ($vendor_info->grade == VendorEnums::VENDOR_GRADE_BLOCK) {
                throw new ValidationException(static::$t->_('vendor_grade_block_not_commit'), ErrCode::$VALIDATE_ERROR);
            }

            //不满足状态(供应商信息通过等级状态正常注册或认证)提示
            if (!($vendor_info->status == Enums::WF_STATE_APPROVED && $vendor_info->grade_status == VendorEnums::VENDOR_GRADE_STATUS_NORMAL && in_array($vendor_info->grade, [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION]))) {
                throw new ValidationException(static::$t->_('vendor_grade_or_status_wrong'), ErrCode::$VALIDATE_ERROR);
            }
        }

        $acceptance_nos = [];

        if (empty($data['inbound_no'])) {
            $data['inbound_no'] = [];
        }

        $inbound_nos = [];
        foreach ($data['inbound_no'] as $k => $v) {
            if ($v['no']) {
                $inbound_nos[] = $v['no'];

                // 该入库通知单已被关联的校验逻辑变更 v15995
                // 由仅判断审批状态(待审批/已审批) 调整为 审批状态(待审批/已审批) + 支付状态(待支付/已支付)
                $inbound = PurchasePayment::getFirst([
                    'conditions' => 'status IN ({status:array}) AND pay_status IN ({pay_status:array}) AND FIND_IN_SET(:inbound_no:, inbound_no)',
                    'bind' => [
                        'status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                        'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
                        'inbound_no' => $v['no']
                    ]
                ]);

                if (!empty($inbound)) {
                    throw new ValidationException(static::$t->_('purchase_storage_order_related', ['inbound_no' => $v['no']]), ErrCode::$VALIDATE_ERROR);
                }

                $inbound_detail = $this->getInbound(['no' => $v['no'], 'mach_code' => $v['mach_code']]);
                if ($inbound_detail['code'] != ErrCode::$SUCCESS) {
                    throw new ValidationException($inbound_detail['message'], $inbound_detail['code']);
                }
            }
        }

        if ($is_link_pa) {
            $p_item = PurchaseApply::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['pa_id']]
            ]);

        } else {
            $p_item = PurchaseOrder::getFirst([
                'conditions' => 'id = ?0',
                'bind' => [$data['po_id']]
            ]);

            // 字段名统一
            if (isset($p_item->cost_company)) {
                $p_item->cost_company_id = $p_item->cost_company;
            }

            //查询所有的关联同一个po的付款申请单  每行上发票金额计算
            $po_id = $data['po_id'];

           $payment_product_amount =  $this->paymentProductAmount($po_id);
           $payment_product_amount  = array_column($payment_product_amount,'ticket_amount','pop_id');
        }

        //V21690 如有验收单时 - 根据验证验收单采购类型判断 - 验收单上的PO单、PUR单与传递的PO单、PUR单是否一致
        if (isset($data['acceptance_no']) && !empty($data['acceptance_no'])) {
            foreach ($data['acceptance_no'] as $k1 => $v1) {
                //校验是否是同一个po
                $acceptance_order = PurchaseAcceptanceModel::findFirst([
                    'conditions' => 'no = :no:',
                    'bind'       => ['no' => $v1['no']]
                ]);

                if (empty($acceptance_order)) {
                    throw new ValidationException(self::$t->_('acceptance_order_is_no'), ErrCode::$VALIDATE_ERROR);
                }

                //V21690 若验收单采购类型是采购订单 && 验收单上的PO号 != 传递的po单单号
                if (!$is_link_pa && $acceptance_order->po != ($p_item->pono ?? '')) {
                    throw new ValidationException(self::$t->_('acceptance_order_not_po'), ErrCode::$VALIDATE_ERROR);
                }

                //V21690 若验收单采购类型是采购申请单 && 验收单上的PUR号 != 传递的pur单号
                if ($is_link_pa && $acceptance_order->po != ($p_item->pano ?? '')) {
                    throw new ValidationException(self::$t->_('acceptance_order_not_pur'), ErrCode::$VALIDATE_ERROR);
                }

                $acceptance_nos[] = $v1['no'];
            }
        }

        // 相关合同不能关联租房付款合同
        if (!empty($data['contract_no'])) {
            $contract_info = Contract::findFirst([
                'conditions' => 'cno = :cno:',
                'bind' => ['cno' => $data['contract_no']],
                'columns' => 'id, contract_type'
            ]);
            if (empty($contract_info)) {
                throw new ValidationException(static::$t->_('purchase_payment_contract_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($contract_info->contract_type == Enums\ContractEnums::CONTRACT_TYPE_RENTING) {
                throw new ValidationException(static::$t->_('purchase_payment_contract_renting_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        $paymentData = [
            'ppno' => $data['ppno'],
            'apply_type' => $p_item->apply_type,
            'apply_date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'create_id' => $data['create_id'],
            'create_name' => $data['create_name'],

            'create_department_name' => DepartmentModel::getFirst([
                    'conditions' => 'id = ?0',
                    'columns' => 'name',
                    'bind' => [$data['create_department_id']]
                ])->name ?? '',

            'create_department_id' => $data['create_department_id'],
            'cost_department' => $p_item->cost_department,
            'cost_department_name' => $p_item->cost_department_name,
            'cost_store' => $p_item->cost_store,
            'cost_store_name' => $p_item->cost_store_name,
            'method' => $data['method'],
            'is_link_pa' => $data['is_link_pa'],
            'po_id' => $data['po_id'] ?? 0,
            'pa_id' => $data['pa_id'] ?? 0,
            'ponos' => $data['pono'] = $p_item->pono ?? '',
            'pano' => $data['pano'] = $p_item->pano ?? '',
            'inbound_no' => implode(',', $inbound_nos),
            'acceptance_no' => implode(',', $acceptance_nos),
            'payment_method' => $data['payment_method'],
            'remark' => $data['remark'] ?? '',
            'attachment' => $data['attachment'],
            'currency' => $p_item->currency,
            'exchange_rate' => $p_item->exchange_rate,//v17472 汇率取PAR关联的PO/PUR的汇率, 因PAR的币种不可修改, 因此汇率直接继承PO/PUR的
            'amount' => bcmul($data['amount'], 1000, 2),  //这个数下面会被改
            'sys_department_id' => $user['sys_department_id'] ?? 0,
            'node_department_id' => $user['node_department_id'] ?? 0,

            // 费用所属公司
            'cost_company_id' => $p_item->cost_company_id ?? '',
            'cost_company_name' => $p_item->cost_company_name ?? '',

            //不读order的，有可能是填写
            'vendor_id' => $data['vendor_id'] ?? '',
            'vendor' => $data['vendor'] ?? '',
            'vendor_contact' => $data['vendor_contact'] ?? '',
            'vendor_phone' => $data['vendor_phone'] ?? '',
            'vendor_email' => $data['vendor_email'] ?? '',
            'vendor_addr' => $data['vendor_addr'] ?? '',// 供应商地址
            'vendor_tax_number' => $data['vendor_tax_number'] ?? '',// 供应商税务号

            'payment_to' => $data['payment_to'] ?? '',
            'loan_time' => $data['loan_time'] ?? '',
            'bank_name' => $data['bank_name'],
            'bank_account_name' => $data['bank_account_name'],
            'bank_no' => $data['bank_no'],
            'swift_code' => $data['swift_code'] ?? '',
            'pay_where' => $data['pay_where'] ?? 1,

            //相关合同单号
            'contract_no' => $data['contract_no'] ?? '',
            //15362新增应付日期
            'due_date' => $data['due_date']
        ];

        if ($is_link_pa) {
            $paymentData['ponos'] = '';
            $paymentData['po_id'] = 0;
        } else {
            $paymentData['pano'] = '';
            $paymentData['pa_id'] = 0;
        }

        // 发票列表处理
        $country_code = get_country_code();
        foreach ($data['receipt'] as $k => $v) {
            $data['receipt'][$k]['vendor'] = $v['vendor'] ?? '';
            $data['receipt'][$k]['product_name'] = !$v['budget_id'] && ProductService::getInstance()->getTkey($v['product_name'],static::$language ?? 'th') ? ProductService::getInstance()->getTkey($v['product_name'],static::$language ?? 'th') :  $v['product_name'];//非新数据 产品名称
            $data['receipt'][$k]['unit'] =  !$v['budget_id'] && ProductService::getInstance()->getTkey($v['unit'],static::$language ?? 'th') ? ProductService::getInstance()->getTkey($v['unit'],static::$language ?? 'th') : $v['unit'];//单位

            // 付款单关联的产品id(订单OR申请单)
            if ($is_link_pa) {
                //关联采购申请单
                $data['receipt'][$k]['pop_id']  = 0;
                $productDetail = PurchaseApplyProduct::findFirst( [
                    'conditions' => 'id = ?0',
                    'bind' => [$v['pap_id']]
                ]);

            } else {
                //采购单行产品 含税金额
                $all_total_amount = 0;

                //付款申请单行产品发票金额
                $all_ticket_amount = 0;

                //关联采购订单
                $data['receipt'][$k]['pap_id']  = 0;
                $productDetail = PurchaseOrderProduct::getFirst([
                    'conditions' => 'id = ?0',
                    'columns' => 'total,pay_total,price,is_check,total_price,vat7,vat7_rate',
                    'bind' => [$v['pop_id']]
                ]);
            }

            $all_total_amount = bcadd($productDetail->total_price, $productDetail->vat7);
            $all_ticket_amount = $v['ticket_amount']*1000;
            $all_ticket_amount = bcadd( $all_ticket_amount,$payment_product_amount[$v['pop_id']] ?? 0);


            if (bcsub($all_total_amount, $all_ticket_amount) < 0) {
                $this->logger->info('采购订单或采购申请单对应产品id===' . ($v['pop_id'] ?? 0) . "==" . ($v['pap_id'] ?? 0));
                throw new ValidationException(static::$t->_('purchase_payment_not_more_than_purchase_order', ['p_id' => $v['pop_id']]), ErrCode::$VALIDATE_ERROR);
            }

            if (empty($productDetail)) {
                $this->logger->info('该采购订单或采购申请单对应产品不存在==='.($v['pop_id']??0)."==".($v['pap_id']??0));
                throw new ValidationException(static::$t->_('the_purchase_payment_rel_pap_or_pop_null', [
                    'rel_pid' => $is_link_pa ? $v['pap_id'] : $v['pop_id']
                ]), ErrCode::$VALIDATE_ERROR);
            }

            //V21117 PO是强制验收的，par上没有关联验收单，且没有关联入库单,
            if (!$is_link_pa && isset($productDetail->is_check) && empty($data['acceptance_no']) && empty($data['inbound_no']) && 1 == $productDetail->is_check && $data['method'] == PurchaseEnums::PAYMENT_PAY_METHOD_1) {
                throw new ValidationException(static::$t->_('purchase_acceptance_order_required'), ErrCode::$VALIDATE_ERROR);
            }

            //V21690 当采购付款的采购方式选择货到付款时，在提交时，若PUR是强制验收的，par上没有关联验收单，那么在提交par时，会校验：付款项目需要提供验收单，请关联验收单！
            if ($is_link_pa && $data['method'] == PurchaseEnums::PAYMENT_PAY_METHOD_1 && isset($productDetail->is_check) && 1 == $productDetail->is_check && empty($data['acceptance_no'])) {
                throw new ValidationException(static::$t->_('purchase_acceptance_order_required'), ErrCode::$VALIDATE_ERROR);
            }

            //直接关联采购申请单的直接全用。==灵活先不限制吧。
            if ($is_link_pa) {
                $data['receipt'][$k]['budget_id'] = $productDetail->budget_id;
                $data['receipt'][$k]['level_code'] = $productDetail->level_code;
                $data['receipt'][$k]['product_id'] = $productDetail->product_id;
                $data['receipt'][$k]['product_name'] = $productDetail->product_name;
                $data['receipt'][$k]['ledger_account_id'] = $productDetail->ledger_account_id;
                $data['receipt'][$k]['total'] = $productDetail->total;
                $data['receipt'][$k]['unit'] = $productDetail->unit;
                //$data['receipt'][$k]['total_price'];  不含税金额，会根据单价*数量算
                //tax_total_price //含税金额也是
            }

            //用采购订单或采购申请单的金额， 避免前端串改金额
            $data['receipt'][$k]['not_tax_price'] = bcdiv($productDetail->price, $this->digits, $this->digits_num);

            //用关联的采购订单或采购申请单的vat7税率和税额
            $data['receipt'][$k]['vat7_rate'] = $productDetail->vat7_rate;
            $data['receipt'][$k]['tax_ratio'] = bcdiv($productDetail->vat7,1000,2);

            // 菲律宾的: 发票类型
            $data['receipt'][$k]['invoice_type'] = $country_code == GlobalEnums::PH_COUNTRY_CODE ? $v['invoice_type'] : GlobalEnums::FINANCIAL_INVOICE_TYPE_0;

            // 印尼: 是否有增值税票
            $data['receipt'][$k]['is_with_vat_invoice'] = $country_code == GlobalEnums::ID_COUNTRY_CODE ? $v['is_with_vat_invoice'] : GlobalEnums::IS_WITH_VAT_INVOICE_DEFAULT;
        }

        $this->handleProductData($data);

        //如果关联采购申请单-发票金额（含税）不能大于含税金额，否则释放预算会有问题。
        if ($is_link_pa) {
            foreach ($data['receipt'] as $k => $v) {
                if ($v['ticket_amount'] > $v['tax_total_price']) {
                    throw new ValidationException(self::$t->_("receipt_amount_gt_purchase_apply_amount"), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        if ($data['count_total'] == 0) {
            throw new ValidationException(self::$t->_('purchase_payment_no_pay_num'), ErrCode::$VALIDATE_ERROR);
        }

        $paymentData['receipt'] = $data['receipt'];
        $paymentData['amount'] = $data['amount'];
        $paymentData['not_tax_amount'] = $data['not_tax_amount'];
        $paymentData['vat7_amount'] = $data['vat7_amount'];
        $paymentData['wht_amount'] = $data['wht_amount'];
        $paymentData['receipt_amount'] = $data['receipt_amount'];
        $paymentData['real_amount'] = $data['real_amount'];
        $paymentData['cur_amount'] = $data['cur_amount'];
        $paymentData['ticket_amount_tax'] = $data['ticket_amount_tax'];

        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            if (isset($data['is_clearance']) && is_numeric($data['is_clearance']) && in_array($data['is_clearance'],
                    [Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_YES, Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_NO])) {
                $paymentData['is_clearance'] = $data['is_clearance'];
            }
            if (!empty($data['actual_clearance_date'])) {
                $paymentData['actual_clearance_date'] = $data['actual_clearance_date'];
            }
            if (!empty($data['expect_clearance_date'])) {
                $paymentData['expect_clearance_date'] = $data['expect_clearance_date'];
            }
            $paymentData['clearance_no'] = $data['clearance_no'] ?? '';
        }

        $this->logger->info('purchasePayment-create-info:paymentData===='.json_encode($paymentData,JSON_UNESCAPED_UNICODE));

        return $paymentData;
    }

    /**
     * 计算相关提取出来，多一次循环
     *
     * @param array $data
     * @param bool $is_mul_thousands 是否乘以（单价乘以1w)
     * @param bool $is_cal_total 是否需重新计算总额
     * @return mixed
     * @throws ValidationException
     */
    public function handleProductData(&$data,$is_mul_thousands=true, $is_cal_total = true){
        //金额总计
        $amount = 0;

        //不含税总价
        $not_tax_amount = 0;

        //vat7总价
        $vat7_amount = 0;

        //wht总价
        $wht_amount = 0;

        //发票总价，用这个金额审批
        $receipt_amount = 0;

        //总实付金额
        $real_amount = 0;

        //本次实付金额=v9311改了算的方法
        $cur_amount = 0;

        //发票税额总计 v12460
        $ticket_amount_tax = 0;

        //数量
        $count_total = 0;

        $countryCode = get_country_code();
        //v12460 总实付金额（行上的字段） - 计算逻辑改成 不含税单价*订单数量+不含税单价*订单数量*vat7税率 - 该行关联的PO上的WHT金额，如果没有关联PO 就取0 (2022.04.15)，历史数据不调整 (代码逻辑暂留下个需求用到)

        $_pop_ids = array_values(array_filter(array_unique(array_column($data['receipt'],  'pop_id'))));
        $po_product_item = [];
        if (!empty($_pop_ids)) {
            $_po_product_item = PurchaseOrderProduct::find([
                'conditions' => 'id IN ({pop_ids:array})',
                'columns' => 'id, wht_amount',
                'bind' => ['pop_ids' => $_pop_ids]
            ])->toArray();
            $po_product_item = array_column($_po_product_item, 'wht_amount', 'id');
        }


        foreach ($data['receipt'] as $k => &$v) {
            //发票金额，用户输入的
            //ticket_amount 发票税额=发票金额（含税）减 发票金额（不含税）
            $v['ticket_tax'] =  bcsub($v['ticket_amount'],$v['ticket_amount_not_tax'],2);
            $v['ticket_amount'] = round($v['ticket_amount'],2);
            if($is_mul_thousands){
                $v['ticket_amount'] = bcmul($v['ticket_amount'],1000);
            }

            $receipt_amount = bcadd($receipt_amount,$v['ticket_amount'],2);


            //发票金额（不含税金额），也是用户输入的，主要用来算wht
            $v['ticket_amount_not_tax'] = round($v['ticket_amount_not_tax'],2);
            $v['ticket_amount_not_tax'] = bcadd($v['ticket_amount_not_tax'],0,2);
            //都算完再乘以1000
            /*if($is_mul_thousands){
                $v['ticket_amount_not_tax'] = bcmul($v['ticket_amount_not_tax'],1000);
            }*/


            //不含税单价
            $v['not_tax_price'] = round($v['not_tax_price'],$this->digits_num);
            $v['not_tax_price'] = bcadd($v['not_tax_price'],0,$this->digits_num);

            //不含税总价
            $v['total_price'] = round($v['not_tax_price']*$v['total'],2);
            $v['total_price'] = bcadd($v['total_price'],0,2);
            if($is_mul_thousands){
                $v['total_price'] = bcmul($v['total_price'],1000);
            }
            $not_tax_amount = bcadd($not_tax_amount,$v['total_price'],2);

            //数量
            $count_total+= $v['total'];


            $v['tax_ratio'] = bcadd($v['tax_ratio'],0,2);
            if($is_mul_thousands){
                $v['tax_ratio'] = bcmul($v['tax_ratio'],1000);
                $v['ticket_tax'] = bcmul($v['ticket_tax'],1000);
            }
            //发票税额总计计算
            $ticket_amount_tax = bcadd($ticket_amount_tax,$v['ticket_tax'],2);

            $v['deductible_vat_tax'] = (string)$v['deductible_vat_tax'];
            //可抵扣税额=VAT税额*可抵扣VAT税率
            $v['deductible_vat_amount'] = bcdiv(bcmul($v['ticket_tax'],$v['deductible_vat_tax']),100,2);

            //税额和
            $vat7_amount = bcadd($vat7_amount ,$v['tax_ratio'],2);

            //含税金额
            $v['tax_total_price'] = bcadd($v['tax_ratio'] ,$v['total_price'],2);
            $amount = bcadd($amount,$v['tax_total_price'],2);


            //v8381修改
            $roundTax = $is_mul_thousands ?  $v['tax_ratio']/1000 : $v['tax_ratio'];

            //v12460总实付金额计算wht金额取po行上的, 没有就是0
            $roundWht = $po_product_item[$v['pop_id']]??0;
            $roundWht =  bcdiv($roundWht,1000,2);  //逻辑暂留下个需求会用到. 12836用到了
            //$roundWht = $v['wht_amount'];


            $v['wht_amount'] = bcadd($v['wht_amount'],0,2);
            if($is_mul_thousands){
                $v['wht_amount'] = bcmul($v['wht_amount'],1000);
            }
            $wht_amount = bcadd($wht_amount,$v['wht_amount'],2);


            $v['real_amount'] = bcsub(
                bcadd(
                    round($v['not_tax_price'] * $v['total'], 2),     //不含税单价*订单数量
                    round($roundTax, 2),  //不含税单价*订单数量*vat7税率
                    2
                ),
                round($roundWht, 2),  //-订单数量*不含税单价*WHT税率
                2
            );
            if($is_mul_thousands){
                $v['real_amount'] = bcmul($v['real_amount'],1000);
            }
            $real_amount = bcadd($real_amount,$v['real_amount'],2);



            //进项税调整
            $v['input_tax'] = round($v['not_tax_price']*$v['total']*0.063399,2);
            $v['input_tax'] = bcadd($v['input_tax'],0,2);
            if($is_mul_thousands){
                $v['input_tax'] = bcmul($v['input_tax'],1000);
            }


            //可抵扣进项税
            $v['deduct_input_tax'] = round($v['not_tax_price']*$v['total']*0.006601,2);
            $v['deduct_input_tax'] = bcadd($v['deduct_input_tax'],0,2);
            if($is_mul_thousands){
                $v['deduct_input_tax'] = bcmul($v['deduct_input_tax'],1000);
            }


            if($is_mul_thousands){
                //价格保留4位小数
                //单价
                $v['not_tax_price'] = bcmul($v['not_tax_price'], $this->digits);
                //发票金额（不含税）
                $v['ticket_amount_not_tax'] = bcmul($v['ticket_amount_not_tax'],1000);
            }


            if ($v['ticket_amount_not_tax'] > $v['ticket_amount']) {
                throw new ValidationException(self::$t->_("ticket_amount_not_tax_not_gt_ticket_amount"));
            }
        }

        //如果含税金额=发表金额-v8381===v9331去掉了改成，发票金额（不含税）*WHT税率
        /*$is_eq = false;
        if($amount == $receipt_amount){
            $is_eq = true;
        }

        //不可以用$v
        foreach ($data['receipt'] as $k=>&$item){
            if($is_eq){
                //wht金额=订单数量*不含税单价*WHT税率
                if ('PH' != strtoupper($countryCode)) {
                    $item['wht_amount'] = round($item['not_tax_price']*$item['total']*$item['wht_ratio']/100,2);
                }
                if($is_mul_thousands){
                    $item['wht_amount'] = bcmul($item['wht_amount'],1000);
                }
            }else{
                //WHT金额= 发票金额*WHT税率
                //发票金额上面乘以了1000，这不用继续乘
                if ('PH' != strtoupper($countryCode)) {
                    $item['wht_amount'] = round($item['ticket_amount'] * $item['wht_ratio']/100,2);
                } elseif($is_mul_thousands) {
                    $item['wht_amount'] = bcmul($item['wht_amount'],1000);
                }
            }
            $item['wht_amount'] = bcadd($item['wht_amount'],0,2);
            $wht_amount = bcadd($wht_amount,$item['wht_amount'],2);

            //本次实付金额
            $item['cur_amount'] = ''.($item['ticket_amount']-$item['wht_amount']);
            $item['cur_amount'] = bcadd($item['cur_amount'],0,2);
            $cur_amount = bcadd($cur_amount,$item['cur_amount'],2);

            //单价最后乘以1wF
            if($is_mul_thousands){
                //价格保留4位小数
                $item['not_tax_price'] = bcmul($item['not_tax_price'], 10000);
            }
        }*/

        // 总数后端算下
        $data['amount'] = $amount;// 含税金额总计
        $data['not_tax_amount'] = $not_tax_amount;// 不含税金额
        $data['vat7_amount'] = $vat7_amount;//vat总计
        $data['count_total'] = $count_total;//

        $data['real_amount'] = $real_amount;// 实付金额总计
        $data['wht_amount'] = $wht_amount;// wht金额总计
        $data['receipt_amount'] = $receipt_amount;// 发票金额总计

        // 本次付款金额 = 发票金额（含税）总计-WHT金额总计
        $data['cur_amount'] = bcsub($receipt_amount, $wht_amount,2);
        $data['ticket_amount_tax'] = $ticket_amount_tax;
    }

    /**
     * 获取入库通知单详情
     *
     * @param $data
     * @return array
     */
    public function getInbound($data)
    {
        try {
            $inbound_no = $data['no'] ?? '';
            if (in_array($inbound_no, $data['used_no'] ?? [])) {
                throw new ValidationException(static::$t->_('purchase_payment_inbound_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            // 该入库通知单没有选择货主
            if (!isset($data['mach_code'])) {
                throw new ValidationException('purchase_payment_inbound_no_owner', ErrCode::$VALIDATE_ERROR);
            }

            $post_str = (new FfwService($data['mach_code']))->buildRequestParam(['inboundSn' => $inbound_no]);
            $data = (new FfwService($data['mach_code']))->postRequest('Inbound/getInBoundDetail', $post_str);

            // 暂无数据，请填写正确的入库通知单编号
            if (empty($data['data'])) {
                throw new ValidationException(static::$t->_('purchase_payment_inbound_data_null', ['inbound_no' => $inbound_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 该入库通知单未完成，请完成后再申请付款
            if ($data['data'][0]['status'] != 50) {
                throw new ValidationException(static::$t->_('purchase_payment_inbound_not_finished', ['inbound_no' => $inbound_no]), ErrCode::$VALIDATE_ERROR);
            }

            $goodsList = [];
            foreach ($data['data'][0]['goodsList'] as $k => $v) {
                $goodsList[] = [
                    'rowNumber'     =>  $v['rowNumber'],//行号(顺序号)
                    'barCode'       =>  $v['barCode'],//商品条形码
                    'goodsName'     =>  $v['goodsName'],//商品名称
                    'specification' =>  $v['specification'],//商品规格
                    'number'        =>  $v['number'],//通知数量&入库数量
                    'inNum'         =>  $v['inNum'],//收货数量
                    'price'         =>  bcdiv($v['price'], 100, 2),//单价
                    'totalPrice'    =>  bcdiv($v['totalPrice'], 100, 2),//金额
                ];
            }

            $data['data'] = [
                'no'            =>  $data['data'][0]['inBoundSn'],
                'status'        =>  $data['data'][0]['status'],
                'createdTime'   =>  $data['data'][0]['createdTime'],
                'completeTime'  =>  $data['data'][0]['completeTime'],
                'goodsList'     =>  $goodsList,
            ];

            return $data;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch(\Exception $e) {
            $this->logger->error('purchasePayment-create-failed:' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 获取采购付款申请单详情
     *
     * @param int $id
     * @param int $uid
     * @param boolean $download
     * @param int $type
     * @return array
     */
    public function getDetail(int $id, $uid = 0, $download = false, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';
        $data = [];

        try {
            // 支付详情: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPurchasePaymentPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $payment = PurchasePayment::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($payment)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            $req = (new PayFlowService(Enums::WF_PURCHASE_PAYMENT))->getRequest($id);
            if (empty($req->id)) {
                throw new BusinessException("获取工作流批次失败 [biz_id:{$id}]", ErrCode::$BUSINESS_ERROR);
            }

            $receipts = $payment->getReceipts();
            $attachments = $payment->getAttachments(['columns' => 'bucket_name, object_key, file_name']);
            $data = $payment->toArray();

            $attach_list = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type:',
                'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT_SU],
                'columns'    => ['id', 'bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
            ])->toArray();

            $attach_supplement_list = [];
            if (!empty($attach_list)) {
                foreach ($attach_list as $item) {
                    $attach_supplement_list[] = $item;
                }
            }
            $data['required_supplement_file'] = $attach_supplement_list;

            // 历史数据兼容处理 - 费用所属网点/总部: -1,1 总部
            $data['cost_store'] = in_array($data['cost_store'], [-1, 1]) ? 1 : 2;
            if ($data['is_link_pa']) {
                $apply = PurchaseApply::findFirst([
                    'id =:id:',
                    'bind' => ['id' => $data['pa_id']]
                ]);

                $apply = empty($apply) ? [] : $apply->toArray();
                $data['department_id'] = $apply['cost_department'] ?? 0;
                $data['department_name'] = $apply['cost_department_name'] ?? null;
                $data['store_id'] = $apply['cost_store_name'] ?? null;

                if ($data['cost_company_id']) {
                    $data['cost_company_id'] = $apply['cost_company_id'] ?? '';
                }

            } else {
                $order = PurchaseOrder::findFirst([
                    'id =:po_id:',
                    'bind' => ['po_id' => $data['po_id']]
                ]);
                $order = empty($order) ? [] : $order->toArray();
                $data['department_id'] = $order['cost_department'] ?? 0;
                $data['department_name'] = $order['cost_department_name'] ?? null;
                $data['store_id'] = $order['cost_store_name'] ?? null;
                $data['purchase_type'] = $order['purchase_type'] ?? null;
                if ($data['cost_company_id']) {
                    $data['cost_company_id'] = $order['cost_company'] ?? '';
                }
            }

            // 处理费用所属公司为空的老数据
            if (empty($data['cost_company_name']) && !empty($data['cost_company_id'])) {
                $data['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($data['cost_company_id']);
            }

            $receipts = $receipts ? $receipts->toArray() : [];
            $categories = array_values(array_filter(array_column($receipts, 'category_a')));
            $categories = array_merge($categories, array_values(array_filter(array_column($receipts, 'category_b'))));
            $budgetIds = array_values(array_filter(array_column($receipts, 'budget_id')));

            if ($categories) {
                $categories = PurchaseProductCategory::find([
                    'conditions' => ' id in ({ids:array}) ',
                    'bind' => ['ids' => $categories]
                ])->toArray();
                $categories = array_column($categories, null,'id');
            }

            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets = $budgetService->budgetObjectList($budgetIds);
            }

            $ledger_account_ids = array_values(array_filter(array_column($receipts,'ledger_account_id')));
            $ledgerIdToName = [] ;
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'],'name','id');
                }
            }

            foreach ($receipts as $k => $receipt) {
                if (isset($categories) && isset($categories[$receipt['category_a']])) {
                    $receipts[$k]['category_a_text'] = $categories[$receipt['category_a']]['name'];
                }

                if (isset($categories) && isset($categories[$receipt['category_b']])) {
                    $receipts[$k]['category_b_text'] = $categories[$receipt['category_b']]['name'];
                }

                if (isset($budgets) && isset($budgets[$receipt['budget_id']])) {
                    $receipts[$k]['budget_text'] = $budgets[$receipt['budget_id']]['name_' . strtolower(substr(static::$language, -2))];
                }

                //核算科目名字
                $receipts[$k]['ledger_account_name'] = $ledgerIdToName[$receipt['ledger_account_id']] ?? '';

                // 老数据的费用所属网点获取: -1 总部; -2 其他=>按网点处理
                if (empty($receipt['cost_store_id'])) {
                    $receipts[$k]['cost_store_id'] = $data['cost_store'];
                    $receipts[$k]['cost_store_name'] = $data['cost_store'] == 1 ? self::$t['payment_cost_store_type_1'] : $data['cost_store_name'];
                }

                // 发票类型
                $receipts[$k]['invoice_type'] = '';
                $receipts[$k]['invoice_type_label'] = '';
                if (!empty($receipt['invoice_type'])) {
                    $receipts[$k]['invoice_type'] = $receipt['invoice_type'];
                    $receipts[$k]['invoice_type_label'] = static::$t->_(GlobalEnums::$financial_invoice_type_item[$receipt['invoice_type']]) ?? '';
                }

                // 是否有增值税票
                $receipts[$k]['is_with_vat_invoice'] = $receipt['is_with_vat_invoice'] ? $receipt['is_with_vat_invoice'] : '';
            }

            if ($budgetIds) {
                $data['receipt_v1'] = $receipts;
            } else {
                $data['receipt'] = $receipts;
            }

            $ask = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $data['ask_id'] = $ask ? $ask->id:'';
            $data['auth_logs'] = $this->getAuditLogs($req, $data, $download);
            $data['attachment'] = $attachments ? $attachments->toArray() : [];

            $data['edit_logs'] = (new PayFlowService(Enums::WF_PURCHASE_PAYMENT))->getEditLog($req);
            $data['can_edit'] = false;
            $data['can_edit_fields'] = (object)[];
            $data['is_can_edit_ledger_account'] = false;
            if ($type == self::LIST_TYPE_AUDIT) {
                $can_edit_fields = (new PayFlowService(Enums::WF_PURCHASE_PAYMENT))->getCanEditFieldByReq($req, $uid);
                if ($can_edit_fields !== false) {
                    $data['can_edit_fields'] = $can_edit_fields;
                    $data['can_edit'] = true;
                }

                // 是否是 APS 北京节点: 能不能修改 核算科目
                if (isset($can_edit_fields['meta']) && in_array('ledger_account_id', $can_edit_fields['meta'])) {
                    $sys_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds(true);
                    if (!empty($data['cost_company_id']) && !empty($sys_company_ids['FlashExpress']) && (string)$data['cost_company_id'] == $sys_company_ids['FlashExpress']) {
                        $data['is_can_edit_ledger_account'] = true;
                    }
                }
            }
            //获取vendor信息
            $vendorDetail = Vendor::findFirstByVendorId($data['vendor_id']);
            if (!empty($vendorDetail)) {
                $data['ownership']     = $vendorDetail->ownership;
            }

            //下面会重新赋值，直接给data数组赋值没用
            $data = $this->handleDetailData($data);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchase_payment-detail-failed: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $code == ErrCode::$SUCCESS ? $data : []
        ];
    }

    /**
     * 处理详情数据
     *
     * @param array $data
     * @return array
     */
    private function handleDetailData($data = [])
    {
        if (empty($data)) {
            return [];
        }

        $whtArr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        if (isset($data['receipt'])) {
            foreach ($data['receipt'] as $k => $v) {
                $data['receipt'][$k] = [
                    'id'            =>  $v['id'],
                    'pop_id'        =>  $v['pop_id'],
                    'pono'          =>  $data['ponos'],
                    'vendor'        =>  $v['vendor'],
                    'category_a'    =>  $v['category_a'],
                    'category_b'    =>  $v['category_b'],
                    'category_a_text'    =>  $v['category_a_text'],
                    'category_b_text'    =>  $v['category_b_text'],
                    'product_id'    =>  $v['product_id'],
                    'product_name'  =>  static::$t->_($v['product_name']),
                    'product_option' =>  $v['product_option'],
                    'product_option_code' =>  $v['product_option_code'],
                    'finance_code'  =>  $v['finance_code'],
                    'total'         =>  $v['total'],
                    'unit'          =>  static::$t->_($v['unit']),
                    'not_tax_price' =>  bcdiv($v['not_tax_price'], $this->digits, $this->digits_num),
                    'total_price'   =>  bcdiv($v['total_price'], 1000, 2),
                    'tax_total_price' =>  bcdiv($v['tax_total_price'], 1000, 2),
                    'percent' => intval($v['percent']),
                    'ticket_amount' =>  bcdiv($v['ticket_amount'], 1000, 2),
                    'ticket_amount_not_tax' => bcdiv($v['ticket_amount_not_tax'], 1000, 2),
                    'ticket_tax'    => bcdiv($v['ticket_tax'], 1000, 2),
                    'input_tax'     =>  bcdiv($v['input_tax'], 1000, 2),
                    'deduct_input_tax' =>  bcdiv($v['deduct_input_tax'], 1000, 2),
                    'tax_ratio'     =>  bcdiv($v['tax_ratio'], 1000, 2),
                    'ticket_date'   =>  $v['ticket_date'],
                    'ticket_number' =>  $v['ticket_number'],
                    'invoice_type'  =>  $v['invoice_type'],
                    'invoice_type_label' => $v['invoice_type_label'],
                    'wht_type'      =>  $v['wht_type'],
                    'wht_type_name' =>  $whtArr[$v['wht_type']] ?? '',
                    'wht_ratio'     =>  $v['wht_ratio'],
                    'wht_amount'    =>  bcdiv($v['wht_amount'], 1000, 2),
                    'real_amount'   =>  bcdiv($v['real_amount'], 1000, 2),
                    'ledger_account_name' => $v['ledger_account_name'],
                    'ledger_account_id' => $v['ledger_account_id'],
                    'cost_store_id' => $v['cost_store_id'],
                    'cost_store_name' => $v['cost_store_name'],
                    'cost_center_name' => $v['cost_center_name'],
                    'vat7_rate' => $v['vat7_rate'],
                    'is_with_vat_invoice' => $v['is_with_vat_invoice'],
                    'pay_method' => $v['pay_method'],
                    'pay_num' => $v['pay_num']
                ];
            }
        } else {
            foreach ($data['receipt_v1'] as $k => $v) {
                $data['receipt_v1'][$k] = [
                    'id'            =>  $v['id'],
                    'pop_id'        =>  $v['pop_id'],
                    'pono'          =>  $data['ponos'],
                    'vendor'        =>  $v['vendor'],
                    'budget_id'     => $v['budget_id'],
                    'budget_text'   => $v['budget_text'],
                    'product_id'    =>  $v['product_id'],
                    'product_name'  =>  static::$t->_($v['product_name']),
                    'product_option' =>  $v['product_option'],
                    'product_option_code' =>  $v['product_option_code'],
                    'finance_code'  =>  $v['finance_code'],
                    'total'         =>  $v['total'],
                    'unit'          =>  static::$t->_($v['unit']),
                    'not_tax_price' =>  bcdiv($v['not_tax_price'], $this->digits, $this->digits_num),
                    'total_price'   =>  bcdiv($v['total_price'], 1000, 2),
                    'tax_total_price' =>  bcdiv($v['tax_total_price'], 1000, 2),
                    'percent' => intval($v['percent']),
                    'ticket_amount' =>  bcdiv($v['ticket_amount'], 1000, 2),
                    'ticket_amount_not_tax' => bcdiv($v['ticket_amount_not_tax'], 1000, 2),
                    'ticket_tax'    => bcdiv($v['ticket_tax'], 1000, 2),
                    'input_tax'     =>  bcdiv($v['input_tax'], 1000, 2),
                    'deduct_input_tax' =>  bcdiv($v['deduct_input_tax'], 1000, 2),
                    'tax_ratio'     =>  bcdiv($v['tax_ratio'], 1000, 2),
                    'ticket_date'   =>  $v['ticket_date'],
                    'ticket_number' =>  $v['ticket_number'],
                    'invoice_type'  => $v['invoice_type'],
                    'invoice_type_label' => $v['invoice_type_label'],
                    'wht_type'      =>  $v['wht_type'],
                    'wht_type_name' =>  $whtArr[$v['wht_type']] ?? '',
                    'wht_ratio'     =>  $v['wht_ratio'],
                    'wht_amount'    =>  bcdiv($v['wht_amount'], 1000, 2),
                    'real_amount'   =>  bcdiv($v['real_amount'], 1000, 2),
                    'ledger_account_name' => $v['ledger_account_name'],
                    'ledger_account_id' => $v['ledger_account_id'],
                    'cost_store_id' => $v['cost_store_id'],
                    'cost_store_name' => $v['cost_store_name'],
                    'cost_center_name' => $v['cost_center_name'],
                    'vat_invoice_number' => $v['vat_invoice_number'],
                    'product_desc'      => $v['product_desc'],
                    'deductible_vat_tax' => (string) $v['deductible_vat_tax'] ?? '',
                    'deductible_vat_amount' => bcdiv($v['deductible_vat_amount'], 1000, 2),
                    'vat7_rate' => $v['vat7_rate'],
                    'is_with_vat_invoice' => $v['is_with_vat_invoice'],
                    'pay_method' => $v['pay_method'],
                    'pay_num' => $v['pay_num']
                ];
            }
        }

        // 查询付款单的mach_code配置, 若无, 则异常处理, 生产的老数据都已跑完
        $cargo_owner = PurchasePaymentCargoModel::find([
            'conditions' => 'payment_id = :payment_id:',
            'bind'       => ['payment_id' => $data['id']],
        ])->toArray();
        $cargo_owner = array_column($cargo_owner, null, 'inbound_no');
        $inbound_no_item = !empty($data['inbound_no']) ? explode(',', $data['inbound_no']) : [];
        foreach ($inbound_no_item as $k => $v) {
            if ($v) {
                $tmp = $this->getInbound(['no' => $v, 'mach_code' => $cargo_owner[$v]['mach_code']]);
                $goodList = [];
                if (!empty($tmp) && !empty($tmp['data']) && !empty($tmp['data']['goodsList'])) {
                    $goodList = $tmp['data']['goodsList'];
                }

                $inbound_no[] = [
                    'no'        =>  $v,
                    'mach_code' => $cargo_owner[$v]['mach_code']??'',
                    'mach_name' => $cargo_owner[$v]['mach_name']??'',
                    //'goodsList' =>  $this->getInbound(['no'=>$v])['data']['goodsList'] ?: []
                    'goodsList'   => $goodList,
                ];
            }
        }

        $detailData = [
            'id'                =>  $data['id'],
            'ppno'              =>  $data['ppno'],
            'apply_date'        =>  $data['apply_date'],
            'create_name'       =>  $data['create_name'],
            'create_id'         =>  $data['create_id'],
            'create_department' =>  $data['create_department_name'],
            'create_department_id' => $data['create_department_id'],
            'store_id'          =>  $data['store_id'],
            'cost_store'          =>  $data['cost_store'],
            'department_id'     =>  $data['department_id'],
            'department_name'   =>  $data['department_name'],
            'method'            =>  $data['method'],
            'is_link_pa'        =>  $data['is_link_pa'],
            'po_id'             =>  $data['po_id'],
            'pono'              =>  $data['ponos'],
            'pa_id'             =>  $data['pa_id'],
            'pano'              =>  $data['pano'],
            'contract_no'       =>  $data['contract_no'],
            'inbound_no'        =>  $inbound_no ?? [],
            'payment_method'    =>  $data['payment_method'],
            'remark'            =>  $data['remark'],
            'attachment'        =>  $data['attachment'],
            'amount'            =>  bcdiv($data['amount'],1000,2),
            'auth_logs'         =>  $data['auth_logs'],
            'currency'          =>  $data['currency'],
            'ask_id'            =>  $data['ask_id'],

            //不读order的，有可能是填写
            'vendor'            =>  $data['vendor'],
            'vendor_contact'    =>  $data['vendor_contact'],
            'vendor_phone'      =>  $data['vendor_phone'],
            'vendor_email'      =>  $data['vendor_email'],
            'vendor_addr'       =>  $data['vendor_addr'],
            'vendor_tax_number' =>  $data['vendor_tax_number'],
            'payment_to'        =>  $data['payment_to']??'',
            'loan_time'         =>  $data['loan_time'],
            'bank_name'         =>  $data['bank_name'],
            'bank_account_name' =>  $data['bank_account_name'],
            'bank_no'           =>  $data['bank_no'],
            'swift_code'        =>  $data['swift_code'],

            'not_tax_amount'    =>  bcdiv($data['not_tax_amount'],1000,2),
            'vat7_amount'       =>  bcdiv($data['vat7_amount'],1000,2),
            'wht_amount'        =>  bcdiv($data['wht_amount'],1000,2),
            'real_amount'       =>  bcdiv($data['real_amount'],1000,2),
            'receipt_amount'    =>  bcdiv($data['receipt_amount'],1000,2),
            'cur_amount'        =>  bcdiv($data['cur_amount'],1000,2),
            'ticket_amount_tax' =>  bcdiv($data['ticket_amount_tax'],1000,2),

            'edit_logs'                  => $data['edit_logs'],
            'can_edit'                   => $data['can_edit'],
            'can_edit_fields'            => $data['can_edit_fields'],
            'is_can_edit_ledger_account' => $data['is_can_edit_ledger_account'],
            'status'                     => $data['status'],
            'pay_status'                 => $data['pay_status'],
            'is_after_ap_th'             => $data['is_after_ap_th'],
            'cost_company_id'            => $data['cost_company_id'],
            'cost_company_name'          => $data['cost_company_name'],
            'pay_where'                  => $data['pay_where'],
            'pay_where_text'             => static::$t->_(PayEnums::$pay_where_id_to_lang_key[$data['pay_where']]),
            'apply_email'                => $data['apply_email'],
            'acceptance_no'              => !empty($data['acceptance_no']) ? explode(',', $data['acceptance_no']) : [],
            'supplement_invoice'         => $data['supplement_invoice'],
            'required_supplement_file'   => $data['required_supplement_file'],
            'due_date'                   => $data['due_date'],
            'sap_btd_id'                 => $data['sap_btd_id'],
        ];

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $detailData['is_clearance']          = $data['is_clearance'] == Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_NULL ? '': $data['is_clearance'];
            $detailData['actual_clearance_date'] = !empty($data['actual_clearance_date'])
                ? date('Y-m-d', strtotime($data['actual_clearance_date']))
                : '';
            $detailData['expect_clearance_date'] = !empty($data['expect_clearance_date'])
                ? date('Y-m-d', strtotime($data['expect_clearance_date']))
                : '';
            $detailData['clearance_no']          = $data['clearance_no'];
            $detailData['purchase_type']         = $data['purchase_type'];
            $detailData['ownership']             = $data['ownership'];
        }

        if (isset($data['receipt'])) {
            $detailData = array_merge($detailData, ['receipt' => $data['receipt']]);
        } else {
            $detailData = array_merge($detailData, ['receipt_v1' => $data['receipt_v1']]);
        }

        return $detailData;
    }

    /**
     * 获取采购付款申请单列表
     *
     * @param $condition
     * @param $user
     * @param int $type$export
     * @param bool $export
     * @return array
     */
    public function getList($condition, $user = [], $type = 0, $export = false)
    {
        $condition['uid'] = $uid = $user['id'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num-1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 支付列表: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPurchasePaymentPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['p' => PurchasePayment::class]);
            if (!empty($condition['product_name']) || $export || !empty($condition['product_code'])) {
                $builder->leftjoin(PurchasePaymentReceipt::class, 'p.id = pr.ppid', 'pr');

                // 查询付款的科目
                if (!empty($condition['budget_ids'])) {
                    $builder->inWhere('pr.budget_id', $condition['budget_ids']);
                }
            }
            if (!empty($condition['is_clearance'])) {
                $builder->inWhere('p.is_clearance', $condition['is_clearance']);
            }

            $builder = $this->getListCondition($builder, $condition, $type, $user);

            // 取总条数
            $count = (int) $builder->columns('COUNT(DISTINCT(p.id)) AS total')->getQuery()->getSingleResult()->total;

            // 取列表
            $items = [];
            if ($count) {
                $column_str = 'p.id,p.ppno,p.apply_date,p.currency,p.create_department_name,p.create_name,p.create_id,
            p.amount,p.receipt_amount,p.status,p.pay_status,p.real_pay_at,p.operation_remark,p.is_after_ap_th,p.ticket_amount_tax,p.pay_at, p.is_pay_module,
            p.is_clearance,p.clearance_no,p.actual_clearance_date,p.expect_clearance_date
            ';

                //导出的字段
                $column_str .= ',p.method,p.ponos,p.loan_time,p.vendor,p.vendor_contact,p.vendor_phone,p.vendor_email,p.vendor_addr,p.vendor_tax_number,p.payment_to,p.not_tax_amount,p.vat7_amount,p.approve_at';

                //新增导出字段
                $column_str .= ',p.wht_amount,p.real_amount,p.remark,p.cur_amount,p.bank_name,p.bank_account_name,p.bank_no,p.swift_code';

                // 新增导出字段
                $column_str .= ',p.cost_department_name, p.is_link_pa,p.pano,p.contract_no,p.payment_method,p.pay_where,p.cost_company_name';

                if ($export) {
                    $column_str .= ',pr.ticket_amount,pr.ticket_tax,pr.ticket_amount_not_tax,pr.wht_type,pr.wht_ratio,pr.wht_amount as wht_amount_d,pr.budget_id,pr.product_name,pr.cost_store_id,pr.cost_store_name,pr.cost_center_name,pr.product_desc,pr.product_option_code,p.supplement_updated_at,p.supplement_invoice,pr.ticket_number,pr.vat_invoice_number,pr.ticket_date,pr.pay_method,pr.pay_num,pr.percent,p.due_date,pr.ledger_account_id';
                } else {
                    $builder->groupBy('p.id');
                    $builder->limit($page_size, $offset);
                }

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $column_str .= ',log.audit_at';
                }

                if ($type == self::LIST_TYPE_PAY) {
                    if (isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
                        $builder->orderBy('p.approve_at ASC');
                    } else {
                        $builder->orderBy('p.pay_at DESC');
                    }
                } else if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('p.id desc');
                }

                $builder->columns($column_str);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $uid);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            print $e->getTraceAsString(). PHP_EOL;die;

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('purchase-payment-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 支付待办统计
     * @param int $user_id
     *
     * @return mixed
     */
    public function getPayPendingCount(int $user_id)
    {
        $pay_pending_count = 0;

        if (empty($user_id)) {
            return $pay_pending_count;
        }

        // 是付款人: 终审通过且待支付的
        $pay_staff_ids = $this->getPurchasePaymentPayStaffIds();
        if (in_array($user_id, $pay_staff_ids)) {
            $pay_pending_count = PurchasePayment::count([
                'conditions' => 'status = :audit_status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
                'bind' => [
                    'audit_status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
                ],
            ]);
        }

        return $pay_pending_count;
    }

    /**
     * 获取列表条件
     * @param object $builder
     * @param array $condition
     * @param int $type
     * @param array $user
     * @return mixed|object
     * @throws BusinessException
     */
    private function getListCondition(object $builder, array $condition, $type = 0, $user = [])
    {
        $ppno = $condition['ppno'] ?? '';
        $cost_company_id = $condition['cost_company_id'] ?? [];

        // v11037: 支持审批状态多选
        $status = isset($condition['status']) && is_array($condition['status']) ? $condition['status'] : [];
        if (!empty($condition['status']) && !is_array($condition['status'])) {
            $status = [$condition['status']];
        }

        $payStatus = $condition['pay_status'] ?? 0;
        $apply_date_start = $condition['created_at_start'] ?? '';
        $apply_date_end = $condition['created_at_end'] ?? '';
        $product_name = $condition['product_name'] ?? '';
        $create_id = $condition['create_id'] ?? '';
        $pono = $condition['pono'] ?? '';
        $pano = $condition['pano'] ?? '';

        $approved_start_date    = $condition['approved_start_date'] ?? '';
        $approved_end_date      = $condition['approved_end_date'] ?? '';

        $sap_btd_id = $condition['sap_btd_id'] ?? '';//SAP发票号

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $product_code = $condition['product_code'] ?? '';//产品编码

        //审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PURCHASE_PAYMENT], $condition['uid'], 'p');

        } else if ($type == self::LIST_TYPE_PAY) {
            // 支付列表
            $builder->andWhere('p.status = :main_status: AND p.is_pay_module = :is_pay_module:', [
                'main_status' => Enums::WF_STATE_APPROVED,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
            ]);

            // 待处理
            if ($flag == GlobalEnums::AUDIT_TAB_PENDING) {
                $builder->andWhere('p.pay_status = :pay_status_pending:', ['pay_status_pending' => Enums::PAYMENT_PAY_STATUS_PENDING]);
            } else if ($flag == GlobalEnums::AUDIT_TAB_PROCESSED) {
                // 已处理
                $builder->inWhere('p.pay_status', [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_NOTPAY]);
            }

        } else if($type == self::LIST_TYPE_APPLY) {
            if ($condition['uid']) {
                $builder->andWhere('p.create_id = :uid:', ['uid' => $condition['uid']]);
            }

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
            $biz_table_info = ['table_alias' => 'p', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PURCHASE_PAYMENT], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 数据查询模块
            // OA供应商编号
            if (!empty($condition['vendor_id'])) {
                $builder->andWhere('p.vendor_id = :vendor_id:', ['vendor_id' => $condition['vendor_id']]);
            }

            // 供应商名称
            if (!empty($condition['vendor'])) {
                $builder->andWhere('p.vendor LIKE :vendor_name:', ['vendor_name' => "{$condition['vendor']}%"]);
            }

            //兼容：purchase_paymentAction task
            if(!empty($user)) {
                // 对接通用数据权限
                // 业务表参数
                $table_params = [
                    'table_alias_name' => 'p',
                    'create_id_field' => 'create_id',
                    'create_node_department_id_filed' => 'cost_department',
                ];
                $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_PAYMENT, $table_params);
            }
        }

        if (!empty($ppno)) {
            $builder->andWhere('p.ppno = :ppno:', ['ppno' => $ppno]);
        }

        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('p.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('p.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('p.create_id = :create_id: or p.create_name = :create_id:', ['create_id' => $create_id]);
        }

        //申请状态
        if (!empty($status)) {
            $builder->inWhere('p.status', array_values($status));
        }

        //支付状态
        if (!empty($payStatus)) {
            $builder->andWhere('p.pay_status = :pay_status:', ['pay_status' => $payStatus]);
        }

        //申请日期
        if (!empty($apply_date_start)) {
            $builder->andWhere('p.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('p.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        //产品名称
        if (!empty($product_name)) {
            $builder->andWhere('pr.product_name = :product_name:', ['product_name' => $product_name]);
        }

        if (!empty($pono)) {
            $builder->andWhere('p.ponos = :pono:', ['pono' => $pono]);
        }

        if (!empty($pano)) {
            $builder->andWhere('p.pano = :pano:', ['pano' => $pano]);
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
         * 发现历史bug，采购订单，按照产品编码搜索不起作用，在此增加产品编码
         */
        if (!empty($product_code)) {
            $builder->andWhere('pr.product_option_code = :product_option_code:', ['product_option_code' => $product_code]);
        }

        // 付款日期: 银行流水日期
        if (!empty($condition['pay_date_start'])) {
            $builder->andWhere('p.real_pay_at >= :pay_date_start:', ['pay_date_start' => $condition['pay_date_start'] . ' 0:0:0']);
        }

        if (!empty($condition['pay_date_end'])) {
            $builder->andWhere('p.real_pay_at <= :pay_date_end:', ['pay_date_end' => $condition['pay_date_end'] . ' 23:59:59']);
        }

        //起始日期
        if (!empty($approved_start_date)) {
            $approved_start_date .= ' 00:00:00';
            $builder->andWhere('p.approve_at >= :approve_start_date:', ['approve_start_date' => $approved_start_date]);
        }

        //截止日期
        if (!empty($approved_end_date)) {
            $approved_end_date .= ' 23:59:59';
            $builder->andWhere('p.approve_at <= :approve_end_date:', ['approve_end_date' => $approved_end_date]);
        }

        //SAP发票号
        if (!empty($sap_btd_id)) {
            $builder->andWhere('p.sap_btd_id = :sap_btd_id:', ['sap_btd_id' => $sap_btd_id]);
        }

        return $builder;
    }

    /**
     * 处理列表数据
     *
     * @param array $items
     * @param int $uid
     * @return array
     */
    private function handleListItems($items = [], $uid = 0) {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $budgetIds = array_values(array_filter(array_column($items, 'budget_id')));
        if ($budgetIds) {
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
        }

        // 是否是付款人
        $pay_staff_id = $this->getPurchasePaymentPayStaffIds();
        $is_payer = !empty($uid) && in_array($uid, $pay_staff_id);

        foreach ($items as &$item) {
            $status = Enums::$loan_status[$item['status']] ?? '';
            $pay_status = Enums::$loan_pay_status[$item['pay_status']] ?? '';
            $item['amount'] = bcdiv($item['amount'], 1000,2);
            $item['receipt_amount'] = bcdiv($item['receipt_amount'], 1000,2);
            $item['status_text'] = static::$t->_($status);
            $payment_currency = GlobalEnums::$currency_item[$item['currency']] ?? '';
            $item['currency_text'] = static::$t->_($payment_currency);
            $item['pay_status_text'] = static::$t->_($pay_status);
            $item['pay_note'] = $item['pay_status'] != Enums::LOAN_PAY_STATUS_PENDING ? $item['operation_remark'] : '';


            //不含税金额总计
            $item['not_tax_amount'] = bcdiv($item['not_tax_amount'],1000,2);

            //含税金额总计
            $item['vat7_amount'] = bcdiv($item['vat7_amount'],1000,2);

            //是否能够下载
            $item['is_can_download'] = $this->isCanDownload($item);

            //WHT金额总计
            $item['wht_amount'] = bcdiv($item['wht_amount'],1000,2);
            //总实付金额总计
            $item['real_amount'] = bcdiv($item['real_amount'],1000,2);
            //本次付款金额
            $item['cur_amount'] = bcdiv($item['cur_amount'],1000,2);
            $item['ticket_amount_tax'] = bcdiv($item['ticket_amount_tax'],1000,2);

            // 预算科目名称
            $item['budget_text'] = '';
            if (isset($budgets) && isset($budgets[$item['budget_id']])) {
                $item['budget_text'] = $budgets[$item['budget_id']]['name_' . strtolower(substr(static::$language, -2))];
            }

            // 费用总部/网点
            if (empty($item['cost_store_id'])) {
                $item['cost_store'] = $item['cost_store'] ?? '';
                $item['cost_store_id'] = $item['cost_store'];
                $item['cost_store_name'] = $item['cost_store'] == 1 ? self::$t['payment_cost_store_type_1'] : $item['cost_store_name'] ?? '';
            }

            // 审批已处理列表: 已终审通过 且 已支付/未支付, 且 未走支付模块的 且 是支付人, 则已处理时间 取支付时间
            if ($is_payer && isset($item['audit_at']) && $item['status'] == Enums::WF_STATE_APPROVED && !$item['is_pay_module'] && in_array($item['pay_status'], [Enums::LOAN_PAY_STATUS_PAY, Enums::LOAN_PAY_STATUS_NOTPAY])) {
                $item['audit_at'] = $item['pay_at'];
            }
        }
        return $items;
    }

    /**
     * 获取审批日志
     * @param $req
     * @param $data
     * @param boolean $download
     * @return array
     * @throws BusinessException
     */
    private function getAuditLogs($req,$data,$download=FALSE)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if($download){
            $temp = [];
            foreach ($auth_logs as $k=>$v){
                //如果申请的就跳过
                if($v['action']==0){
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        $purchase_payment_pay_staff_id = $this->getPurchasePaymentPayStaffIds();
        //查询支付模块的审批流
        if ($data['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT, $data['ppno']);
            if (!empty($payment_data)) {
                $pay_flow_service = new Pay_PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                return $auth_logs;
            }
        }
        $us = new UserService();
        if ($data['status'] == Enums::CONTRACT_STATUS_APPROVAL && $data['pay_status'] == Enums::LOAN_PAY_STATUS_PENDING) {
            $payPendingLogs = [
                'staff_id' => '',
                'staff_name' => '',
                'action_name' => self::$t->_(Enums::$loan_pay_status[$data['pay_status']]),
                'audit_at' => $data['approve_at'],
                'audit_at_datetime' => $data['approve_at'],
                'action' => 5,
                "info" => ''
            ];
            foreach ($purchase_payment_pay_staff_id as $staff_id) {
                $current = $us->getUserById($staff_id);
                if (!empty($current) && !is_string($current)) {
                    //待支付
                    $payPendingLogs['list'][] = [
                        'staff_id' => $staff_id,
                        'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name??''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title' => $current->getJobTitle()->name ??'',
                    ];

                }
            }
            array_unshift($auth_logs,$payPendingLogs);
        }
        if ($data['status'] == Enums::CONTRACT_STATUS_APPROVAL && ($data['pay_status'] == Enums::LOAN_PAY_STATUS_PAY || $data['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY)) {
            //支付操作后，不要待支付
            $current = $us->getUserById($data['pay_id']);
            //放入付款
            if ($current && !is_string($current)) {
                $payLogs = [
                    'staff_id' => $data['pay_id'],
                    'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name??''),
                    'staff_department' => $current->getDepartment()->name ?? '',
                    'job_title' => $current->getJobTitle()->name ??'',
                    'action_name' => self::$t->_(Enums::$loan_pay_status[$data['pay_status']]),
                    'audit_at' => $data['pay_at'],
                    'audit_at_datetime' => $data['pay_at'],
                    'action' => 5,
                    "info" => ''
                ];
                array_unshift($auth_logs, $payLogs);
            }
        }

        return $auth_logs;
    }

    /**
     * 采购付款申请单详情下载
     *
     * @param int $id
     * @param int $uid
     * @param int $type
     * @return array
     */
    public function download(int $id, $uid = 0, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $download_url = '';

        $lang = static::$language;
        try {
            $data = $this->getDetail($id, $uid, true, $type);
            if ($data['code'] != ErrCode::$SUCCESS) {
                return $data;
            }

            $data = $data['data'];
            if (!$this->isCanDownload($data)) {
                throw new ValidationException("can't download", ErrCode::$VALIDATE_ERROR);
            }

            $data['payment_method'] = static::$t->_('global.payment.method.' . $data['payment_method']);
            $data['method']         = static::$t->_('purchase_payment_method_' . $data['method']);
            $data['currency_text']  = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
            $country_code = get_country_code();
            if (!empty($data['cost_company_name'])) {
                $departId = $data['cost_company_id'];
            } else {
                $companyModel = SysDepartmentModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind'       => ['id' => $data['department_id']]
                ]);
                if (!empty($companyModel) && !empty($companyModel->company_name)) {
                    $departId = $companyModel->company_id;
                } else {
                    $companyFindName = GlobalEnums::PH_COUNTRY_CODE == $country_code ? 'Flash Express_PH' : 'Flash Express';
                    $companyModel    = SysDepartmentModel::findFirst([
                        'conditions' => ' name = :name: ',
                        'bind'       => ['name' => $companyFindName]
                    ]);
                    $departId        = !empty($companyModel) ? $companyModel->company_id : 0;
                }
            }

            $departModel = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => ['id' => $departId]
            ]);
            // 公司名称
            if (GlobalEnums::VN_COUNTRY_CODE == $country_code) {
                $data['company_name'] = !empty($departModel) ? $departModel->name : 'Flash Express';
            } else {
                $data['company_name'] = !empty($departModel) ? $departModel->name : '';
            }
            if (!empty($data['cost_company_name'])) {
                $data['company_name'] = $data['cost_company_name'];
            }

            // 公司地址
            $data['sap_company_address'] = !empty($departModel) ? $departModel->sap_company_address : '';

            // tax id
            $data['sap_tax_id'] = !empty($departModel) ? $departModel->sap_tax_id : '';

            // 两个版本数据处理
            if (!empty($data['receipt'])) {
                // 获取PO单产品分类信息
                $_category_a_ids = array_values(array_filter(array_unique(array_column($data['receipt'],  'category_a'))));
                $_category_b_ids = array_values(array_filter(array_unique(array_column($data['receipt'],  'category_b'))));
                $_category_ids = array_values(array_filter(array_unique(array_merge($_category_a_ids ?? [], $_category_b_ids ?? []))));

                $_category_item = [];
                if (!empty($_category_ids)) {
                    $_category_item = PurchaseProductCategory::find([
                        'conditions'    =>  'id IN ({ids:array})',
                        'columns'       =>  'id, name_key',
                        'bind'          =>  ['ids' => $_category_ids]
                    ])->toArray();

                    $_category_item = array_column($_category_item, 'name_key', 'id');
                }

                foreach ($data['receipt'] as &$v) {
                    $_category_a_name_key = $_category_item[$v['category_a'] ?? ''] ?? '';
                    $v['category_a_name'] = !empty($_category_a_name_key) ? static::$t->_($_category_a_name_key) : '';

                    $_category_b_name_key = $_category_item[$v['category_b'] ?? ''] ?? '';
                    $v['category_b_name'] = !empty($_category_b_name_key) ? static::$t->_($_category_b_name_key) : '';
                }
            } else if (!empty($data['receipt_v1'])) {
                // 获取PO单产品分类信息
                $_category_a_ids = array_values(array_filter(array_unique(array_column($data['receipt_v1'],  'category_a'))));
                $_category_b_ids = array_values(array_filter(array_unique(array_column($data['receipt_v1'],  'category_b'))));
                $_category_ids = array_values(array_filter(array_unique(array_merge($_category_a_ids ?? [], $_category_b_ids ?? []))));

                $_category_item = [];
                if (!empty($_category_ids)) {
                    $_category_item = PurchaseProductCategory::find([
                        'conditions'    =>  'id IN ({ids:array})',
                        'columns'       =>  'id, name_key',
                        'bind'          =>  ['ids' => $_category_ids]
                    ])->toArray();

                    $_category_item = array_column($_category_item, 'name_key', 'id');
                }

                foreach ($data['receipt_v1'] as &$v) {
                    $_category_a_name_key = $_category_item[$v['category_a'] ?? ''] ?? '';
                    $v['category_a_name'] = !empty($_category_a_name_key) ? static::$t->_($_category_a_name_key) : '';

                    $_category_b_name_key = $_category_item[$v['category_b'] ?? ''] ?? '';
                    $v['category_b_name'] = !empty($_category_b_name_key) ? static::$t->_($_category_b_name_key) : '';
                }
            }

            $file_path = sys_get_temp_dir() . '/';
            $file_name = 'payment_' . md5($id) . '_' . $lang . '.pdf';
            $file_full_path = $file_path . $file_name;

            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            $view->setVars($data);
            $view->start();
            $view->disableLevel([
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]);

            if (isset($data['receipt_v1']) && $data['receipt_v1']) {
                $view->render('purchase', 'payment_v1_' . $lang);
            } else {
                $view->render('purchase', 'payment_' . $lang);
            }

            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'=>'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader('');
            $mpdf->SetHTMLFooter('');
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_full_path ,'f');

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_full_path, $file_full_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_full_path);
            $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';

        }catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }catch (\Mpdf\MpdfException $e){
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }catch(Exception $e){
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('Purchase-payment-detail-download-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => ['url' => $download_url]
        ];
    }

    /**
     * 导出列表
     *
     * @param array $condition
     * @param $user
     * @param int $type
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function export($condition = [], $user = [], $type = 0)
    {
        $uid = $user['id'] ?? 0;
        $result = [
            'code' => ErrCode::$VALIDATE_ERROR,
            'message' => 'error',
            'data' => null
        ];

        //导出锁
        if($this->checkLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid)){
            $result['code'] = ErrCode::$BUSINESS_ERROR;
            $result['message'] = 'exporting now Please wait!';
            return $result;
        }else{
            $this->setLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid,1,10);
        }

        ini_set('memory_limit', '2048M');

        $data = $this->getList($condition, $user, $type, true);
        if($data['code']!=ErrCode::$SUCCESS){
            $result['code'] = $data['code'];
            $result['message'] = $data['message'];
            return $result;
        }

        $whtArr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        $data = $data['data']['items'];


        // 核算科目编码与名称
        $ledger_account_ids = array_values(array_unique(array_filter(array_column($data, 'ledger_account_id'))));
        $ledger_account_item = [];
        if (!empty($ledger_account_ids)) {
            $ledger_account_item = LedgerAccount::find([
                'columns'=>'id, account, name_en AS name',
                'conditions' => 'id IN ({ids:array})',
                'bind' => ['ids' => $ledger_account_ids]
            ])->toArray();
            $ledger_account_item = array_column($ledger_account_item, null, 'id');
        }

        $new_data = [];
        $i = 0;
        $supplement_status = PurchaseEnums::$supplement_invoice_status;
        $supplement_attach_status = PurchaseEnums::$supplement_attach_status;
        //当一个PAR有多行时，只在第一行展示该字段的值（不含税金额总计、VAT总计、含税金额总计、发票金额总计、发票税额总计、WHT金额总计、总实付金额总计、本次PAR总付款金额）
        $new_data_ppno = [];
        foreach ($data as $key => $val) {
            ++$i;
            $remark = $val['remark'];
            $remark  = mb_strlen($remark) > 200 ? mb_substr($remark, 0, 200) : $remark;
            $supplement_attach = PurchaseEnums::IS_SUPPLEMENT_ATTACH_NO_NEED;
            if ($val['supplement_invoice'] == PurchaseEnums::IS_SUPPLEMENT_INVOICE_NO) {
                $supplement_attach = PurchaseEnums::IS_SUPPLEMENT_ATTACH_NO_NEED;
            } else if ($val['supplement_invoice'] == PurchaseEnums::IS_SUPPLEMENT_INVOICE_YES && !empty($val['supplement_updated_at'])) {
                $supplement_attach = PurchaseEnums::IS_SUPPLEMENT_ATTACH_YES;
            } else if ($val['supplement_invoice'] == PurchaseEnums::IS_SUPPLEMENT_INVOICE_YES && empty($val['supplement_updated_at'])) {
                $supplement_attach = PurchaseEnums::IS_SUPPLEMENT_ATTACH_NO;
            }

            $ledger_account_info = $ledger_account_item[$val['ledger_account_id']] ?? [];
            $ticket_amount = bcdiv(  $val['ticket_amount'],1000,2);//发票金额含税
            $wht_amount_d = bcdiv($val['wht_amount_d'], 1000, 2);//wht税额
            $is_unfirst_line = in_array($val['ppno'], $new_data_ppno) ? true : false;

            $new_data[$key] = [
                $val['ppno'],
                $val['create_name'],
                $val['create_id'],
                $val['apply_date'],
                $val['status_text'],
                $val['approve_at'] ??'-',
                $val['pay_status_text'],
                $val['real_pay_at'],

                static::$t['payment_is_link_pa_'.$val['is_link_pa']] ?? '',// 是否是框架合同

                $val['pano'],// 相关采购申请单

                $val['ponos'],
                $val['contract_no'],// 相关合同
                static::$t['global.payment.method.'.$val['payment_method']] ?? '',// 付款方式
                static::$t['pay_where.'.$val['pay_where']] ?? '',// 境内境外支付
                static::$t['purchase_payment_method_'.$val['method']] ?? '',// 采购方式


                $val['cost_department_name'],// 费用所属部门
                $val['cost_company_name'],// 费用所属公司
                $val['currency_text'],
                $val['loan_time'],
                $val['vendor'],
                $val['vendor_email'],
                $val['vendor_addr'],//供应商地址
                $val['vendor_tax_number'],//供应商税务号
                $val['bank_name'],
                $val['bank_account_name'],
                $val['bank_no'],
                $val['swift_code'],
                $val['payment_to'],
                $remark, //备注
                $val['due_date'],
                static::$t->_(Enums\OrdinaryPaymentEnums::$clearance_state[$val['is_clearance']]), //是否涉及清关
                $val['clearance_no'],           //清关编号
                !empty($val['expect_clearance_date'])
                    ? date('Y-m-d', strtotime($val['expect_clearance_date']))
                    : '',  //预计清关日期
                !empty($val['actual_clearance_date'])
                    ? date('Y-m-d', strtotime($val['actual_clearance_date']))
                    : '',  //清关日期

                static::$t[$supplement_status[$val['supplement_invoice']]] ?? '',
                static::$t[$supplement_attach_status[$supplement_attach]] ?? '',
                $val['budget_text'],
                $val['product_name'],
                $val['cost_store_name'],
                $val['cost_center_name'],
                $val['product_option_code'],
                $val['product_desc'],
                $val['ticket_number'],
                $val['vat_invoice_number'],
                $val['ticket_date'],

                $ticket_amount,//发票金额含税
                bcdiv(  $val['ticket_amount_not_tax'],1000,2),//发票金额不含税
                bcdiv( $val['ticket_tax'],1000,2),//发票税额
                $whtArr[$val['wht_type']] ?? '',//wht类别
                is_numeric($val['wht_ratio']) ? $val['wht_ratio'] . '%' : '',//wht税率
                bcdiv($val['wht_amount_d'], 1000, 2),//wht税额
                $is_unfirst_line ? 0 : $val['not_tax_amount'],//不含税金额总计
                $is_unfirst_line ? 0 : $val['vat7_amount'],//VAT总计
                $is_unfirst_line ? 0 : $val['amount'],//含税金额总计
                $is_unfirst_line ? 0 : $val['receipt_amount'],//发票金额总计
                $is_unfirst_line ? 0 : $val['ticket_amount_tax'],//发票税额总计
                $is_unfirst_line ? 0 : $val['wht_amount'],//WHT金额总计
                $is_unfirst_line ? 0 : $val['real_amount'],//总实付金额总计
                $is_unfirst_line ? 0 : $val['cur_amount'],//本次PAR总付款金额
                bcsub($ticket_amount, $wht_amount_d, 2),//行支付金额 = 行的发票金额（含税）-行上的wht金额
                isset(PurchaseEnums::$payment_pay_method_list[$val['pay_method']]) ? static::$t[PurchaseEnums::$payment_pay_method_list[$val['pay_method']]] : '',//付款形式
                is_numeric($val['percent']) ? $val['percent'] . '%' : '',//付款比例
                $val['pay_num'],//付款数量

                $ledger_account_info['account'] ?? '', // 会计科目编码
                $ledger_account_info['name'] ?? '', // 会计科目名称
            ];

            if (!in_array($val['ppno'], $new_data_ppno)) {
                $new_data_ppno[] = $val['ppno'];
            }
        }
        $file_name = "purchase_payment_".date("YmdHis");
        $header = [
            static::$t->_('global.number'),//编号
            static::$t->_('global.applicant.name'),   //申请人
            static::$t->_('global.applicant.id'),//申请人工号
            static::$t->_('global.apply.date'),//申请日期
            static::$t->_('global.apply.status.text'), //处理状态
            static::$t->_('purchase_apply_approve_date'),   //审核通过时间
            static::$t->_('global_pay_status'), //支付状态
            static::$t->_('purchase_payment_field_real_pay_at'),//银行流水日期
            static::$t->_('global_whether_frame_contract'), //是否框架合同 √
            static::$t->_('purchase_order_field_link_apply'), //相关采购申请单 √

            static::$t->_('purchase_apply_field_link_order_code'),//相关采购订单编码
            static::$t->_('payment_store_renting_contract_no'),//相关合同 √
            static::$t->_('payment_store_renting_pay_method'),//付款方式 √
            static::$t->_('purchase_apply_field_product_overseas_payment'),//境内/境外支付 √
            static::$t->_('purchasing_method'),//采购方式 √

            static::$t->_('re_filed_apply_cost_department'),//费用所属部门
            static::$t->_('re_field_cost_company_name'),//费用所属公司 √

            static::$t->_('purchase_order_field_currency'),//币种
            static::$t->_('purchase_order_field_loan_time'),//信贷期限
            static::$t->_('purchase_order_field_vendor'),//供应商名称
            static::$t->_('purchase_order_field_vendor_email'),//供应商邮箱
            static::$t->_('purchase_order_field_vendor_addr'),//供应商地址
            static::$t->_('purchase_order_field_vendor_tax_number'),//供应商税务号
            static::$t->_('purchase_order_field_bank_name'),   //供应商银行名称
            static::$t->_('purchase_order_field_bank_account_name'),   //供应商银行账户名称
            static::$t->_('purchase_order_field_bank_no'),   //供应商银行账号
            static::$t->_('purchase_order_field_swift_code'),   //swift code
            static::$t->_('purchase_order_field_payment_to'),//支付给
            static::$t->_('global.remark'),//备注
            static::$t->_('purchase_order_field_due_date'),//应付日期
            static::$t->_('ordinary_payment_is_clearance'), // 是否涉及清关
            static::$t->_('ordinary_payment_clearance_no'), // 清关编号
            static::$t->_('ordinary_payment_expect_clearance_date'), // 预计清关日期
            static::$t->_('ordinary_payment_clearance_date'), // 清关日期
            static::$t->_('ordinary_payment_is_supplement_invoice'), // 是否需要补充发票
            static::$t->_('ordinary_payment_supplement_invoice_status'), // 补充附件状态


            static::$t->_('acceptance_budget'),//预算分类
            static::$t->_('storage_product_name'),//产品名称
            static::$t->_('re_filed_apply_cost_store'),//费用所属网点/总部
            static::$t->_('payment_store_renting_cost_center'),//费用所属中心
            static::$t->_('asset_barcode'),//barcode
            static::$t->_('purchase_payment_invoice_head_26'),//产品描述
            static::$t->_('re_field_invoice_no'),//发票编号
            static::$t->_('ordinary_payment_invoice_no'),//增值税发票
            static::$t->_('re_filed_ticket_date'),//发票日期

            static::$t->_('purchase_payment_invoice_head_30'),   //发票金额含税
            static::$t->_('purchase_payment_invoice_head_22'),   //发票金额不含税
            static::$t->_('purchase_payment_invoice_head_31'),   //发票税额
            static::$t->_('purchase_payment_invoice_head_18'),   //wht类别
            static::$t->_('purchase_payment_invoice_head_19'),   //wht税率
            static::$t->_('purchase_payment_invoice_head_20'),   //wht金额

            static::$t->_('purchase_order_field_subtotal_amount'),//不含税金额
            static::$t->_('purchase_order_field_taxation'),//VAT7总计
            static::$t->_('purchase_order_field_amount'),//含税金额总计
            static::$t->_('purchase_payment_field_receipt_amount'),//发票金额总计
            static::$t->_('purchase_payment_field_ticket_amount_tax'),//发票税额总计
            static::$t->_('purchase.allwht'),//WHT金额总计
            static::$t->_('purchase.alltotalprice'),//总实付金额总计
            static::$t->_('purchase_payment_field_cur_amount'),//本次PAR总付款金额
            static::$t->_('purchase_payment_field_line_amount'),//行支付金额
            static::$t->_('purchase_payment_field_pay_method'),//付款形式
            static::$t->_('purchase_payment_field_percent'),//付款比例
            static::$t->_('purchase_payment_field_pay_num'),//付款数量
            static::$t->_('purchase_payment_field_ledger_code'), // 会计科目编码
            static::$t->_('purchase_payment_field_ledger_name'), // 会计科目名称
        ];

        $excel_data = $this->exportExcel($header, $new_data, $file_name);
        if (!empty($excel_data['data'])) {
            $result['code'] = ErrCode::$SUCCESS;
            $result['message'] = 'success';
            $result['data'] = $excel_data['data'];
        }

        $this->unLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid);

        return $result;
    }

    /**
     * 导出列表
     *
     * @param array $condition
     * @param $user
     * @param int $type
     * @return array
     * @throws BusinessException
     */
    public function export_invoice($condition = [], $user = [], $type = 0)
    {
        $uid = $user['id'] ?? 0;
        $result = [
            'code' => ErrCode::$VALIDATE_ERROR,
            'message' => 'error',
            'data' => ''
        ];

        //导出锁
        if($this->checkLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid)){
            $result['code'] = ErrCode::$BUSINESS_ERROR;
            $result['message'] = 'exporting now Please wait!';
            return $result;
        }else{
            $this->setLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid,1,10);
        }

        ini_set('memory_limit', '2048M');

        $data = $this->getList($condition, $user, $type, true);
        if($data['code']!=ErrCode::$SUCCESS){
            $result['code'] = $data['code'];
            $result['message'] = $data['message'];
            return $result;
        }

        $data = $data['data']['items'];
        $paymentIds = array_column($data,'id');
        $pp_nos = array_column($data,'ppno','id');
        $receipt = PurchasePaymentReceipt::find([
            'conditions' => 'ppid IN({paymentIds:array})',
            'bind' => ['paymentIds'=>$paymentIds]
        ])->toArray();
        $receipt = $this->handleReceiptData($receipt,$data);
        if ($receipt['code'] != ErrCode::$SUCCESS) {
            $result['code'] = $receipt['code'];
            $result['message'] = $receipt['message'];
            return $result;
        }

        $new_data = [];
        $i = 0;
        foreach ($receipt['data'] as $key => $val) {
            ++$i;
            $new_data[$key] = [
                $val['pono'], //PO号
                $pp_nos[$val['ppid']],//采购付款申请单号
                $val['vendor'],//供应商名称
                $val['budget_id'] ? $val['budget_text'] : $val['category_a_name'], //产品分类（一级分类）
                $val['budget_id'] ? $val['product_name'] : $val['category_b_name'],//申请事项（二级分类）
                $val['product_name'],//产品名称
                $val['product_desc'],//产品描述
                $val['total'],//数量
                $val['unit'],//单位
                $val['not_tax_price'],//不含税总价
                $val['total_price'],//总价
                $val['tax_ratio'],//税率
                $val['tax_total_price'],//含税总价
                $val['wht_type_text'],//wht类别
                is_numeric($val['wht_ratio']) ? $val['wht_ratio'] . '%' : '',//wht税率
                $val['wht_amount'],//wht金额
                $val['real_amount'],//总实付金额


                $val['ticket_number'],//发票号
                $val['vat_invoice_number'],//增值税发票号

                $val['ticket_date'],//开票日期
                $val['ticket_amount'],//开票金额
                $val['ticket_amount_not_tax'],//发票金额不含税
                $val['percent'],//付款比例
                $val['deductible_vat_tax'],//可抵扣VAT税率
                $val['deductible_vat_amount'], //可抵扣税金额
                $val['input_tax'],//进项税调整
                $val['deduct_input_tax'],//可抵扣进项税
            ];
        }

        $file_name = "purchase_payment_".date("YmdHis");
        $header = [
            static::$t->_('purchase_payment_invoice_head_1'),   //PO号
            static::$t->_('purchase_payment_invoice_head_17'),   //采购付款申请单号

            static::$t->_('purchase_payment_invoice_head_2'),   //供应商名称
            static::$t->_('purchase_payment_invoice_head_3'),   //产品分类（一级分类）
            static::$t->_('purchase_payment_invoice_head_4'),   //申请事项（二级分类）
            static::$t->_('purchase_payment_invoice_head_5'),   //产品名称
            static::$t->_('purchase_payment_invoice_head_26'),   //产品描述

            static::$t->_('purchase_payment_invoice_head_6'),   //数量
            static::$t->_('purchase_payment_invoice_head_7'),   //单位
            static::$t->_('purchase_payment_invoice_head_8'),   //不含税总价
            static::$t->_('purchase_payment_invoice_head_9'),   //总价
            static::$t->_('purchase_payment_invoice_head_10'),  //税率
            static::$t->_('purchase_payment_invoice_head_11'),  //含税总价
            static::$t->_('purchase_payment_invoice_head_18'),   //wht类别
            static::$t->_('purchase_payment_invoice_head_19'),   //wht税率
            static::$t->_('purchase_payment_invoice_head_20'),   //wht金额
            static::$t->_('purchase_payment_invoice_head_21'),   //总实付金额

            static::$t->_('purchase_payment_invoice_head_12'),  //发票号
            static::$t->_('purchase_payment_invoice_head_13'),  //开票日期
            static::$t->_('purchase_payment_invoice_head_14'),  //开票金额
            static::$t->_('purchase_payment_invoice_head_22'),   //发票金额不含税
            static::$t->_('purchase_payment_invoice_head_23'),   //付款比例
            static::$t->_('purchase_payment_invoice_head_24'),   //可抵扣VAT税率
            static::$t->_('purchase_payment_invoice_head_25'),   //可抵扣税金额


            static::$t->_('purchase_payment_invoice_head_15'),  //进项税调整
            static::$t->_('purchase_payment_invoice_head_16'),  //可抵扣进项税
        ];

        $excel_data = $this->exportExcel($header, $new_data, $file_name);
        if (!empty($excel_data['data'])) {
            $result['code'] = ErrCode::$SUCCESS;
            $result['message'] = 'success';
            $result['data'] = $excel_data['data'];
        }

        $this->unLock(RedisKey::PURCHASE_EXPORT_LOCK.$uid);

        return $result;
    }

    /**
     * 采购付款申请单-支付
     *
     * @param int $id
     * @param array $data
     * @param array $user
     * @param int $is_from 1本模块，2支付模块
     * @return array
     */
    public function pay(int $id, array $data, array $user, $is_from = PayEnums::IS_FROM_SELF)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 存在其他模块回调的情况, 需验证
            if (!in_array($data['pass_or_not'] ?? 0, [PurchaseEnums::PAYMENT_PAY_OPTION_PAID, PurchaseEnums::PAYMENT_PAY_OPTION_UNPAID])) {
                throw new BusinessException('采购付款申请单-支付失败-支付选项参数[pass_or_not]异常: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            if ($data['pass_or_not'] == PurchaseEnums::PAYMENT_PAY_OPTION_UNPAID && empty($data['note'])) {
                throw new ValidationException(static::$t->_('pay_remark_must_input_hint'), ErrCode::$VALIDATE_ERROR);
            }

            $purchase_payment_pay_staff_id = $this->getPurchasePaymentPayStaffIds();
            if (!in_array($user['id'], $purchase_payment_pay_staff_id) && $is_from == PayEnums::IS_FROM_SELF) {
                throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
            }

            $payment = PurchasePayment::getFirst([
                'conditions' => 'id = :id: AND status = :status:',
                'bind' => ['id' => $id, 'status' => Enums::WF_STATE_APPROVED],
                'for_updated' => true
            ]);
            if (empty($payment)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待支付状态, 不可重复支付
            if ($payment->pay_status != Enums::PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('repeated_payment_error_hint', ['serial_no' => $payment->ppno]), ErrCode::$VALIDATE_ERROR);
            }

            // 单据进入了支付管理模块, 但参数标识有误
            if ($is_from == PayEnums::IS_FROM_SELF && $payment->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info("根据采购付款申请单ppid = {$id} 获取到采购付款申请单订单信息：" . json_encode($payment->toArray(), JSON_UNESCAPED_UNICODE));

            // 将用户的支付选项转换为表的支付状态
            $pay_status = $data['pass_or_not'] == PurchaseEnums::PAYMENT_PAY_OPTION_PAID ? Enums::LOAN_PAY_STATUS_PAY : Enums::LOAN_PAY_STATUS_NOTPAY;
            $updateData = [
                'pay_status' => $pay_status,
                'pay_id' => $user['id'],
                'operation_remark' => $data['note'] ?? '',
                'real_pay_at' => $data['real_pay_at'] ?? null,
                'pay_at' => date('Y-m-d H:i:s')
            ];
            if (!$data['real_pay_at']) {
                unset($updateData['real_pay_at']);
            }

            $bool = $payment->i_update($updateData);
            if ($bool === false) {
                throw new BusinessException("采购付款申请单-支付失败-更新付款申请单[ppno={$payment->ppno}]失败, 原因可能是: " . get_data_object_error_msg($payment) . '; 数据: ' . json_encode($updateData, JSON_UNESCAPED_UNICODE) , ErrCode::$BUSINESS_ERROR);
            }

            // 是否开启了预算
            $budget_status = (new EnumsService())->getBudgetStatus();
            if ($pay_status == Enums::LOAN_PAY_STATUS_PAY && $budget_status) {
                // 是否需要释放预算
                $is_free_flag = false;

                // 待重算预算的 预算科目 => 预算金额
                $paymentAmount = [];

                $enums_service = EnumsService::getInstance();

                // 关联的pa(申请单)
                if ($payment->is_link_pa) {
                    $apply = PurchaseApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $payment->pa_id]
                    ]);

                    if (empty($apply)) {
                        throw new ValidationException(static::$t->_('purchase_payment_linked_pa_data_null', ['pa_id' => $payment->pa_id]), ErrCode::$VALIDATE_ERROR);
                    }

                    // 申请单是新数据
                    if (!$this->isOldData($apply->created_at)) {
                        $receipts = $payment->getReceipts();

                        // 付款单关联申请单时, 是关联申请单的所有行
                        foreach ($receipts as $receipt) {
                            $product = PurchaseApplyProduct::findFirst([
                                'conditions'=> 'id = :id:',
                                'bind' => ['id' => $receipt->pap_id]
                            ]);
                            if (empty($product)) {
                                throw new ValidationException(static::$t->_('purchase_payment_linked_pa_data_null', ['pa_id' => $payment->pa_id]), ErrCode::$VALIDATE_ERROR);
                            }

                            // 初始化预算科目 与 对应的金额
                            $_budget_object_amount = $paymentAmount[$receipt->level_code] ?? 0;

                            // 如果释放过，直接用释放金额
                            if ($product->is_free) {
                                $paymentAmount[$product->level_code] = bcadd($_budget_object_amount, $product->free_amount);
                                continue;
                            }

                            // 付款单在关联申请单时, 币种不可修改, 继承申请单的币种, 币种一样, 金额可直接对比
                            if ($product->all_total != $receipt->ticket_amount) {
                                $is_free_flag = true;
                            }

                            // 根据汇率转换为国家默认币种的金额
                            $receipt_ticket_amount = $enums_service->amountExchangeRateCalculation($receipt->ticket_amount, $payment->exchange_rate, 0);

                            $product->is_free = 1;
                            $product->free_amount = $receipt_ticket_amount;
                            $paymentAmount[$product->level_code] = bcadd($_budget_object_amount, $receipt_ticket_amount, 0);
                            if ($product->save() === false) {
                                throw new BusinessException('采购付款单支付-付款单关联申请单-申请单的行更新失败, 业务数据 = ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($product), ErrCode::$BUSINESS_ERROR);
                            }
                        }
                    }
                } else {
                    // 关联的PO单
                    $order = PurchaseOrder::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $payment->po_id]
                    ]);
                    if (empty($order)) {
                        throw new ValidationException(static::$t->_('purchase_payment_linked_po_data_null', ['po_id' => $payment->po_id]), ErrCode::$VALIDATE_ERROR);
                    }

                    $this->logger->info('根据采购付款申请单poid=' . $payment->po_id . '; 获取到关联的采购订单信息: ' . json_encode($order->toArray(), JSON_UNESCAPED_UNICODE));

                    $apply = PurchaseApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $order->pa_id]
                    ]);
                    if (empty($apply)) {
                        throw new ValidationException(static::$t->_('purchase_order_linked_pa_data_null', ['pa_id' => $order->pa_id]), ErrCode::$VALIDATE_ERROR);
                    }

                    $this->logger->info('根据采购订单表pa_id=' . $order->pa_id . '; 获取到关联的采购申请单信息: ' . json_encode($apply->toArray(), JSON_UNESCAPED_UNICODE));

                    // 申请单是新数据
                    if (!$this->isOldData($apply->created_at)) {
                        $purchase_apply_products = $apply->getProducts();

                        $purchase_apply_products_array = $purchase_apply_products->toArray();
                        $this->logger->info('获取到关联的采购申请单的产品列表信息: ' . json_encode($purchase_apply_products_array, JSON_UNESCAPED_UNICODE));

                        // 获取申请单的行关联的订单已被全额付款的情况 申请单行 => 发票含税金额合计
                        // ① 获取PAR行关联的PO行的PUR行(下一步仅需在PUR行 与 PAR行有关联时校验)
                        $receipts_pop_ids = $payment->getReceipts()->toArray();
                        $receipts_pop_ids = array_column($receipts_pop_ids, 'pop_id');

                        $order_product_ids = $order->getProducts()->toArray();
                        $order_product_ids = array_column($order_product_ids, 'apply_product_id', 'id');

                        $payment_linked_pup_ids = [];
                        foreach ($receipts_pop_ids as $pop_id) {
                            if (!empty($order_product_ids[$pop_id])) {
                                $payment_linked_pup_ids[] = $order_product_ids[$pop_id];
                            }
                        }

                        // ② 获取PUR行关联的订单已被全额付款的情况
                        $linked_payment_receipts = ApplyService::getInstance()->getAllRelatedOrdersFullPaidMap($apply->id, $purchase_apply_products_array, true, $payment_linked_pup_ids);

                        // 遍历申请单的行, 查验是否需要释放预算
                        foreach ($purchase_apply_products as $product) {
                            // 初始化预算科目 与 对应的金额
                            $_budget_object_amount = $paymentAmount[$product->level_code] ?? 0;

                            // 如果释放过，直接用释放金额
                            if ($product->is_free) {
                                $paymentAmount[$product->level_code] = bcadd($_budget_object_amount, $product->free_amount);
                                continue;
                            }

                            // 该行的是否被全额付款
                            if (!isset($linked_payment_receipts[$product->id])) {
                                // 根据汇率转换为国家默认币种的金额
                                $_product_all_total = $enums_service->amountExchangeRateCalculation($product->all_total, $apply->exchange_rate, 0);
                                $paymentAmount[$product->level_code] = bcadd($_budget_object_amount, $_product_all_total);
                                continue;
                            }

                            $is_free_flag = true;

                            // 该行对应的发票含税金额合计: 已根据汇率做了转换
                            $_product_receipts_amount = $linked_payment_receipts[$product->id];
                            $paymentAmount[$product->level_code] = bcadd($_budget_object_amount, $_product_receipts_amount);

                            // 更新该行的预算金额释放状态
                            $product->is_free = 1;
                            $product->free_amount = $_product_receipts_amount;
                            if ($product->save() === false) {
                                throw new BusinessException("采购付款申请单-支付失败-更新申请单行数据[pur_id={$product->id}]失败, 原因可能是: " . get_data_object_error_msg($product) . '; 数据: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) , ErrCode::$BUSINESS_ERROR);
                            }
                        }
                    }
                }

                // 兼容菲律宾/马来/印尼/越南历史未开启预算的部分数据
                $re_budget_flag = $this->getHistoryBudgetStatus($apply->created_at);
                if ($is_free_flag && $re_budget_flag && !empty($paymentAmount)) {
                    $result = (new BudgetService())->re_back_budget($apply->pano, $user, BudgetService::ORDER_TYPE_2, $paymentAmount);
                    $this->logger->info('purchase_payment  释放预算判断 params ' . json_encode([$apply->pano, $user, BudgetService::ORDER_TYPE_2, $paymentAmount], JSON_UNESCAPED_UNICODE) . '; results ' . json_encode([$result], JSON_UNESCAPED_UNICODE));

                    if ($result['code'] != ErrCode::$SUCCESS) {
                        throw new ValidationException($result['message'], ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // 未支付, 回滚该采购付款申请单已关联的采购订单的关联状态, 便于这些采购订单允许被其他付款单关联
            // v10778
            if ($pay_status == Enums::LOAN_PAY_STATUS_NOTPAY && !$payment->is_link_pa && !empty($payment->po_id)) {
                $purchase_order = PurchaseOrder::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $payment->po_id]
                ]);

                if (empty($purchase_order)) {
                    throw new ValidationException(static::$t->_('purchase_payment_linked_po_data_null', ['po_id' => $payment->po_id]), ErrCode::$VALIDATE_ERROR);
                }

                $purchase_apply = PurchaseApply::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'=>['id' => $purchase_order->pa_id]
                ]);

                if (empty($purchase_apply)) {
                    throw new ValidationException(static::$t->_('purchase_order_linked_pa_data_null', ['pa_id' => $purchase_order->pa_id]), ErrCode::$VALIDATE_ERROR);
                }

                $payment_receipt = PurchasePaymentReceipt::find([
                    'conditions'    =>  'ppid = :ppid:',
                    'bind'          =>  ['ppid' => $payment->id]
                ])->toArray();

                $flag = 0;
                foreach ($payment_receipt as $k => $v) {
                    if (empty($v['pop_id'])) {
                        continue;
                    }

                    $order_product = PurchaseOrderProduct::getFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $v['pop_id']]
                    ]);

                    if (empty($order_product)) {
                        continue;
                    }

                    $order_product->pay_total = max(bcsub($order_product->pay_total, $v['total']), 0);

                    // 已经付款单数量为0，且不是老数据的时候
                    if ($order_product->pay_total == 0 && !$this->isOldData($purchase_apply->created_at ?? 0)) {
//                        $order_product->is_can_update = 1;
                        $flag++;
                    }

                    $update_bool = $order_product->i_update();
                    if ($update_bool === false) {
                        throw new BusinessException('采购付款申请单撤销失败-修改采购订单产品已购数量失败, 原因可能是:' . get_data_object_error_msg($order_product) . '; 数据: ' . json_encode($order_product->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }

                // 回滚采购订单关联状态
                $updateData = ['is_cite' => PurchaseEnums::IS_CITE_NO];
                if ($flag && !$this->isOldData($purchase_apply->created_at ?? 0)) {
//                    $updateData['is_can_update'] = 1;
                }

                if ($purchase_order->i_update($updateData) === false) {
                    throw new BusinessException("采购付款单支付-未支付-采购订单状态更新失败[pono={$purchase_order->pono}], 业务数据 = " . json_encode($updateData, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($purchase_order), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
            $this->delUnReadNumsKeyByStaffIds($purchase_payment_pay_staff_id);

        } catch (ValidationException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchase-payment-pay-update-failed: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 处理发票数据
     *
     * @param array $receipt
     * @return array
     */
    private function handleReceiptData($receipt,$data){
        try {
            $budgetIds = array_values(array_filter(array_unique(array_column($receipt, 'budget_id'))));
            $budgetService = new BudgetService();
            $budgetList = $budgetService->budgetObjectList($budgetIds);

            // 获取PO ID
            $_pop_ids = array_values(array_filter(array_unique(array_column($receipt,  'pop_id'))));
            $_po_product_item = [];
            if (!empty($_pop_ids)) {
                $_po_product_item = PurchaseOrderProduct::find([
                    'conditions' => 'id IN ({pop_ids:array})',
                    'columns' => 'id, poid',
                    'bind' => ['pop_ids' => $_pop_ids]
                ])->toArray();
                $_po_product_item = array_column($_po_product_item, 'poid', 'id');
            }

            // 获取PO单信息
            $_po_ids = $_po_product_item ? array_values(array_filter(array_unique(array_values($_po_product_item)))) : [];
            $_po_item = [];
            if (!empty($_po_ids)) {
                $_po_item = PurchaseOrder::find([
                    'conditions'    =>  'id IN ({po_ids:array})',
                    'columns'       =>  'id, pono',
                    'bind'          =>  ['po_ids' => $_po_ids]
                ])->toArray();
                $_po_item = array_column($_po_item, 'pono' ,'id');
            }

            // 获取PO单产品分类信息
            $_category_a_ids = array_values(array_filter(array_unique(array_column($receipt,  'category_a'))));
            $_category_b_ids = array_values(array_filter(array_unique(array_column($receipt,  'category_b'))));
            $_category_ids = array_values(array_filter(array_unique(array_merge($_category_a_ids ?? [], $_category_b_ids ?? []))));
            $_category_item = [];
            if (!empty($_category_ids)) {
                $_category_item = PurchaseProductCategory::find([
                    'conditions'    =>  'id IN ({ids:array})',
                    'columns'       =>  'id, name_key',
                    'bind'          =>  ['ids' => $_category_ids]
                ])->toArray();

                $_category_item = array_column($_category_item, 'name_key', 'id');
            }

            $whtArr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

            foreach ($receipt as $k => $v) {
                $_poid = $_po_product_item[$v['pop_id']] ?? '';
                $receipt[$k]['pono'] = $_po_item[$_poid] ?? '';

                $receipt[$k]['wht_type_text']         = $whtArr[$v['wht_type']] ?? '';
                $receipt[$k]['wht_ratio']             = $v['wht_ratio'] ?? '';
                $receipt[$k]['wht_amount']            = bcdiv($v['wht_amount'], 1000, 2);
                $receipt[$k]['real_amount']           = bcdiv($v['real_amount'], 1000, 2);
                $receipt[$k]['vat_invoice_number']    = $v['vat_invoice_number'];
                $receipt[$k]['ticket_amount_not_tax'] = bcdiv($v['ticket_amount_not_tax'],1000,2);
                $receipt[$k]['percent']               = intval($v['percent']);
                $receipt[$k]['deductible_vat_tax']    = (string)$v['deductible_vat_tax']??'';
                $receipt[$k]['deductible_vat_amount'] = bcdiv($v['deductible_vat_amount'], 1000, 2);

                $_category_a_name_key = $_category_item[$v['category_a']] ?? '';
                $receipt[$k]['category_a_name'] = !empty($_category_a_name_key) ? static::$t->_($_category_a_name_key) : '';

                $_category_b_name_key = $_category_item[$v['category_b']] ?? '';
                $receipt[$k]['category_b_name'] = !empty($_category_b_name_key) ? static::$t->_($_category_b_name_key) : '';

                $receipt[$k]['budget_id'] = $v['budget_id'];
                $receipt[$k]['budget_text'] = $v['budget_id'] && isset($budgetList[$v['budget_id']]) ? $budgetList[$v['budget_id']]['name_'.strtolower(substr(self::$language, -2))] : '';
                $receipt[$k]['product_name'] = $v['budget_id'] ? $v['product_name'] : static::$t->_($v['product_name']);
                $receipt[$k]['product_desc'] = $v['product_desc'];

                $receipt[$k]['unit'] = static::$t->_($v['unit']);
                $receipt[$k]['not_tax_price'] = bcdiv($v['not_tax_price'],$this->digits,$this->digits_num);
                $receipt[$k]['total_price'] = bcdiv($v['total_price'],1000,2);
                $receipt[$k]['tax_total_price'] = bcdiv($v['tax_total_price'],1000,2);
                $receipt[$k]['ticket_amount'] = bcdiv($v['ticket_amount'],1000,2);
                $receipt[$k]['input_tax'] = bcdiv($v['input_tax'],1000,2);
                $receipt[$k]['deduct_input_tax'] = bcdiv($v['deduct_input_tax'],1000,2);
                $receipt[$k]['tax_ratio'] = bcdiv($v['tax_ratio'],1000,2);
            }
        } catch (Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-payment-receipt-select-failed:' . $real_message);
        }

        return [
            'code'      =>  $code ?? ErrCode::$SUCCESS,
            'message'   =>  $message ?? '',
            'data'      =>  $receipt
        ];
    }

    /**
     * 采购付款申请单撤回
     *
     * @param int $id
     * @return array
     */
    public function cancel($id){
        $db = $this->getDI()->get('db_oa');
        try {

            $payment_receipt = PurchasePaymentReceipt::find([
                'conditions'    =>  'ppid=?0',
                'bind'          =>  [$id]
            ])->toArray();



            $payment = PurchasePayment::findFirst(
                [
                    'conditions'=>'id=?0',
                    'bind'=>[$id]
                ]
            );

            if(empty($payment)){
                throw new ValidationException("not found payment", ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            if($payment->is_link_pa){
                $purchase_apply = PurchaseApply::findFirst(
                    [
                        'conditions'=>'id = ?0',
                        'bind'=>[$payment->pa_id]
                    ]
                );


                if(empty($purchase_apply)){
                    throw new ValidationException("not found apply", ErrCode::$CONTRACT_GET_INFO_ERROR);
                }


                foreach ($payment_receipt as $k => $v) {
                    if (empty($v['pap_id'])) {
                        continue;
                    }
                    $apply_product = PurchaseApplyProduct::findFirst(
                        [
                            'conditions' => 'id=?0',
                            'bind' => [$v['pap_id']]
                        ]
                    );
                    $apply_product->order_total = 0;
                    //且不是老数据的时候
                    if (!$this->isOldData($purchase_apply->created_at)) {
                        $apply_product->is_can_update = 1;
                    }
                    $update_bool = $apply_product->i_update();
                    if ($update_bool === false) {
                        throw new Exception("采购付款申请单撤销失败-修改采购申请单产品已购数量失败", ErrCode::$CONTRACT_GET_INFO_ERROR);
                    }
                }


                $updateData = ['is_cite' => 0];
                if (!$this->isOldData($purchase_apply->created_at)) {
                    $updateData['is_can_update'] = 1;
                }
                $purchase_apply->i_update($updateData);

            }else{
                $po_id = $payment->po_id;
                $purchase_order = PurchaseOrder::findFirst(
                    [
                        'conditions' => 'id =?0',
                        'bind' => [$po_id]
                    ]
                );
                $purchase_apply = PurchaseApply::findFirst(
                    [
                        'conditions'=>'id = ?0',
                        'bind'=>[$purchase_order->pa_id]
                    ]
                );

                $db->begin();
                $flag = 0;
                foreach ($payment_receipt as $k => $v) {
                    if (empty($v['pop_id'])) {
                        continue;
                    }
                    $order_product = PurchaseOrderProduct::getFirst(
                        [
                            'conditions' => 'id=?0',
                            'bind' => [$v['pop_id']]
                        ]
                    );
                    $order_product->pay_total = MAX(bcsub($order_product->pay_total, $v['total']), 0);

                    //已经付款单数量为0，且不是老数据的时候
                    if ($order_product->pay_total == 0 && !$this->isOldData($purchase_apply->created_at)) {
                        $order_product->is_can_update = 1;
                        $flag++;
                    }
                    $update_bool = $order_product->i_update();
                    if ($update_bool === false) {
                        throw new Exception("采购付款申请单撤销失败-修改采购订单产品已购数量失败", ErrCode::$CONTRACT_GET_INFO_ERROR);
                    }
                }

                if (!empty($po_id)) {
                    $order = PurchaseOrder::findFirst(
                        [
                            'id = ?0',
                            'bind' => [$po_id]
                        ]
                    );

                    $updateData = ['is_cite' => 0];
                    if ($flag && !$this->isOldData($purchase_apply->created_at)) {
                        $updateData['is_can_update'] = 1;
                    }
                    $order->i_update($updateData);
                }
                $db->commit();
            }


        } catch (Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-payment-receipt-select-failed:' . $real_message);
            $db->rollback();
        }
        return [
            'code'      =>  $code ?? ErrCode::$SUCCESS,
            'message'   =>  $message ?? '',
            'data'      =>  []
        ];
    }

    public function isCanDownload($item)
    {
        $result = '0';

        //待支付或者已支付可以下载
        if (!empty($item) && $item['status'] == Enums::WF_STATE_APPROVED && (in_array($item['pay_status'], [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]))) {
            $result = '1';
        }

        return $result;
    }

    /**
     * 判断是否已经申请了重复的付款申请单
     * @return array
     */

    public function isHaveSamePaymentByPo($param)
    {
        $data = ['is_exist'=>false,'ppno'=>''];
        $message = '';
        try {

            $item = PurchaseOrder::findFirst(
                [
                    'conditions'=> 'id = :id:',
                    'bind'=>['id'=>$param['po_id']]
                ]
            );
            if(empty($item)){
                throw new BusinessException('not found order');
            }

            //关联采购订单 且发票金额等于此次发票金额，待支付或已支付
            $item = PurchasePayment::findFirst(
                [
                    'conditions'=>'po_id = :po_id: and receipt_amount = :receipt_amount: and status in (1,3) and pay_status in (1,2)',
                    'bind' => ['po_id'=>$param['po_id'],'receipt_amount'=>$param['receipt_amount']*1000]
                ]
            );

            if(!empty($item)){
                $data['is_exist'] = true;
                $data['ppno'] = $item->ppno;
            }
        }catch (Exception $e) {
            $code = ErrCode::$MYSQL_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-payment-isHaveSamePaymentByPo-failed:' . $real_message);
        }
        return [
            'code' => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data' => $data
        ];
    }

    /**
     * 获取采购订单产品名称列表
     * @param $product_name
     * @return array
     */
    public function productNameList($product_name){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $itemList = PurchasePaymentReceipt::find(
            [
                'conditions'=>"product_name like :product_name:",
                'column'=>'product_name',
                'group'=>'product_name',
                'bind'=>['product_name'=> '%'.$product_name.'%']
            ]
        )->toArray();

        foreach ($itemList as $key => $item) {
            $data[] = [
                'id' => $key + 1,
                'name' => $item['product_name']
            ];
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    // 兼容菲律宾/马来历史未开启预算的部分数据
    public function getHistoryBudgetStatus($created_at){
        $state_date_time = '2022-01-01 00:00:00';
        $country_code = get_country_code();
        $re_budget_flag = true;
        if (in_array($country_code,['MY','PH']) && $created_at < $state_date_time) {
            $re_budget_flag = false;
        }
        // 兼容印尼/越南  TODO 优化代码
        $state_date_time = '2022-03-31 00:00:00';
        if (in_array($country_code,['ID','VN']) && $created_at < $state_date_time) {
            $re_budget_flag = false;
        }
        return $re_budget_flag;
    }

    /**
     * 校验验收单 - 根据验收单-采购类型 1采购订单2采购申请单
     * 验证验收单上的PO单、PUR单与传递的PO
     * @param array $data 参数组
     * @return array
     */
    public function checkAcceptance($data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $acceptance_order = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $data['no']],
            ]);

            if (empty($acceptance_order)) {
                throw new ValidationException(self::$t->_('acceptance_order_is_no'), ErrCode::$VALIDATE_ERROR);
            }

            //V21690 若验收单采购类型是采购订单 && 验收单上的PO号 != 传递的po参数
            if ($acceptance_order->acceptance_type == PurchaseEnums::ACCEPTANCE_TYPE_PO && $acceptance_order->po != $data['po']) {
                throw new ValidationException(self::$t->_('acceptance_order_not_po'), ErrCode::$VALIDATE_ERROR);
            }

            //V21690 若验收单采购类型是采购申请单 && 验收单上的PUR号 != 传递的po参数
            if ($acceptance_order->acceptance_type == PurchaseEnums::ACCEPTANCE_TYPE_PU && $acceptance_order->po != $data['po']) {
                throw new ValidationException(self::$t->_('acceptance_order_not_pur'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $this->logger->warning('purchasePayment-acceptance-failed:' . $e->getMessage());
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 关联po下 产品发票金额
     * */
    public function paymentProductAmount($po_id)
    {

        $builder    = $this->modelsManager->createBuilder();
        $column_str = 'sum(p.ticket_amount) as ticket_amount,p.pop_id';
        $builder->from(['o' => PurchasePayment::class]);
        $builder->leftjoin(PurchasePaymentReceipt::class, 'o.id=p.ppid', 'p');
        $builder->columns($column_str);
        $builder->andWhere('o.po_id = :id: and o.status in ('.Enums::CONTRACT_STATUS_PENDING.','.Enums::CONTRACT_STATUS_APPROVAL.')  and o.pay_status!='.Enums::LOAN_PAY_STATUS_NOTPAY, ['id' => $po_id]);
        $builder->groupBy('p.pop_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 押金单条数据详情处理
     * @Date: 9/27/22 3:27 PM
     * @param array $params 条件
     * @return  array
     * @author: peak pan
     **/
    public function depositDetail(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        //获取付款申请主表信息
        $data = [];
        try {
            $main_model = PurchasePaymentReceipt::findFirst([
                'id = :id:',
                'bind'    => ['id' => $params['id']],
                'columns' => [
                    'id',
                    'ppid',
                    'ledger_account_id',
                    'cost_store_name',
                    'cost_center_name',
                    'ticket_date as cost_start_date',
                    'ticket_amount_not_tax as amount_no_tax',
                    'vat7_rate as vat_rate',
                    'ticket_tax as amount_vat',
                    'ticket_amount as amount_have_tax',
                    'wht_type as wht_category',
                    'wht_ratio as wht_rate',
                    'wht_amount as amount_wht',
                    'budget_id',
                    'product_name',
                ]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }
            $purchase_payment = PurchasePayment::findFirst([
                'id = :id:',
                'bind' => ['id' => $main_model->ppid]
            ]);
            if (empty($purchase_payment)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }

            $data         = DepositService::getInstance()->getDepositInfo($main_model, $purchase_payment, $params);
            $data['head'] = [
                'type'                       => static::$t[DepositEnums::$deposit_modules[$params['type']]],
                'id'                         => $main_model->id,
                'apply_no'                   => $purchase_payment->ppno,
                'apply_id'                   => $purchase_payment->create_id ?? '',
                'apply_name'                 => $purchase_payment->create_name ?? '',
                'apply_email'                => $purchase_payment->apply_email ?? '',
                'cost_department_name'       => $purchase_payment->cost_department_name,
                'apply_node_department_name' => $purchase_payment->create_department_name,
                'create_company_name'        => $purchase_payment->cost_company_name,
                'cost_store_type'            => static::$t[Enums::$payment_cost_store_type[$purchase_payment->cost_store]],
                'currency'                   => static::$t[GlobalEnums::$currency_item[$purchase_payment->currency]],//币种
                'cost_department_id'         => $purchase_payment->cost_department,
            ];

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-采购-获取数据详情信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     *  押金列表分页列表 数据
     *
     * @Date: 9/27/22 3:27 PM
     * @param array $condition 条件
     * @param array $user
     * @param int $type 类型
     * @return  array
     * @author: peak pan
     */
    public function getDepositList(array $condition, array $user, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? DepositEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? DepositEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - DepositEnums::PAGE_NUM);

        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            if (isset($condition['sta_date']) && isset($condition['end_date']) && $condition['sta_date'] > $condition['end_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['sta_return_date']) && isset($condition['end_return_date']) && $condition['sta_return_date'] > $condition['end_return_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            $condition['uid'] = $user['id'];
            $builder          = $this->modelsManager->createBuilder();

            if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
                $columns = [
                    'op.ppno as apply_no',//申请单号
                    'op.create_id as apply_id',//申请人工号
                    'op.create_name as apply_name',//申请人姓名
                    'op.apply_date as created_at',//申请日期
                    'op.cost_company_name as create_company_name',//费用所属公司
                    'op.cost_company_id', 'op.cost_department_name',//费用所属部门
                    'op.cost_store as cost_store_type',//费用所属网点/总部
                    'op.create_name as apply_name',//押金负责人
                    'op.contract_no',//相关合同
                    'ca.status as contract_status',//合同押金状态
                    'ca.terminal_at as expiry_date', 'de.contract_no as contract_no_b',
                    'op.currency', 'opd.budget_id',//预算分类
                    'opd.id AS detail_id', // 明细ID
                    'opd.product_id',//明细分类
                    'opd.cost_store_name',//费用所属网点/总部
                    'opd.cost_center_name', '"" as cost_start_date',//费用发生期间
                    'opd.ticket_amount_not_tax as amount_no_tax',//不含税金额（含WHT）
                    'opd.vat7_rate as vat_rate',//SST税率
                    'opd.ticket_tax as amount_vat',//SST税额
                    'opd.ticket_amount as amount_have_tax',//含税金额
                    'opd.wht_type as wht_category',//WHT类
                    'opd.wht_ratio as wht_rate',//WHT税率
                    'opd.wht_amount as amount_wht', '"" as sum_money', 'dr.deposit_money',//押金总金额
                    'dr.status',//押金归还状态
                    'de.return_money',//归还金额
                    'dr.loss_money as loss_money_return',//损失总金额
                    'dr.other_return_money',// 其他退款金额
                    'dr.other_return_info',// 其他退款说明
                    'dr.bank_flow_date',//银行流水日期
                    'dr.return_info', '"" as return_attachment',//归还详情附加
                    'dl.loss_bear_id', 'dl.loss_budget_id',//损失类型
                    'dl.loss_department_id',//损失部门名称 网点/总部
                    'dl.loss_money', '"" as loss_attachment', 'de.id as deposit_id',
                    'dr.id as deposit_return_id', 'dl.id as deposit_loss_id', 'de.apply_id as deposit_create_name',
                    'de.return_status', 'dl.loss_organization_id', 'opd.ticket_amount',
                    'op.create_department_name AS biz_apply_department_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            } else {
                $columns = [
                    'op.ppno as apply_no', 'opd.id', 'op.create_id as apply_id',
                    'op.create_name as apply_name', 'op.apply_date as created_at',
                    'op.cost_company_name as create_company_name',//费用所属公司
                    'op.cost_department_name', 'op.create_id',
                    'op.create_department_name AS biz_apply_department_name',
                    'op.create_name', 'op.cost_company_id', 'opd.cost_store_name',
                    'opd.ticket_amount_not_tax as amount_no_tax', 'opd.wht_amount',
                    'opd.ticket_tax as amount_vat',//SST税额
                    'opd.wht_amount as amount_wht',//WHT税额
                    'op.currency', 'ca.status', 'ca.terminal_at as expiry_date',
                    'de.return_money', 'de.deposit_money',//押金总金额
                    'de.loss_money',//损失金额
                    'op.contract_no', 'de.contract_no as contract_no_b',
                    'de.return_status', 'de.apply_id as deposit_create_id',
                    'de.apply_name as deposit_create_name', 'opd.ticket_amount',
                    'op.cost_store as cost_store_type',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            }

            $builder->from(['opd' => PurchasePaymentReceipt::class]);
            //组合搜索条件
            $builder->columns($columns);
            $builder = $this->getDepositCondition($builder, $condition, $type, $user);
            $count   = (int)$builder->columns('COUNT(DISTINCT opd.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                $builder->columns($columns);
                if ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
                    $builder->groupBy('dl.id,opd.id');
                } else {
                    $builder->groupBy('opd.id');
                }
                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_ASK])) {
                    $builder->orderBy('opd.id DESC');
                }
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDepositItems($items, $condition);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('押金管理-采购-列表数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     *  押金列表数据处理
     *
     * @Date: 9/27/22 3:27 PM
     * @param array $items 数据
     * @param array $condition 条件
     * @return  array
     * @author: peak pan
     */
    private function handleDepositItems(array $items, $condition)
    {
        if (empty($items)) {
            return [];
        }

        $wht_category_arr  = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');
        $cost_company_list = [];
        $cost_company_ids  = array_values(array_unique(array_filter(array_column($items, 'cost_company_id'))));
        if ($cost_company_ids) {
            $cost_company_arr  = (new DepartmentService())->getDepartmentInfoByIds($cost_company_ids);
            $cost_company_list = array_column($cost_company_arr, 'name', 'id');
        }

        if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
            $budget_ids            = array_values(array_unique(array_filter(array_column($items, 'budget_id'))));
            $budget_id_arr         = DepositService::getInstance()->getBudgetObjectIdByName($budget_ids);

            $product_ids           = array_values(array_unique(array_filter(array_column($items, 'product_id'))));
            $product_id_arr        = empty($product_ids) ? [] : DepositService::getInstance()->getBudgetObjectProductIdByName($product_ids);
            $deposit_return_ids    = array_values(array_unique(array_filter(array_column($items, 'deposit_id'))));
            $return_attachment     = empty($deposit_return_ids) ? [] : DepositService::getInstance()->getIdByUrlAttach($deposit_return_ids, Enums::OSS_PURCHASE_PAYMENT_TYPE_DEPOSIT_ADD);
            $loss_bear_name_arr    = array_column(array_merge(DepositEnums::$vendor_arr, (new PurchaseService())->getCooCostCompany()), 'cost_company_name', 'cost_company_id');
            $loss_budget_ids       = array_values(array_unique(array_filter(array_column($items, 'loss_budget_id'))));
            $loss_budget_arr       = empty($loss_budget_ids) ? [] : DepositService::getInstance()->getBudgetIdsByname($loss_budget_ids); //损失类型
            $loss_organization_arr = DepositEnums::$organization_type;

            foreach ($items as &$item) {
                $item['contract_no']         = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                $item['contract_status_id']  = $item['contract_status'];
                $item['contract_status']     = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];
                $item['currency']            = static::$t[GlobalEnums::$currency_item[$item['currency']]];//币种
                $item['sum_money']           = bcdiv(bcsub($item['ticket_amount'], $item['amount_wht'], 3), 1000, 2);//实付金额
                $item['sum_money']           = !empty($item['sum_money']) ? $item['sum_money'] : '';
                $item['deposit_money']       = $item['sum_money'];//押金总金额
                $item['deposit_money']       = !empty($item['deposit_money']) ? $item['deposit_money'] : '';
                $item['cost_store_type']     = static::$t[Enums::$payment_cost_store_type[$item['cost_store_type']]] ?? '';
                $item['create_company_name'] = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['amount_no_tax']       = (string)bcdiv($item['amount_no_tax'], 1000, 2) ?? '';
                $item['amount_vat']          = (string)bcdiv($item['amount_vat'], 1000, 2) ?? '';
                $item['amount_have_tax']     = (string)bcdiv($item['amount_have_tax'], 1000, 2) ?? '';
                $item['deposit_apply_name']  = !empty($item['deposit_create_name']) ? $item['deposit_create_name'] : $item['apply_id'];
                $item['amount_wht']          = (string)bcdiv($item['amount_wht'], 1000, 2) ?? '';
                $item['wht_category']        = $wht_category_arr[$item['wht_category']] ?? '';
                $item['vat_rate']            = $item['vat_rate'] . '%' ?? '0%';
                $item['wht_rate']            = $item['wht_rate'] . '%' ?? '';
                $item['budget_id']           = $budget_id_arr[$item['budget_id']] ?? '';//预算分类
                $item['product_id']          = $product_id_arr[$item['product_id']] ?? '';//明细分类
                $item['status']              = empty($item['return_status']) ? DepositEnums::DEPOSIT_RETURN_STATUS_NOT : $item['return_status'];
                $item['return_status_id']    = $item['return_status'] ?? '1';
                $item['return_status']       = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['return_money']        = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';//归还金额
                $item['other_return_money']  = bcdiv($item['other_return_money'], 1000, 2); // 其他退款金额
                $item['loss_money_return']   = $item['loss_money_return'] != '' ? bcdiv($item['loss_money_return'], 1000, 2) : '';//损失总金额
                $item['return_attachment']   = !empty($return_attachment[$item['deposit_id']]) ?
                    implode(',', $return_attachment[$item['deposit_id']]) : '';//归还详情附加
                $item['loss_bear_id']        = $loss_bear_name_arr[$item['loss_bear_id']] ?? '';//损失承担方
                $item['loss_budget_id']      = $loss_budget_arr[$item['loss_budget_id']] ?? '';//损失类型
                $item['loss_department_id']  = static::$t[$loss_organization_arr[$item['loss_organization_id']]] ?? '';//损失部门名称 网点/总部
                $item['loss_money']          = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';//损失金额
                $item['created_at']          = show_time_zone($item['created_at'], 'Y-m-d');
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }
        } else {
            foreach ($items as &$item) {
                $item['type']                 = DepositEnums::DEPOSIT_PURCHASE_PAYMENT;
                $item['return_money']         = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';
                $item['deposit_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';
                $item['sum_deposit_money']    = bcdiv(bcsub($item['ticket_amount'], $item['amount_wht'], 3), 1000, 2);
                $item['contract_no']          = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                $item['contract_status']      = $item['status'] ?? '';
                $item['contract_status_name'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
                $item['expiry_date']          = '';
                $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];
                $item['cost_store_type_text'] = $item['cost_store_name'];
                $item['return_status_id']     = $item['return_status'] ?? '1';
                $item['return_status']        = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['created_at']           = show_time_zone($item['created_at'], 'Y-m-d');
                $item['create_id']            = empty($item['deposit_create_id']) ? $item['apply_id'] : $item['deposit_create_id'];
                $item['create_name']          = empty($item['deposit_create_id']) ? $item['apply_name'] : $item['deposit_create_name'];
                $item['create_company_name']  = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }
        }
        return $items;
    }

    /**
     *  列表复合搜索条件  押金
     *
     * @Date: 9/27/22 3:27 PM
     * @param object $builder 对象
     * @param array $condition 条件
     * @param int $type 模块类型
     * @param array $user
     * @return  array
     * @throws BusinessException
     * @author: peak pan
     */
    private function getDepositCondition(object $builder, array $condition, int $type, array $user = [])
    {
        $apply_no          = $condition['apply_no'] ?? '';
        $create_company_id = $condition['create_company_id'] ?? '';
        $cost_store_type   = $condition['cost_store_type'] ?? '';
        $contract_no       = $condition['contract_no'] ?? '';
        $sta_date          = $condition['sta_date'] ?? '';
        $end_date          = $condition['end_date'] ?? '';
        $create_id         = $condition['create_name'] ?? '';
        $sta_return_date   = $condition['sta_return_date'] ?? '';
        $end_return_date   = $condition['end_return_date'] ?? '';
        $return_status     = $condition['return_status'] ?? '';

        $flag = in_array($condition['flag'] ?? 0, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $condition['flag'] : GlobalEnums::AUDIT_TAB_PENDING;

        // 是否接入通用数据权限
        $is_access_common_data_permission = $condition['is_access_common_data_permission'] ?? false;

         // 当前登录用户提交的所有申请数据
        $builder->leftjoin(PurchasePayment::class, 'op.id = opd.ppid', 'op');
        $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
        $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
        $builder->leftjoin(Contract::class, 'co.cno = op.contract_no', 'co');
        $builder->leftjoin(ContractArchive::class, 'ca.cno=co.cno', 'ca');

        $builder->andWhere('de.id IS NULL OR (de.id IS NOT NULL AND de.deposit_type = :deposit_type:)', ['deposit_type' => DepositEnums::DEPOSIT_PURCHASE_PAYMENT]);

        if (in_array($type, [self::LIST_TYPE_APPLY, self::LIST_TYPE_DATA, DepositEnums::LIST_TYPE_DATA_EXPORT]) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $builder->andWhere('op.real_pay_at >= :real_pay_at:', ['real_pay_at' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
        }

        if ($type == self::LIST_TYPE_APPLY) {
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
            $builder->andWhere('op.create_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
        } elseif ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], 'dr');
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
        } elseif ($type == self::LIST_TYPE_DATA) {
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
        } elseif ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
            //数据导出  和列表不一致
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            if (!empty($condition['export_type'])) {
                $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
                $builder->andWhere('op.create_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
            } else {
                $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
            }
        } elseif ($type == self::LIST_TYPE_ASK) {
            $biz_table_info = ['table_alias' => 'dr'];
            $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $condition['is_reply'], [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], $biz_table_info);
        }

        // 对接通用数据权限
        if ($is_access_common_data_permission === true) {
            // 业务表参数
            $table_params = [
                'type' => SettingEnums::DATA_PERMISSION_TYPE_MULTI_ENTIEY,
                'entity_item' => [
                    'biz' => [
                        'table_alias_name' => 'op',
                        'create_id_field' => 'create_id',
                        'create_node_department_id_filed' => 'create_department_id',
                        'extra_condtions' => 'de.apply_id IS NULL', // 预留, 暂时不用
                    ],
                    'deposit' => [
                        'table_alias_name' => 'de',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NOT NULL', // 预留, 暂时不用
                    ],
                ]
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_DEPOSIT_PURCHASE_PAYMENT, $table_params);
        }

        //申请编号
        if (!empty($apply_no)) {
            if (is_array($apply_no)) {
                $builder->inWhere('op.ppno', $apply_no);

            } else {
                $builder->andWhere('op.ppno = :ppno:', ['ppno' => $apply_no]);
            }
        }

        //申请时间-截止日期
        if (!empty($end_date) && !empty($sta_date)) {
            $sta_date .= ' 00:00:00';
            $end_date .= ' 23:59:59';
            $builder->betweenWhere('op.apply_date', $sta_date, $end_date);
        }

        //归还-截止日期
        if (!empty($end_return_date) && !empty($sta_return_date)) {
            $sta_return_date .= ' 00:00:00';
            $end_return_date .= ' 23:59:59';
            $builder->betweenWhere('dr.return_date', $sta_return_date, $end_return_date);

            //如果有归还时间 且没有归还状态的时候 默认为已归还
            if (empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE]);
            } else if (in_array($return_status, [DepositEnums::DEPOSIT_RETURN_STATUS_NOT, DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION, DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE])) {
                //如果有归还时间 且归还状态为 未归还、法务介入中、法务已确定的时候 为默认状态0
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_DEFAULT]);
            } else {
                //如果有归还时间 且归还状态不为未归还、法务介入中、法务已确定，空的时候 状态为当前选择的状态
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
            }
        } else {
            //如果有归还时间为空 且归还状态不为空按照选择的归还状态查询
            if (!empty($return_status)) {
                if ($return_status == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                    $builder->andWhere('(de.return_status = :return_status:) or (de.return_status is null)', ['return_status' => $return_status]);
                } else {
                    $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
                }
            }
        }

        //押金负责人
        if (!empty($create_id)) {
            $builder->andWhere('(((de.apply_id ="" or de.apply_id is null ) and (op.create_id = :create_id: or op.create_name = :create_id:)) or ((de.apply_id = :create_id: or de.apply_name = :create_id:)) and (de.apply_id !="" or de.apply_name !="") )', ['create_id' => $create_id]);
        }

        //费用所属公司
        if (!empty($create_company_id)) {
            $builder->andWhere('op.cost_company_id = :cost_company_id:', ['cost_company_id' => $create_company_id]);
        }

        if (!empty($cost_store_type)) {
            if ($cost_store_type == Enums::HEAD_OFFICE_STORE_FLAG) {
                $builder->andWhere('opd.cost_store_id = :cost_store_id:', ['cost_store_id' => Enums::PAYMENT_HEADER_STORE_ID]);
            } else {
                $builder->andWhere('opd.cost_store_id = :cost_store_id:', ['cost_store_id' => $cost_store_type]);
            }
        }

        if (!empty($contract_no)) {
            if (is_array($contract_no)) {
                $builder->andWhere('(op.contract_no in ({contract_no:array}) and (de.contract_no ="" or de.contract_no is null)) or (de.contract_no in ({contract_no:array}))', ['contract_no' => $contract_no]);
            } else {
                $builder->andWhere('((de.contract_no ="" or de.contract_no IS NULL ) and op.contract_no = :contract_no:) or (de.contract_no = :contract_no:)', ['contract_no' => $contract_no]);
            }
        }

        $builder->andWhere('op.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);

        return $builder;
    }

    /**
     * 添加文件附件
     * @param $data
     * @param $user
     * @return array
     */
    public function addFile($data, $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'ok';
        $real_message = '';
        $db           = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 订单是否存在
            $payment = PurchasePayment::findFirst(
                [
                    'conditions' => 'id=?0',
                    'bind'       => [$data['id']]
                ]
            );
            if (empty($payment)) {
                throw new BusinessException('该订单不存在，采购付款单ID => ' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($data['required_supplement_file'])) {
                $attach_arr = [];
                foreach ($data['required_supplement_file'] as $k => $file) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT_SU;
                    $tmp['oss_bucket_key']  = $data['id'];
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $file['bucket_name'];
                    $tmp['object_key']      = $file['object_key'];
                    $tmp['file_name']       = $file['file_name'];
                    $tmp['created_at']      = date('Y-m-d H:i:s');
                    $attach_arr[]           = $tmp;
                }
                if (!empty($attach_arr)) {
                    // 删除历史关联附件
                    $old_model = AttachModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:',
                        'bind'       => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT_SU,
                                         'oss_bucket_key'  => $data['id']]
                    ]);
                    if (!empty($old_model)) {
                        $del_bool = $old_model->delete();
                        if ($del_bool === false) {
                            throw new BusinessException('付款申请-附件删除失败，attachment data => ' .
                                json_encode(['required_supplement_file' => $data['required_supplement_file'], 'message' => get_data_object_error_msg($old_model)], JSON_UNESCAPED_UNICODE),
                                ErrCode::$BUSINESS_ERROR);
                        }
                    }
                    // 编辑附件
                    $attach      = new AttachModel();
                    $attach_bool = $attach->batchInsert($attach_arr);
                    if ($attach_bool === false) {
                        throw new BusinessException('付款申请-附件添加失败，attachment data => ' .
                            json_encode(['required_supplement_file' => $data['required_supplement_file'], 'message' => get_data_object_error_msg($attach)], JSON_UNESCAPED_UNICODE),
                            ErrCode::$BUSINESS_ERROR);
                    }

                    //记录补充附件更新日期supplement_updated_at
                    $payment_bool = $payment->i_update(['supplement_updated_at' => date('Y-m-d H:i:s')]);
                    if ($payment_bool === false) {
                        throw new BusinessException('付款申请-附件添加记录时间失败，attachment data => ' .
                            json_encode(['required_supplement_file' => $data['required_supplement_file'], 'message' => get_data_object_error_msg($payment)], JSON_UNESCAPED_UNICODE),
                            ErrCode::$BUSINESS_ERROR);
                    }

                }
            }

            //与丹丹确认，可以不校验该逻辑
            //1. 当采购付款单的审批状态为"已通过"，支付状态不为"未支付"（即支付状态为待支付或者已支付），采购付款单的采购方式为"预付"，
            //且关联的采购订单的采购类型不在系统配置"不展示清关信息的采购订单的采购类型"，
            //且供应商归属地不等于马来西亚时，则点击补充附件的时候，展示且支持用户编辑清关信息：
            // 判断清关信息是否有变更
            if ($payment->is_clearance == Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_YES &&
                $data['actual_clearance_date'] != $payment->actual_clearance_date ||
                (array_key_exists('clearance_no', $data) && $data['clearance_no'] != $payment->clearance_no)
            ) {
                // 获取变更前的清关信息
                $oldData = [
                    'clearance_no'          => $payment->clearance_no ?? '',
                    'actual_clearance_date' => $payment->actual_clearance_date ?? '',
                ];

                // 获取变更后的清关信息
                $newData = [
                    'clearance_no'          => $data['clearance_no'] ?? '',
                    'actual_clearance_date' => $data['actual_clearance_date'] ?? '',
                ];

                //保存清关日志
                $clearance_log = new PurchasePaymentClearanceLogModel();
                $clearance_log->save([
                    'ppno'                 => $payment->ppno,
                    'old_data'             => json_encode($oldData, JSON_UNESCAPED_UNICODE),
                    'new_data'             => json_encode($newData, JSON_UNESCAPED_UNICODE),
                    'editor_staff_info_id' => $user['id'],
                ]);

                $updateParams = [];
                if ($data['actual_clearance_date'] !== $payment->actual_clearance_date) {
                    $updateParams['actual_clearance_date'] = !empty($data['actual_clearance_date']) ? $data['actual_clearance_date'] : null;
                }

                if (array_key_exists('clearance_no', $data) && $data['clearance_no'] !== $payment->clearance_no) {
                    $updateParams['clearance_no'] = $data['clearance_no'];
                }
                $payment_bool = $payment->i_update($updateParams);
                if ($payment_bool === false) {
                    throw new BusinessException('付款申请-更新清关数据失败，update data => ' .
                        json_encode(['data' => $updateParams, 'message' => get_data_object_error_msg($payment)], JSON_UNESCAPED_UNICODE),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('押金管理-采购-附件数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result ?? []
        ];
    }

    /**
     * 指定时间区间未上传附件单号
     * */
    public function getAttachNoList($condition = [])
    {
        $start        = $condition['start'] ?? '';
        $end          = $condition['end'] ?? '';
        $payment_data = PurchasePayment::find([
            'conditions' => 'supplement_updated_at >:start_date: and supplement_updated_at <=:end_date: and status in({status:array}) and pay_status in ({pay_status:array})',
            'columns'    => 'ppno',
            'bind'       => ['start_date' => $start, 'end_date' => $end, 'status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL], 'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]]
        ])->toArray();


        return !empty($payment_data) ? array_unique(array_filter(array_column($payment_data, 'ppno'))) : [];
    }

    /**
     * 获取支付截止时间超过7天没有补充附件的单号
     * @param array $condition
     * @return array
     */
    public function getSupplementAttachments($condition = [])
    {
        $pay_at = $condition['pay_at'] ?? null;

        $payment_data = PurchasePayment::find([
            'conditions' => 'real_pay_at<:pay_date: and status = :status: and pay_status = :pay_status: and supplement_invoice = :supplement_invoice: and supplement_updated_at is null',
            'columns'    => 'ppno,create_id,supplement_updated_at',
            'bind'       => ['pay_date' => $pay_at, 'status' => Enums::CONTRACT_STATUS_APPROVAL, 'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY, 'supplement_invoice' => PurchaseEnums::IS_SUPPLEMENT_INVOICE_YES]
        ])->toArray();

        $res     = $emails = [];
        $id_list = !empty($payment_data) ? array_values(array_unique(array_filter(array_column($payment_data, 'create_id')))) : [];
        // 申请邮箱
        if (!empty($id_list)) {
            $emails = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,email',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => [
                    'ids' => $id_list,
                ]
            ])->toArray();
        }

        foreach ($payment_data as $item) {
            $res[$item['create_id']][] = $item['ppno'];
        }

        return [
            'no_list'    => $res,
            'email_list' => !empty($emails) ? array_column($emails, 'email', 'staff_info_id') : []
        ];
    }

    /**
     * 创建同步至SAP单据
     * @param array $request_data 同步SAP信息组
     * @return array|mixed
     */
    public function createSapOrder($request_data)
    {
        $item = $this->getSapOrderItems($request_data);
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SupplierInvoiceBundleMaintainRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         <SupplierInvoice actionCode="01" ItemListCompleteTransmissionIndicator="true">
            <BusinessTransactionDocumentTypeCode>004</BusinessTransactionDocumentTypeCode>
            <MEDIUM_Name>' . $request_data['ppno'] . '</MEDIUM_Name>
            <Date>' . $request_data['apply_date'] . '</Date>
            <ReceiptDate>' . $request_data['apply_date'] . '</ReceiptDate>
            <TransactionDate>' . $request_data['approve_at'] . '</TransactionDate>
            <DocumentItemGrossAmountIndicator>false</DocumentItemGrossAmountIndicator>
            <GrossAmount currencyCode="' . $request_data['currency_text'] . '">' . $request_data['receipt_amount'] . '</GrossAmount>
            <TaxAmount currencyCode="' . $request_data['currency_text'] . '">' . $request_data['ticket_amount_tax'] . '</TaxAmount>
            <Status>
                <DataEntryProcessingStatusCode>3</DataEntryProcessingStatusCode>
            </Status>
            <CustomerInvoiceReference actionCode="01">
                <BusinessTransactionDocumentReference>
                    <ID>' . $request_data['ppno'] . '</ID>
                    <TypeCode>28</TypeCode>
                </BusinessTransactionDocumentReference>
            </CustomerInvoiceReference>
            <BuyerParty actionCode="01">
                <PartyKey>
                    <PartyTypeCode>200</PartyTypeCode>
                    <PartyID>' . $request_data['sap_company_id'] . '</PartyID>
                </PartyKey>
            </BuyerParty>
            <SellerParty actionCode="01">
                <PartyKey>
                    <PartyTypeCode>147</PartyTypeCode>
                    <PartyID>' . $request_data['sap_supplier_no'] . '</PartyID>
                </PartyKey>
            </SellerParty>
            ' . $item . '
            <a3ok:FlashContractNo>' . $request_data['pono'] . '</a3ok:FlashContractNo>
         </SupplierInvoice>
      </glob:SupplierInvoiceBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        //接口传输需要用到sap_user_id_2的配置信息，所以这里调取普通付款的SapService服务层传输
        $this->logger->info('purchase-payment-sap-data:post_xml====== '.$post_xml);
        $return_xml = SapService::getInstance()->httpRequestXml('/sap/managesupplierinvoicein', $post_xml);
        $this->logger->info('purchase-payment-sap-data:return_xml===== ' . $return_xml);

        preg_match_all("/\<SupplierInvoice\>(.*?)\<\/SupplierInvoice\>/s", $return_xml, $purchase_payments);
        foreach ($purchase_payments[1] as $k => $purchase_payment) {
            preg_match_all("/\<BusinessTransactionDocumentID\>(.*?)\<\/BusinessTransactionDocumentID\>/", $purchase_payment, $business_transaction_document_id);
            preg_match_all("/\<UUID\>(.*?)\<\/UUID\>/", $purchase_payment, $uuid);
        }

        $return_data = [];
        if (isset($business_transaction_document_id[1][0])) {
            $return_data = [
                'business_transaction_id' => trim($business_transaction_document_id[1][0]) ?? '',
                'uuid' => trim($uuid[1][0]) ?? '',
            ];
        }

        //记录请求日志
        $log_model = new RequestSapLog();
        $log_model->save([
            'uuid' => $return_data['uuid'] ?? '',
            'order_code' => $request_data['ppno'],
            'type' => 9,
            'request_data' => $post_xml,
            'response_data' => $return_xml ?? '',
            'create_at' => date('Y-m-d H:i:s'),
        ]);

        return $return_data;
    }

    /**
     * 获取同步至SAP明细信息
     * @param array $request_data 同步SAP信息组
     * @return string
     */
    private function getSapOrderItems($request_data)
    {
        $item = '';
        if (!empty($request_data['purchase_payment_receipt'])) {
            $country_code = get_country_code();
            foreach ($request_data['purchase_payment_receipt'] as $receipt) {
                $item .= '<Item actionCode="01">
                <BusinessTransactionDocumentItemTypeCode>002</BusinessTransactionDocumentItemTypeCode>
                <Quantity unitCode="EA">' . $receipt['total'] . '</Quantity>
                <SHORT_Description languageCode="ZH">' . $receipt['product_desc'] . '</SHORT_Description>
                <NetUnitPrice>
                    <Amount currencyCode="' . $request_data['currency_text'] . '">' . $receipt['not_tax_price'] . '</Amount>
                    <BaseQuantity>' . $receipt['no_tax_num'] . '</BaseQuantity>
                    <BaseQuantityTypeCode>EA</BaseQuantityTypeCode>
                </NetUnitPrice>
                <Product actionCode="01">
                    <CashDiscountDeductibleIndicator>true</CashDiscountDeductibleIndicator>
                    <ProductCategoryIDKey>
                        <ProductCategoryInternalID>' . $receipt['wrs_code'] . '</ProductCategoryInternalID>
                    </ProductCategoryIDKey>
                    <ProductKey>
                        <ProductTypeCode>' . $request_data['product_type_code'] . '</ProductTypeCode>
                        <ProductIdentifierTypeCode>1</ProductIdentifierTypeCode>';
                        $receipt['product_option_code'] ? $item .= '<ProductID>' . ($request_data['purchase_type'] == PurchaseEnums::PURCHASE_TYPE_COST ? '' : $receipt['product_option_code']) . '</ProductID>' : $item;
                    $item .= '</ProductKey>
                </Product>
                <ProductTax actionCode="01">
                    <ProductTaxationCharacteristicsCode listID="' . $country_code . '">' . $receipt['vat7_rate'] . '</ProductTaxationCharacteristicsCode>
                    <CountryCode>' . $country_code . '</CountryCode>
                    <WithholdingTaxationCharacteristicsCode listID="' . $country_code . '" listAgencyID="' . $receipt['list_agency_id'] . '">' . $receipt['wht_ratio'] . '</WithholdingTaxationCharacteristicsCode>
                </ProductTax>';

                //非服务类才有关联采购订单信息
                if ($request_data['purchase_type'] != PurchaseEnums::PURCHASE_TYPE_SERVICE) {
                    $item .= '<PurchaseOrderReference actionCode="01">
                        <BusinessTransactionDocumentReference>
                            <ID>' . $request_data['sap_order_no'] . '</ID>
                            <ItemID>' . $receipt['sap_item_id'] . '</ItemID>
                        </BusinessTransactionDocumentReference>
                    </PurchaseOrderReference>';
                } else {
                    //不关联采购订单的要传递成本分摊
                    $item .= '<AccountingCodingBlockDistribution AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true" actionCode="01">
                        <AccountingCodingBlockAssignment actionCode="01">
                        <AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>
                        <GeneralLedgerAccountAliasCode>' . $receipt['ledger_account'] . '</GeneralLedgerAccountAliasCode>
                        <CostCentreID>' . $receipt['cost_center_name'] . '</CostCentreID>
                        </AccountingCodingBlockAssignment>
                    </AccountingCodingBlockDistribution>';
                }

                $item .= '</Item>';
            }
        }
        return $item;
    }
}
