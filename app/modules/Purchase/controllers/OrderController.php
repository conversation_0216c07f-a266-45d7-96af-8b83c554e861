<?php

namespace App\Modules\Purchase\Controllers;

use App\Library\Enums;
use App\Library\Enums\PurchaseEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Purchase\Services\PayFlowService;
use App\Modules\Purchase\Services\VendorService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;
use App\Modules\Setting\Services\PaymentModeService;

class OrderController extends BaseController
{

    /**
     * 采购订单新增/重新提交: 获得默认值
     * @Permission(action='my.order.apply')
     *
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $res = OrderService::getInstance()->defaultData($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 新增采购订单
     *
     * @Permission(action='my.order.apply')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();

        $this->logger->info('原始请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        // 执行参数校验
        OrderService::getInstance()->addParamValidate($data);

        $lock_key = md5('purchase_order_add_' . $data['pa_id']);
        $res = $this->atomicLock(function() use ($data) {
            return OrderService::getInstance()->saveOne($data, $this->user);
        }, $lock_key, 10);
        $this->logger->info('响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);

    }


    /**
     * 采购订单修改
     * @Permission(action='my.order.apply')
     *
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function updateAction()
    {
        $data = $this->request->get();
        $this->logger->info("我的申请-采购订单修改[请求参数]: ". json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            $data = array_only($data,array_keys(OrderService::$validate_update_param));
            Validation::validate($data, OrderService::$validate_update_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁
        $lock_key = md5("purchase_order_my_apply_update_{$data['id']}_{$this->user['id']}");
        $res = $this->atomicLock(function() use ($data) {
            return OrderService::getInstance()->updateTotal($data, $this->user, BaseService::LIST_TYPE_APPLY);
        }, $lock_key, 10);

        $this->logger->info("我的申请-采购订单修改[返回参数]: ". json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购订单交货日期修改
     * @Permission(action='my.order.apply')
     *
     * @return Response|ResponseInterface
     */
    public function updateDeliveryDateAction()
    {
        $data = $this->request->get();

        $this->logger->info("采购订单交货日期修改[请求参数]: ". json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            $data = array_only($data,array_keys(OrderService::$validate_update_delivery_date_param));
            Validation::validate($data, OrderService::$validate_update_delivery_date_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = OrderService::getInstance()->updateDeliveryDate($data, $this->user);

        $this->logger->info("采购订单修改[返回参数]: ". json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 采购申请单获取修改日志
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getUpdateLogAction()
    {
        $data = $this->request->get();
        try {
            $data = array_only(
                $data,
                array_keys(
                    [
                        'id' => 'Required|IntGe:1'
                    ]
                )
            );
            Validation::validate(
                $data,
                [
                    'id' => 'Required|IntGe:1'
                ]
            );
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = OrderService::getInstance()->getUpdateLog($data['id'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    /**
     * 我的申请-采购订单-列表
     *
     * @Permission(action='my.order.search')
     * @return Response|ResponseInterface
     */
    public function myListAction()
    {
        $params = $this->request->get();
        $res = OrderService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 我的审核-采购订单-列表
     *
     * @Permission(action='audit.order.search')
     * @return Response|ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res = OrderService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 数据查询-采购订单-列表
     * @Permission(action='data.order.search')
     *
     * @return Response|ResponseInterface
     * @throws Exception\BusinessException
     */
    public function queryListAction()
    {
        $params = $this->request->get();
        if (isset($params['export']) && $params['export'] == 1) {
            $res = OrderService::getInstance()->export($params, $this->user, BaseService::LIST_TYPE_DATA);
        } else {
            $res = OrderService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购订单-撤销
     *
     * @Permission(action='my.order.cancel')
     * @return mixed
     * @throws \Exception
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            $validate_cancel = [
                'id' => 'Required|IntGe:1',
                'note' => 'Required|StrLenGeLe:1,100'
            ];
            Validation::validate($data, $validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('purchase_order_cancel_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id, $note) {
            return (new PayFlowService(Enums::WF_PURCHASE_ORDER))->cancel($id, $note, $this->user);
        }, $lock_key, 10);

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 采购订单-审核
     * 
     * @Permission(action='audit.order.audit')
     */
    public function auditAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');
        $pass_or_not = $this->request->get('pass_or_not', 'int');
        $note = $this->request->get('note', 'trim');

        $this->logger->info('purchase_order_request_params: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);

            // vat/sst、wht 校验
            $biz_params = $data['data'] ?? [];
            if (!empty($biz_params) && $biz_params['can_edit']) {
                $product = $biz_params['product'] ?? $biz_params['product_v1'] ?? [];
                $vat_config = EnumsService::getInstance()->getVatRateValueItem();
                $wht_config = EnumsService::getInstance()->getWhtRateMap();

                // 公司列表配置
                $company_list = EnumsService::getInstance()->getSysDepartmentCompanyIds();

                $can_edit_meta_fields = $biz_params['can_edit_fields']['meta'] ?? [];
                foreach ($product as $item) {
                    if (in_array('vat7_rate', $can_edit_meta_fields) && !in_array($item['vat7_rate'], $vat_config)) {
                        throw new ValidationException($this->t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    // 有预扣税则校验wht
                    if ($biz_params['is_tax'] == PurchaseEnums::IS_WITHHOLDING_TAX_YES) {
                        $_wht_info = $wht_config[$item['wht_cate']] ?? [];
                        if (in_array('wht_cate', $can_edit_meta_fields) && empty($_wht_info)) {
                            throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                        }

                        if (in_array('wht_rate', $can_edit_meta_fields) && empty($_wht_info['rate_list'][$item['wht_rate']])) {
                            throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                        }
                    }

                    // 采购类型为库存类 且 费用公司为快递公司时, 明细行的产品编号为必填 v15814
                    $purchase_type = $biz_params['purchase_type'] ?? '';
                    $cost_company = $biz_params['cost_company'] ?? '';
                    $product_option_code_length = mb_strlen($item['product_option_code'] ?? '');
                    if ($purchase_type == PurchaseEnums::PURCHASE_TYPE_STOCK && $cost_company == $company_list['FlashExpress'] && $product_option_code_length < 1) {
                        throw new ValidationException($this->t->_('purchase_order_stock_product_option_code_error'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5("purchase_order_{$pass_or_not}_{$id}_{$this->user['id']}");
        $res = $this->atomicLock(function () use ($id, $note, $pass_or_not, $data) {
            $service = new PayFlowService(Enums::WF_PURCHASE_ORDER, $data['data'] ?? []);
            if ($pass_or_not == 1) {
                //通过
                return $service->approve($id, $note, $this->user);
            } else if($pass_or_not == 2) {
                //驳回
                return $service->reject($id, $note, $this->user);
            }
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);

    }

    /**
     * 无权限公用-采购订单-查看
     *
     * @Token
     */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        $is_filter = $this->request->get("flag");
        if(empty($is_filter)){
            $is_filter = false;
        }else{
            $is_filter = true;
        }
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->getDetail($id,$this->user['id'],FALSE,$is_filter);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购订单-查看
     *
     * @Permission(action='my.order.view')
     */
    public function myDetailAction(){
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的审核-采购订单-查看
     *
     * @Permission(action='audit.order.view')
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = OrderService::getInstance()->getDetail($data['id'], $this->user['id'],false,false,true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-采购订单-查看
     *
     * @Permission(action='data.order.view')
     */
    public function queryDetailAction(){
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->getDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购订单-下载
     * 
     * @Permission(action='my.order.download')
     */
    public function myDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        $pdf_format = isset($data['pdf_format']) ? $data['pdf_format'] : 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = OrderService::getInstance()->download($id, $pdf_format, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的审核-采购订单-下载
     * 
     * @Permission(action='audit.order.download')
     */
    public function auditDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        $pdf_format = isset($data['pdf_format']) ? $data['pdf_format'] : 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = OrderService::getInstance()->download($id, $pdf_format, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-采购订单-下载
     * 
     * @Permission(action='data.order.download')
     */
    public function queryDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        $pdf_format = isset($data['pdf_format']) ? $data['pdf_format'] : 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = OrderService::getInstance()->download($id, $pdf_format);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请单列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicListAction()
    {
        $params = $this->request->get();
        $res = OrderService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 采购订单的产品明细数量修改后 - 重新计算
     * @Token
     */
    public function calculateAction(){
        $data = $this->request->get();
        try {
            Validation::validate($data, OrderService::$validate_products_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        OrderService::getInstance()->handleProductsData($data,false, false);
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }

    /**
     * 供应商查询
     * @Token
     */
    public function searchVendorAction() {
        $data = $this->request->get();
        $search_name = $this->request->get('search_name', 'trim');
        if(is_null($search_name)) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }

        $res = VendorService::getInstance()->searchVendorList($data['search_name']);
        if($res['code'] == ErrCode::$SUCCESS){
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 根据产品名模糊搜索产品名称和id
     * @Token
     */
    public function getProductNameListAction(){
        $product_name = $this->request->get('product_name','string');
        $res = OrderService::getInstance()->productNameList($product_name);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 采购单公共枚举
     * @Token
     */
    public function getPurchaseParamsAction()
    {
        $res = OrderService::getInstance()->getPurchaseParams();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购合同列表
     * @Token
     */
    public function getPurchaseContractListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate(
                $params,
                [
                    'cno' => 'Required|StrLenGe:8|>>>:'.$this->t['purchase_contract_no_error_01']
                ]
            );
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = OrderService::getInstance()->getPurchaseContractList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询 - 是否预算
     * @Permission(action='data.order.release_budget')
     *
     * @return Response|ResponseInterface
     */
    public function releaseBudgetAction()
    {
        $data = $this->request->get();
        $this->logger->info('数据查询-采购订单修改[请求参数]: '. json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            $data = array_only($data,array_keys(OrderService::$validate_update_param));
            Validation::validate($data, OrderService::$validate_update_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁
        $lock_key = md5("purchase_order_data_search_release_budget_{$data['id']}_{$this->user['id']}");
        $res = $this->atomicLock(function() use ($data) {
            return OrderService::getInstance()->updateTotal($data, $this->user, BaseService::LIST_TYPE_DATA);
        }, $lock_key, 10);

        $this->logger->info('数据查询-采购订单修改[返回参数]: '. json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的申请-采购申请单关闭
     * @Permission(action='my.order.close')
     */
    public function closeAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, OrderService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->close($data, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-采购申请单关闭
     * @Permission(action='data.order.close')
     */
    public function dataCloseAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, OrderService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->close($data, $this->user, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    /**
     * 采购申请单获得页面初始枚举
     * @Token
     */
    public function getPageDefaultAction()
    {
        $res = OrderService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购订单-添加发货记录
     * @Permission(action='my.order.apply')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deliveryAddAction()
    {
        $params = $this->request->get();
        //过滤非必要参数
        $params = params_filter($params, OrderService::$not_must_delivery_add);
        //参数校验
        Validation::validate($params, OrderService::$validate_delivery_add);
        //执行添加
        $res = OrderService::getInstance()->deliveryAdd($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    /**
     * 采购订单-查看发货记录
     * @Permission(menu='purchase')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deliveryDetailAction()
    {
        $params = $this->request->get();
        //参数校验
        Validation::validate($params, OrderService::$validate_delivery_detail);
        //执行添加
        $res = OrderService::getInstance()->deliveryDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-采购订单-编辑
     * @Permission(action='data.order.update')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87545
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dataUpdateAction()
    {
        $data = trim_array($this->request->get());
        Validation::validate($data, OrderService::$validate_data_update);
        // 加锁
        $lock_key = md5("purchase_data_order_update_{$data['id']}_{$this->user['id']}");
        $res = $this->atomicLock(function () use ($data) {
            return OrderService::getInstance()->dataUpdate($data, $this->user);
        }, $lock_key, 15);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);

    }
}
