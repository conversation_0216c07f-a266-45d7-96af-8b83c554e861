<?php

namespace App\Modules\Purchase\Controllers;

use App\Library\Enums;
use App\Library\Enums\PurchaseEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\PayFlowService;
use App\Modules\Purchase\Services\PaymentService;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PaymentController extends BaseController
{

    /**
     * 采购付款申请单-新增 / 重新提交 获取默认值
     *
     * @Permission(action='my.payment.apply')
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $param = $this->request->get();
        $res = PaymentService::getInstance()->defaultData($param, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的申请-采购付款申请单-新增
     *
     * @Permission(action='my.payment.apply')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();

        //执行参数校验
        PaymentService::getInstance()->validation($data);

        $res = PaymentService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取入库通知单内容
     *
     * @Permission(action='my.payment.apply')
     * @return Response|ResponseInterface
     */
    public function getInboundAction()
    {
        $data = $this->request->get();
        try {
            $validate_cancel = [
                'no'        => 'Required|Str',
                'mach_code' => 'Required|StrLenGeLe:1,128'
            ];
            Validation::validate($data, $validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = PaymentService::getInstance()->getInbound($data);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = PaymentService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购付款申请单-详情
     * @api https://yapi.flashexpress.pub/project/133/interface/api/7532
     * @Permission(action='my.payment.view')
     * @return Response|ResponseInterface
     */
    public function myDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = PaymentService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的审核-采购付款申请单-详情
     *
     * @Permission(action='audit.payment.view')
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = PaymentService::getInstance()->getDetail($id, $this->user['id'], false, BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-采购付款申请单-详情
     *
     * @Permission(action='data.payment.view')
     * @return Response|ResponseInterface
     */
    public function queryDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = PaymentService::getInstance()->getDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购付款申请单-列表
     * @api https://yapi.flashexpress.pub/project/133/interface/api/7424
     * @Permission(action='my.payment.search')
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function myListAction()
    {
        $params = $this->request->get();
        if (isset($params['export_invoice']) && $params['export_invoice'] == 1) {
            $res = PaymentService::getInstance()->export_invoice($params, $this->user, BaseService::LIST_TYPE_APPLY);
        }else{
            $res = PaymentService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_APPLY);
        }
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的审核-采购付款申请单-列表
     *
     * @Permission(action='audit.payment.search')
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        if (isset($params['export']) && $params['export'] == 1) {
            $res = PaymentService::getInstance()->export($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        } else {
            $res = PaymentService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        }

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 数据查询-采购付款申请单-列表
     *
     * @Permission(action='data.payment.search')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function queryListAction()
    {
        $params = trim_array($this->request->get());

        // 增加参数校验
        $validate_params = [];
        if (!empty($params['pay_date_start'])) {
            $validate_params['pay_date_start'] = 'Date|>>>:params error[pay_date_start YYYY-MM-DD]';
        }

        if (!empty($params['pay_date_end'])) {
            $validate_params['pay_date_end'] = 'Date|>>>:params error[pay_date_end YYYY-MM-DD]';
        }

        if (!empty($params['approved_start_date'])) {
            $validate_params['approved_start_date'] = 'Date|>>>:params error[approved_start_date YYYY-MM-DD]';
        }

        if (!empty($params['approved_end_date'])) {
            $validate_params['approved_end_date'] = 'Date|>>>:params error[approved_end_date YYYY-MM-DD]';
        }

        Validation::validate($params, $validate_params);

        if (isset($params['export']) && $params['export'] == 1) {
            $res = PaymentService::getInstance()->export($params, $this->user, BaseService::LIST_TYPE_DATA);
        } else if (isset($params['export_invoice']) && $params['export_invoice'] == 1) {
            $res = PaymentService::getInstance()->export_invoice($params, $this->user, BaseService::LIST_TYPE_DATA);
        } else {
            $res = PaymentService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购付款申请单-撤销
     *
     * @Permission(action='my.payment.cancel')
     * @return Response|ResponseInterface
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            $validate_cancel = [
                'id' => 'Required|IntGe:1',
                'note' => 'Required|StrLenGeLe:1,100'
            ];
            Validation::validate($data, $validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = PaymentService::getInstance()->cancel($id);
        if ($res['code'] != ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message']);
        }
        $res = (new PayFlowService(Enums::WF_PURCHASE_PAYMENT))->cancel($id, $note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购付款申请单-审核
     *
     * @Permission(action='audit.payment.audit')
     * @return Response|ResponseInterface
     */
    public function auditAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $pass_or_not = $this->request->get("pass_or_not",'int');
        $note = $this->request->get('note', 'trim');

        $this->logger->info('purchase_payment_request_params: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);

            // wht、可抵扣税 校验
            $biz_params = $data['data'] ?? [];
            if (!empty($biz_params) && $biz_params['can_edit']) {
                $product = $biz_params['receipt'] ?? $biz_params['receipt_v1'] ?? [];
                $wht_config = EnumsService::getInstance()->getWhtRateMap();
                $deductible_config = EnumsService::getInstance()->getDeductibleRateValueItem();

                $can_edit_meta_fields = $biz_params['can_edit_fields']['meta'] ?? [];
                foreach ($product as $item) {
                    if (in_array('deductible_vat_tax', $can_edit_meta_fields) && !in_array($item['deductible_vat_tax'], $deductible_config)) {
                        throw new ValidationException($this->t->_('deductable_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                    }

                    $_wht_info = $wht_config[$item['wht_type']] ?? [];
                    if (in_array('wht_type', $can_edit_meta_fields) && empty($_wht_info)) {
                        throw new ValidationException($this->t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (in_array('wht_ratio', $can_edit_meta_fields) && empty($_wht_info['rate_list'][$item['wht_ratio']])) {
                        throw new ValidationException($this->t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $model = new PayFlowService(Enums::WF_PURCHASE_PAYMENT,$data['data']??[]);

        if($pass_or_not == 1){
            //通过
            $res = $model->approve($id,$note,$this->user);
        }else if($pass_or_not == 2){
            //拒绝
            $res = PaymentService::getInstance()->cancel($id);
            if ($res['code'] != ErrCode::$SUCCESS) {
                return $this->returnJson($res['code'], $res['message']);
            }
            $res = $model->reject($id,$note,$this->user);
        }
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购付款申请单-下载
     *
     * @Permission(action='my.payment.download')
     * @return Response|ResponseInterface
     */
    public function myDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->download($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的审核-采购付款申请单-下载
     *
     * @Permission(action='audit.payment.download')
     * @return Response|ResponseInterface
     */
    public function auditDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->download($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-采购付款申请单-下载
     *
     * @Permission(action='data.payment.download')
     * @return Response|ResponseInterface
     */
    public function queryDownloadAction(){
        $data = $this->request->get();
        try {
            Validation::validate($data, ['id' => 'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->download($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 付款支付-采购付款申请单-支付
     *
     * @Permission(action='purchase.pay.pay')
     * @return Response|ResponseInterface
     */
    public function payAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1',
                'pass_or_not' => 'Required|IntIn:' . PurchaseEnums::PAYMENT_PAY_OPTION_PAID . ',' . PurchaseEnums::PAYMENT_PAY_OPTION_UNPAID,
                'real_pay_at' => 'IfIntEq:pass_or_not,' . PurchaseEnums::PAYMENT_PAY_OPTION_PAID . '|Required|Date',
                'note' => 'IfIntEq:pass_or_not,' . PurchaseEnums::PAYMENT_PAY_OPTION_UNPAID . '|Required|StrLenGeLe:1,1000'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->pay($data['id'], $data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购申请单列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicListAction()
    {
        $params = $this->request->get();
        $res = PaymentService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 获得Wht类别
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getWhtAction(){
        $data = EnumsService::getInstance()->getFormatWhtRateConfig();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 采购付款申请单计算
     * @Token
     */
    public function calculateAction(){
        $data = $this->request->get();
        try {
            Validation::validate($data, ['receipt' => 'Required|Arr|ArrLenGe:1|>>>:'. $this->t->_('purchase_pay_receipt_arr_err')]);

            Validation::validate($data, PaymentService::$validate_receipt_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        PaymentService::getInstance()->handleProductData($data,false);
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }

    /**
     * 判断是否有一样的付款申请单-
     * @Token
     */
    public function isHaveSamePaymentAction(){
        $validate = [
            'po_id' =>'Required|Int',
            'receipt_amount' =>'Required|FloatGt:0'
        ];
        $data = $this->request->get();
        try {
            Validation::validate($data, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = PaymentService::getInstance()->isHaveSamePaymentByPo($data);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 根据产品名模糊搜索产品名称和id
     * @Token
     */
    public function getProductNameListAction(){
        $product_name = $this->request->get('product_name','string');
        $res = PaymentService::getInstance()->productNameList($product_name);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }


    /**
     * 校验验收单是否可以关联
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47569
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkAcceptanceAction()
    {
        $data            = trim_array($this->request->get());
        $validate_cancel = [
            'no'         => 'Required|Str',
            'po'         => 'Required|Str',
        ];
        Validation::validate($data, $validate_cancel);
        $res = PaymentService::getInstance()->checkAcceptance($data);
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 申请单查询添加补充附件
     * @Permission(action='data.payment.addfile')
     * @return mixed
     */
    public function addSupplementInvoiceAction()
    {
        $data = $this->request->get();

        try {
            Validation::validate($data, BaseService::$validate_supplement);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->addFile($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], isset($res['data']) ? $res['data'] : []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 申请单添加补充附件
     * @Permission(action='my.payment.addfile')
     * @return mixed
     */
    public function applySupplementInvoiceAction()
    {
        $data = $this->request->get();

        try {
            Validation::validate($data, BaseService::$validate_supplement);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->addFile($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], isset($res['data']) ? $res['data'] : []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购支付-采购付款申请单-列表
     *
     * @Permission(action='purchase.pay.search')
     * @return Response|ResponseInterface
     */
    public function payListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$payment_pay_list_validate);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购付款-支付详情
     *
     * @Permission(action='purchase.pay.view')
     * @return Response|ResponseInterface
     */
    public function payDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ['id' => 'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = PaymentService::getInstance()->getDetail($data['id'], $this->user['id'], false, BaseService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的审核-采购付款申请单-下载
     *
     * @Permission(action='purchase.pay.download')
     * @return Response|ResponseInterface
     */
    public function payDownloadAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ['id' => 'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = PaymentService::getInstance()->download($data['id'], $this->user['id'], BaseService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}

