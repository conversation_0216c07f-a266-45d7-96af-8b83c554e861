<?php

namespace App\Modules\Purchase\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentAddService;
use App\Modules\Purchase\Services\ApplyService;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\CategoryService;
use App\Modules\Purchase\Services\DepartmentService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Purchase\Services\PayFlowService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ApplyController extends BaseController
{
    // 产品明细单词最多提交 50 条
    static private $purchase_product_item_max_count = 50;

    /**
     * 采购申请单添加
     * @Permission(action='my.apply.apply')
     * @API https://yapi.flashexpress.pub/project/133/interface/api/20398
     *
     * @return Response|ResponseInterface
     */
    public function addAction()
    {
        $data = $this->request->get();
        $this->getDI()->get('logger')->info('原始请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            // 费用所属公司 = FE, 则费用所属中心必填
            $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
            if (isset($data['cost_company_id']) && in_array($data['cost_company_id'], $cost_company_id)) {
                ApplyService::$validate_param['products[*].cost_center_name'] = 'Required|StrLenGeLe:1,256|>>>:'.$this->t['cost_center_required_error'];
            } else {
                ApplyService::$validate_param['products[*].cost_center_name'] = 'StrLenGeLe:0,256|>>>:cost center error';
            }

            Validation::validate($data, ApplyService::$validate_param);

            // 产品明细最多50条
            if (count($data['products']) > self::$purchase_product_item_max_count) {
                throw new ValidationException($this->t['purchase_product_item_verification_error'], ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ApplyService::getInstance()->saveOne($data, $this->user);

        $this->getDI()->get('logger')->info('响应参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购申请单修改
     * @Permission(action='my.apply.apply')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function updateAction()
    {
        $data = $this->request->get();
        try {
            $data = array_only($data, array_keys(ApplyService::$validate_update_param));
            Validation::validate($data, ApplyService::$validate_update_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁
        $lock_key = md5("purchase_apply_my_apply_update_{$data['id']}_{$this->user['id']}");
        $res = $this->atomicLock(function() use ($data) {
            return ApplyService::getInstance()->updateTotal($data, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 采购申请单获取修改日志
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getUpdateLogAction(){
        $data = $this->request->get();
        try {
            $data = array_only($data,array_keys(ApplyService::$validate_detail));
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ApplyService::getInstance()->getUpdateLog($data['id'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }




    /**
     * 采购申请单获得默认值
     * @Permission(action='my.apply.apply')
     *
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $res = ApplyService::getInstance()->defaultData($this->user);
        if($res['code'] == ErrCode::$SUCCESS){
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请单列表
     * @Permission(action='my.apply.search')
     *
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = ApplyService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 采购申请单详情
     * @Permission(action='my.apply.view')
     *
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    /**
     * 采购申请单详情 - 无权限公用
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        $flag = $this->request->get('flag');
        try {
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->getDetail($id,$this->user['id'],false ,$flag);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款撤销
     * @Permission(action='my.apply.cancel')
     *
     * @return Response|ResponseInterface
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, ApplyService::$validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('purchase_apply_cancel_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id, $note) {
            return (new PayFlowService(Enums::WF_PURCHASE_APPLY))->cancel($id, $note, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购申请单-审核列表
     * @Permission(action='audit.apply.search')
     * @return Response|ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res = ApplyService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }


    /**
     * 采购申请单-审核详情
     * @Permission(action='audit.apply.view')
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->getDetail($id, $this->user['id'],false,null,true);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购申请单-审核
     * @Permission(action='audit.apply.audit')
     */
    public function auditAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $flag = $this->request->get("flag",'int');
        $note = $this->request->get('note', 'trim');

        $this->logger->info('purchase_apply_request_params: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, ApplyService::$validate_audit);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5("purchase_apply_{$flag}_{$id}_{$this->user['id']}");
        $res      = $this->atomicLock(function () use ($id, $note, $flag, $data) {
            $service = new PayFlowService(Enums::WF_PURCHASE_APPLY,$data['data'] ?? []);
            if (empty($flag)) {
                //通过
                return $service->approve($id, $note, $this->user);
            } else {
                //拒绝
                return $service->reject($id, $note, $this->user);
            }
        }, $lock_key, 10);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 返回产品一级分类+二级分类(财务编码)+产品名称+规格+产品编码
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getProductListAction()
    {
        try {
            $data = $this->request->get();
            Validation::validate($data, ApplyService::$validate_product);
            $productList = ApplyService::getInstance()->productList($data);
            return $this->returnJson(ErrCode::$SUCCESS, '', $productList);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error(
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function getDepartmentListAction()
    {
        $pid = intval($this->request->get("id"));

        if(empty($pid)){
            $pid = 999;
        }

        $res = DepartmentService::getInstance()->getList($pid);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 根据部门ID获得PcCode
     * @Token
     * @return
     */
    public function getPcCodeAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('department_id');
        try {
            Validation::validate($data, ApplyService::$validate_department);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->getPcCode($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请单-数据列表
     * @Permission(action='data.apply.search')
     * @return Response|ResponseInterface
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $res = ApplyService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }


    /**
     * 采购申请单-数据详情
     * @Permission(action='data.apply.view')
     * @return Response|ResponseInterface
     */
    public function dataDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        //uid=null，不用判断权限，能看所有
        $res = ApplyService::getInstance()->getDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请已通过下载
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function downloadDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ApplyService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        //uid=null，不用判断权限，能看所有
        $res = ApplyService::getInstance()->download($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请单-数据下载
     * @Permission(action='data.apply.export')
     * @return Response|ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $res = ApplyService::getInstance()->export($params,BaseService::LIST_TYPE_DATA, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 采购申请单 - 征询回复列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicListAction()
    {
        $params = $this->request->get();
        $res = ApplyService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购申请单计算
     * @Token
     */
    public function calculateAction(){
        $data = $this->request->get();
        try {
            Validation::validate($data, ApplyService::$validate_products_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        //uid=null，不用判断权限，能看所有
        ApplyService::getInstance()->handleProductsData($data,false);
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }

    /**
     * 获取核算科目
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/25558
     * @return Response|ResponseInterface
     */
    public function getAllLedgerAccountsAction(){
        $params = $this->request->get();
        $res = LedgerAccountService::getInstance()->getList([], $params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }


    /**
     * 根据预算科目id获得核算科目名称和id
     * @Token
     */
    public function getLedgerAccountAction(){
        $budget_id = $this->request->get('budget_id','int');
        $product_id = $this->request->get('product_id','int');
        $res = LedgerAccountService::getInstance()->getLedgerAccountByBudgetIdOrProdcutId($budget_id,$product_id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 根据产品名模糊搜索产品名称和id
     * @Token
     */
    public function getProductNameListAction(){
        $product_name = $this->request->get('product_name','string');
        $res = ApplyService::getInstance()->productNameList($product_name);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 获取采购类型列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/29063
     * @return Response|ResponseInterface
     */
    public function get_purchase_typeAction(){
        $res = ApplyService::getInstance()->purchase_type_list();
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
    }

    /**
     * 根据采购类型-获取财务分类
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/53263
     * @return Response|ResponseInterface
     */
    public function get_wrs_codeAction(){
        $type = $this->request->get('purchase_type','int');
        $res = ApplyService::getInstance()->purchase_wrs_code($type);
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
    }

    /**
     * 网点列表搜索
     * @Token
     */
    public function getStoreListAction()
    {
        $param = $this->request->get();

        try {
            Validation::validate($param, [
                'store_name' => 'Required|StrLenGeLe:1,255|>>>:store_name error',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = OrdinaryPaymentAddService::getInstance()->getStoreList($param, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 下载批量上传模板
     * @Permission(action='my.apply.apply')
     */
    public function downloadBatchTplAction()
    {
        $lock_key = md5('PurchaseApplyProductsDownloadBatchTpl_' . $this->user['id']);
        $res = $this->atomicLock(function() {
            return ApplyService::getInstance()->getDownloadTpl();
        }, $lock_key, 10);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量上传
     * @Permission(action='my.apply.apply')
     *
     */
    public function batchUploadAction()
    {
        try {
            $params = $this->request->get();
            $validate_params = [
                'cost_department' => 'Required|IntGe:1|>>>:param error[cost_department]',
                'cost_store' => 'Required|IntIn:1,2|>>>:param error[cost_store]',
            ];

            Validation::validate($params, $validate_params);

            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException('not found file');
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension ,['xlsx'])){
                throw new ValidationException('File format error');
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excelData = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    3 => \Vtiful\Kernel\Excel::TYPE_STRING,// 网点名称
//                    9 => \Vtiful\Kernel\Excel::TYPE_DOUBLE,// 税率
                ])
                ->setSkipRows(0)
                ->getSheetData();
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excelData)) {
                throw new ValidationException('data empty or read data failed');
            }

        }  catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 导入: 1. 文件格式校验; 2. 文件数据提取 3. 数据校验
        $lock_key = md5('purchase_apply_batch_upload_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excelData, $params){
             return ApplyService::getInstance()->batchUploadCheck($excelData, $params, $this->user);
        }, $lock_key, 20);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 采购申请-采购申请-新增-产品编号-选择
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/58879
     * @return Response|ResponseInterface
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ApplyService::$validate_search_barcode);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ApplyService::getInstance()->searchBarcode($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-采购申请单关闭
     * @Permission(action='my.apply.close')
     */
    public function closeAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ApplyService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->close($data, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-采购申请单关闭
     * @Permission(action='data.apply.close')
     */
    public function dataCloseAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ApplyService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ApplyService::getInstance()->close($data, $this->user, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    /**
     * 采购申请单获得页面初始枚举
     * @Token
     */
    public function getPageDefaultAction()
    {
        $res = ApplyService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据查询-采购申请单-修改
     * @Permission(action='data.apply.edit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70352
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $data = $this->request->get();
        $data = array_only($data, array_keys(ApplyService::$validate_update_param));
        Validation::validate($data, ApplyService::$validate_update_param);
        // 加锁
        $lock_key = md5("purchase_data_apply_edit_{$data['id']}_{$this->user['id']}");
        $res      = $this->atomicLock(function () use ($data) {
            return ApplyService::getInstance()->updateTotal($data, $this->user, BaseService::LIST_TYPE_DATA);
        }, $lock_key, 5);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);

    }

    /**
     * 数据查询-采购申请单-编辑
     * @Permission(action='data.apply.update')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87542
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dataUpdateAction()
    {
        $data = trim_array($this->request->get());
        Validation::validate($data, ApplyService::$validate_data_update);
        // 加锁
        $lock_key = md5("purchase_data_apply_update_{$data['id']}_{$this->user['id']}");
        $res = $this->atomicLock(function () use ($data) {
            return ApplyService::getInstance()->dataUpdate($data, $this->user);
        }, $lock_key, 15);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);

    }
}
