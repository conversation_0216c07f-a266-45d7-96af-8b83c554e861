<?php

namespace App\Modules\ReserveFund\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\ReserveFund\Services\ApplyService;
use App\Modules\User\Models\AttachModel;

class ReserveFundApply extends Base implements BankFlowModelInterface, PayModelInterface
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('reserve_fund_apply');


        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_RESERVE_FUND_APPLY . " and deleted=0",
                ],
                "alias"  => "File",
            ]
        );
    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'rfano = :no:',
                'bind'       => ['no' => $no],
            ]
        );
    }


    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'rfano in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind       = [
            'nos'        => $no,
            'status'     => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING,
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions         = 'rfano in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind'       => $bind,
            ]
        );
    }


    public function getFormatData()
    {
        return [
            'oa_value'   => $this->id,
            'oa_type'    => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND,
            'no'         => $this->rfano,
            'amount'     => bcdiv($this->amount, 1000, 2),
            'currency'   => $this->currency,
            'status'     => $this->status,
            'pay_status' => $this->pay_status,
        ];
    }

    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found reserve_fund or reserve_fund pay_status is error');
        }


        $item                     = [];
        $item['pay_status']       = Enums::LOAN_PAY_STATUS_PAY;                       //是否已付款
        $item['return_status']    = Enums\ReserveFundReturnEnums::BACK_STATUS_NOT;    //支付时将归还状态变更为未归还
        $item['pay_id']           = $data['create_id'];
        $item['operation_remark'] = $data['ticket_no'];
        $item['real_pay_at']      = $data['date'];
        $item['pay_at']           = date("Y-m-d H:i:s");
        $item['pay_account']      = $data['bank_account'];
        $item['pay_bank']         = $data['bank_name'];
        $item['pay_from']         = 2;
        $item['updated_at']       = date("Y-m-d H:i:s");

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("备用金-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $sql  = 'update reserve_fund_apply set 
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         return_status=' . Enums\ReserveFundReturnEnums::BACK_STATUS_NOT . ',
                         pay_id="' . $data['create_id'] . '",
                         operation_remark="' . $data['ticket_no'] . '",
                         real_pay_at="' . $data['date'] . '",
                         pay_at="' . date("Y-m-d H:i:s") . '",
                         updated_at="' . date("Y-m-d H:i:s") . '",
                         pay_account="' . $data['bank_account'] . '",
                         pay_bank="' . $data['bank_name'] . '",
                         pay_from=2 where id in (' . implode(',', $ids) . ')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('备用金付款-批量更新失败==' . $sql);
        }
        return true;
    }


    public function cancel(array $user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY || $this->return_status != Enums\ReserveFundReturnEnums::BACK_STATUS_NOT) {
            throw new BusinessException('not found reserve_fund or reserve_fund pay_status is error');
        }


        $item                     = [];
        $item['pay_status']       = Enums::LOAN_PAY_STATUS_PENDING;    //是否已付款
        $item['pay_id']           = 0;
        $item['operation_remark'] = '';
        $item['real_pay_at']      = null;
        $item['pay_at']           = null;
        $item['pay_account']      = '';
        $item['pay_bank']         = '';
        $item['pay_from']         = 1;
        //12855 归还状态在支付时由0改为1,撤回时要由1改为0
        $item['return_status'] = Enums\ReserveFundReturnEnums::BACK_STATUS_INIT;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("备用金-撤销支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        return true;
    }

    public function getPayData()
    {
        $date_at = date('Y-m-d');
        $arr = [
            'oa_type'                      => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND,
            'no'                           => $this->rfano,
            'apply_staff_id'               => $this->create_id,
            'apply_staff_name'             => $this->create_name,
            'cost_department_id'           => $this->create_department_id,
            'cost_department_name'         => $this->create_department_name,
            'apply_date'                   => $this->apply_date,
            'pay_method'                   => Enums::PAYMENT_METHOD_BANK_TRANSFER,
            'currency'                     => $this->currency,
            'amount_total_no_tax'          => bcdiv($this->amount, 1000, 2),          //不含税金额
            'amount_total_vat'             => 0,                                      //税额
            'amount_total_have_tax'        => bcdiv($this->amount, 1000, 2),          //含税金额（含VAT含WHT）
            'amount_total_wht'             => 0,                                      //wht总计
            'amount_total_have_tax_no_wht' => bcdiv($this->amount, 1000, 2),          //含税金额总计（含VAT不含WHT）
            'amount_loan'                  => 0,                                      //冲减借款金额,
            'amount_reserve'               => 0,
            'amount_discount'              => 0,                                                      //折扣
            'amount_total_actually'        => bcdiv($this->amount, 1000, 2),                          //实付金额
            'amount_remark'                => $this->apply_reason,                                     //备注
            'default_planned_pay_date'     => $date_at,//应付日期
            'planned_pay_date'             => $date_at,//计划支付日期
        ];

        //费用公司id
        $arr['cost_company_id'] = SysDepartmentModel::getCompanyIdByDepartmentId($arr['cost_department_id']);
        //费用公司名字
        $arr['cost_company_name'] = '';
        if (!empty($arr['cost_company_id'])) {
            $arr['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($arr['cost_company_id']);
        }
        $arr['pays'] = [];

        $tmp                      = [];
        $tmp['bank_name']         = Enums::$bank_type[$this->payee_bank] ?? '';
        $tmp['bank_account']      = $this->payee_account;
        $tmp['bank_account_name'] = $this->payee_username;
        $tmp['amount']            = $arr['amount_total_actually'];
        $arr['pays'][]            = $tmp;

        return $arr;
    }

    public function getPayCallBackData($data)
    {
        /*$validate_pay_param = [
            'id' => 'Required|IntGe:1',
            'pass_or_not' => 'Required|IntIn:1,2',
            'real_pay_at' => 'IfIntEq:pass_or_not,1|Required|Date',
            'pay_bank' => 'IfIntEq:pass_or_not,1|Required|StrIn:Thai Military Bank,Siam Commercial Bank,Union Bank 1213,Union Bank 0429|>>>:payment bank error',
            'pay_account' => 'IfIntEq:pass_or_not,1|Required|StrIn:**********,**********,************,************|>>>:payment bank account error',
            'note' => 'IfIntEq:pass_or_not,2|Required|StrLenGeLe:1,1000',
        ];*/
        $pay_status         = $data['pay_status'];
        $new                = [];
        $new['pass_or_not'] = 2;
        if ($pay_status == Enums::LOAN_PAY_STATUS_PAY) {
            $new['pass_or_not'] = 1;
            $new['real_pay_at'] = $data['pay_bank_flow_date'] ?? date("Y-m-d");
            $new['pay_bank']    = $data['pay_bank_name'];
            $new['pay_account'] = $data['pay_bank_account'];
        } else {
            $new['note']        = $data['not_pay_reason'];
            $new['real_pay_at'] = null;
        }

        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     *
     * @param $no
     * @param $pay_id
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no, $pay_id)
    {
        $myself = self::findFirst(
            [
                'conditions' => 'rfano = :rfano: and status = :status: and pay_status = :pay_status:',
                'bind'       => [
                    'rfano'      => $no,
                    'status'     => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PENDING,
                ],
            ]
        );
        if (!isset($myself->create_id)) {
            return [];
        }
        $apply_info                     = ApplyService::getInstance()->getUserMetaFromBi($myself->create_id);
        $bank_info                      = [];
        $bank_info['bank_name']         = $apply_info['payee_bank_text'] ?? '';
        $bank_info['bank_account']      = $apply_info['payee_account'] ?? '';
        $bank_info['bank_account_name'] = $apply_info['payee_username'] ?? '';
        return [
            'type'  => 1,
            'items' => $bank_info,
        ];
    }

    //打上支付模块标记
    public function updatePayTag(): bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module' => 1]) === false) {
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'rfano = :no: AND status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind'       => [
                'no'            => $data['payment_no'],
                'status'        => Enums::WF_STATE_APPROVED,
                'pay_status'    => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 本模块的单据, 不可变更收款人信息
        if (empty($main_model)) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($main_model->toArray(),
                JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info  = $data['pay'][0] ?? [];
        $bank_type = array_flip(Enums::$bank_type);
        $sync_data = [
            'payee_username'         => $pay_info['bank_account_name'],
            'payee_bank'             => $bank_type[$pay_info['bank_name']] ?? '',
            'payee_account'          => $pay_info['bank_account'],
            'updated_at'             => date('Y-m-d H:i:s'),
            'last_update_id'         => $user['id'],
            'last_update_name'       => $user['name'],
            'last_update_department' => $user['department'],
            'last_update_job_title'  => $user['job_title'],
            'last_update_at'         => date('Y-m-d H:i:s'),
        ];

        if ($main_model->i_update($sync_data) === false) {
            throw new BusinessException('备用金申请单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($main_model),
                ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($main_model->toArray(),
                JSON_UNESCAPED_UNICODE));

        return true;
    }
}
