<?php

namespace App\Util;

final class RedisKey
{
    const CONTRACT_CREATE_COUNTER = 'contract_create_counter';  //合同编号计数器
    const CONTRACT_TEMPLATE_BASE_COUNTER = 'contract_template_base_create_counter';  //合同基础模版计数器
    const CONTRACT_TEMPLATE_VERSION_COUNTER = 'contract_template_version_create_counter';  //合同版本模版计数器
    const CONTRACT_ELECTRONIC_COUNTER = 'electronic_contract_number_counter';  //电子合同计数器

    const CONTRACT_RENT_ARCHIVE_EDIT_SAVE_KEY = 'rent_contract_archive_edit_save_';


    const VENDOR_CREATE_CHINA_COUNTER = 'vendor_create_china_counter';  //生成中国供应商计数器

    const VENDOR_CREATE_THAILAND_COUNTER = 'vendor_create_thailand_counter';  //生成泰国供应商计数器

    const LOAN_CREATE_COUNTER = 'loan_create_counter';  //借款编号计数器

    const BUDGET_OBJECT_PRODUCT_CREATE_COUNTER = 'budget_object_product_create_counter';  //预算科目明细编号计数器

    const LOAN_BACK_ADD_LOCK_PREFIX = 'loan_back_add_'; //借款归还申请锁

    const PURCHASE_APPLY_COUNTER ='purchase_create_counter';

    const PURCHASE_ORDER_COUNTER ='purchase_order_create_counter';

    const PURCHASE_PAYMENT_COUNTER ='purchase_payment_create_counter';

    const PURCHASE_EXPORT_LOCK = 'purchase_export_lock';

    const PURCHASE_DOWNLOAD_LOCK = 'purchase_download_lock';
    const PURCHASE_SET_IMPORT_LOCK = 'purchase_set_import_lock_';//采购员设置-导入
    const PURCHASE_SET_EXPORT_LOCK = 'purchase_set_export_lock_';//采购员设置-导出

    const REIMBURSEMENT_CREATE_COUNTER = 'reimbursement_create_counter';

    const AGENCY_PAYMENT_CREATE_COUNTER = 'agency_payment_create_counter';
    const AGENCY_PAYMENT_CREATE_DETAIL_COUNTER = 'agency_payment_create_detail_counter';

    // 报销相关key
    const REIMBURSEMENT_DATA_EXPORT_LOCK_PREFIX = 'reimbursement_data_export_';
    const REIMBURSEMENT_AUDIT_EXPORT_LOCK_PREFIX = 'reimbursement_audit_export_';
    const REIMBURSEMENT_PAY_LIST_LOCK_PREFIX = 'reimbursement_pay_list_';
    const REIMBURSEMENT_AUDIT_LIST_LOCK_PREFIX = 'reimbursement_audit_list_';
    const REIMBURSEMENT_AUDIT_LOCK_PREFIX = 'reimbursement_audit_action_';
    const REIMBURSEMENT_GET_STAFF_TRIP_LIST_LOCK_PREFIX = 'reimbursement_staff_trip_';
    const REIMBURSEMENT_GET_STAFF_TRIP_ROOMMATE_LIST_LOCK_PREFIX = 'reimbursement_staff_trip_roommate_';
    const REIMBURSEMENT_EXPORT_DETAIL_STORE_SUPPORT_V2_PREFIX = 'reimbursement_export_detail_store_support_v2_';
    const REIMBURSEMENT_EXPORT_SUPPORT_RELATED_DETAIL_PREFIX = 'reimbursement_export_support_related_detail_';



    const PURCHASE_SAVE_LOCK = 'purchase_save_lock'; //保存采购订单锁

    const WAGES_CREATE_COUNTER = 'wages_create_counter';

    const HC_BUDGET_LIST_EXPORT_LOCK = 'hc_budget_list_export_1_lock';

    const HC_BUDGET_LIST_EXPORT_2_LOCK = 'hc_budget_list_export_2_lock';

    const PAYMENT_STORE_RENTING_APPLY_COUNTER = 'payment_store_renting_apply_counter';  // 付款管理 - 网点租房付款申请计数器

    // 租房付款-数据查询导出redis锁前缀
    const PAYMENT_STORE_RENTING_DATA_EXPORT_LOCK_PREFIX = 'store_renting_payment_data_export_';
    // 租房付款-数据查询-批量下载redis锁前缀
    const PAYMENT_STORE_RENTING_DATA_BATCH_DOWNLOAD_LOCK_PREFIX = 'store_renting_payment_data_batch_download_';

    // 租房付款-审批导出redis锁前缀
    const PAYMENT_STORE_RENTING_AUDIT_EXPORT_LOCK_PREFIX = 'store_renting_payment_audit_export_';

    //租房付款-补充附件redis 锁
    const PAYMENT_STORE_RENTING_SUPPLEMENT_LOCK_PREFIX = 'store_renting_payment_add_supplement';
    //租房付款-批量导入数据redis 锁
    const PAYMENT_STORE_RENTING_IMPORT_LOCK_PREFIX = 'store_renting_payment_import_';

    const HC_BUDGET_APPROVAL_LOCK = 'hc_budget_approval_lock';

    const ORDINARY_PAYMENT_APPLY_COUNTER = 'ordinary_payment_apply_counter';  // 普通付款 申请计数器

    const WAGE_APPLY_COUNTER ='wage_apply_counter';

    const ACCESS_DATA_WORK_ORDER_COUNTER ='access_data_work_order';// 取数工单编号

    const STAFF_UN_READ_NUM = 'staff_un_read_num';  //员工未读消息数，staff_un_read_num_22568

    const JOB_TRANSFER_DO_TRANSFER_LOCK = 'do_job_transfer_lock';

    const JOB_TRANSFER_BATCH_ADD_KEY = 'batch_add_job_transfer_key'; //批量添加转岗
    const JOB_TRANSFER_ASYNC_IMPORT_SPECIAL_TRANSFER = 'batch_add_job_special_transfer_key'; //批量添加特殊转岗

    const VENDOR_CREATE_DEFAULT_COUNTER = 'vendor_create_default_counter';  //生成默认供应商计数器
    const VENDOR_CREATE_DEFAULT_COUNTER_PREFIX = 'DFS';  //生成默认供应商计数器前缀

    const PURCHASE_STORAGE_SAVE_LOCK = 'purchase_storage_save_lock_'; //保存采购入库通知单锁
    const PURCHASE_STORAGE_DEAL_SCM_LOCK = 'storage_in_deal_scm_lock_';//采购入库单回调

    const INVENTORY_CHECK_SERIAL_NUMBER_COUNTER = 'inventory_check_serial_number_counter';  //资产盘点单盘点编号计数器

    const BUDGET_MAIN_APPLY_COUNTER = 'budget_main_apply_counter';  // 财务预算申请 申请计数器
    const BUDGET_WITHHOLDING_ADD_COUNTER = 'budget_withholding_add_counter';//费用预提-新增-计数器

    const BUDGET_WITHHOLDING_EXPORT_LOCK = 'budget_withholding_export_lock_';//费用预提-导出
    const BUDGET_WITHHOLDING_DETAIL_EXPORT_LOCK = 'budget_withholding_detail_export_lock_';//费用预提-费用明细-导出
    const BUDGET_WITHHOLDING_ADD_LOCK = 'budget_withholding_add_lock_';//费用预提-新增
    const BUDGET_WITHHOLDING_RECOMMIT_LOCK = 'budget_withholding_recommit_lock_';//费用预提-重新提交
    const BUDGET_WITHHOLDING_IMPORT_DETAIL_LOCK = 'budget_withholding_import_detail_lock_';//费用预提-新增/重新提交-费用明细
    const BUDGET_WITHHOLDING_CANCEL_LOCK = 'budget_withholding_cancel_lock_';//费用预提-撤回
    const BUDGET_WITHHOLDING_CLOSE_LOCK = 'budget_withholding_close_lock_';//费用预提-关闭
    const BUDGET_WITHHOLDING_PASS_LOCK = 'budget_withholding_pass_lock_';//费用预提-审核-通过
    const BUDGET_WITHHOLDING_REJECT_LOCK = 'budget_withholding_reject_lock_';//费用预提-审核-驳回


	const WORKFLOW_MANAGEMENT_SAVE_COUNTER = 'workflow_management_save';  // 可视化审批流保存锁

    const PLAN_CHECK_SERIAL_NUMBER_COUNTER = 'plan_check_serial_number_counter';  //物料盘点单盘点编号计数器

    const MATERIAL_ASSET_OUT_STORAGE_COUNTER = 'material_asset_out_storage_number_counter';//资产领用出库编号计数器

    const MATERIAL_ASSET_APPLY_COUNTER = 'material_asset_apply_number_counter';//资产领用申请编号计数器

    const MATERIAL_ASSET_APPLY_AUDIT_EXPORT = 'material_asset_apply_audit_export_lock';//资产领用申请-审核-导出

    const MATERIAL_ASSET_OUT_STORAGE_SAVE_LOCK = 'material_asset_out_storage_save_lock_'; //资产领用出库-关联申请单-保存锁

    const MATERIAL_ASSET_APPLY_BATCH_AUDIT_LOCK = 'material_asset_apply_batch_audit_lock';//资产领用申请-批量审核-保存锁

    const MATERIAL_ASSET_OUT_STORAGE_IMPORT_ADD_LOCK = 'material_asset_out_storage_import_add_lock';//资产领用出库-导入新增-保存锁

    const ADMINISTRATION_TICKET_EXPORT_LOCK = 'administration_ticket_export_lock_'; //行政工单导出锁

    //资产台账-批量导入-批量报废时间锁
    const MATERIAL_ASSET_SCRAP_UPLOAD_LOCK = 'material_asset_scrap_update_lock';
    //资产台账-批量导入-导入修改财务时间锁
    const MATERIAL_ASSET_FINANCE_UPLOAD_LOCK = 'material_asset_finance_update_lock';
    //资产工单-导出时间锁
    const ASSET_WORK_ORDER_EXPORT_LOCK = 'asset_work_order_export_lock';
    //资产盘点-查看任务（盘点报表）-导出锁
    const MATERIAL_INVENTORY_EXPORT_ASSETS = 'material_inventory_check_staff_asset_export_lock_';

    //薪资抵扣-借款应扣明细编号
    const SALARY_DEDUCT_LOAN_ORDER_LOCK = 'salary_deduct_loan_order_lock';
    //薪资抵扣-备用金应扣明细编号
    const SALARY_DEDUCT_RESERVE_FUND_ORDER_LOCK = 'salary_deduct_reserve_fund_order_lock';
    //薪资抵扣总数-下载应扣（抵扣）明细
    const SALARY_DEDUCT_DETAILED_EXPORT_LOCK = 'salary_deduct_detailed_export_lock';
    //薪资抵扣总数-详情-上传实扣金额-异步任务
    const SALARY_DEDUCT_IMPORT_DEDUCT_LOCK = 'salary_deduct_import_deduct_lock';
    //薪资抵扣总数据-详情页-确认扣款正确
    const SALARY_DEDUCT_CONFIRM_LOCK = 'salary_deduct_confirm_lock';

    const MATERIAL_WMS_APPLY_COUNTER = 'material_wms_apply_number_counter';//耗材申请编号计数key
    const MATERIAL_WMS_OUT_STORAGE_COUNTER = 'material_wms_out_storage_number_counter';
    const MATERIAL_WMS_PLAN_IMPORT_STORE_LOCK = 'material_wms_plan_import_store_lock_';//添加计划-批量导入网点
    const MATERIAL_PACKAGE_ALLOT_COUNTER = 'material_package_allot_apply_number_counter';//耗材调拨单计数器key
    const MATERIAL_PACKAGE_ALLOT_ADD_LOCK = 'material_package_allot_apply_lock';//耗材调拨单创建
    const MATERIAL_PACKAGE_ALLOT_CANCEL_LOCK = 'material_package_allot_cancel_lock';//耗材调拨单取消
    const MATERIAL_PACKAGE_ALLOT_EXPORT_LOCK = 'material_package_allot_export_lock';//耗材调拨单导出
    const MATERIAL_PACKAGE_ALLOT_SKU_EXPORT_LOCK = 'material_package_allot_sku_export_lock';//耗材调拨单-调拨信息导出
    const MATERIAL_PACKAGE_STOCK_EXPORT_LOCK = 'material_package_stock_export_lock';//耗材进销存导出
    /******* 离职资产模块redis锁的key前缀 Start *****/
    const LEAVE_ASSET_EDIT_SAVE_LOCK = 'leave_asset_edit_save_lock'; //编辑/更新-暂存
    const LEAVE_ASSET_EDIT_ADD_LOCK = 'leave_asset_edit_add_lock'; //编辑/更新-添加
    const LEAVE_ASSET_EDIT_DELETE_LOCK = 'leave_asset_edit_delete_lock'; //编辑/更新-删除

    const LEAVE_ASSET_BATCH_EDIT_LOCK = 'leave_asset_batch_edit_lock'; //批量编辑
    const LEAVE_ASSET_EXPORT_LOCK = 'leave_asset_export_lock'; //导出
    const LEAVE_ASSET_BATCH_CONFIRM_LOCK = 'leave_asset_batch_confirm_lock'; //批量确认
    const LEAVE_ASSET_AMOUNT_LOG_NO = 'leave_asset_amount_log_no'; //金额日志生成批次号

    /******* 离职资产模块redis锁的key前缀 End *****/

    /******* 资产退回处理模块redis锁的key前缀 Start *****/
    const ASSET_RETURN_CREATE_COUNTER = 'material_asset_return_create_counter';//退回申请单-单号计数器
    const ASSET_RETURN_WAIT_BATCH_RECEIPT_LOCK = 'material_asset_return_wait_batch_receipt_lock'; //资产退回处理-待处理-批量收货
    const ASSET_RETURN_WAIT_BATCH_REJECT_LOCK = 'material_asset_return_wait_batch_reject_lock'; //资产退回处理-待处理-批量拒收
    const ASSET_RETURN_WAIT_BATCH_FINISH_LOCK = 'material_asset_return_wait_batch_finish_lock'; //资产退回处理-待处理-完成工单
    const ASSET_RETURN_WAIT_CREATE_STORAGE_CREATE_COUNTER = 'material_asset_return_wait_create_storage_counter';//创建scm退库单-单号计数器
    const ASSET_RETURN_WAIT_CREATE_STORAGE_LOCK = 'material_asset_return_wait_create_storage_lock'; //资产退回处理-待处理-创建scm退库单
    const ASSET_RETURN_CANCEL_STORAGE_LOCK = 'material_asset_return_cancel_storage_lock'; //资产退回处理-退货入库通知单-撤回
    const ASSET_RETURN_DELETE_STORAGE_LOCK = 'material_asset_return_delete_storage_lock'; //资产退回处理-退货入库通知单-删除

    const ASSET_RETURN_WAIT_EXPORT = 'material_asset_return_wait_export_lock';//资产退回处理-待处理-导出
    const ASSET_RETURN_TRANSFER_EXPORT = 'material_asset_return_transfer_export_lock';//资产退回处理-已转交-导出
    const ASSET_RETURN_DONE_EXPORT = 'material_asset_return_done_export_lock';//资产退回处理-已处理-导出
    const ASSET_RETURN_STORAGE_EXPORT = 'material_asset_return_storage_export_lock';//资产退回处理-退回入库-导出
    /******* 资产退回处理模块redis锁的key前缀 End *****/


    const INTERIOR_BATCH_ORDER_APPLY_COUNTER = 'interior_batch_apply_number_counter';//批量下单工服申请编号计数key

    const WAREHOUSE_ID_COUNTER = 'warehouse_id_number_counter_';//仓库ID编号计数key
    const WAREHOUSE_STATUS_CHANGE_AUDIT_NO_COUNTER = 'warehouse_status_change_audit_counter_';//仓库状态变更OA审批单号
    const WAREHOUSE_STORE_CHANGE_AUDIT_NO_COUNTER = 'warehouse_store_change_audit_counter_';//仓库网点变更OA审批单号


    /************* 支付管理模块redis锁key前缀 Start *************/
    const PAYMENT_ONLINE_PAY_LOCK = 'finalpay_online_export_';
    const PAYMENT_ADD_SUPPLEMENT = 'payment_add_supplement_';//补充附件
    /************* 支付管理模块redis锁key前缀 End *************/

    /****** 系统设置模块 Start ******/
    // 翻译同步工具
    const SYS_SETTING_SYNC_LANGUAGE_LOCK_PREFIX = 'sys_setting_pull_lang_';
    const SYS_SETTING_SYNC_LANGUAGE_CENTER_VERSION = 'sys_setting_pull_lang_center_version';
    const SYS_SETTING_SYNC_LANGUAGE_LAST_CENTER_VERSION = 'sys_setting_pull_lang_last_center_version';
    const SYS_SETTING_SYNC_LANGUAGE_HOSTS_VERSION = 'sys_setting_pull_lang_hosts_version';

    // 通用数据配置模块锁前缀: 保存员工管辖配置
    const COMMON_DATA_PERMISSION_SAVE_STAFF_CONFIG_LOCK_PREFIX = 'common_data_permission_save_staff_config_';

    // 会计科目规则配置批量新增锁前缀
    const SYS_SETTING_ACCOUNTING_RULE_BATCH_ADD_LOCK_PREFIX = 'sys_setting_accounting_rule_batch_add_';

    // 会计科目规则配置单条新增锁前缀
    const SYS_SETTING_ACCOUNTING_RULE_SINGLE_ADD_LOCK_PREFIX = 'sys_setting_accounting_rule_single_add_';

    // 会计科目规则配置单条更新锁前缀
    const SYS_SETTING_ACCOUNTING_RULE_SINGLE_UPDATE_LOCK_PREFIX = 'sys_setting_accounting_rule_single_update_';


    /****** 系统设置模块 End ******/

    /************* 银行流水模块redis锁key前缀 Start *************/
    //流水上传-导出流水
    const INIT_FLOW_EXPORT_LOCK = 'init_flow_export_';
    //流水管理-收款流水-导出
    const GET_FLOW_EXPORT_LOCK = 'get_flow_export_';
    //流水管理-付款流水-导出
    const PAY_FLOW_EXPORT_LOCK = 'pay_flow_export_';
    //流水管理-收款流水-导出明细备注
    const GET_FLOW_EXPORT_REMARK_LOCK = 'get_flow_export_remark_';
    //流水管理-付款流水-导出明细备注
    const PAY_FLOW_EXPORT_REMARK_LOCK = 'pay_flow_export_remark_';
    //流水管理-付款流水-导出系统单号
    const PAY_FLOW_EXPORT_ORDER_LOCK = 'pay_flow_export_order_';
    /************* 银行流水模块redis锁key前缀 End *************/
    // 分仓设置-导出锁
    const MATERIAL_STORE_STORAGE_EXPORT = 'material_store_storage_export_lock';
    // 分仓设置-导入锁
    const MATERIAL_STORE_STORAGE_IMPORT = 'material_store_storage_import_lock';


    /****** 资金管理模块 Start ******/
    const FUNDS_BANK_ACCOUNT_EXPORT = 'funds_bank_account_export_';
    /****** 资金管理模块 End ******/

    /****** 金碟模块 Start ******/
    const  KING_DEE_SESSION_KEY = 'kingdee_session_key';
    /****** 金碟模块 End ******/
    
    /***** 合同管理 - 仓库管理 Start *****/
    const CONTRACT_WAREHOUSE_ADD_LOCK_PREFIX = 'contract_warehouse_add_';
    const CONTRACT_WAREHOUSE_EDIT_SUBMIT_LOCK_PREFIX = 'contract_warehouse_edit_submit_';
    const CONTRACT_WAREHOUSE_INFO_EXPORT_LOCK_PREFIX = 'contract_warehouse_info_export_';
    const CONTRACT_ELECTRONIC_EXPORT_LOCK_PREFIX = 'contract_electronic_export_';//电子合同制作列表 - 导出
    const CONTRACT_ELECTRONIC_EXPORT_ALL_LOCK_PREFIX = 'contract_electronic_all_export_';//数据查询-其他合同-电子合同列表 - 导出
    const CONTRACT_WAREHOUSE_PRICE_ADD_LOCK_PREFIX = 'contract_warehouse_price_add_';//续签报价

    const WAREHOUSE_REQUIREMENT_ADD_COUNTER = 'warehouse_requirement_add_counter';///仓库需求管理-添加-编号计数器
    const WAREHOUSE_REQUIREMENT_ADD_LOCK_PREFIX = 'warehouse_requirement_add_';//仓库需求管理-添加
    const WAREHOUSE_REQUIREMENT_EXPORT_LOCK_PREFIX = 'warehouse_requirement_export_';//仓库需求管理-导出
    const WAREHOUSE_REQUIREMENT_CANCEL_LOCK_PREFIX ='warehouse_requirement_cancel_';//仓库需求管理-作废
    const WAREHOUSE_REQUIREMENT_CONFIRM_LOCK_PREFIX = 'warehouse_requirement_confirm_';//仓库需求管理-提交确认
    const WAREHOUSE_REQUIREMENT_CONFIRM_OK_LOCK_PREFIX = 'warehouse_requirement_confirm_ok_';//处理需求管理-待确认-去确认-满足需求
    const WAREHOUSE_REQUIREMENT_CONFIRM_DONT_LOCK_PREFIX = 'warehouse_requirement_confirm_dont_';//处理需求管理-待确认-去确认-全部不符合满足
    const WAREHOUSE_REQUIREMENT_TRANSFER_LOCK_PREFIX ='warehouse_requirement_transfer_';//仓库需求管理-关联线索-转移
    const WAREHOUSE_REQUIREMENT_SEARCH_EXPORT_LOCK_PREFIX = 'warehouse_requirement_search_export_';//处理仓库需求-待寻找-导出
    const WAREHOUSE_REQUIREMENT_CONFIRM_EXPORT_LOCK_PREFIX = 'warehouse_requirement_confirm_export_';//处理仓库需求-待确认-导出
    const WAREHOUSE_REQUIREMENT_CONFIRM_THREAD_EXPORT_LOCK_PREFIX = 'warehouse_requirement_confirm_thread_export_';//处理仓库需求-待确认-线索-导出
    const WAREHOUSE_REQUIREMENT_SETTLE_EXPORT_LOCK_PREFIX = 'warehouse_requirement_settle_export_';//处理仓库需求-待入驻-导出
    const WAREHOUSE_REQUIREMENT_RENEWED_EXPORT_LOCK_PREFIX = 'warehouse_requirement_renewed_export_';//处理仓库需求-待续约-导出
    const WAREHOUSE_REQUIREMENT_SETTLE_LOCK_PREFIX = 'warehouse_requirement_settle_';//处理需求管理-待入驻-入驻-确认入驻
    const WAREHOUSE_REQUIREMENT_NEGOTIATIONS_FAILED_LOCK_PREFIX ='warehouse_requirement_negotiations_failed_';//处理仓库需求-待续约-谈判失败
    const WAREHOUSE_REQUIREMENT_IMPORT_LOCK_PREFIX = 'warehouse_requirement_async_import_';//需求管理-导入
    const WAREHOUSE_REQUIREMENT_PRICE_ADD_LOCK_PREFIX = 'warehouse_requirement_price_add_';//续签报价

    const WAREHOUSE_THREAD_ADD_COUNTER = 'warehouse_thread_add_counter';//仓库线索管理-添加-编号计数器
    const WAREHOUSE_THREAD_ADD_LOCK_PREFIX = 'warehouse_thread_add_';//仓库线索管理-添加
    const WAREHOUSE_THREAD_EDIT_LOCK_PREFIX = 'warehouse_thread_edit_';//仓库线索管理-编辑
    const WAREHOUSE_THREAD_EXPORT_LOCK_PREFIX = 'warehouse_thread_export_';//仓库线索管理-导出
    const WAREHOUSE_THREAD_CANCEL_LOCK_PREFIX ='warehouse_thread_cancel_';//仓库线索管理-作废
    const WAREHOUSE_THREAD_PRICE_EXPORT_LOCK_PREFIX = 'warehouse_thread_price_export_';//处理仓库线索-待报价-导出
    const WAREHOUSE_THREAD_PRICE_ADD_COUNTER = 'warehouse_thread_price_add_counter';//仓库线索管理-报价-添加-编号计数器
    const WAREHOUSE_THREAD_PRICE_ADD_LOCK_PREFIX = 'warehouse_thread_price_add_';//仓库线索管理-报价-添加
    const WAREHOUSE_THREAD_PRICE_TALK_FAIL_LOCK_PREFIX ='warehouse_thread_price_talk_fail_';//处理仓库线索-待报价-谈判失败
    const WAREHOUSE_THREAD_PRICE_REJECT_LOCK_PREFIX ='warehouse_thread_price_reject_';//仓库报价审核-驳回
    const WAREHOUSE_THREAD_PRICE_PASS_LOCK_PREFIX ='warehouse_thread_price_pass_';//仓库报价审核-通过
    const WAREHOUSE_THREAD_SIGN_EXPORT_LOCK_PREFIX = 'warehouse_thread_sign_export_';//处理仓库线索-待签约-导出
    const WAREHOUSE_THREAD_SIGN_RECOMMIT_PRICE_LOCK_PREFIX = 'warehouse_thread_sign_recommit_price_';//处理仓库线索-待签约-重新报价
    const CONTRACT_THREAD_SIGN_WAREHOUSE_ADD_LOCK_PREFIX = 'warehouse_thread_sign_warehouse_add_';//处理仓库线索-待签约-新建仓库
    const WAREHOUSE_THREAD_VERIFY_TRANSFER_LOCK_PREFIX ='warehouse_thread_verify_transfer_';//处理仓库线索-待验证-转交

    /***** 合同管理 - 仓库管理 End *****/
    /***** 员工商城 - 员工工服 Start *****/
    const SIZE_EXPORT = 'shop_goods_size_export_';//员工商城-员工工服尺码-导出
    const BATCH_EXPORT = 'goods_batch_export_';//员工商城-批量下单工服-列表-导出
    const BATCH_INFO_EXPORT = 'goods_batch_info_export_';//员工商城-批量下单工服-新建-详细信息-导出
    const GOODS_BATCH_IMPORT = 'goods_batch_import_';//员工商城-批量下单工服-新建-详细信息-覆盖导入
    const BATCH_DETAIL_EXPORT = 'goods_batch_detail_export_';//员工商城-批量下单工服-查看-导出
    const BATCH_CHECK_EXPORT = 'goods_batch_check_export_';//员工商城-批量工服确认-列表-导出
    const BATCH_INFO_CHECK_EXPORT = 'goods_batch_info_check_export_';//员工商城-批量下单工服确认-核对-导出
    const BATCH_DETAIL_CHECK_EXPORT = 'goods_batch_detail_check_export_';//员工商城-批量下单工服确认-核对-详细信息-查看-导出
    const BATCH_CHECK_IMPORT = 'goods_batch_check_import_';//员工商城-批量下单工服确认-核对-详细信息-覆盖导入
    /*****  员工商城  - 员工工服 End *****/

    //员工雇佣类型枚举缓存key
    const STAFF_HIRE_TYPE_ENUM_CACHE = 'staff_hire_type_enum_cache';
    
    /***** 员工商城-收款核对 Start *****/
    const EXPORT_WAIT_COLLECTION = 'export_wait_collection_';//员工商城-收款核对-待核对-导出
    const EXPORT_PROCESSED_COLLECTION = 'export_processed_collection_';// 员工商城--收款核对-已核对-导出
    /***** 员工商城-收款核对 End *****/


    /*** 登录用户token缓存 Start ***/
    const SYS_USER_SESSION_PC_PREFIX = 'oa_pc_user_session_';// PC端OA登录用户的缓存key前缀
    const SYS_USER_SESSION_MOBILE_PREFIX = 'oa_mobile_user_session_';// 移动端OA登录用户的缓存key前缀
    const SYS_USER_TMP_TICKET_PREFIX = 'oa_user_ticket_';// OA用户临时票据前缀

    /*** 登录用户token缓存 End ***/
    // BY获取OA-报销申请红点的待办人预热缓存key
    const OA_REDDOT_PREHOT_CACHE_SCENCE_REIMBURSEMENT_APPLY = 'reimbursement_apply_reddot';
    const OA_REIMBURSEMENT_APPLY_PREHOT_CACHE_KEY = 'oa_reimbursement_apply_reddot_pending_prehot_cache_set';

    // BY获取OA红点的待办人预热缓存key
    const OA_REDDOT_PREHOT_CACHE_SCENCE_AUDIT_PENDING = 'oa_audit_reddot';
    const OA_REDDOT_PENDING_PREHOT_CACHE_KEY = 'oa_reddot_pending_prehot_cache_set';

    // BY获取OA红点的动态高优用户缓存[一级部门负责人~]
    const OA_REDDOT_DYNAMIC_HIGH_PRIORITY_STAFF_CACHE_KEY = 'oa_redhot_dynamic_high_priority_staffs_set';

    /*** 电子合同甲方签字流程 Start ***/
    // otp 邮箱验证码缓存前缀
    const ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_PREFIX = 'oa_ec_otp_code_';

    // 电子合同甲方签约系统sign_token缓存
    const ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_PREFIX = 'oa_ec_sign_token_';


    /*** 电子合同甲方签字流程 End ***/


}
