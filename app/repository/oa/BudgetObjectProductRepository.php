<?php

namespace App\Repository\oa;

use App\Library\Enums\BudgetObjectEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\oa\BudgetObjectProductModel;
use App\Repository\BaseRepository;

/**
 * OA预算科目明细
 * Class BudgetObjectProductRepository
 * @package App\Repository\oa
 */
class BudgetObjectProductRepository extends BaseRepository
{
    // 默认的科目明细名称字段
    protected static $name_field = 'name_th';

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance($sys_lang = null)
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        if (!is_null($sys_lang)) {
            self::$name_field = get_lang_field_name('name_', $sys_lang, 'th', 'cn');
        }

        return self::$instance;
    }

    /**
     * 获取指定预算科目下的明细信息
     *
     * @param string $object_code
     * @param string $pno
     * @return array
     */
    public function getOneByObjectCodeAndPno(string $object_code, string $pno)
    {
        return BudgetObjectProductModel::findFirst([
            'conditions' => 'object_code = :object_code: AND pno = :pno: AND status = :status: AND is_delete = :is_delete:',
            'bind' => [
                'object_code' => $object_code,
                'pno' => $pno,
                'status' => BudgetObjectEnums::BUDGET_OBJECT_PRODUCT_VALID,
                'is_delete' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => ['pno AS product_no', self::$name_field . ' AS product_name'],
            'group' => 'pno'
        ]);
    }

    /**
     * 获取指定预算科目下的明细列表
     *
     * @param string $object_code
     * @return array
     */
    public function getListByBudgetId(string $object_code)
    {
        return BudgetObjectProductModel::find([
            'conditions' => 'object_code = :object_code: AND status = :status: AND is_delete = :is_delete:',
            'bind' => [
                'object_code' => $object_code,
                'status' => BudgetObjectEnums::BUDGET_OBJECT_PRODUCT_VALID,
                'is_delete' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => ['pno AS product_no', self::$name_field . ' AS product_name'],
            'group' => 'pno'
        ])->toArray();
    }


    /**
     * 获取所有有效的预算科目明细
     *
     * @return array
     */
    public function getAllValidList()
    {
        return BudgetObjectProductModel::find([
            'conditions' => 'status = :status: AND is_delete = :is_delete:',
            'bind' => [
                'status' => BudgetObjectEnums::BUDGET_OBJECT_PRODUCT_VALID,
                'is_delete' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => ['object_code', 'pno AS product_no', self::$name_field . ' AS product_name'],
            'group' => 'pno'
        ])->toArray();
    }

    /**
     * 获得特定筛选条件下的预算科目明细列表
     * @param array $params 筛选条件组
     * @param bool $is_only_ids 是否只返回ID，默认false
     * @return array
     */
    public function getListByParams($params, $is_only_ids = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id');
        $builder->from(BudgetObjectProductModel::class);
        $builder->where('is_delete = :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($params['is_purchase'])) {
            $builder->andWhere('is_purchase = :is_purchase:', ['is_purchase' => $params['is_purchase']]);
        }
        $list = $builder->getQuery()->execute()->toArray();
        if ($is_only_ids) {
            return array_column($list, 'id');
        }
        return $list;
    }

    /**
     * 获取指定预算明细的明细列表
     *
     * @param array $product_ids
     * @return array
     */
    public function getListByIds(array $product_ids)
    {
        $product_ids = array_filter($product_ids);
        if (empty($product_ids)) {
            return [];
        }

        return BudgetObjectProductModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => array_values($product_ids),],
            'columns'    => ['id', self::$name_field . ' AS product_name'],
        ])->toArray();
    }

}
