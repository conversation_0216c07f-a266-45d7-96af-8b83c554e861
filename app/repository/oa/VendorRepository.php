<?php

namespace App\Repository\oa;

use App\Modules\Vendor\Models\Vendor;
use App\Repository\BaseRepository;

/**
 * 供应商
 * Class VendorRepository
 * @package App\Repository\oa
 */
class VendorRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return VendorRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getVendorByVendorId($vendor_id){
        $vendor = Vendor::findFirst([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor_id]
        ]);
        return $vendor ? $vendor->toArray() : [];
    }
}
