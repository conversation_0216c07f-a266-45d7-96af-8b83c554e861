<?php

namespace App\Repository;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Models\backyard\BankListModel;
use App\Models\backyard\HrEntryModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrResumeEducationModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\HrWorkExperienceModel;
use App\Models\backyard\JobTransferModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffCriminalTypesModel;
use App\Models\backyard\MessageWarningModel;
use App\Models\backyard\HrJobGradeChangeModel;
use App\Models\backyard\StaffPublicHolidayModel;
use App\Models\backyard\ThailandHolidayModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Repository\HoldStaffManageRepository;
use App\Models\backyard\HoldStaffManageModel;
use Exception;

/**
 * 员工相关基础方法
 * Class HrStaffRepository
 * @package App\Repository
 */
class HrStaffRepository extends BaseRepository
{
    /**
     * 获取指定员工信息
     * @param array $staff_info_ids
     * @return array
     */
    public function getStaffListByStaffIds($staff_info_ids = [])
    {
        $staff_info_list = [];
        if (!empty($staff_info_ids)) {
            $staff_info_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,nick_name,mobile,mobile_company,sex,job_title_grade_v2,node_department_id,sys_department_id,job_title,hire_type,state,hire_date,education,manger,average_tenure,sys_store_id,wait_leave_state,leave_type,leave_reason,email,formal,is_sub_staff,bank_type,bank_no',
                'conditions' => 'staff_info_id in ({staff_info_ids:array})',
                'bind' => [
                    'staff_info_ids' => $staff_info_ids,
                ],
            ])->toArray();
            $staff_info_list = array_column($staff_info_list, null, 'staff_info_id');
        }
        return $staff_info_list;
    }

    /**
     * 根据id获取员工基本详情
     * @param $staff_info_id
     * @return array
     */
    public function getStaffById($staff_info_id)
    {
        $detail = [];
        if (!empty($staff_info_id)) {
            $staff_info = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, name, name_en, nick_name, sex, job_title_grade_v2, sys_department_id, node_department_id, contract_company_id, job_title, hire_type, state, hire_date, education, manger, average_tenure, sys_store_id, wait_leave_state, formal, mobile_company, mobile, bank_type, bank_no, email, personal_email, identity',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);

            $detail = !empty($staff_info) ? $staff_info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 员工基本详情
     * @param $staff_info_id
     * @return array
     */
    public function getStaffDetail($staff_info_id)
    {
        $detail = [];
        if (!empty($staff_info_id)) {
            $staff_info = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id,name,sex,job_title_grade_v2,node_department_id,job_title,hire_type,state,hire_date,education,manger,average_tenure,sys_store_id',
                "conditions" => "staff_info_id = :staff_info_id: and is_sub_staff = 0 and state in (1,3) and formal in (1,4)",
                'bind' => [
                    "staff_info_id" => $staff_info_id,
                ],
            ]);

            $detail = !empty($staff_info) ? $staff_info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取员工item表信息
     * @param $staff_info_ids
     * @param array $items
     * @return array
     */
    public function getStaffItems($staff_info_ids, $items = ['PROFILE_OBJECT_KEY'])
    {
        $list = [];
        if (!empty($staff_info_ids)) {
            $list = HrStaffItemsModel::find([
                'columns' => 'staff_info_id,item,value',
                "conditions" => "staff_info_id in({staff_info_ids:array}) and item in({items:array})",
                'bind' => [
                    "staff_info_ids" => $staff_info_ids,
                    'items' => $items
                ],
            ])->toArray();
        }
        return $list;
    }

    /**
     * 获取指定工号角色信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffRoleList($staff_info_id)
    {
        $role_list = [];
        if (!empty($staff_info_id)) {
            $role_list = HrStaffInfoPositionModel::find([
                'columns' => 'staff_info_id,position_category',
                "conditions" => "staff_info_id = :staff_info_id:",
                'bind' => [
                    "staff_info_id" => $staff_info_id
                ],
            ])->toArray();
        }
        return $role_list;
    }

    /**
     * 员工教育经历
     * @param $staff_info_id
     * @return array
     */
    public function getStaffEducationList($staff_info_id)
    {
        $education_list = [];
        if (!empty($staff_info_id)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('edu.education_level, edu.graduate_school, edu.major, edu.admission_time, edu.graduate_time');
            $builder->from(['edu' => HrResumeEducationModel::class]);
            $builder->leftjoin(HrEntryModel::class, 'edu.resume_id = en.resume_id', 'en');
            $builder->where('edu.is_delete = 0');
            $builder->andWhere('en.staff_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
            $builder->orderBy('edu.graduate_time desc');
            $education_list = $builder->getQuery()->execute()->toArray();
        }
        return $education_list;
    }

    /**
     * 员工过往工作经历
     * @param $staff_info_id
     * @return array
     */
    public function getStaffWorkExperienceList($staff_info_id)
    {
        $experience_list = [];
        if (!empty($staff_info_id)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('w.company_name,w.position_name,w.work_time_start,w.work_time_end');
            $builder->from(['w' => HrWorkExperienceModel::class]);
            $builder->leftjoin(HrEntryModel::class, 'w.resume_id = en.resume_id', 'en');
            $builder->andWhere('en.staff_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
            $builder->orderBy('w.work_time_start desc');
            $experience_list = $builder->getQuery()->execute()->toArray();
        }
        return $experience_list;
    }

    /**
     * 员工转岗记录
     * @param $staff_info_id
     * @param string $sort
     * @return array
     */
    public function getStaffJobTransferList($staff_info_id, $sort = 'after_date desc')
    {
        $job_transfer_list = [];
        if (!empty($staff_info_id)) {
            $job_transfer_list = JobTransferModel::find([
                'columns' => 'staff_id,after_date,current_department_id,current_store_id,current_position_id,current_manager_id,after_department_id,after_store_id,after_position_id,after_manager_id',
                "conditions" => "staff_id = :staff_info_id: and state = 3 and approval_state = 2",
                'bind' => [
                    "staff_info_id" => $staff_info_id,
                ],
                'order' => $sort
            ])->toArray();
        }
        return $job_transfer_list;
    }

    /**
     * 获取员工犯罪记录
     * @param $staff_info_id
     * @return array
     */
    public function getStaffCriminalList($staff_info_id)
    {
        $criminal_list = [];
        if (!empty($staff_info_id)) {
            $criminal_list = StaffCriminalTypesModel::find([
                'columns' => 'staff_info_id,record_type,record_case,record_text,record_status,created_time',
                "conditions" => "staff_info_id = :staff_info_id: and status = 1",
                'bind' => [
                    "staff_info_id" => $staff_info_id,
                ],
                'order' => 'record_status desc,created_time desc'
            ])->toArray();
        }
        return $criminal_list;
    }

    /**
     * 获取指定工号警告信列表
     *
     * @param $staff_info_id
     * @return array
     */
    public function getStaffMessageWarningList($staff_info_id)
    {
        $warning_list = [];
        if (!empty($staff_info_id)) {
            $warning_list = MessageWarningModel::find([
                'columns' => 'staff_info_id,staff_name,type_code,warning_type,date_at',
                "conditions" => "staff_info_id = :staff_info_id: and is_delete = 0",
                'bind' => [
                    "staff_info_id" => $staff_info_id
                ],
                'order' => 'date_at desc'
            ])->toArray();
        }
        return $warning_list;
    }

    /**
     * 员工试用期状态列表
     * @param array $staff_info_ids
     * @return array
     */
    public function getStaffProbationList($staff_info_ids = [])
    {
        $probation_list = [];
        if (!empty($staff_info_ids)) {
            $probation_list = HrProbationModel::find([
                'columns' => 'staff_info_id,status',
                "conditions" => "staff_info_id in ({staff_info_ids:array})",
                'bind' => [
                    "staff_info_ids" => $staff_info_ids,
                ],
            ])->toArray();

            $probation_list = array_column($probation_list, null, 'staff_info_id');
        }
        return $probation_list;
    }

    /**
     * 员工试用期信息
     *
     * @param int $staff_info_id
     * @return array
     */
    public function getStaffProbationInfoByStaffId(int $staff_info_id)
    {
        $probation_info = [];
        if (!empty($staff_info_id)) {
            $probation_model = HrProbationModel::findFirst([
                'columns' => 'staff_info_id, status, formal_at',
                "conditions" => "staff_info_id = :staff_info_id:",
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);

            $probation_info = !empty($probation_model) ? $probation_model->toArray() : [];
        }
        return $probation_info;
    }

    /**
     * 员工下级列表
     * @param array $staff_info_ids
     * @return array
     */
    public function getSubordinateStaffList($staff_info_ids = [])
    {
        $sub_staff_list = [];
        if (!empty($staff_info_ids) && is_array($staff_info_ids)) {
            $sub_staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,state,node_department_id,job_title,sys_store_id,sex,job_title_grade_v2,manger',
                "conditions" => "manger in({superior_ids:array}) and state in (1,3) and is_sub_staff = 0 and formal in (1,4) and staff_info_id not in ({staff_info_ids:array})",
                'bind' => [
                    "superior_ids" => $staff_info_ids,
                    "staff_info_ids" => $staff_info_ids
                ],
            ])->toArray();
        }
        return $sub_staff_list;
    }

    /**
     * 下级总人数
     * @param array $staff_info_ids
     * @return array
     */
    public function getSubordinateCount($staff_info_ids = [])
    {
        $sub_staff_list = [];
        try {
            if (!empty($staff_info_ids) && is_array($staff_info_ids)) {
                $sub_staff_list = HrStaffInfoModel::find([
                    'columns' => 'count(1) as count,manger as superior_id',
                    "conditions" => "manger in({superior_ids:array}) and is_sub_staff = 0 and formal in (1,4) and state in (1,3) and staff_info_id not in ({staff_info_ids:array})",
                    'bind' => [
                        "superior_ids" => $staff_info_ids,
                        "staff_info_ids" => $staff_info_ids
                    ],
                    'group' => 'manger'
                ])->toArray();
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_ids' => $staff_info_ids],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $sub_staff_list;
    }


    /**
     * 缓存数据 5 分钟
     *
     * @description: 获取员工管辖范围 这里是处理好的数据  部门包含了子部门 返回departments=>[1,2,3],大区 片区 都把 -2 转成实际数据
     * @param string $staff_info_id
     * @param int $type 1 管辖范围 2 职级白名单管辖范围
     * @return   array  :  [
     *                     'departments'      => [1,2],  //部门
     *                     'stores'           => [-2],   //网点 有可能是-2
     *                     'regions'          => [1,2],   //大区
     *                     'pieces'           => [1,2],   //片区
     *                     'store_categories' => [1,2],   //网点类型
     *                     ];
     * <AUTHOR> L.J
     * @time       : 2022/10/12 16:56
     */
    public function getStaffJurisdiction( $staff_info_id = '', $type = 1 ) {
        $result  =[];
        if(empty($staff_info_id)){
            return $result;
        }
        $redis     = $this->getDI()->get('redis');
        $redis_key = 'get_staff_jurisdiction_' . $staff_info_id.'_'.$type;
        //这里先读缓存
        $cache                = $redis->get($redis_key);
        if($cache){
            $this->logger->info('getStaffJurisdiction 返回参数: key'.$redis_key.' 内容' . $cache);
            return json_decode($cache,true);
        }
        //获取管辖范围
        $rpc        = new ApiClient('hcm_rpc', '', 'get_staff_jurisdiction');
        $api_params = [
            [
                'staff_id' => $staff_info_id,   //需要获取的用户
                'type'     => $type,
            ],
        ];
        $rpc->setParams($api_params);
        $data = $rpc->execute();

        if(!isset($data['result']['code']) || $data['result']['code'] != ErrCode::$SUCCESS){
            return $result;
        }
        $result = $data['result']['data'] ?? [];
        //这里设置缓存

        $timer = 60 * 5;
        if (get_runtime_env() != 'pro') {
            $timer = 5;
        }

        $redis->setex($redis_key, $timer, json_encode($result,JSON_UNESCAPED_UNICODE));

        return $result;
    }

    /**
     * 批量获取指定工号的在职状态
     * @param array $staff_ids
     * @param array $staff_states 1在职;2离职;3停职;999待离职
     * @return array
     */
    public function getStaffItemByStaffIdStates(array $staff_ids, array $staff_states = [])
    {
        if (empty($staff_ids)) {
            return [];
        }

        $conditions = 'staff_info_id IN ({staff_info_ids:array})';
        $bind = ['staff_info_ids' => $staff_ids];

        $_state_conditions = [];
        foreach ($staff_states as $state) {
            switch ($state) {
                case StaffInfoEnums::STAFF_STATE_IN:
                    $_state_conditions[] = '(state = :state_in: AND wait_leave_state = :wait_leave_state_no:)';
                    $bind['state_in'] = StaffInfoEnums::STAFF_STATE_IN;
                    $bind['wait_leave_state_no'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO;
                    break;
                case StaffInfoEnums::STAFF_STATE_LEAVE:
                    $_state_conditions[] = '(state = :state_leave:)';
                    $bind['state_leave'] = StaffInfoEnums::STAFF_STATE_LEAVE;
                    break;
                case StaffInfoEnums::STAFF_STATE_STOP:
                    $_state_conditions[] = '(state = :state_stop:)';
                    $bind['state_stop'] = StaffInfoEnums::STAFF_STATE_STOP;
                    break;
                case StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE:
                    $_state_conditions[] = '(state = :state_in: AND wait_leave_state = :wait_leave_state_yes:)';
                    $bind['state_in'] = StaffInfoEnums::STAFF_STATE_IN;
                    $bind['wait_leave_state_yes'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES;
                    break;
                default:
                    break;
            }
        }

        if (!empty($_state_conditions)) {
            $conditions .= ' AND (' . implode(' OR ', $_state_conditions) . ')';
        }

        return HrStaffInfoModel::find([
            'columns' => 'staff_info_id, state, wait_leave_state',
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();
    }

    /**
     * 获取员工由指定职级(含)以下变更到大于该职级的最近一条信息
     *
     * @param int $staff_id
     * @param int $job_title_grade 指定职级
     * @return array
     */
    public function getStaffJobGradeLatestChangeInfoByStaffId(int $staff_id, int $job_title_grade = 0)
    {
        $log_model = HrJobGradeChangeModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND before_job_title_grade <= :job_title_grade: AND after_job_title_grade > :job_title_grade:',
            'bind' => ['staff_info_id' => $staff_id, 'job_title_grade' => $job_title_grade],
            'order' => 'change_date DESC'
        ]);

        return !empty($log_model) ? $log_model->toArray() : [];
    }

    /**
     * 获取员工职级变化记录表中员工等于当前申请人，职级生效日期小于等于传递日期的所有记录中职级生效日期最大的数据中的职级
     *
     * @param int $staff_id
     * @param string $change_date 职级生效日期
     * @return array
     */
    public function getStaffJobGradeLatestChangeInfo(int $staff_id, string $change_date = '')
    {
        $log_model = HrJobGradeChangeModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND change_date <= :change_date:',
            'bind' => ['staff_info_id' => $staff_id, 'change_date' => $change_date],
            'order' => 'change_date DESC'
        ]);

        return !empty($log_model) ? $log_model->toArray() : [];
    }

    /**
     * 获取指定部门员工item表信息
     * @param $department_id
     * @return array
     */
    public function getStaffByDepartment($department_id)
    {
        return HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            "conditions" => "node_department_id in ({sys_department_ids:array})",
            'bind'       => ['sys_department_ids' => $department_id],
        ])->toArray();
    }

    /**
     * 获取指定角色工号 正式 在职/停职 非子账号
     * @param $role_id
     * @return array
     */
    public function getStaffListByRoleId($role_id): array
    {
        $list = [];
        if (empty($role_id)) {
            return $list;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hr_staff.staff_info_id',
            'hr_staff.name',
            'hr_staff.job_title',
            'hr_staff.sys_store_id',
            'hr_staff.sys_department_id',
            'hr_staff.node_department_id',
            'hr_staff.state',
        ]);
        $builder->from(['hr_staff' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInfoPositionModel::class, 'hr_staff.staff_info_id = p.staff_info_id', 'p');
        $builder->where('hr_staff.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        $builder->inWhere('hr_staff.state', [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]);
        $builder->inWhere('hr_staff.formal', [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]);//员工属性1正式 4实习生
        $builder->andWhere('p.position_category = :position_category:', ['position_category' => $role_id]);
        $list = $builder->getQuery()->execute()->toArray();

        return $list;
    }


    /**
     * 根据搜索的名称模糊获取 在职正式员工
     * @param string $name 搜索用户名称
     * @param int $limit 搜索条数
     * @return array
     */

    public function getStaffLikeOnLineList(string $name, int $limit = 20)
    {
        return HrStaffInfoModel::find([
            'conditions' => 'state = :state: and  formal = :formal:  and  (name like :name: or staff_info_id like :staff_info_id:)',
            'bind'       => [
                'state'         => StaffInfoEnums::STAFF_STATE_IN,
                'formal'        => StaffInfoEnums::FORMAL_IN,
                'name'          => '%' . $name . '%',
                'staff_info_id' => '%' . $name . '%'
            ],
            'columns'    => 'staff_info_id as consignee_id, name as consignee_name',
            'limit'      => $limit
        ])->toArray();
    }


    /**
     * 根据id 获取在职用户的数据 不区分待离职和正式员工
     * @param int $staff_id 用户id
     * @return array
     */
    public function getStaffInfo(int $staff_id)
    {
        if (empty($staff_id)) {
            return [];
        }
        $staff = [];
        if (!empty($staff_id)) {
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and state = :state:',
                'bind'       => [
                    'staff_info_id' => $staff_id,
                    'state'         => StaffInfoEnums::STAFF_STATE_IN
                ]
            ]);

            if (!empty($staff_info)) {
                $staff['id']   = $staff_info->staff_info_id;
                $staff['name'] = $staff_info->name;
            }
        }
        return $staff;
    }

    /**
     * 根据网点id 获取 在职正式员工
     * @param string $use_land_id 网点id
     * @param string $name 搜索用户名称
     * @param int $limit 搜索条数
     * @return array
     */
    public function getStaffOfficially(string $use_land_id, string $name, int $limit = 20)
    {
        return HrStaffInfoModel::find([
            'conditions' => 'sys_store_id = :sys_store_id: and state = :state: and  formal = :formal:  and  (name like :name: or staff_info_id like :staff_info_id:)',
            'bind'       => [
                'sys_store_id'  => $use_land_id,
                'state'         => StaffInfoEnums::STAFF_STATE_IN,
                'formal'        => StaffInfoEnums::FORMAL_IN,
                'name'          => '%' . $name . '%',
                'staff_info_id' => '%' . $name . '%'
            ],
            'columns'    => 'staff_info_id as consignee_id, name as consignee_name',
            'limit'      => $limit
        ])->toArray();
    }

    /**
     * @param string $q
     * @param int $pageNum
     * @param int $pageSize
     * @return array
     */
    public function searchStaffs(string $q = '', int $pageNum = 1, int $pageSize = 20): array
    {
        $result = [
            'items'      => [],
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => 0,
            ],
        ];

        if (empty($q)) {
            return $result;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['count(*) as count']);
        $builder->from(['si' => HrStaffInfoModel::class]);
        $builder->andWhere('si.name like :q: or si.staff_info_id like :q:',
            ['q' => $q.'%']);
        $builder->andWhere('si.is_sub_staff =  :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        $count = (int)$builder->getQuery()->getSingleResult()->count;
        if (0 == $count) {
            return $result;
        }

        $builder->columns([
            'si.staff_info_id as id',
            'si.name',
            'si.name_en',
            'si.identity',
            'si.mobile',
            'si.nick_name',
            'si.node_department_id',
            'si.sys_store_id',
            'si.identity',
            'si.job_title',
            'si.wait_leave_state',
            'si.state'
        ]);
        $offset = $pageSize * ($pageNum - 1);
        $builder->orderBy('si.id');
        $builder->limit($pageSize, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        $department_ids  = array_values(array_unique(array_column($list, 'node_department_id')));
        $sys_store_ids   = array_values(array_unique(array_column($list, 'sys_store_id')));
        $job_title_ids   = array_values(array_unique(array_column($list, 'job_title')));
        $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
        $store_list      = (new StoreRepository())->getStoreListByIds($sys_store_ids);
        $job_title_list  = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

        $staff_info_ids = array_values(array_unique(array_column($list,'id')));
        $hold_ret = (new HoldStaffManageRepository())->getHoldStaffList([
            "staff_info_ids" => $staff_info_ids,
            "release_state" => HoldStaffManageModel::RELEASE_STATE_HOLD
        ],['id','hold_id','staff_info_id']);
        $hold_list = array_column($hold_ret,null,'staff_info_id');

        foreach ($list as $k => $v) {
            $list[$k]['store_name']         = "";
            $list[$k]['department_name']    = "";
            $list[$k]['job_title_name']     = "";
            $list[$k]['is_hold_ash']        = false;
            $list[$k]['is_suspension_ash']  = true;    // 默认置灰色
            if ($v['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $list[$k]['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }

            if (!empty($store_list[$v['sys_store_id']])) {
                $list[$k]['store_name'] = trim($store_list[$v['sys_store_id']]['name']);
            }

            if (!empty($department_list[$v['node_department_id']])) {
                $list[$k]['department_name'] = trim($department_list[$v['node_department_id']]['name']);
            }

            if (!empty($job_title_list[$v['job_title']])) {
                $list[$k]['job_title_name'] = trim($job_title_list[$v['job_title']]['job_name']);
            }

            if (!empty($hold_list[$v['id']])) {
                $list[$k]['is_hold_ash'] = true;
            }

            if (($v['state'] == StaffInfoEnums::STAFF_STATE_IN && $v['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) ||
                ($v['state'] == StaffInfoEnums::STAFF_STATE_IN && $v['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)) {
                // 在职、待离职 不置灰
                $list[$k]['is_suspension_ash']  = false;
            }
        }

        $result['items']                      = $list;
        $result['pagination']['current_page'] = $pageNum;
        $result['pagination']['per_page']     = $pageSize;
        $result['pagination']['total_count']  = $count;
        return $result;
    }

    /**
     * 检测某天是否是某员工公休日
     * @param array $staff_base_info 某员工基本信息
     * @param string $date 日期
     * @return bool
     */
    public function checkIsStaffHoliday($staff_base_info, $date)
    {
        $condition = 'day = :day: and type in ({type:array})';
        $bind = ['day' => $date, 'type' => [StaffInfoEnums::TYPE_DEFAULT, StaffInfoEnums::TYPE_WEEK_WORKING_DAY_5]];//通用假期，5天班假期
        $staff_info_id = $staff_base_info['staff_info_id'];//员工工号
        $sys_store_id = $staff_base_info['sys_store_id'];//员工网点
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            //若是马来，需要获取工作所在地
            $staff_province = $this->getStaffItems([$staff_base_info['staff_info_id']], ['STAFF_PROVINCE_CODE']);
            $staff_province = array_column($staff_province, 'value', 'staff_info_id');

            //员工工作所在州
            $province_code = $sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG ? ($staff_province[$staff_info_id] ?? StaffInfoEnums::HEAD_OFFICE_PROVINCE_CODE) : strtoupper(substr($sys_store_id, 0, 4));
            $condition .= ' and province_code = :province_code:';
            $bind['province_code'] = $province_code;
        }
        //判断是否员工法定节假日
        $public_holiday = ThailandHolidayModel::findFirst([
            'conditions' => $condition,
            'bind' => $bind,
        ]);
        if (!empty($public_holiday)) {
            $is_staff_holiday = true;
        } else {
            //若不是法定节假日，需要判断是否是员工的个人休息日
            $staff_public_holiday = StaffPublicHolidayModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and date_at = :date_at: and is_deleted = :is_deleted:',
                'bind' => ['staff_info_id' => $staff_info_id, 'date_at' => $date, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            $is_staff_holiday = !empty($staff_public_holiday) ? true : false;
        }
        return $is_staff_holiday;
    }

    /**
     * 检测某个日期是否是公休日【周六日】
     * @param string $date 日期
     * @return bool
     */
    public function checkIsHoliday($date)
    {
        //检测是否是周六日
        $is_holiday = (date('N', strtotime($date)) >= 6) ? true : false;
        if ($is_holiday === false) {
            //非周六日，则判断是否是国家公休日
            $condition = 'day = :day: and type in ({type:array})';
            $bind = ['day' => $date, 'type' => [StaffInfoEnums::TYPE_DEFAULT, StaffInfoEnums::TYPE_WEEK_WORKING_DAY_5]];//通用假期，5天班假期

            //若是马来需要判断总部所在州的公休日
            if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                $condition .= ' and province_code = :province_code:';
                $bind['province_code'] = StaffInfoEnums::HEAD_OFFICE_PROVINCE_CODE;
            }

            $public_holiday = ThailandHolidayModel::findFirst([
                'conditions' => $condition,
                'bind' => $bind,
            ]);
            $is_holiday = !empty($public_holiday) ? true : false;
        }
        return $is_holiday;
    }

    /**
     * 根据输入的值模糊获取在职非待离职正式员工
     * @param string $name 搜索用户名称
     * @param int $limit 搜索条数
     * @return array
     */
    public function likeSearchStaffOnLineData(string $name, int $limit = 20)
    {
        return HrStaffInfoModel::find([
            'conditions' => 'state = :state: and  formal = :formal: and wait_leave_state = :wait_leave_state: and is_sub_staff = :is_sub_staff: and (name like :name: or staff_info_id like :staff_info_id:)',
            'bind'       => [
                'state'            => StaffInfoEnums::STAFF_STATE_IN,
                'formal'           => StaffInfoEnums::FORMAL_IN,
                'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                'is_sub_staff'     => StaffInfoEnums::IS_SUB_STAFF_NO,
                'name'             => $name . '%',
                'staff_info_id'    => $name . '%'
            ],
            'columns'    => 'staff_info_id, name',
            'limit'      => $limit
        ])->toArray();
    }


    /**
     * 根据提供的员工的工号，like名称查询 在职人员
     * @param array $staff_ids 搜索用户名称
     * @param string $name 搜索用户名称
     * @param int $limit 搜索条数
     * @return array
     */

    public function getNameLikeOnLineList(array  $staff_ids, string $name, int $limit = 20)
    {
        return HrStaffInfoModel::find([
            'conditions' => 'state = :state: and  formal = :formal: and staff_info_id in ({staff_ids:array}) and  (name like :name: or staff_info_id like :staff_info_id:)',
            'bind'       => [
                'state'         => StaffInfoEnums::STAFF_STATE_IN,
                'formal'        => StaffInfoEnums::FORMAL_IN,
                'staff_ids' => $staff_ids,
                'name'          => '%' . $name . '%',
                'staff_info_id' => '%' . $name . '%'
            ],
            'columns'    => 'staff_info_id, name, sys_store_id, node_department_id',
            'limit'      => $limit
        ])->toArray();
    }


    /**
     * 读取员工身份证附件表，获取员工身份证正面字段以及审核状态为已通过的身份证照片
     * @param int $staff_id 员工工号
     * @return array
     */
    public function getStaffIdentity($staff_id)
    {
        $detail = [];
        if (!empty($staff_id)) {
            $staff_identity_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and audit_state = :audit_state: and type = :type:',
                'bind' => [
                    'staff_info_id' => $staff_id,
                    'audit_state'   => HrStaffAnnexInfoModel::AUDIT_STATE_PASSED,
                    'type'          => HrStaffAnnexInfoModel::TYPE_ID_CARD,
                ],
            ]);

            $detail = !empty($staff_identity_info) ? $staff_identity_info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取指定员工在职且为编制的数据
     * @param array $staff_info_ids
     * @return array
     */
    public function getStaffListFormalArr($staff_info_ids = [])
    {
        $staff_info_list = [];
        if (!empty($staff_info_ids)) {
            $staff_info_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,nick_name,mobile,sex,job_title_grade_v2,node_department_id,job_title,hire_type,state,hire_date,education,manger,average_tenure,sys_store_id,wait_leave_state,leave_type,leave_reason,email',
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and  formal = :formal:',
                'bind' => [
                    'staff_info_ids' => $staff_info_ids, 'formal' =>  StaffInfoEnums::FORMAL_IN
                ],
            ])->toArray();
            $staff_info_list = array_column($staff_info_list, null, 'staff_info_id');
        }
        return $staff_info_list;
    }

    /**
     * 获取特定筛选条件下员工数
     * @param array $params 请求参数组
     * @return int
     */
    public function getStaffCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(hr.staff_info_id) AS total');
        $builder->from(['hr' => HrStaffInfoModel::class]);
        $state = $params['state'] ?? [StaffInfoEnums::STAFF_STATE_IN];//在职
        $formal = $params['formal'] ?? [StaffInfoEnums::FORMAL_IN];//正式
        $sys_store_id = $params['sys_store_id'] ?? '';//网点
        $builder->inWhere('hr.state', $state);
        $builder->inWhere('hr.formal', $formal);
        if (!empty($sys_store_id)) {
            $builder->andWhere('hr.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        return (int) $builder->getQuery()->getSingleResult()->total;
    }

    /**
     * 批量获取指定员工的基本信息 和 附属信息 (非子账号的)
     *
     * @param array $staff_ids
     * @param string $staff_item
     * @return array
     */
    public function getStaffInfoListByStaffIds(array $staff_ids, string $staff_item = 'BANK_NO_NAME')
    {
        if (empty($staff_ids) || empty($staff_item)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id, s.bank_type, b.bank_name, s.bank_no, s.name, s.mobile, i.value as bank_no_name, s.formal, s.hire_type');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffItemsModel::class, 's.staff_info_id = i.staff_info_id', 'i');
        $builder->leftJoin(BankListModel::class, 's.bank_type = b.bank_id', 'b');
        $builder->where('s.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        $builder->inWhere('s.staff_info_id', array_values($staff_ids));
        $builder->andWhere('i.item = :item:', ['item' => $staff_item]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取在职/待离职状态的员工
     * @param array $staff_info_ids
     * @return array
     */
    public function getOnJobStaffIds(array $staff_info_ids)
    {
        $staff_ids = [];
        if (!empty($staff_info_ids)) {
            $staff_info_list = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) AND state = :state:',
                'bind' => ['staff_info_ids' => $staff_info_ids, 'state' => StaffInfoEnums::STAFF_STATE_IN],
                'columns' => 'staff_info_id',
            ])->toArray();
            $staff_ids = array_column($staff_info_list, 'staff_info_id');
        }

        return $staff_ids;
    }

    /**
     * 获取员工信息列表(精简版)
     *
     * @param string $keywords
     * @param array $conditions
     * @return array
     */
    public function getStaffInfoListByConditions(string $keywords, array $conditions = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->andWhere('staff.staff_info_id LIKE :keywords: OR staff.name LIKE :keywords:', ['keywords' => $keywords . '%']);

        if (isset($conditions['formal'])) {
            $builder->andWhere('staff.formal =  :formal:', ['formal' => $conditions['formal']]);
        }

        if (isset($conditions['is_sub_staff'])) {
            $builder->andWhere('staff.is_sub_staff =  :is_sub_staff:', ['is_sub_staff' => $conditions['is_sub_staff']]);
        }

        $builder->columns([
            'staff.staff_info_id',
            'staff.name'
        ]);

        $builder->orderBy('staff.id');
        $builder->limit($conditions['page_size'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
        return $builder->getQuery()->execute()->toArray();
    }


    public static function isLntCompany($staff_info_id){
        $ids = (new SettingEnvModel())->getSetVal('lnt_company_ids', ',');
        $staffInfo = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id,contract_company_id',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        if(!empty($staffInfo) && in_array($staffInfo->contract_company_id, $ids)){
            return true;
        }
        return false;
    }

    public static function isLntCompanyByInfo($staffInfo){
        $ids = (new SettingEnvModel())->getSetVal('lnt_company_ids', ',');
        if(!empty($staffInfo) && in_array($staffInfo['contract_company_id'], $ids)){
            return true;
        }
        return false;
    }

    
    /**
     * 仅查询特定条件下的员工信息
     * @param array $params 查询参数组
     * @param string $columns 返回字段
     * @return mixed
     */
    public function onlySearchStaff($params, $columns = 'staff_info_id, name')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(HrStaffInfoModel::class);
        //只要非子账号的
        $builder->where('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        //工号或姓名模糊搜索
        $name = $params['name'] ?? '';
        if (!empty($name)) {
            $builder->andWhere('staff_info_id like :name: or name like :name:', ['name' => '%' . $name . '%']);
        }

        //在职状态
        $state = $params['state'] ?? 0;
        if (!empty($state)) {
            $builder->andWhere('state = :state:', ['state' => $state]);
        }

        //员工属性
        $formal = $params['formal'] ?? [];
        if ($formal && is_array($formal)) {
            $builder->inWhere('formal', $formal);
        }

        //员工工号
        $staff_info_id = $params['staff_info_id'] ?? [];
        if (!empty($staff_info_id)) {
            if (is_array($staff_info_id)) {
                $builder->inWhere('staff_info_id', $staff_info_id);
            } else {
                $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
            }
        }

        //不含哪些雇佣类型
        $not_hire_type = $params['not_hire_type'] ?? [];
        if ($not_hire_type && is_array($not_hire_type)) {
            $builder->notInWhere('hire_type', $not_hire_type);
        }

        $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取员工的所属部门信息
     *
     * @param array $staff_ids
     * @return array
     */
    public function getStaffNodeDepartmentInfoByIds(array $staff_ids = []): array
    {
        $list = [];
        if (empty($staff_ids)) {
            return $list;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hr_staff.staff_info_id',
            'hr_staff.node_department_id',
            'dept.name AS node_department_name',
        ]);
        $builder->from(['hr_staff' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysDepartmentModel::class, 'hr_staff.node_department_id = dept.id', 'dept');
        $builder->inWhere('hr_staff.staff_info_id', $staff_ids);
        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list, null, 'staff_info_id');
    }

    /**
     * 获取员工身份证正面字段以及审核状态为已通过的身份证照片
     * @param $staff_id
     * @return array
     */
    public function getStaffIdentityV2($staff_id)
    {
        $detail = [];
        if (!empty($staff_id)) {
            $staff_identity_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND type = :type: AND audit_state = :audit_state:',
                'bind'       => [
                    'staff_info_id' => $staff_id,
                    'type'          => HrStaffAnnexInfoModel::TYPE_ID_CARD,
                    'audit_state'   => 1,
                ],
            ]);

            $detail = !empty($staff_identity_info) ? $staff_identity_info->toArray() : [];
        }

        return $detail;
    }

    /**
     * 获取员工基本信息
     * @param integer $staff_info_id 员工工号
     * @return array
     */
    public function getStaffBaseInfo($staff_info_id)
    {
        $staff_info = $this->getStaffById($staff_info_id);
        if (!$staff_info) {
            return [];
        }

        $data = [
            'staff_id'           => $staff_info_id,
            'staff_name'         => $staff_info['name'],
            'sys_department_id'  => $staff_info['sys_department_id'],
            'node_department_id' => $staff_info['node_department_id'],
            'job_id'             => $staff_info['job_title'],
        ];

        $department_list              = (new DepartmentRepository())->getDepartmentByIds([
            $staff_info['sys_department_id'],
            $staff_info['node_department_id'],
        ], 2);
        $data['sys_department_name']  = $department_list[$staff_info['sys_department_id']]['name'] ?? '';
        $data['node_department_name'] = $department_list[$staff_info['node_department_id']]['name'] ?? '';

        $job_info         = (new HrJobTitleRepository())->getJobTitleInfo($staff_info['job_title'], false);
        $data['job_name'] = $job_info ? $job_info->job_name : '';
        return $data;
    }

    /**
     * V21791-当申请人直线上级等于配置的CMO工号时该节点需要审批
     * @param integer $staff_info_id 申请人工号
     * @return int
     */
    public function isCMO($staff_info_id)
    {
        $is_cmo = 0;
        $apply_user_info = $this->getStaffById($staff_info_id);
        if (!$apply_user_info) {
            return $is_cmo;
        }
        $cmo_staff_info_id = EnumsService::getInstance()->getSettingEnvValue('cmo_staff_info_id');
        return $cmo_staff_info_id == $apply_user_info['manger'] ? 1 : 0;
    }

    /**
     * 获取员工指定日期的职级
     */
    public function getStaffJobGradeByDate(int $staff_id, string $date)
    {
        $job_title_grade_v2 = '';
        $model              = HrStaffTransferModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND stat_date = :stat_date:',
            'bind'       => ['staff_info_id' => $staff_id, 'stat_date' => $date],
        ]);

        if (!empty($model)) {
            $extend             = json_decode($model->extend, true);
            $job_title_grade_v2 = $extend['job_title_grade_v2'] ?? '';
        }

        return $job_title_grade_v2;
    }


    /**
     * 指定工号是否为个人代理
     * @param $staff_info_id
     * @return bool
     */
    public static function isAgentStaff($staff_info_id): bool
    {
        if (empty($staff_info_id)) {
            return false;
        }
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ]
        ]);
        if (!empty($staff_info) && in_array($staff_info->hire_type, [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT])) {
            return true;
        }
        return false;
    }
}
