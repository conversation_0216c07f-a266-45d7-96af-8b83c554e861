<?php

/**
 * Created by PhpStorm.
 * Date: 2023/8/22
 * Time: 10:26
 */

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Third\Services\KingDeeService;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Reimbursement\Services\ListService as ReimbursementListService;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\DepartmentRepository;
use App\Models\oa\PaymentModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\Enums\SysConfigEnums;

class KingDeeTask Extends BaseTask
{

    /**
     * 同步供应商数据
     * */
    public function sync_vendor_dataAction()
    {
        $this->checkLock(__METHOD__);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            //获取组织编码
            $tip_1 = '未配置指定的金蝶的组织bu 配置,请联系产品配置';
            $tip_2 = '未配置的组织代码,请联系产品配置';
            $tip_3 = '组织架构中找不到公司配置的组织代码,请联系产品配置';

            $company_id = EnumsService::getInstance()->getSettingEnvValueIds('kingdee_company_ids');
            if (empty($company_id)) {
                throw new ValidationException($tip_1, ErrCode::$VALIDATE_ERROR);
            }

            $kingdee_organization_code = EnumsService::getInstance()->getSettingEnvValue('kingdee_organization_code');
            if (empty($kingdee_organization_code)) {
                throw new ValidationException($tip_2, ErrCode::$VALIDATE_ERROR);
            }

            //获取配置的bu对应的金碟组织代码信息
            $assigned_organization_code = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $company_id]
            ])->toArray();

            $assigned_organization_codes = [];
            foreach ($assigned_organization_code as $value) {
                if (empty($value['sap_company_id'])) {
                    $tip_4 = '组织架构中找不到公司' . $value['id'] . '配置的组织代码,请联系产品配置';
                    throw new ValidationException($tip_4, ErrCode::$VALIDATE_ERROR);
                }

                //去除默认组织编码
                if ($value['sap_company_id'] == $kingdee_organization_code) {
                    continue;
                }
                $assigned_organization_codes[] = $value['sap_company_id'];
            }

            $assigned_organization_codes = array_unique($assigned_organization_codes);
            $assigned_organization_codes = implode(',', $assigned_organization_codes);
            if (empty($assigned_organization_codes)) {
                throw new ValidationException($tip_3, ErrCode::$VALIDATE_ERROR);
            }

            $i = 0;
            $z = 0;//记录成功执行数

            $res = Vendor::find([
                'conditions' => 'is_send_kingdee = :is_send_kingdee:',
                'bind'       => ['is_send_kingdee' => KingDeeEnums::IS_SEND_KING_DEE_1],
                'limit'      => 100,
                'offset'     => $i,
            ]);

            while (!empty($res->toArray())) {
                foreach ($res as $item) {
                    $send_data = [
                        'organization_code'          => $kingdee_organization_code,
                        'vendor_id'                  => empty($item->kingdee_supplier_no) ? $item->vendor_id : $item->kingdee_supplier_no,
                        'vendor_name'                => $item->vendor_name,
                        'assigned_organization_code' => $assigned_organization_codes
                    ];
                    $response  = KingDeeService::getInstance()->syncVendorData($send_data);
                    if ($response['ResponseStatus']['IsSuccess']) {
                        $item->kingdee_supplier_no = empty($item->kingdee_supplier_no) ? $item->vendor_id : $item->kingdee_supplier_no;
                        $item->is_send_kingdee     = KingDeeEnums::IS_SEND_KING_DEE_2;
                        $bool                      = $item->save();
                        if ($bool === false) {
                            $this->logger->warning('供应商同步金碟失败' . $item->vendor_id);
                        }
                        $z++;

                    } else {
                        $item->is_send_kingdee = KingDeeEnums::IS_SEND_KING_DEE_3;
                        $bool                  = $item->save();
                        if ($bool === false) {
                            $this->logger->warning('供应商同步金碟失败' . $item->vendor_id);
                        }
                    }
                }

                $i   += 100;
                $res = Vendor::find([
                    'conditions' => 'is_send_kingdee = :is_send_kingdee:',
                    'bind'       => ['is_send_kingdee' => KingDeeEnums::IS_SEND_KING_DEE_1],
                    'limit'      => 100,
                    'offset'     => $i,
                ]);
            }

            echo '成功处理' . $z . '条数';
        } catch (ValidationException $e) {
            $this->logger->notice('kingdee_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('kingdee_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit;
    }

    /**
     * 推送日期抽象
     * @param $params
     * @return array
     * @throws Exception
     */
    protected function getPushDate($params)
    {
        // 获取推送日期配置
        $task_send_day_num = EnvModel::getEnvByCode(KingDeeEnums::TASK_SEND_DAY_NUM, '');
        if (empty($task_send_day_num) || !is_numeric($task_send_day_num) || $task_send_day_num < 1 || $task_send_day_num > 31) {
            throw new ValidationException('推送日期配置无效', ErrCode::$VALIDATE_ERROR);
        }
        //计算本月日期1-5号只执行上个月 5-31号只执行本月数据
        $start_date = date('Y-m-01');//1号
        $end_date = date('Y-m-d',strtotime(date('Y-m-'.$task_send_day_num)));//5号
        $today = $params[0] ? $params[0] : date('Y-m-d');
        if ($today >= $start_date && $today < $end_date) {
            //若今天在1-4之间，则需要获取上个月(上月1号含～本月1号不含)的数据同步至金蝶
            $date_start = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' -1 month'));
            $date_end = date('Y-m-01 00:00:00');//本月1号不含
        } else {
            //5号之后的日期，需要获取本月（本月1号含～下月1号不含）的数据同步至SAP
            $date_start = date('Y-m-01 00:00:00');//本月1号含
            $date_end = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' +1 month'));//下月1号不含
        }
        return [$date_start, $date_end];
    }

    /**
     * 获取设置的费用所属公司
     * @return array
     * @throws ValidationException
     */
    protected function getKingdeeCompanyIds()
    {
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        if (empty($kingdee_company_ids)) {
            throw new ValidationException('未配置金蝶BU公司ID配置,请联系产品配置', ErrCode::$VALIDATE_ERROR);
        }
        return $kingdee_company_ids;
    }

    /**
     * 报销-应付数据推送金蝶
     * 执行频率，1号或6号到31号凌晨1点
     * @param $params
     */
    public function reimbursement_payableAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);
            $business_type_no = 'Y01';

            //会计科目
            $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();
            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);
            //到期日=当前日期+180天
            $expire_date = date('Y-m-d', strtotime('+ 180 days'));
            //获取系统配置中的金蝶个人供应商编码
            $vendor_id = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //应付数据推送
            $total = 0;
            $success_count = 0;

            //应付数据推送(正向)
            $page = 1;
            $params = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYABLE, 'max_id' => 0];
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    $payable_params = ReimbursementListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_NO,
                            'expire_date' => $expire_date,
                            'vendor_id' => $vendor_id,
                            'sap_company_list' => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum' => $company_project_enum,
                            'business_type_no' => $business_type_no,
                        ]);
                    $response = KingDeeService::getInstance()->payable($payable_params);
                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => null,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }
                $log .= '第' . $page . '页应付数据推送(正向)处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            //应付数据推送(反向)
            $page = 1;
            $params['max_id'] = 0;
            $params['is_cancel'] = KingDeeEnums::IS_CANCEL_PAY_YES;
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页应付数据推送(反向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    $payable_params = ReimbursementListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_YES,
                            'expire_date' => $expire_date,
                            'vendor_id' => $vendor_id,
                            'sap_company_list' => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum' => $company_project_enum,
                            'business_type_no' => $business_type_no,
                        ]);
                    $response = KingDeeService::getInstance()->payable($payable_params, KingDeeEnums::IS_CANCEL_PAY_YES);
                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => null,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_YES
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页应付数据推送(反向)处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            $log .= 'reimbursement_payable 应付数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'reimbursement_payable 应付数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'reimbursement_payable 应付数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'reimbursement_payable_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 报销-付款数据推送金蝶
     * 执行频率，1号或6号到31号凌晨3点
     * @param $params
     */
    public function reimbursement_paybillAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            // 获取推送日期配置
            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            $business_type_no = 'Y01';
            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);

            //获取系统配置中的金蝶个人供应商编码
            $vendor_id = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //付款数据推送
            $total = 0;
            $success_count = 0;
            $page = 1;
            $params = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYBILL, 'max_id' => 0];
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页付款数据推送处理开始' . PHP_EOL;

                //获取流转到支付模块的单据信息组
                $nos = array_column($deal_data, 'no');
                $payment_list = PaymentModel::find([
                    'columns' => 'no, pay_method',
                    'conditions' => 'no in ({nos:array})',
                    'bind' => ['nos' => $nos]
                ])->toArray();
                $payment_list = array_column($payment_list, 'pay_method', 'no');

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    //如果未对接支付模块，则固定为银行转账，如果对接了支付模块，则取支付模块对应的支付方式，需要按照以下枚举传输值
                    if ($item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                        $pay_method = $payment_list[$item->no] ?? 0;
                    } else {
                        $pay_method = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER;//银行转账
                    }
                    //WHT税额所有明细行总计，需要保证精确度
                    $details = $item->getDetails()->toArray();
                    $abstract = !empty($item->no) ? 'Payment_'.$item->no : '';
                    $wht_tax_amount = bcdiv(array_sum(array_column($details, 'wht_tax_amount')), 1000, 2);
                    $response = KingDeeService::getInstance()->paybill([
                        'bill_no' => $country_code . $item->no,
                        'pay_at' => date('Y-m-d' , strtotime($item->pay_at)),
                        'vendor_id' => $vendor_id,
                        'currency' => $item->currency,
                        'sap_company_id' => $sap_company_list[$item->cost_company_id]['sap_company_id'] ?? '',
                        'remark' => $item->remark,
                        'no' => $item->no,
                        'pay_method' => KingDeeEnums::$settle_type[$pay_method] ?? '',
                        'pay_bank_account' => $item->pay_bank_account,
                        'amount' => bcdiv(($item->amount - $item->loan_amount), 1000, 2),
                        'real_amount' => bcdiv($item->real_amount, 1000, 2),
                        'wht_tax_amount' => $wht_tax_amount,
                        'business_type_no' => $business_type_no,
                        'abstract' => $abstract,
                    ]);

                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYBILL,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => $item->pay_at,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页付款数据推送处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            $log .= 'reimbursement_paybill 付款数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'reimbursement_paybill 付款数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'reimbursement_paybill 付款数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'reimbursement_paybill_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

}