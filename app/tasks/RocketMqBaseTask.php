<?php

use App\Library\RocketMQ;
use MQ\Exception\AckMessageException;
use MQ\Exception\MessageNotExistException;

abstract class RocketMqBaseTask extends BaseTask
{
    //子类去实现,这个接口,处理消息主逻辑 true表示成功,可以删除消息了
    abstract protected function processOneMsg($msgBody);

    protected $tq;

    protected $consumeOnceLimit = 3;

    public function initialize()
    {
        parent::initialize();

        $this->initSignalHandler();
    }

    //子类去实现,这个接口,是否需要退出
    protected function beforeGetMsg()
    {
        //凌晨时,强制退出,为了每天的日志分隔 和 避免内存泄漏
        if (date('Hi') == '0401') {
            sleep(10);
            return true;
        }
        return false;
    }

    /**
     * 解密消息体
     *
     * @param string $msgBody
     * @return false|array
     */
    protected function getMessageData(string $msgBody)
    {
        $data = json_decode(base64_decode($msgBody), true);
        if (!isset($data['data'])) {
            return false;
        }

        unset($msgBody);
        return $data ?? [];
    }

    public function mainAction()
    {
        $this->fire();
    }


    public function fire()
    {
        if (empty($this->tq)) {
            exit('please check topic groupID');
        }
        $client = (new RocketMQ($this->tq));
        $consumer = $client->getConsumer();

        $start_log = 'MQstart ' . date('Y-m-d H:i:s');
        echo $start_log . PHP_EOL;
        $this->logger->info($start_log);
        $waitSeconds = 20;
        if (get_runtime() == 'dev') {
            $waitSeconds = 3;
        }
        $i = 0;
        while (true) {
            $needExit = $this->beforeGetMsg();
            $this->logger->info('before_get_msg:' . $needExit);
            if ($needExit) {
                break;
            }

            pcntl_signal_dispatch();
            try {
                $messages = $consumer->consumeMessage(
                    $this->consumeOnceLimit, // 一次最多消费3条(最多可设置为16条)
                    $waitSeconds // 长轮询时间3秒（最多可设置为30秒）
                );

            } catch (\Exception $e) {
                if ($e instanceof MessageNotExistException) {//没有消息可以消费
                    $i++;
                    // 消费N次仍无数据, 歇会再继续
                    if (get_runtime_env() == 'pro' && $i >= 100) {
                        sleep(5);
                        break;
                    }

                    continue;
                }

                $this->logger->error('Mq Exception : ' . $e->getMessage());
                sleep(10);
                break;
            }

            $receiptHandles = [];
            foreach ($messages as $message) {
                $msgBody = $message->getMessageBody();
                $processSuc = $this->processOneMsg($msgBody);
                if ($processSuc) {
                    $receiptHandles[] = $message->getReceiptHandle();
                    $this->logger->info('processOneMsg success' . json_encode($message->getReceiptHandle()));
                } else {
                    $this->logger->notice('processOneMsg failed, msgBody=' . json_encode($msgBody));
                }
            }
            unset($messages);
            if (empty($receiptHandles)) {
                $this->logger->notice('receiptHandles is empty!');
                sleep(10);
                break;
            }

            try {
                $consumer->ackMessage($receiptHandles);
                $this->logger->info('consumer ack Succeed!' . json_encode($receiptHandles));
                unset($receiptHandles);
            } catch (\Exception $e) {
                if ($e instanceof AckMessageException) {
                    $this->logger->error('Ack Error, RequestId:' . $e->getRequestId());
                    foreach ($e->getAckMessageErrorItems() as $errorItem) {
                        $this->logger->error(sprintf('ReceiptHandle:%s, ErrorCode:%s, ErrorMsg:%s',
                            $errorItem->getReceiptHandle(), $errorItem->getErrorCode(), $errorItem->getErrorCode()));
                    }
                } else {
                    $this->logger->error('Ack Exception : ' . $e->getMessage());
                }

                sleep(10);
                break;
            }

            //主动释放内存
            gc_collect_cycles();
        }

        $end_log = 'MQend ' . date('Y-m-d H:i:s');
        $this->logger->info($end_log);
        exit(PHP_EOL . $end_log . ' [restart]');
    }

    protected function initSignalHandler()
    {
        // 注册
        pcntl_signal(SIGHUP, [$this, 'signalHandler']);
        pcntl_signal(SIGINT, [$this, 'signalHandler']);
        pcntl_signal(SIGTERM, [$this, 'signalHandler']);
    }

    /**
     * @param $signo
     */
    private function signalHandler($signo)
    {
        switch ($signo) {
            case SIGHUP:
            case SIGINT:
            case SIGTERM:
                exit(PHP_EOL . 'exit');
                break;
            default:
                // 处理所有其他信号
                break;
        }
    }

}
