<?php
namespace App\Models\oa;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\AgencyPayment\Services\AgencyPaymentPayService;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Library\Validation\ValidationException;
use App\Modules\Pay\Models\Payment;

/**
 * 代理支付明细表
 * Class AgencyPaymentDetailModel
 * @package App\Models\oa
 */
class AgencyPaymentDetailModel extends Base implements BankFlowModelInterface,PayModelInterface
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('agency_payment_detail');

        //代理支付批次
        $this->hasOne(
            'agency_payment_id',
            AgencyPaymentModel::class,
            'id', [
                'alias' => 'AgencyPayment'
            ]
        );

        //支付模块
        $this->hasOne(
            'no',
            PaymentModel::class,
            'no',
            [
                'alias' => 'Payment',
                'params' => [
                    'conditions' => 'oa_type = :oa_type:',
                    'bind' => ['oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT],
                ]
            ]
        );
    }

    /**
     * 通过单号找到model
     * @param string $no 单据号
     * @return mixed
     */
    public function getModelByNo(string $no)
    {
        return self::findFirst([
            'conditions' => 'no = :no:',
            'bind' => ['no' => $no],
        ]);
    }

    /**
     * 通过单号数组获得Model=待支付的
     * @param array $no 单据号组
     * @param bool $has_pay 是否包含已支付
     * @param bool $is_row
     * @return array|mixed
     */
    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'no in ({nos:array}) and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'no in ({nos:array}) and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY];
        }
        $details = self::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);

        //校验单据行所关联的单据头的审批状态是“已通过”，如果不是已通过，则提示：代理支付的单号没有审批通过，不可关联。如果导入文件里某一代理单据行校验不通过，则本次上传整体失败
        $detail_list = $details->toArray();
        if ($detail_list) {
            $agency_payment_ids = array_values(array_unique(array_column($detail_list, 'agency_payment_id')));
            $approved_count = AgencyPaymentModel::count([
                'conditions' => 'id in ({ids:array}) and status = :status:',
                'bind' => ['ids' => $agency_payment_ids, 'status' => Enums::WF_STATE_APPROVED]
            ]);
            if (count($agency_payment_ids) != $approved_count) {
                return [
                    'msg' => 'agency_payment_detail_pay_flow_error'
                ];
            }
        }
        return $details;
    }

    /**
     * 通过model获得银行流水格式化数据
     * @return array|mixed
     * @throws ValidationException
     */
    public function getFormatData()
    {
        $agency_payment_info = AgencyPaymentService::getInstance()->getAgencyPaymentInfoById($this->agency_payment_id);
        return [
            'oa_value' => $this->id,
            'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT,
            'no' => $this->no,
            'amount' => $this->amount_total_actually,
            'currency' => $agency_payment_info->currency,
            'status'   => $agency_payment_info->status,
            'pay_status' => $this->pay_status
        ];
    }

    /**
     * model 关联银行流水  bank_name=银行名字  bank_account=银行账号   date=银行流水日期   签收日期=银行流水日期
     * @param array $data 参数组
     * @return bool|mixed
     * @throws BusinessException
     */
    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING) {
            throw new BusinessException('not found agency payment detail or agency payment detail pay status is error');
        }

        $item = [];
        $now = date('Y-m-d H:i:s');
        $item['pay_status'] = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY;//已付款
        $item['is_pay'] = AgencyPaymentEnums::IS_PAY_YES;
        $item['pay_staff_id'] = $data['create_id'];
        $item['pay_bk_name'] = $data['bank_name'];
        $item['pay_bk_account'] = $data['bank_account'];
        $item['pay_at'] = $now;
        $item['pay_bank_flow_date'] = $data['date'];
        $item['pay_remark'] = $data['ticket_no'] ?? '';
        $item['pay_from'] = 2;
        $item['updated_at'] = $now;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("代理支付-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }


    /**
     * 批量关联银行流水
     * @param array $ids 单据ID组
     * @param array $data 支付信息组
     * @return bool|mixed
     * @throws BusinessException
     */
    public function batch_link($ids, $data)
    {
        $now = date('Y-m-d H:i:s');
        $sql = 'update agency_payment_detail set 
                         pay_status=' . PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY . ',
                         is_pay="' . AgencyPaymentEnums::IS_PAY_YES . '",
                         pay_staff_id="' . $data['create_id'] . '",
                         pay_bk_name="' . $data['bank_name'] . '",
                         pay_bk_account="' . $data['bank_account'] . '",
                         pay_at="' . $now . '",
                         pay_bank_flow_date="' . $data['date'] . '",
                         pay_remark="' . $data['ticket_no'] . '",
                         updated_at="' . $now . '",
                         pay_from=2 where id in (' . implode(',', $ids) . ')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('代理支付-批量更新失败==' . $sql);
        }
        return true;
    }

    /**
     * 关联银行流水撤销
     * @param array $user
     * @return bool|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function cancel($user)
    {
        //判断现有的状态
        if (empty($this) || $this->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            throw new BusinessException('not found agency payment detail or agency payment detail pay status is error');
        }
        $now = date('Y-m-d H:i:s');
        $item = [];
        $item['payer_bank'] = '';
        $item['pay_status'] = PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING;
        $item['is_pay'] = 0;
        $item['pay_staff_id'] = 0;
        $item['pay_bk_name'] = '';
        $item['pay_bk_account'] = '';
        $item['pay_bank_flow_date'] = null;
        $item['pay_at'] = null;
        $item['pay_remark'] = '';
        $item['pay_from'] = 1;
        $item['updated_at'] = $now;
        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("代理支付-撤销失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }

        //变更单据头
        $agency_payment_info = AgencyPaymentService::getInstance()->getAgencyPaymentInfoById($this->agency_payment_id);
        $agency_payment_info->pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING;
        $agency_payment_info->updated_at = $now;
        $bool = $agency_payment_info->save();
        if ($bool === false) {
            throw new BusinessException('代理支付-撤销-变更单据头支付信息失败', ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    /**
     * model 批量确认银行流水 - 代理支付没有
     * @param array $ids 单据ID组
     * @param array $data 支付信息组
     * @return bool|mixed
     */
    public function batch_confirm($ids, $data)
    {
        return true;
    }

    /**
     * 获得支付模块需要数据
     * @return array
     * @throws ValidationException
     */
    public function getPayData()
    {
        $agency_payment_info = AgencyPaymentService::getInstance()->getAgencyPaymentInfoById($this->agency_payment_id);
        //应付日期
        $default_planned_pay_date = $this->due_date ?: date('Y-m-d');
        //计划支付日期
        $planned_pay_date = $this->due_date ?: date('Y-m-d');
        if (isCountry('TH')) {
            $planned_pay_date = $this->due_date ? date('Y-m-d',
                strtotime($this->due_date . ' + 1 day')) : date('Y-m-d');
        }
        $arr = [
            'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT,
            'no' => $this->no,
            'apply_staff_id' => $agency_payment_info->apply_id,
            'apply_staff_name' => $agency_payment_info->apply_name,
            'cost_department_id' => $agency_payment_info->cost_sys_department_id,//费用所属部门id
            'cost_department_name' => $agency_payment_info->cost_department_name,//费用所属部门名称
            'cost_company_id' => $agency_payment_info->cost_company_id,//费用所属公司id
            'cost_company_name' => $agency_payment_info->cost_company_name,//费用所属公司名字
            'apply_date' => $agency_payment_info->apply_date,//申请时间
            'pay_method' => $agency_payment_info->pay_method,
            'pay_where' =>1,//境内
            'currency' => $agency_payment_info->currency,
            'amount_total_no_tax' => $this->amount_no_tax,//不含税金额总计 = 不含税金额
            'amount_total_vat' => $this->amount_total_vat, //VAT总计 = VAT税额
            'amount_total_have_tax' => $this->payable_amount,//含税金额总计(含VAT含WHT) = 应付金额
            'amount_total_wht' => $this->amount_total_wht, //WHT总计 = WHT税额
            'amount_total_have_tax_no_wht' => $this->amount_total_actually,   //含税金额总计(含VAT不含WHT) = 实付金额
            'amount_loan' => 0,//冲减借款金额
            'amount_reserve' => 0,
            'amount_discount' => 0,//折扣
            'amount_total_actually' => $this->amount_total_actually,//实付金额总计 = 实付金额
            'amount_remark' => $this->remark,//备注
            'default_planned_pay_date' => $default_planned_pay_date,//应付日期
            'planned_pay_date' => $planned_pay_date,//计划支付日期
        ];

        $arr['pays'][] = [
            'staff_info_id' => $this->payee_staff_id ? $this->payee_staff_id : NULL,//收款人ID
            'bank_name' => $this->bank_name,//收款人银行
            'bank_account' => $this->bank_account,//收款人账号
            'bank_account_name' => $this->bank_account_name,//收款人户名
            'amount' => $this->amount_total_actually,//付款金额
            'bank_address' => '',
            'swift_code' => '',
            'contract_no' => ''
        ];
        return $arr;
    }

    /**
     * 支付模块回调修改的时候调用
     * @param array $data 参数组
     * @return array
     */
    public function getPayCallBackData($data)
    {
        $new = [];
        $pay_status = $data['pay_status'];
        if ($pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            //三级审批人-支付；已支付
            $new['pay_bank_flow_date'] = $data['pay_bank_flow_date'] ?? null;
            $new['pay_status'] = $pay_status;
            $new['pay_bk_name'] = $data['pay_bank_name'];
            $new['pay_bk_account'] = $data['pay_bank_account'];
            $new['pay_remark'] = $data['pay_remark'] ?? '';
            $new['is_pay'] = AgencyPaymentEnums::IS_PAY_YES;
        } else if ($pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
            //申请人-撤回；未支付
            $new['pay_status'] = $pay_status;
            $new['pay_remark'] = $data['not_pay_reason'] ?? '';
            $new['is_pay'] = AgencyPaymentEnums::IS_PAY_NO;
            $new['pay_bk_name'] = '';
            $new['pay_bk_account'] = '';
        }
        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     * @param string $no 单据号
     * @param integer $pay_id 支付信息ID
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no, $pay_id)
    {
        return [];
    }

    /**
     * 更新支付模块标记(标记为已进入支付模块)
     * 在审批通过那一刻已经到打上标记了，推送到支付模块无需重复打标记
     * @return bool
     */
    public function updatePayTag():bool
    {
        return true;
    }
    
    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }

}
