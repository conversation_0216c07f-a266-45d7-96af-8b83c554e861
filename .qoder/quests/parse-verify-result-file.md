# parseVerifyResultFile 方法设计文档

## 1. 概述

本文档设计 `parseVerifyResultFile` 方法，该方法用于解析FlashPay SFTP验证结果文件并处理支付失败的OA单据。该方法参考了现有的 `processResultRow` 方法的实现逻辑。

## 2. 功能需求

### 2.1 核心功能
- 解析Excel验证结果文件，文件包含以下表头：
  - Row No：行号
  - Merchant Batch No：商户批次号
  - Merchant Order No：商户订单号（OA交易号）
  - Fail reasons：失败原因

### 2.2 处理逻辑
1. 处理当前批次下的所有OA单据
2. 将OA单据标记为FlashPay支付失败
3. 更新支付状态为pay支付失败
4. 更新发送状态为失败
5. 返回到一级支付人（重新发起审批流程）
6. 存储支付失败结果和错误原因

## 3. 技术架构

### 3.1 输入参数
```php
public function parseVerifyResultFile($file_path)
```

### 3.2 数据流程图
```mermaid
graph TD
    A[开始解析验证结果文件] --> B[读取Excel文件]
    B --> C[验证文件格式]
    C --> D[解析表头映射]
    D --> E[逐行处理数据]
    E --> F[根据Merchant Order No查找OA单据]
    F --> G[更新支付状态为失败]
    G --> H[存储失败原因]
    H --> I[重新发起审批流程]
    I --> J[返回处理结果]
    
    F --> K[未找到对应单据]
    K --> L[记录预警日志]
    L --> E
```

### 3.3 核心处理逻辑
```mermaid
sequenceDiagram
    participant P as parseVerifyResultFile
    participant E as Excel Reader
    participant DB as Database
    participant WF as WorkflowService
    participant NS as NotificationService
    
    P->>E: 读取Excel文件
    E-->>P: 返回数据行
    
    loop 处理每一行数据
        P->>DB: 查询PaymentOnlinePay记录
        alt 找到记录
            P->>DB: 查询Payment记录
            P->>DB: 更新支付状态为失败
            P->>WF: 重新发起审批流程
        else 未找到记录
            P->>NS: 发送预警通知
        end
    end
    
    P-->>P: 返回处理结果
```

## 4. 详细设计

### 4.1 方法签名
```php
/**
 * 解析验证结果文件并处理支付失败单据
 * @param string $file_path Excel文件路径
 * @return array 处理结果
 * @throws BusinessException|ValidationException
 */
public function parseVerifyResultFile($file_path): array
```

### 4.2 数据结构设计

#### 4.2.1 Excel表头映射
```php
private function mapVerifyResultColumns($headers): array
{
    $columnMap = [];
    $requiredColumns = [
        'Row No' => 'row_no',
        'Merchant Batch No' => 'merchant_batch_no', 
        'Merchant Order No' => 'merchant_order_no',
        'Fail reasons' => 'fail_reasons'
    ];
    // 映射逻辑
    return $columnMap;
}
```

#### 4.2.2 处理结果数据结构
```php
$result = [
    'success' => true,
    'processed_count' => 0,
    'failed_count' => 0,
    'details' => [
        [
            'merchant_order_no' => 'xxx',
            'oa_no' => 'xxx', 
            'fail_reasons' => 'xxx',
            'status' => 'success|failed',
            'message' => 'xxx'
        ]
    ]
];
```

### 4.3 核心处理方法

#### 4.3.1 处理单行验证结果
```php
/**
 * 处理验证结果文件单行数据
 * @param array $row 行数据
 * @param array $columnMap 列映射
 * @return array 处理结果
 */
private function processVerifyResultRow($row, $columnMap): array
{
    $db = $this->getDI()->get('db_oa');
    $db->begin();
    
    try {
        // 1. 获取关键字段值
        $merchantOrderNo = trim($row[$columnMap['merchant_order_no']] ?? '');
        $failReasons = trim($row[$columnMap['fail_reasons']] ?? '');
        
        // 2. 验证必要字段
        if (empty($merchantOrderNo)) {
            throw new BusinessException('商户订单号为空');
        }
        
        // 3. 查询PaymentOnlinePay记录
        $paymentOnlineModel = PaymentOnlinePayModel::findFirst([
            'conditions' => 'out_trade_no = :out_trade_no:',
            'bind' => ['out_trade_no' => $merchantOrderNo],
        ]);
        
        if (empty($paymentOnlineModel)) {
            // 发送预警通知
            $alertMessage = sprintf('验证结果处理：未找到交易号 %s 的支付记录', $merchantOrderNo);
            FlashPayHelper::sendNotice($alertMessage);
            throw new Exception($alertMessage);
        }
        
        // 4. 查询Payment记录
        $payment = Payment::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $paymentOnlineModel->payment_id],
            'for_update' => true,
        ]);
        
        if (empty($payment)) {
            $alertMessage = sprintf('验证结果处理：未找到支付单据 %s', $merchantOrderNo);
            FlashPayHelper::sendNotice($alertMessage);
            throw new Exception($alertMessage);
        }
        
        // 5. 更新支付状态
        $this->updatePaymentStatusToFailed($payment, $paymentOnlineModel, $failReasons);
        
        $db->commit();
        
        return [
            'merchant_order_no' => $merchantOrderNo,
            'oa_no' => $payment->no,
            'fail_reasons' => $failReasons,
            'status' => 'success',
            'message' => '处理成功'
        ];
        
    } catch (Exception $e) {
        $db->rollback();
        return [
            'merchant_order_no' => $merchantOrderNo ?? '',
            'oa_no' => '',
            'fail_reasons' => $failReasons ?? '',
            'status' => 'failed', 
            'message' => $e->getMessage()
        ];
    }
}
```

#### 4.3.2 更新支付状态为失败
```php
/**
 * 更新支付状态为失败并重新发起流程
 * @param Payment $payment 支付记录
 * @param PaymentOnlinePayModel $paymentOnlineModel 在线支付记录
 * @param string $failReasons 失败原因
 * @return void
 * @throws Exception
 */
private function updatePaymentStatusToFailed($payment, $paymentOnlineModel, $failReasons): void
{
    $zeroTime = gmdate('Y-m-d H:i:s');
    
    // 1. 更新Payment表状态
    $payment->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED; // pay支付失败
    $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED; // 发送状态:失败
    $payment->payer_id = $this->getPayer(); // 最终支付人
    $payment->payer_date = $zeroTime; // 最终支付人操作时间
    $payment->updated_at = $zeroTime;
    
    if (!$payment->update()) {
        throw new Exception('更新支付记录失败: ' . implode(', ', $payment->getMessages()));
    }
    
    // 2. 更新PaymentOnlinePay表失败原因
    $paymentOnlineModel->fail_reasons = $failReasons; // 存储失败原因
    $paymentOnlineModel->updated_at = $zeroTime;
    
    if (!$paymentOnlineModel->update()) {
        throw new Exception('更新在线支付记录失败: ' . implode(', ', $paymentOnlineModel->getMessages()));
    }
    
    // 3. 重新发起审批流程（返回到一级支付人）
    $this->flashPayFailedReject($payment, $payment->payer_id);
}
```

#### 4.3.3 获取支付人
```php
/**
 * 获取pay在线支付人
 * @return array
 */
private function getPayer(): array
{
    $payerArr = \App\Modules\Common\Services\EnumsService::getInstance()->getPayModulePayer();
    return array_shift($payerArr);
}
```

#### 4.3.4 重新发起审批流程
```php
/**
 * 支付失败单据重新发起审批流程
 * @param Payment $payment 支付单据
 * @param array $userId 支付人用户ID
 * @throws BusinessException
 */
private function flashPayFailedReject($payment, $userId): void
{
    // 获取审批流请求
    $payFlowService = new PayFlowService();
    $request = $payFlowService->getRequest($payment->id);
    
    // 记录驳回原因
    $rejectReason = '验证结果显示支付失败，系统自动驳回重新发起';
    $userService = new UserService();
    $user = $userService->getUserById($userId);
    $user = (new BaseController())->format_user($user);
    
    WorkflowServiceV2::getInstance()->saveAuditLog($request, 0, $user, Enums::WF_ACTION_REJECT, $rejectReason);
    
    // 重新发起审批流程
    $payFlowService->recommit($payment, []);
}
```

## 5. 错误处理策略

### 5.1 异常分类处理
```php
// 1. 文件格式错误
if (!file_exists($excelFile)) {
    throw new BusinessException('Excel文件不存在: ' . $excelFile);
}

// 2. 数据格式错误
if (empty($columnMap)) {
    throw new BusinessException('Excel表头格式不正确，无法识别必要字段');
}

// 3. 业务数据异常
if (empty($paymentOnlineModel)) {
    FlashPayHelper::sendNotice($alertMessage);
    // 继续处理下一条，不中断整个流程
}
```

### 5.2 预警通知机制
- 未找到交易号对应的支付记录时发送飞书预警
- 系统异常时发送错误通知
- 处理完成后发送汇总报告

## 6. 性能优化考虑

### 6.1 批量处理优化
- 数据库事务按行处理，避免长事务
- 批量查询优化，减少数据库访问次数
- 内存使用优化，处理大文件时分批处理

### 6.2 日志记录
```php
$this->logger->info('验证结果文件处理开始: ' . $file_path);
$this->logger->info('处理订单: ' . $payment->no . ' 结果: 成功/失败');
$this->logger->error('处理订单失败: ' . $e->getMessage());
```

## 7. 测试用例设计

### 7.1 正常流程测试
- 标准Excel文件解析
- 正常支付失败单据处理
- 审批流程重新发起

### 7.2 异常情况测试  
- 文件不存在或格式错误
- 表头映射失败
- 找不到对应的支付记录
- 数据库操作失败
- 工作流操作失败

### 7.3 边界条件测试
- 空文件处理
- 大文件处理性能
- 特殊字符处理
- 并发处理安全性

## 8. 部署和监控

### 8.1 部署要求
- 确保Excel读取库可用
- 数据库连接正常
- 工作流服务正常
- 通知服务正常

### 8.2 监控指标
- 文件处理成功率
- 处理耗时统计
- 错误类型统计
- 业务影响评估